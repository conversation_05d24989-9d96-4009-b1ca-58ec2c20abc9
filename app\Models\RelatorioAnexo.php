<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class RelatorioAnexo extends Model
{
    use HasFactory;

    protected $table = 'relatorio_anexos';

    protected $fillable = [
        'relatorio_sessao_id',
        'nome_arquivo',
        'nome_original',
        'caminho_arquivo',
        'tamanho_arquivo',
        'tipo_mime',
        'tipo_anexo',
        'descricao',
    ];

    // Relacionamentos
    public function relatorioSessao()
    {
        return $this->belongsTo(RelatorioSessao::class);
    }

    // Métodos auxiliares
    public function getUrlAttribute()
    {
        return Storage::disk('public')->url($this->caminho_arquivo);
    }

    public function getTamanhoFormatadoAttribute()
    {
        $bytes = $this->tamanho_arquivo;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getIsImagemAttribute()
    {
        return $this->tipo_anexo === 'imagem' || str_starts_with($this->tipo_mime, 'image/');
    }

    public function getIsDocumentoAttribute()
    {
        return $this->tipo_anexo === 'documento' || in_array($this->tipo_mime, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain'
        ]);
    }
}
