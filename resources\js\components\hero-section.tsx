import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON> } from '@inertiajs/react';
import { Award, ChevronRight, Clock, Heart, MapPin, Search, Shield, Users } from 'lucide-react';

export default function HeroSection() {
    return (
        <main className="overflow-hidden">
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-36">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="md:w-1/2">
                            <div>
                                <h1 className="max-w-md text-5xl font-medium text-balance md:text-6xl">
                                    Fisioterapia Domiciliar
                                    <span className="block text-primary">Especializada</span>
                                </h1>
                                <p className="my-8 max-w-2xl text-xl text-balance text-muted-foreground">
                                    Tratamentos fisioterapêuticos profissionais no conforto e segurança da sua casa. Fisioterapeutas credenciados,
                                    equipamentos modernos e resultados comprovados.
                                </p>

                                <div className="flex items-center gap-3">
                                    <Button asChild size="lg" className="pr-4.5">
                                        <Link href="/buscar">
                                            <Search className="mr-2 h-4 w-4" />
                                            <span className="text-nowrap">Buscar Serviços</span>
                                            <ChevronRight className="ml-1 opacity-50" />
                                        </Link>
                                    </Button>
                                    <Button asChild size="lg" variant="outline" className="pl-5">
                                        <Link href="/contato">
                                            <Heart className="fill-primary/25 stroke-primary" />
                                            <span className="text-nowrap">Fale Conosco</span>
                                        </Link>
                                    </Button>
                                </div>
                            </div>

                            <div className="mt-10">
                                <p className="text-muted-foreground">Nossos diferenciais:</p>
                                <div className="mt-6 grid max-w-2xl grid-cols-2 gap-6 md:grid-cols-3">
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg">
                                            <Shield className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">Profissionais</p>
                                            <p className="text-xs text-muted-foreground">Credenciados</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg">
                                            <Clock className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">Atendimento</p>
                                            <p className="text-xs text-muted-foreground">24/7</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg">
                                            <MapPin className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">Cobertura</p>
                                            <p className="text-xs text-muted-foreground">Grande SP</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg">
                                            <Users className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">+500</p>
                                            <p className="text-xs text-muted-foreground">Pacientes</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg">
                                            <Award className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">4.9★</p>
                                            <p className="text-xs text-muted-foreground">Avaliação</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg">
                                            <Heart className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">Cuidado</p>
                                            <p className="text-xs text-muted-foreground">Personalizado</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="mt-24 translate-x-12 perspective-near md:absolute md:top-40 md:-right-6 md:bottom-16 md:left-1/2 md:mt-0 md:translate-x-0">
                        <div className="relative h-full before:absolute before:-inset-x-4 before:top-0 before:bottom-7 before:skew-x-6 before:rounded-[calc(var(--radius)+1rem)] before:border before:border-foreground/5 before:bg-foreground/5">
                            <div className="relative h-full -translate-y-12 skew-x-6 overflow-hidden rounded-[var(--radius)] border border-transparent shadow-md ring-1 shadow-foreground/10 ring-foreground/5">
                                <img
                                    src="/images/hero-physiotherapy.jpg"
                                    alt="Fisioterapeuta atendendo paciente"
                                    className="h-full w-full object-cover"
                                    loading="eager"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    );
}
