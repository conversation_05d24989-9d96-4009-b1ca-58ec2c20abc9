<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('horarios_base', function (Blueprint $table) {
            $table->id();
            $table->foreignId('fisioterapeuta_id')->constrained('users')->onDelete('cascade');
            $table->tinyInteger('dia_semana'); // 0=domingo, 1=segunda, ..., 6=sábado
            $table->time('hora_inicio');
            $table->time('hora_fim');
            $table->string('periodo_nome')->nullable(); // Ex: "Manhã", "Tarde", "Noite"
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Índices para performance
            $table->index(['fisioterapeuta_id', 'dia_semana', 'ativo']);
            $table->index(['fisioterapeuta_id', 'ativo']);
            
            // Constraint para evitar sobreposição de horários no mesmo dia
            $table->unique(['fisioterapeuta_id', 'dia_semana', 'hora_inicio', 'hora_fim'], 'unique_horario_base');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('horarios_base');
    }
};
