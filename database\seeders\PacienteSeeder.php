<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;

class PacienteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pacientes = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('paciente123'),
                'role' => 'paciente',
                'active' => true,
                'phone' => '(11) 98888-1111',
                'birth_date' => '1965-03-15',
                'address' => 'Rua das Flores, 123',
                'city' => 'São Paulo',
                'state' => 'SP',
                'zip_code' => '01234-567',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('paciente123'),
                'role' => 'paciente',
                'active' => true,
                'phone' => '(11) 98888-2222',
                'birth_date' => '1978-07-22',
                'address' => 'Av. <PERSON>, 456',
                'city' => 'São Paulo',
                'state' => 'SP',
                'zip_code' => '01310-100',
            ],
            [
                'name' => '<PERSON> <PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('paciente123'),
                'role' => 'paciente',
                'active' => true,
                'phone' => '(11) 98888-3333',
                'birth_date' => '1952-11-08',
                'address' => 'Rua Augusta, 789',
                'city' => 'São Paulo',
                'state' => 'SP',
                'zip_code' => '01305-000',
            ],
            [
                'name' => 'Ana Ferreira',
                'email' => '<EMAIL>',
                'password' => bcrypt('paciente123'),
                'role' => 'paciente',
                'active' => true,
                'phone' => '(11) 98888-4444',
                'birth_date' => '1985-12-03',
                'address' => 'Rua da Consolação, 321',
                'city' => 'São Paulo',
                'state' => 'SP',
                'zip_code' => '01302-000',
            ],
            [
                'name' => 'Roberto Costa',
                'email' => '<EMAIL>',
                'password' => bcrypt('paciente123'),
                'role' => 'paciente',
                'active' => true,
                'phone' => '(11) 98888-5555',
                'birth_date' => '1970-05-18',
                'address' => 'Rua Oscar Freire, 654',
                'city' => 'São Paulo',
                'state' => 'SP',
                'zip_code' => '01426-000',
            ],
            [
                'name' => 'Lucia Pereira',
                'email' => '<EMAIL>',
                'password' => bcrypt('paciente123'),
                'role' => 'paciente',
                'active' => true,
                'phone' => '(11) 98888-6666',
                'birth_date' => '1960-09-25',
                'address' => 'Rua Haddock Lobo, 987',
                'city' => 'São Paulo',
                'state' => 'SP',
                'zip_code' => '01414-000',
            ],
            [
                'name' => 'Pedro Almeida',
                'email' => '<EMAIL>',
                'password' => bcrypt('paciente123'),
                'role' => 'paciente',
                'active' => true,
                'phone' => '(11) 98888-7777',
                'birth_date' => '1988-01-12',
                'address' => 'Rua Bela Cintra, 147',
                'city' => 'São Paulo',
                'state' => 'SP',
                'zip_code' => '01415-000',
            ],
            [
                'name' => 'Fernanda Lima',
                'email' => '<EMAIL>',
                'password' => bcrypt('paciente123'),
                'role' => 'paciente',
                'active' => true,
                'phone' => '(11) 98888-8888',
                'birth_date' => '1975-04-30',
                'address' => 'Rua Estados Unidos, 258',
                'city' => 'São Paulo',
                'state' => 'SP',
                'zip_code' => '01427-000',
            ]
        ];

        foreach ($pacientes as $paciente) {
            User::create($paciente);
        }
    }
}
