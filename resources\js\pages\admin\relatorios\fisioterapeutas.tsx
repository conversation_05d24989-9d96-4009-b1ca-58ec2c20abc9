import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Activity, ArrowLeft, Clock, DollarSign, Download, Filter, RefreshCw, Star, TrendingUp, Users } from 'lucide-react';
import { useState } from 'react';
import { Bar, BarChart, CartesianGrid, Cell, Line, LineChart, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface Stats {
    total_fisioterapeutas: number;
    fisioterapeutas_ativos: number;
    fisioterapeutas_inativos: number;
    satisfacao_media: number;
    tempo_resposta_medio: number;
    receita_media_por_fisio: number;
    sessoes_media_por_fisio: number;
    taxa_utilizacao: number;
}

interface RankingFisio {
    fisioterapeuta: string;
    total_sessoes: number;
    satisfacao_media: number;
    receita_total: number;
    posicao: number;
}

interface EspecialidadesDemanda {
    especialidade: string;
    demanda: number;
    fisioterapeutas: number;
}

interface DistribuicaoGeografica {
    regiao: string;
    quantidade: number;
    percentual: number;
}

interface PerformanceTempo {
    data: string;
    sessoes: number;
    satisfacao: number;
    receita: number;
}

interface Props {
    stats: Stats;
    rankingFisioterapeutas: RankingFisio[];
    especialidadesDemanda: EspecialidadesDemanda[];
    distribuicaoGeografica: DistribuicaoGeografica[];
    performanceTempo: PerformanceTempo[];
    filtros: {
        data_inicio?: string;
        data_fim?: string;
    };
}

const COLORS = ['#3b82f6', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#f97316'];

export default function RelatorioFisioterapeutas({
    stats,
    rankingFisioterapeutas,
    especialidadesDemanda,
    distribuicaoGeografica,
    performanceTempo,
    filtros,
}: Props) {
    const [dataInicio, setDataInicio] = useState(filtros.data_inicio || '');
    const [dataFim, setDataFim] = useState(filtros.data_fim || '');
    const [isLoading, setIsLoading] = useState(false);

    const handleFilter = () => {
        setIsLoading(true);
        router.get(
            route('admin.relatorios.fisioterapeutas'),
            {
                data_inicio: dataInicio,
                data_fim: dataFim,
            },
            {
                preserveState: true,
                onFinish: () => setIsLoading(false),
            },
        );
    };

    const handleExport = (formato: string) => {
        const params = new URLSearchParams({
            tipo: 'fisioterapeutas',
            formato,
            data_inicio: dataInicio,
            data_fim: dataFim,
        });

        window.open(`${route('admin.relatorios.export')}?${params.toString()}`, '_blank');
    };

    const formatNumber = (value: number) => {
        return new Intl.NumberFormat('pt-BR').format(value);
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const formatPercentage = (value: number | null | undefined) => {
        return `${(value || 0).toFixed(1)}%`;
    };

    const formatTime = (minutes: number) => {
        const hours = Math.floor(minutes / 60);
        const mins = Math.round(minutes % 60);
        if (hours > 0) {
            return `${hours}h ${mins}min`;
        }
        return `${mins}min`;
    };

    const getRankingBadge = (posicao: number) => {
        if (posicao === 1) {
            return <Badge className="bg-yellow-500 text-white">🥇 1º</Badge>;
        } else if (posicao === 2) {
            return <Badge className="bg-gray-400 text-white">🥈 2º</Badge>;
        } else if (posicao === 3) {
            return <Badge className="bg-amber-600 text-white">🥉 3º</Badge>;
        } else {
            return <Badge variant="outline">{posicao}º</Badge>;
        }
    };

    return (
        <AppLayout>
            <Head title="Relatório de Fisioterapeutas" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href={route('admin.relatorios.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Relatório de Fisioterapeutas</h1>
                            <p className="text-muted-foreground">Análise de performance e métricas dos fisioterapeutas</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={() => handleExport('csv')} className="gap-2">
                            <Download className="h-4 w-4" />
                            Exportar CSV
                        </Button>
                        <Button variant="outline" onClick={() => handleExport('excel')} className="gap-2">
                            <Download className="h-4 w-4" />
                            Exportar Excel
                        </Button>
                    </div>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div className="space-y-2">
                                <Label htmlFor="data_inicio">Data Início</Label>
                                <Input id="data_inicio" type="date" value={dataInicio} onChange={(e) => setDataInicio(e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="data_fim">Data Fim</Label>
                                <Input id="data_fim" type="date" value={dataFim} onChange={(e) => setDataFim(e.target.value)} />
                            </div>
                            <div className="flex items-end">
                                <Button onClick={handleFilter} disabled={isLoading} className="w-full gap-2">
                                    {isLoading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Filter className="h-4 w-4" />}
                                    Aplicar Filtros
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Métricas Principais */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Fisioterapeutas</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(stats.total_fisioterapeutas)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Fisioterapeutas Ativos</CardTitle>
                            <Activity className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{formatNumber(stats.fisioterapeutas_ativos)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Satisfação Média</CardTitle>
                            <Star className="h-4 w-4 text-yellow-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600">{(stats.satisfacao_media || 0).toFixed(1)}/5</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Taxa de Utilização</CardTitle>
                            <TrendingUp className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-600">{formatPercentage(stats.taxa_utilizacao)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Tempo de Resposta Médio</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatTime(stats.tempo_resposta_medio)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Receita Média por Fisio</CardTitle>
                            <DollarSign className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.receita_media_por_fisio)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Sessões Média por Fisio</CardTitle>
                            <Activity className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{(stats.sessoes_media_por_fisio || 0).toFixed(1)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Fisioterapeutas Inativos</CardTitle>
                            <Users className="h-4 w-4 text-red-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{formatNumber(stats.fisioterapeutas_inativos)}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Gráficos */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Performance ao Longo do Tempo</CardTitle>
                            <CardDescription>Evolução das métricas principais</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <LineChart data={performanceTempo}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="data" />
                                    <YAxis />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="sessoes" stroke="#3b82f6" strokeWidth={2} name="Sessões" />
                                    <Line type="monotone" dataKey="satisfacao" stroke="#f59e0b" strokeWidth={2} name="Satisfação" />
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Distribuição Geográfica</CardTitle>
                            <CardDescription>Fisioterapeutas por região</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <PieChart>
                                    <Pie
                                        data={distribuicaoGeografica}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ regiao, percentual }) => `${regiao} ${(percentual || 0).toFixed(1)}%`}
                                        outerRadius={80}
                                        fill="#8884d8"
                                        dataKey="quantidade"
                                    >
                                        {distribuicaoGeografica.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                        ))}
                                    </Pie>
                                    <Tooltip />
                                </PieChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Demanda por Especialidades */}
                <Card>
                    <CardHeader>
                        <CardTitle>Demanda por Especialidades</CardTitle>
                        <CardDescription>Especialidades mais procuradas vs disponibilidade</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={especialidadesDemanda}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="especialidade" />
                                <YAxis />
                                <Tooltip />
                                <Bar dataKey="demanda" fill="#3b82f6" name="Demanda" />
                                <Bar dataKey="fisioterapeutas" fill="#22c55e" name="Fisioterapeutas" />
                            </BarChart>
                        </ResponsiveContainer>
                    </CardContent>
                </Card>

                {/* Ranking de Fisioterapeutas */}
                <Card>
                    <CardHeader>
                        <CardTitle>Ranking de Fisioterapeutas</CardTitle>
                        <CardDescription>Top performers do período</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {rankingFisioterapeutas.map((fisio, index) => (
                                <div key={index} className="flex items-center justify-between rounded-lg border p-4">
                                    <div className="flex items-center gap-4">
                                        {getRankingBadge(fisio.posicao)}
                                        <div>
                                            <p className="font-medium">{fisio.fisioterapeuta}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatNumber(fisio.total_sessoes)} sessões • Satisfação: {(fisio.satisfacao_media || 0).toFixed(1)}/5
                                            </p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <p className="font-medium text-green-600">{formatCurrency(fisio.receita_total)}</p>
                                        <p className="text-sm text-muted-foreground">Receita total</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
