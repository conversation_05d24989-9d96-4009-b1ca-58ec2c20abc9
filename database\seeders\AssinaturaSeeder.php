<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Assinatura;
use App\Models\User;
use App\Models\Plano;
use Carbon\Carbon;

class AssinaturaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pacientes = User::where('role', 'paciente')->get();
        $planos = Plano::where('active', true)->get();

        if ($pacientes->isEmpty() || $planos->isEmpty()) {
            return;
        }

        $statuses = ['ativa', 'suspensa', 'cancelada'];
        $assinaturas = [];

        // Criar assinaturas para alguns pacientes
        foreach ($pacientes->take(6) as $paciente) {
            $plano = $planos->random();
            $status = $statuses[array_rand($statuses)];

            // Definir datas baseadas no status
            $startDate = Carbon::now()->subDays(rand(30, 365));
            $endDate = null;
            $currentPeriodStart = Carbon::now()->startOfMonth();
            $currentPeriodEnd = Carbon::now()->endOfMonth();

            if ($status === 'cancelada') {
                $endDate = Carbon::now()->subDays(rand(1, 30));
                $currentPeriodEnd = $endDate;
            }

            $assinaturas[] = [
                'user_id' => $paciente->id,
                'plano_id' => $plano->id,
                'status' => $status,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'sessions_used' => $status === 'ativa' ? rand(0, $plano->sessions_per_month) : 0,
                'current_period_start' => $currentPeriodStart,
                'current_period_end' => $currentPeriodEnd,
                'monthly_price' => $plano->price,
                'created_at' => $startDate,
                'updated_at' => now(),
            ];
        }

        Assinatura::insert($assinaturas);
    }
}
