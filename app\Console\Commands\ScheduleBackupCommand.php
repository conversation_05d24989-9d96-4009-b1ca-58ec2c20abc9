<?php

namespace App\Console\Commands;

use App\Jobs\ProcessBackupJob;
use App\Jobs\CleanupOldBackupsJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ScheduleBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:schedule {type=full : Type of backup (full or database)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Schedule automatic backup of the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');
        
        if (!in_array($type, ['full', 'database'])) {
            $this->error('Invalid backup type. Use "full" or "database".');
            return 1;
        }

        try {
            $this->info("Scheduling {$type} backup...");
            
            // Dispatch backup job
            ProcessBackupJob::dispatch($type);
            
            $this->info('Backup job has been queued successfully!');
            
            // Also schedule cleanup of old backups
            CleanupOldBackupsJob::dispatch(30); // Keep backups for 30 days
            
            $this->info('Cleanup job has been queued successfully!');
            
            Log::info('Backup scheduled via command', [
                'type' => $type,
                'scheduled_at' => now()
            ]);
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Failed to schedule backup: ' . $e->getMessage());
            
            Log::error('Failed to schedule backup via command', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            
            return 1;
        }
    }
}
