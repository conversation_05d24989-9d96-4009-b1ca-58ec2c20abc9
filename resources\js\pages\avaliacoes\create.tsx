import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import { Star, User } from 'lucide-react';
import { useState } from 'react';

interface Agendamento {
    id: number;
    data: string;
    fisioterapeuta: {
        id: number;
        nome: string;
        avatar?: string;
    };
}

interface Props {
    agendamento: Agendamento;
    pontos_positivos: Record<string, string>;
    pontos_melhorar: Record<string, string>;
}

export default function AvaliacaoCreate() {
    const { agendamento, pontos_positivos, pontos_melhorar } = usePage().props as unknown as Props;

    const { data, setData, post, processing, errors } = useForm({
        agendamento_id: agendamento.id,
        nota_geral: 0,
        nota_pontualidade: 0,
        nota_profissionalismo: 0,
        nota_eficacia: 0,
        comentario: '',
        recomendaria: true,
        pontos_positivos: [] as string[],
        pontos_melhorar: [] as string[],
        anonima: false,
    });

    const [hoveredStar, setHoveredStar] = useState<{ [key: string]: number }>({});

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('avaliacoes.store'));
    };

    const renderStars = (field: string, value: number) => {
        return (
            <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                    <button
                        key={star}
                        type="button"
                        className="focus:outline-none"
                        onMouseEnter={() => setHoveredStar({ ...hoveredStar, [field]: star })}
                        onMouseLeave={() => setHoveredStar({ ...hoveredStar, [field]: 0 })}
                        onClick={() => setData(field as any, star)}
                    >
                        <Star className={`h-6 w-6 ${star <= (hoveredStar[field] || value) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} />
                    </button>
                ))}
            </div>
        );
    };

    const formatarData = (data: string) => {
        return new Date(data).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const breadcrumbs = [
        { title: 'Início', href: '/dashboard' },
        { title: 'Agendamentos', href: '/paciente/agendamentos' },
        { title: 'Avaliar Sessão', href: `/avaliacoes/create?agendamento=${agendamento.id}` },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Avaliar Sessão" />

            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Avaliar Sessão</h1>
                    <p className="text-muted-foreground">Compartilhe sua experiência para ajudar outros pacientes</p>
                </div>

                {/* Informações da Sessão */}
                <Card>
                    <CardHeader>
                        <CardTitle>Informações da Sessão</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center gap-4">
                            {agendamento.fisioterapeuta.avatar ? (
                                <img
                                    src={agendamento.fisioterapeuta.avatar}
                                    alt={agendamento.fisioterapeuta.nome}
                                    className="h-12 w-12 rounded-full object-cover"
                                />
                            ) : (
                                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                                    <User className="h-6 w-6 text-muted-foreground" />
                                </div>
                            )}
                            <div>
                                <h3 className="font-semibold">{agendamento.fisioterapeuta.nome}</h3>
                                <p className="text-sm text-muted-foreground">{formatarData(agendamento.data)}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Formulário de Avaliação */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Nota Geral */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Star className="h-5 w-5" />
                                Avaliação Geral *
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label className="text-base font-medium">Como você avalia a sessão de forma geral?</Label>
                                <div className="mt-2">{renderStars('nota_geral', data.nota_geral)}</div>
                                {errors.nota_geral && <p className="mt-1 text-sm text-red-600">{errors.nota_geral}</p>}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Avaliações Específicas */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Avaliação Detalhada</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div>
                                <Label className="text-base font-medium">Pontualidade</Label>
                                <div className="mt-2">{renderStars('nota_pontualidade', data.nota_pontualidade)}</div>
                            </div>

                            <div>
                                <Label className="text-base font-medium">Profissionalismo</Label>
                                <div className="mt-2">{renderStars('nota_profissionalismo', data.nota_profissionalismo)}</div>
                            </div>

                            <div>
                                <Label className="text-base font-medium">Eficácia do Tratamento</Label>
                                <div className="mt-2">{renderStars('nota_eficacia', data.nota_eficacia)}</div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Pontos Positivos */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Pontos Positivos</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 gap-3 md:grid-cols-3">
                                {Object.entries(pontos_positivos).map(([key, label]) => (
                                    <div key={key} className="flex items-center space-x-2">
                                        <Checkbox
                                            id={`positivo-${key}`}
                                            checked={data.pontos_positivos.includes(key)}
                                            onCheckedChange={(checked) => {
                                                if (checked) {
                                                    setData('pontos_positivos', [...data.pontos_positivos, key]);
                                                } else {
                                                    setData(
                                                        'pontos_positivos',
                                                        data.pontos_positivos.filter((p) => p !== key),
                                                    );
                                                }
                                            }}
                                        />
                                        <Label htmlFor={`positivo-${key}`} className="text-sm">
                                            {label}
                                        </Label>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Pontos a Melhorar */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Pontos a Melhorar</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 gap-3 md:grid-cols-3">
                                {Object.entries(pontos_melhorar).map(([key, label]) => (
                                    <div key={key} className="flex items-center space-x-2">
                                        <Checkbox
                                            id={`melhorar-${key}`}
                                            checked={data.pontos_melhorar.includes(key)}
                                            onCheckedChange={(checked) => {
                                                if (checked) {
                                                    setData('pontos_melhorar', [...data.pontos_melhorar, key]);
                                                } else {
                                                    setData(
                                                        'pontos_melhorar',
                                                        data.pontos_melhorar.filter((p) => p !== key),
                                                    );
                                                }
                                            }}
                                        />
                                        <Label htmlFor={`melhorar-${key}`} className="text-sm">
                                            {label}
                                        </Label>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Comentário */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Comentário</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Textarea
                                placeholder="Conte mais sobre sua experiência (opcional)"
                                value={data.comentario}
                                onChange={(e) => setData('comentario', e.target.value)}
                                rows={4}
                                maxLength={1000}
                            />
                            <div className="mt-2 flex justify-between text-sm text-muted-foreground">
                                <span>Opcional - Ajude outros pacientes com sua experiência</span>
                                <span>{data.comentario.length}/1000</span>
                            </div>
                            {errors.comentario && <p className="mt-1 text-sm text-red-600">{errors.comentario}</p>}
                        </CardContent>
                    </Card>

                    {/* Opções */}
                    <Card>
                        <CardContent className="pt-6">
                            <div className="space-y-4">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="recomendaria"
                                        checked={data.recomendaria}
                                        onCheckedChange={(checked) => setData('recomendaria', checked as any)}
                                    />
                                    <Label htmlFor="recomendaria">Eu recomendaria este fisioterapeuta</Label>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Checkbox id="anonima" checked={data.anonima} onCheckedChange={(checked) => setData('anonima', checked as any)} />
                                    <Label htmlFor="anonima">Manter avaliação anônima</Label>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Botões */}
                    <div className="flex gap-3">
                        <Button type="submit" disabled={processing || data.nota_geral === 0}>
                            {processing ? 'Enviando...' : 'Enviar Avaliação'}
                        </Button>
                        <Button type="button" variant="outline" onClick={() => window.history.back()}>
                            Cancelar
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
