<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Agendamento;
use App\Models\User;
use App\Models\Fisioterapeuta;
use Carbon\Carbon;

class AgendamentoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pacientes = User::where('role', 'paciente')->get();
        $fisioterapeutas = User::where('role', 'fisioterapeuta')->get();

        if ($pacientes->isEmpty() || $fisioterapeutas->isEmpty()) {
            return;
        }

        // Vamos criar agendamentos apenas para pacientes com assinaturas
        $assinaturas = \App\Models\Assinatura::with('user')->get();

        if ($assinaturas->isEmpty()) {
            return;
        }

        $agendamentos = [];
        $statuses = ['agendado', 'confirmado', 'em_andamento', 'concluido', 'cancelado'];
        $tipos = ['Avaliação Inicial', 'Sessão de Fisioterapia', 'Retorno'];

        // Criar agendamentos para os próximos 30 dias
        for ($i = 0; $i < 30; $i++) {
            $assinatura = $assinaturas->random();
            $fisioterapeuta = $fisioterapeutas->random();

            // Datas variadas - passado, presente e futuro
            $baseDate = Carbon::now()->subDays(rand(0, 30))->addDays(rand(0, 60));
            $scheduledAt = $baseDate->setHour(rand(8, 17))->setMinute([0, 30][rand(0, 1)]);

            $status = $statuses[array_rand($statuses)];

            // Ajustar status baseado na data
            if ($scheduledAt->isPast()) {
                $status = ['concluido', 'cancelado'][rand(0, 1)];
            } elseif ($scheduledAt->isToday() || $scheduledAt->isTomorrow()) {
                $status = ['confirmado', 'agendado'][rand(0, 1)];
            }

            $serviceType = $tipos[array_rand($tipos)];

            $agendamentos[] = [
                'paciente_id' => $assinatura->user_id,
                'fisioterapeuta_id' => $fisioterapeuta->id,
                'assinatura_id' => $assinatura->id,
                'scheduled_at' => $scheduledAt,
                'duration' => 60,
                'status' => $status,
                'service_type' => $serviceType,
                'notes' => $this->getObservacaoByTipo($serviceType),
                'address' => $assinatura->user->address ?? 'Endereço não informado',
                'price' => rand(80, 150) + (rand(0, 99) / 100), // Valor entre 80.00 e 150.99
                'started_at' => $status === 'concluido' ? $scheduledAt : null,
                'finished_at' => $status === 'concluido' ? $scheduledAt->copy()->addMinutes(60) : null,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        Agendamento::insert($agendamentos);
    }

    private function getObservacaoByTipo(string $tipo): string
    {
        $observacoes = [
            'Avaliação Inicial' => [
                'Primeira consulta - avaliação completa',
                'Avaliação pós-cirúrgica',
                'Reavaliação mensal',
                'Avaliação de dor lombar',
                'Avaliação neurológica'
            ],
            'Sessão de Fisioterapia' => [
                'Sessão de fisioterapia ortopédica',
                'Exercícios de fortalecimento',
                'Terapia manual',
                'Eletroterapia',
                'Exercícios respiratórios'
            ],
            'Retorno' => [
                'Retorno para acompanhamento',
                'Verificação da evolução',
                'Ajuste do plano de tratamento',
                'Orientações domiciliares',
                'Reavaliação dos exercícios'
            ]
        ];

        return $observacoes[$tipo][array_rand($observacoes[$tipo])];
    }
}
