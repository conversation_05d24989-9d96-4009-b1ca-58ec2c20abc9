import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Edit, Save, User, UserCheck } from 'lucide-react';
import { useState } from 'react';

interface Afiliado {
    id: number;
    codigo_afiliado: string;
    nome: string;
    email: string;
    telefone: string;
    cpf: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    experiencia: string;
    motivacao: string;
    canais_divulgacao: string[];
    status: 'pendente' | 'aprovado' | 'rejeitado' | 'suspenso';
    ativo: boolean;
    total_vendas: number;
    total_comissoes: number;
    created_at: string;
    data_aprovacao?: string;
    aprovado_por?: {
        id: number;
        name: string;
    };
}

interface Props {
    afiliado: Afiliado;
}

export default function AfiliadoPerfil({ afiliado }: Props) {
    const [isEditing, setIsEditing] = useState(false);

    const breadcrumbs = [
        { title: 'Dashboard', href: '/afiliado/dashboard' },
        { title: 'Perfil', href: '/afiliado/perfil' },
    ];

    const { data, setData, put, processing, errors, reset } = useForm({
        telefone: afiliado.telefone || '',
        endereco: afiliado.endereco || '',
        cidade: afiliado.cidade || '',
        estado: afiliado.estado || '',
        cep: afiliado.cep || '',
        experiencia: afiliado.experiencia || '',
        motivacao: afiliado.motivacao || '',
        canais_divulgacao: afiliado.canais_divulgacao || [],
    });

    const canaisDisponiveis = ['Instagram', 'Facebook', 'WhatsApp', 'LinkedIn', 'TikTok', 'YouTube', 'Blog/Site', 'Indicação Pessoal', 'Outros'];

    const experienciaOptions = [
        { value: 'nenhuma', label: 'Nenhuma experiência' },
        { value: 'iniciante', label: 'Iniciante (até 1 ano)' },
        { value: 'intermediario', label: 'Intermediário (1-3 anos)' },
        { value: 'avancado', label: 'Avançado (mais de 3 anos)' },
    ];

    const estadosBrasil = [
        'AC',
        'AL',
        'AP',
        'AM',
        'BA',
        'CE',
        'DF',
        'ES',
        'GO',
        'MA',
        'MT',
        'MS',
        'MG',
        'PA',
        'PB',
        'PR',
        'PE',
        'PI',
        'RJ',
        'RN',
        'RS',
        'RO',
        'RR',
        'SC',
        'SP',
        'SE',
        'TO',
    ];

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put('/afiliado/perfil', {
            onSuccess: () => {
                setIsEditing(false);
            },
        });
    };

    const handleCancel = () => {
        reset();
        setIsEditing(false);
    };

    const handleCanalChange = (canal: string, checked: boolean) => {
        if (checked) {
            setData('canais_divulgacao', [...data.canais_divulgacao, canal]);
        } else {
            setData(
                'canais_divulgacao',
                data.canais_divulgacao.filter((c) => c !== canal),
            );
        }
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: 'secondary',
            aprovado: 'default',
            rejeitado: 'destructive',
            suspenso: 'destructive',
        } as const;

        const labels = {
            pendente: 'Pendente',
            aprovado: 'Aprovado',
            rejeitado: 'Rejeitado',
            suspenso: 'Suspenso',
        };

        return <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>{labels[status as keyof typeof labels] || status}</Badge>;
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Meu Perfil" />

            <div className="min-h-screen bg-background">
                {/* Header */}
                <section className="bg-gradient-to-b from-background to-muted/30 py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 className="text-3xl font-bold">Meu Perfil</h1>
                                <p className="text-muted-foreground">Gerencie suas informações pessoais e configurações</p>
                            </div>
                            <div className="flex gap-2">
                                {!isEditing ? (
                                    <Button onClick={() => setIsEditing(true)}>
                                        <Edit className="mr-2 h-4 w-4" />
                                        Editar Perfil
                                    </Button>
                                ) : (
                                    <div className="flex gap-2">
                                        <Button variant="outline" onClick={handleCancel}>
                                            Cancelar
                                        </Button>
                                        <Button onClick={handleSubmit} disabled={processing}>
                                            <Save className="mr-2 h-4 w-4" />
                                            {processing ? 'Salvando...' : 'Salvar'}
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </section>

                <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
                    <div className="grid gap-6 lg:grid-cols-3">
                        {/* Informações Básicas */}
                        <div className="lg:col-span-1">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Informações Básicas
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Nome</Label>
                                        <p className="font-medium">{afiliado.nome}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Email</Label>
                                        <p className="font-medium">{afiliado.email}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">CPF</Label>
                                        <p className="font-medium">{afiliado.cpf}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Código de Afiliado</Label>
                                        <p className="font-mono font-bold text-primary">{afiliado.codigo_afiliado}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                                        <div className="mt-1">{getStatusBadge(afiliado.status)}</div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Estatísticas */}
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <UserCheck className="h-5 w-5" />
                                        Estatísticas
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Total de Vendas</Label>
                                        <p className="text-2xl font-bold">{afiliado.total_vendas}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Total de Comissões</Label>
                                        <p className="text-2xl font-bold text-green-600">{formatCurrency(afiliado.total_comissoes)}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Membro desde</Label>
                                        <p className="font-medium">{formatDate(afiliado.created_at)}</p>
                                    </div>
                                    {afiliado.data_aprovacao && (
                                        <div>
                                            <Label className="text-sm font-medium text-muted-foreground">Aprovado em</Label>
                                            <p className="font-medium">{formatDate(afiliado.data_aprovacao)}</p>
                                            {afiliado.aprovado_por && (
                                                <p className="text-sm text-muted-foreground">por {afiliado.aprovado_por.name}</p>
                                            )}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Formulário de Edição */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Dados Pessoais</CardTitle>
                                    <CardDescription>{isEditing ? 'Edite suas informações pessoais' : 'Suas informações pessoais'}</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        {/* Contato */}
                                        <div className="space-y-4">
                                            <h4 className="text-lg font-medium">Contato</h4>
                                            <div className="grid gap-4 sm:grid-cols-2">
                                                <div>
                                                    <Label htmlFor="telefone">Telefone</Label>
                                                    <Input
                                                        id="telefone"
                                                        value={data.telefone}
                                                        onChange={(e) => setData('telefone', e.target.value)}
                                                        disabled={!isEditing}
                                                        placeholder="(11) 99999-9999"
                                                    />
                                                    {errors.telefone && <p className="text-sm text-red-600">{errors.telefone}</p>}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Endereço */}
                                        <div className="space-y-4">
                                            <h4 className="text-lg font-medium">Endereço</h4>
                                            <div>
                                                <Label htmlFor="endereco">Endereço Completo</Label>
                                                <Input
                                                    id="endereco"
                                                    value={data.endereco}
                                                    onChange={(e) => setData('endereco', e.target.value)}
                                                    disabled={!isEditing}
                                                    placeholder="Rua, número, complemento"
                                                />
                                                {errors.endereco && <p className="text-sm text-red-600">{errors.endereco}</p>}
                                            </div>
                                            <div className="grid gap-4 sm:grid-cols-3">
                                                <div>
                                                    <Label htmlFor="cidade">Cidade</Label>
                                                    <Input
                                                        id="cidade"
                                                        value={data.cidade}
                                                        onChange={(e) => setData('cidade', e.target.value)}
                                                        disabled={!isEditing}
                                                        placeholder="São Paulo"
                                                    />
                                                    {errors.cidade && <p className="text-sm text-red-600">{errors.cidade}</p>}
                                                </div>
                                                <div>
                                                    <Label htmlFor="estado">Estado</Label>
                                                    <Select
                                                        value={data.estado}
                                                        onValueChange={(value) => setData('estado', value)}
                                                        disabled={!isEditing}
                                                    >
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="UF" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {estadosBrasil.map((estado) => (
                                                                <SelectItem key={estado} value={estado}>
                                                                    {estado}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.estado && <p className="text-sm text-red-600">{errors.estado}</p>}
                                                </div>
                                                <div>
                                                    <Label htmlFor="cep">CEP</Label>
                                                    <Input
                                                        id="cep"
                                                        value={data.cep}
                                                        onChange={(e) => setData('cep', e.target.value)}
                                                        disabled={!isEditing}
                                                        placeholder="00000-000"
                                                    />
                                                    {errors.cep && <p className="text-sm text-red-600">{errors.cep}</p>}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Experiência */}
                                        <div className="space-y-4">
                                            <h4 className="text-lg font-medium">Experiência</h4>
                                            <div>
                                                <Label htmlFor="experiencia">Nível de Experiência</Label>
                                                <Select
                                                    value={data.experiencia}
                                                    onValueChange={(value) => setData('experiencia', value)}
                                                    disabled={!isEditing}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Selecione sua experiência" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {experienciaOptions.map((option) => (
                                                            <SelectItem key={option.value} value={option.value}>
                                                                {option.label}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                {errors.experiencia && <p className="text-sm text-red-600">{errors.experiencia}</p>}
                                            </div>
                                        </div>

                                        {/* Motivação */}
                                        <div className="space-y-4">
                                            <h4 className="text-lg font-medium">Motivação</h4>
                                            <div>
                                                <Label htmlFor="motivacao">Por que quer ser afiliado?</Label>
                                                <Textarea
                                                    id="motivacao"
                                                    value={data.motivacao}
                                                    onChange={(e) => setData('motivacao', e.target.value)}
                                                    disabled={!isEditing}
                                                    placeholder="Conte-nos sua motivação..."
                                                    rows={4}
                                                />
                                                {errors.motivacao && <p className="text-sm text-red-600">{errors.motivacao}</p>}
                                            </div>
                                        </div>

                                        {/* Canais de Divulgação */}
                                        <div className="space-y-4">
                                            <h4 className="text-lg font-medium">Canais de Divulgação</h4>
                                            <div className="grid gap-3 sm:grid-cols-2">
                                                {canaisDisponiveis.map((canal) => (
                                                    <div key={canal} className="flex items-center space-x-2">
                                                        <Checkbox
                                                            id={canal}
                                                            checked={data.canais_divulgacao.includes(canal)}
                                                            onCheckedChange={(checked) => handleCanalChange(canal, checked as boolean)}
                                                            disabled={!isEditing}
                                                        />
                                                        <Label htmlFor={canal} className="text-sm">
                                                            {canal}
                                                        </Label>
                                                    </div>
                                                ))}
                                            </div>
                                            {errors.canais_divulgacao && <p className="text-sm text-red-600">{errors.canais_divulgacao}</p>}
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
