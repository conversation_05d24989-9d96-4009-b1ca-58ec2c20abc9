<?php

namespace App\Mail;

use App\Services\ReactEmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class BoasVindasMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(
        public string $nome,
        public string $email,
        public string $tipoUsuario,
        public ?string $loginUrl = null
    ) {}

    public function envelope(): Envelope
    {
        $assuntos = [
            'paciente' => 'Bem-vindo(a) à F4 Fisio! 🎉',
            'fisioterapeuta' => 'Bem-vindo(a) à equipe F4 Fisio! 👨‍⚕️',
            'empresa' => 'Bem-vindo(a) à F4 Fisio! 🏢',
        ];

        return new Envelope(
            subject: $assuntos[$this->tipoUsuario] ?? 'Bem-vindo(a) à F4 Fisio!',
            from: [config('mail.from.address') => config('mail.from.name')]
        );
    }

    public function content(): Content
    {
        return new Content(
            htmlView: 'emails.react-email-wrapper',
            with: [
                'htmlContent' => $this->getReactEmailContent(),
                'fallbackView' => 'emails.boas-vindas-fallback',
                'fallbackData' => [
                    'nome' => $this->nome,
                    'email' => $this->email,
                    'tipoUsuario' => $this->tipoUsuario,
                    'loginUrl' => $this->loginUrl ?? route('login'),
                ],
            ],
        );
    }

    private function getReactEmailContent(): string
    {
        try {
            $reactEmailService = app(ReactEmailService::class);
            
            return $reactEmailService->renderBoasVindas(
                $this->nome,
                $this->email,
                $this->tipoUsuario,
                $this->loginUrl
            );
        } catch (\Exception $e) {
            // Log do erro e retorna string vazia para usar fallback
            \Log::error('Erro ao renderizar React Email para boas-vindas', [
                'error' => $e->getMessage(),
                'nome' => $this->nome,
                'tipoUsuario' => $this->tipoUsuario,
            ]);
            
            return '';
        }
    }

    public function attachments(): array
    {
        return [];
    }
}
