import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Link } from '@inertiajs/react';

export default function FAQsSection() {
    const faqItems = [
        {
            id: 'item-1',
            question: 'Posso cancelar meu plano a qualquer momento?',
            answer: 'Sim, você pode cancelar seu plano a qualquer momento sem multa ou burocracia. O cancelamento será efetivo no final do período já pago.',
        },
        {
            id: 'item-2',
            question: 'Como funciona o pagamento?',
            answer: 'O pagamento é mensal via PIX, cartão de crédito ou débito. Para sessões avulsas, o pagamento é feito no ato do atendimento.',
        },
        {
            id: 'item-3',
            question: 'E se eu não usar todas as sessões do mês?',
            answer: 'As sessões não utilizadas podem ser reagendadas dentro do mesmo mês. Oferecemos flexibilidade total para sua agenda.',
        },
        {
            id: 'item-4',
            question: 'Há taxa de adesão ou outros custos?',
            answer: 'Não há taxa de adesão, taxa de cancelamento ou custos ocultos. O valor do plano é transparente e inclui tudo que está descrito.',
        },
        {
            id: 'item-5',
            question: 'Vocês atendem em qual região?',
            answer: 'Atendemos toda a Grande São Paulo. Entre em contato para confirmar se sua região está na nossa área de cobertura.',
        },
        {
            id: 'item-6',
            question: 'Como funciona o agendamento?',
            answer: 'O agendamento pode ser feito através da nossa plataforma online, por telefone ou WhatsApp. Você escolhe o melhor horário e nosso fisioterapeuta vai até sua casa.',
        },
        {
            id: 'item-7',
            question: 'Os fisioterapeutas são credenciados?',
            answer: 'Sim, todos os nossos fisioterapeutas são credenciados pelo CREFITO e possuem experiência comprovada em fisioterapia domiciliar.',
        },
        {
            id: 'item-8',
            question: 'Que equipamentos são utilizados?',
            answer: 'Utilizamos equipamentos profissionais portáteis, incluindo aparelhos de eletroterapia, ultrassom, laser e materiais para exercícios terapêuticos.',
        },
    ];

    return (
        <section id="faq" className="bg-background py-20">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="text-center">
                    <h2 className="text-3xl font-medium text-balance md:text-4xl">Perguntas Frequentes</h2>
                    <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                        Tire suas dúvidas sobre nossos serviços de fisioterapia domiciliar. Encontre respostas rápidas e abrangentes sobre nossos
                        planos e atendimento.
                    </p>
                </div>

                <div className="mt-16">
                    <Accordion
                        type="single"
                        collapsible
                        className="w-full rounded-(--radius) border border-transparent bg-card px-8 py-6 shadow ring-1 ring-foreground/5"
                    >
                        {faqItems.map((item) => (
                            <AccordionItem key={item.id} value={item.id} className="border-dotted py-2">
                                <AccordionTrigger className="cursor-pointer text-base font-medium hover:no-underline">
                                    {item.question}
                                </AccordionTrigger>
                                <AccordionContent>
                                    <p className="text-base leading-relaxed text-muted-foreground">{item.answer}</p>
                                </AccordionContent>
                            </AccordionItem>
                        ))}
                    </Accordion>

                    <p className="mt-6 text-center text-muted-foreground">
                        Não encontrou o que procura? Entre em contato com nossa{' '}
                        <Link href="/contato" className="font-medium text-primary hover:underline">
                            equipe de atendimento
                        </Link>{' '}
                        ou fale conosco pelo{' '}
                        <a
                            href="https://wa.me/5511978196207?text=Olá! Tenho uma dúvida sobre os serviços da F4 Fisio."
                            target="_blank"
                            rel="noopener noreferrer"
                            className="font-medium text-primary hover:underline"
                        >
                            WhatsApp
                        </a>
                    </p>
                </div>
            </div>
        </section>
    );
}
