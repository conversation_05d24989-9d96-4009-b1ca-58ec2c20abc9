<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Afiliado extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'codigo_afiliado',
        'nome',
        'email',
        'telefone',
        'cpf',
        'endereco',
        'cidade',
        'estado',
        'cep',
        'experiencia',
        'motivacao',
        'canais_divulgacao',
        'status',
        'link_afiliado',
        'observacoes_admin',
        'data_aprovacao',
        'data_rejeicao',
        'aprovado_por',
        'total_vendas',
        'total_comissoes',
        'vendas_mes_atual',
        'comissoes_mes_atual',
        'ativo',
    ];

    protected $casts = [
        'canais_divulgacao' => 'array',
        'data_aprovacao' => 'datetime',
        'data_rejeicao' => 'datetime',
        'total_vendas' => 'decimal:2',
        'total_comissoes' => 'decimal:2',
        'comissoes_mes_atual' => 'decimal:2',
        'ativo' => 'boolean',
    ];

    // Relacionamentos
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function aprovadoPor()
    {
        return $this->belongsTo(User::class, 'aprovado_por');
    }

    public function vendas()
    {
        return $this->hasMany(VendaAfiliado::class);
    }

    // Scopes
    public function scopePendentes($query)
    {
        return $query->where('status', 'pendente');
    }

    public function scopeAprovados($query)
    {
        return $query->where('status', 'aprovado');
    }

    public function scopeAtivos($query)
    {
        return $query->where('ativo', true);
    }

    // Métodos auxiliares
    public function gerarCodigoAfiliado()
    {
        do {
            $codigo = 'AF' . strtoupper(Str::random(8));
        } while (self::where('codigo_afiliado', $codigo)->exists());

        return $codigo;
    }

    public function gerarLinkAfiliado()
    {
        $baseUrl = config('app.url');
        return $baseUrl . '?ref=' . $this->codigo_afiliado;
    }

    public function aprovar($adminId, $observacoes = null)
    {
        $this->update([
            'status' => 'aprovado',
            'data_aprovacao' => now(),
            'aprovado_por' => $adminId,
            'observacoes_admin' => $observacoes,
            'link_afiliado' => $this->gerarLinkAfiliado(),
        ]);
    }

    public function rejeitar($adminId, $observacoes = null)
    {
        $this->update([
            'status' => 'rejeitado',
            'data_rejeicao' => now(),
            'aprovado_por' => $adminId,
            'observacoes_admin' => $observacoes,
        ]);
    }

    public function suspender($observacoes = null)
    {
        $this->update([
            'status' => 'suspenso',
            'observacoes_admin' => $observacoes,
            'ativo' => false,
        ]);
    }

    public function reativar()
    {
        $this->update([
            'status' => 'aprovado',
            'ativo' => true,
        ]);
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pendente' => ['text' => 'Pendente', 'color' => 'yellow'],
            'aprovado' => ['text' => 'Aprovado', 'color' => 'green'],
            'rejeitado' => ['text' => 'Rejeitado', 'color' => 'red'],
            'suspenso' => ['text' => 'Suspenso', 'color' => 'gray'],
        ];

        return $badges[$this->status] ?? ['text' => 'Desconhecido', 'color' => 'gray'];
    }

    public function getFormattedTotalVendasAttribute()
    {
        return 'R$ ' . number_format($this->total_vendas, 2, ',', '.');
    }

    public function getFormattedTotalComissoesAttribute()
    {
        return 'R$ ' . number_format($this->total_comissoes, 2, ',', '.');
    }

    public function getFormattedComissoesMesAtualAttribute()
    {
        return 'R$ ' . number_format($this->comissoes_mes_atual, 2, ',', '.');
    }

    // Métodos de cálculo
    public function calcularComissaoMensal()
    {
        // Buscar vendas do mês atual
        $vendasMesAtual = $this->vendas()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('comissao');

        $this->update(['comissoes_mes_atual' => $vendasMesAtual]);

        return $vendasMesAtual;
    }

    public function atualizarEstatisticas()
    {
        $totalVendas = $this->vendas()->sum('valor_venda');
        $totalComissoes = $this->vendas()->sum('comissao');
        $vendasMesAtual = $this->vendas()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        $this->update([
            'total_vendas' => $totalVendas,
            'total_comissoes' => $totalComissoes,
            'vendas_mes_atual' => $vendasMesAtual,
        ]);

        $this->calcularComissaoMensal();
    }
}
