import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';
import React from 'react';

export interface LoadingButtonProps extends React.ComponentProps<typeof Button> {
    loading?: boolean;
    loadingText?: string;
    icon?: React.ReactNode;
}

export function LoadingButton({ loading = false, loadingText, icon, children, disabled, className, ...props }: LoadingButtonProps) {
    const isDisabled = disabled || loading;

    return (
        <Button {...props} disabled={isDisabled} className={cn(loading && 'cursor-not-allowed', className)}>
            {loading ? (
                <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {loadingText || children}
                </>
            ) : (
                <>
                    {icon && <span className="mr-2">{icon}</span>}
                    {children}
                </>
            )}
        </Button>
    );
}
