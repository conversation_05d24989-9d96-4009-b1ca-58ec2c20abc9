import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, Clock, Save } from 'lucide-react';
import React from 'react';

interface DisponibilidadeFormData {
    tipo: 'disponivel' | 'indisponivel' | 'bloqueio' | '' | undefined;
    data_inicio: string;
    data_fim: string;
    hora_inicio: string;
    hora_fim: string;
    dias_semana: number[];
    recorrente: boolean;
    motivo: string;
    [key: string]: any;
}

const diasSemana = [
    { value: 0, label: 'Domingo' },
    { value: 1, label: 'Segunda-feira' },
    { value: 2, label: 'Terça-feira' },
    { value: 3, label: 'Quarta-feira' },
    { value: 4, label: 'Quinta-feira' },
    { value: 5, label: 'Sexta-feira' },
    { value: 6, label: 'Sábado' },
];

export default function Create() {
    const { data, setData, post, processing, errors } = useForm<DisponibilidadeFormData>({
        tipo: undefined,
        data_inicio: '',
        data_fim: '',
        hora_inicio: '',
        hora_fim: '',
        dias_semana: [],
        recorrente: false,
        motivo: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('fisioterapeuta.disponibilidade.store'));
    };

    const handleDiaSemanaChange = (dia: number, checked: boolean) => {
        if (checked) {
            setData('dias_semana', [...data.dias_semana, dia]);
        } else {
            setData(
                'dias_semana',
                data.dias_semana.filter((d) => d !== dia),
            );
        }
    };

    const getTipoLabel = (tipo: string) => {
        switch (tipo) {
            case 'disponivel':
                return 'Disponível';
            case 'indisponivel':
                return 'Indisponível';
            case 'bloqueio':
                return 'Bloqueio';
            default:
                return '';
        }
    };

    const getTipoDescription = (tipo: string) => {
        switch (tipo) {
            case 'disponivel':
                return 'Horários em que você está disponível para atendimentos';
            case 'indisponivel':
                return 'Horários em que você não está disponível temporariamente';
            case 'bloqueio':
                return 'Bloqueio permanente de horários (ex: compromissos pessoais)';
            default:
                return '';
        }
    };

    return (
        <AppLayout>
            <Head title="Nova Disponibilidade" />

            <div className="py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center space-x-4">
                            <Link href={route('fisioterapeuta.disponibilidade.index')}>
                                <Button variant="ghost" size="sm">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Voltar
                                </Button>
                            </Link>
                            <div>
                                <h2 className="text-3xl font-bold tracking-tight">Nova Disponibilidade</h2>
                                <p className="text-muted-foreground">Configure um novo horário de disponibilidade ou bloqueio</p>
                            </div>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit}>
                        <div className="space-y-6">
                            {/* Tipo */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <Calendar className="mr-2 h-5 w-5" />
                                        Tipo de Disponibilidade
                                    </CardTitle>
                                    <CardDescription>Selecione o tipo de disponibilidade que deseja configurar</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div>
                                            <Label htmlFor="tipo">Tipo *</Label>
                                            <Select value={data.tipo || undefined} onValueChange={(value: any) => setData('tipo', value)}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione o tipo" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="disponivel">Disponível</SelectItem>
                                                    <SelectItem value="indisponivel">Indisponível</SelectItem>
                                                    <SelectItem value="bloqueio">Bloqueio</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.tipo && <p className="mt-1 text-sm text-red-600">{errors.tipo}</p>}
                                        </div>

                                        {data.tipo && (
                                            <div className="rounded-lg bg-blue-50 p-4">
                                                <h4 className="font-medium text-blue-900">{getTipoLabel(data.tipo)}</h4>
                                                <p className="mt-1 text-sm text-blue-700">{getTipoDescription(data.tipo)}</p>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Período */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <Calendar className="mr-2 h-5 w-5" />
                                        Período
                                    </CardTitle>
                                    <CardDescription>Defina o período de validade desta disponibilidade</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="data_inicio">Data de Início *</Label>
                                            <Input
                                                id="data_inicio"
                                                type="date"
                                                value={data.data_inicio}
                                                onChange={(e) => setData('data_inicio', e.target.value)}
                                                min={new Date().toISOString().split('T')[0]}
                                            />
                                            {errors.data_inicio && <p className="mt-1 text-sm text-red-600">{errors.data_inicio}</p>}
                                        </div>

                                        <div>
                                            <Label htmlFor="data_fim">Data de Fim</Label>
                                            <Input
                                                id="data_fim"
                                                type="date"
                                                value={data.data_fim}
                                                onChange={(e) => setData('data_fim', e.target.value)}
                                                min={data.data_inicio || new Date().toISOString().split('T')[0]}
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Deixe em branco para disponibilidade indefinida</p>
                                            {errors.data_fim && <p className="mt-1 text-sm text-red-600">{errors.data_fim}</p>}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Horário */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <Clock className="mr-2 h-5 w-5" />
                                        Horário
                                    </CardTitle>
                                    <CardDescription>Defina o horário de início e fim</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="hora_inicio">Hora de Início *</Label>
                                            <Input
                                                id="hora_inicio"
                                                type="time"
                                                value={data.hora_inicio}
                                                onChange={(e) => setData('hora_inicio', e.target.value)}
                                            />
                                            {errors.hora_inicio && <p className="mt-1 text-sm text-red-600">{errors.hora_inicio}</p>}
                                        </div>

                                        <div>
                                            <Label htmlFor="hora_fim">Hora de Fim *</Label>
                                            <Input
                                                id="hora_fim"
                                                type="time"
                                                value={data.hora_fim}
                                                onChange={(e) => setData('hora_fim', e.target.value)}
                                                min={data.hora_inicio}
                                            />
                                            {errors.hora_fim && <p className="mt-1 text-sm text-red-600">{errors.hora_fim}</p>}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Recorrência */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Recorrência</CardTitle>
                                    <CardDescription>Configure se esta disponibilidade se repete semanalmente</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="flex items-center space-x-2">
                                            <Checkbox
                                                id="recorrente"
                                                checked={data.recorrente}
                                                onCheckedChange={(checked) => setData('recorrente', !!checked)}
                                            />
                                            <Label htmlFor="recorrente">Esta disponibilidade se repete semanalmente</Label>
                                        </div>

                                        {data.recorrente && (
                                            <div>
                                                <Label>Dias da Semana *</Label>
                                                <div className="mt-2 grid grid-cols-2 gap-2 md:grid-cols-4">
                                                    {diasSemana.map((dia) => (
                                                        <div key={dia.value} className="flex items-center space-x-2">
                                                            <Checkbox
                                                                id={`dia-${dia.value}`}
                                                                checked={data.dias_semana.includes(dia.value)}
                                                                onCheckedChange={(checked) => handleDiaSemanaChange(dia.value, !!checked)}
                                                            />
                                                            <Label htmlFor={`dia-${dia.value}`} className="text-sm">
                                                                {dia.label}
                                                            </Label>
                                                        </div>
                                                    ))}
                                                </div>
                                                {errors.dias_semana && <p className="mt-1 text-sm text-red-600">{errors.dias_semana}</p>}
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Motivo */}
                            {(data.tipo === 'indisponivel' || data.tipo === 'bloqueio') && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Motivo</CardTitle>
                                        <CardDescription>Descreva o motivo desta indisponibilidade ou bloqueio</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div>
                                            <Label htmlFor="motivo">Motivo</Label>
                                            <Textarea
                                                id="motivo"
                                                value={data.motivo}
                                                onChange={(e) => setData('motivo', e.target.value)}
                                                placeholder="Ex: Consulta médica, viagem, etc."
                                                rows={3}
                                            />
                                            {errors.motivo && <p className="mt-1 text-sm text-red-600">{errors.motivo}</p>}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Actions */}
                            <div className="flex justify-end space-x-4">
                                <Link href={route('fisioterapeuta.disponibilidade.index')}>
                                    <Button variant="outline" type="button">
                                        Cancelar
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    <Save className="mr-2 h-4 w-4" />
                                    {processing ? 'Salvando...' : 'Salvar Disponibilidade'}
                                </Button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}
