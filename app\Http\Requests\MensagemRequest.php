<?php

namespace App\Http\Requests;

use App\Models\Mensagem;
use Illuminate\Foundation\Http\FormRequest;

class MensagemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'destinatario_id' => 'required|exists:users,id',
            'conteudo' => 'required|string|min:1|max:2000',
            'tipo' => 'nullable|in:' . implode(',', array_keys(Mensagem::getTipos())),
            'agendamento_id' => 'nullable|exists:agendamentos,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'destinatario_id.required' => 'O destinatário é obrigatório.',
            'destinatario_id.exists' => 'Destinatário inválido.',
            'conteudo.required' => 'O conteúdo da mensagem é obrigatório.',
            'conteudo.min' => 'A mensagem deve ter pelo menos 1 caractere.',
            'conteudo.max' => 'A mensagem não pode ter mais de 2000 caracteres.',
            'tipo.in' => 'Tipo de mensagem inválido.',
            'agendamento_id.exists' => 'Agendamento inválido.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'destinatario_id' => 'destinatário',
            'conteudo' => 'conteúdo',
            'tipo' => 'tipo',
            'agendamento_id' => 'agendamento',
        ];
    }
}
