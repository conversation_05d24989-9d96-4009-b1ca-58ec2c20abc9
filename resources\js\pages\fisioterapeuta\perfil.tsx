import { AvatarUpload } from '@/components/avatar-upload';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { Save, User } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/fisioterapeuta/dashboard',
    },
    {
        title: 'Perfil',
        href: '/fisioterapeuta/perfil',
    },
];

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    avatar?: string;
}

interface Fisioterapeuta {
    id: number;
    crefito: string;
    specializations: string[];
    bio: string;
    hourly_rate: number;
    available_areas: string[];
    available: boolean;
    rating: number;
    total_reviews: number;
}

interface Props {
    fisioterapeuta: Fisioterapeuta;
    user: User;
    especialidades: string[];
    areas: string[];
}

export default function FisioterapeutaPerfil({ fisioterapeuta, user, especialidades, areas }: Props) {
    const { data, setData, put, processing, errors, isDirty } = useForm({
        // Dados do usuário
        name: user.name || '',
        phone: user.phone || '',

        // Dados do fisioterapeuta
        crefito: fisioterapeuta.crefito || '',
        specializations: fisioterapeuta.specializations || [],
        bio: fisioterapeuta.bio || '',
        hourly_rate: fisioterapeuta.hourly_rate || 0,
        available_areas: fisioterapeuta.available_areas || [],
        available: fisioterapeuta.available || false,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('fisioterapeuta.perfil.update'));
    };

    const handleSpecializationChange = (especialidade: string, checked: boolean) => {
        if (checked) {
            setData('specializations', [...data.specializations, especialidade]);
        } else {
            setData(
                'specializations',
                data.specializations.filter((s) => s !== especialidade),
            );
        }
    };

    const handleAreaChange = (area: string, checked: boolean) => {
        if (checked) {
            setData('available_areas', [...data.available_areas, area]);
        } else {
            setData(
                'available_areas',
                data.available_areas.filter((a) => a !== area),
            );
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Meu Perfil" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                            <User className="h-5 w-5" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold">Meu Perfil</h1>
                            <p className="text-muted-foreground">Gerencie suas informações profissionais</p>
                        </div>
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Avatar */}
                    <div className="lg:col-span-1">
                        <AvatarUpload
                            user={user}
                            uploadUrl={route('fisioterapeuta.perfil.avatar.upload')}
                            removeUrl={route('fisioterapeuta.perfil.avatar.remove')}
                        />
                    </div>

                    {/* Formulário */}
                    <div className="lg:col-span-2">
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Informações Pessoais */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informações Pessoais</CardTitle>
                                    <CardDescription>Suas informações básicas de contato</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="name">Nome Completo</Label>
                                            <Input
                                                id="name"
                                                value={data.name}
                                                onChange={(e) => setData('name', e.target.value)}
                                                placeholder="Seu nome completo"
                                            />
                                            <InputError message={errors.name} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone">Telefone</Label>
                                            <Input
                                                id="phone"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                placeholder="(11) 99999-9999"
                                            />
                                            <InputError message={errors.phone} />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Informações Profissionais */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informações Profissionais</CardTitle>
                                    <CardDescription>Dados sobre sua formação e experiência</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid gap-4 sm:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="crefito">CREFITO</Label>
                                            <Input
                                                id="crefito"
                                                value={data.crefito}
                                                onChange={(e) => setData('crefito', e.target.value)}
                                                placeholder="CREFITO-3/123456-F"
                                            />
                                            <InputError message={errors.crefito} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="hourly_rate">Valor da Hora (R$)</Label>
                                            <Input
                                                id="hourly_rate"
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                value={data.hourly_rate}
                                                onChange={(e) => setData('hourly_rate', parseFloat(e.target.value) || 0)}
                                                placeholder="80.00"
                                            />
                                            <InputError message={errors.hourly_rate} />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="bio">Biografia Profissional</Label>
                                        <Textarea
                                            id="bio"
                                            value={data.bio}
                                            onChange={(e) => setData('bio', e.target.value)}
                                            placeholder="Conte um pouco sobre sua experiência, formação e especialidades..."
                                            rows={4}
                                        />
                                        <InputError message={errors.bio} />
                                        <p className="text-sm text-muted-foreground">{data.bio.length}/1000 caracteres</p>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="available"
                                            checked={data.available}
                                            onCheckedChange={(checked) => setData('available', checked)}
                                        />
                                        <Label htmlFor="available">Disponível para novos agendamentos</Label>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Especialidades */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Especialidades</CardTitle>
                                    <CardDescription>Selecione suas áreas de especialização</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                                        {especialidades.map((especialidade) => (
                                            <div key={especialidade} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`especialidade-${especialidade}`}
                                                    checked={data.specializations.includes(especialidade)}
                                                    onCheckedChange={(checked) => handleSpecializationChange(especialidade, checked as boolean)}
                                                />
                                                <Label htmlFor={`especialidade-${especialidade}`} className="text-sm font-normal">
                                                    {especialidade}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                    {errors.specializations && <p className="mt-2 text-sm text-destructive">{errors.specializations}</p>}
                                </CardContent>
                            </Card>

                            {/* Áreas de Atendimento */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Áreas de Atendimento</CardTitle>
                                    <CardDescription>Selecione as regiões onde você atende</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid gap-3 sm:grid-cols-2">
                                        {areas.map((area) => (
                                            <div key={area} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`area-${area}`}
                                                    checked={data.available_areas.includes(area)}
                                                    onCheckedChange={(checked) => handleAreaChange(area, checked as boolean)}
                                                />
                                                <Label htmlFor={`area-${area}`} className="text-sm font-normal">
                                                    {area}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                    {errors.available_areas && <p className="mt-2 text-sm text-destructive">{errors.available_areas}</p>}
                                </CardContent>
                            </Card>

                            {/* Botão de Salvar */}
                            <div className="flex justify-end">
                                <Button type="submit" disabled={processing || !isDirty} className="gap-2">
                                    <Save className="h-4 w-4" />
                                    {processing ? 'Salvando...' : 'Salvar Alterações'}
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
