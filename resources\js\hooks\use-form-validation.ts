import {
    isStrongPassword,
    isValidCEP,
    isValidCNPJ,
    isValidCPF,
    isValidDate,
    isValidEmail,
    isValidPhone,
    validationMessages,
} from '@/lib/validations';
import { useCallback, useState } from 'react';

export interface ValidationRule {
    required?: boolean;
    email?: boolean;
    password?: boolean;
    cpf?: boolean;
    cnpj?: boolean;
    phone?: boolean;
    cep?: boolean;
    date?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: any) => boolean | string;
    passwordConfirmation?: string; // nome do campo de senha para confirmar
}

export interface ValidationRules {
    [fieldName: string]: ValidationRule;
}

export interface ValidationErrors {
    [fieldName: string]: string;
}

export interface UseFormValidationReturn {
    errors: ValidationErrors;
    validateField: (fieldName: string, value: any, rules?: ValidationRule) => string | null;
    validateForm: (data: Record<string, any>) => boolean;
    clearError: (fieldName: string) => void;
    clearAllErrors: () => void;
    setError: (fieldName: string, message: string) => void;
    hasErrors: boolean;
}

export function useFormValidation(rules: ValidationRules): UseFormValidationReturn {
    const [errors, setErrors] = useState<ValidationErrors>({});

    const validateField = useCallback(
        (fieldName: string, value: any, fieldRules?: ValidationRule): string | null => {
            const rule = fieldRules || rules[fieldName];
            if (!rule) return null;

            // Campo obrigatório
            if (rule.required) {
                // Para checkboxes (boolean)
                if (typeof value === 'boolean' && !value) {
                    return validationMessages.required;
                }
                // Para strings (incluindo campos de senha)
                if (typeof value === 'string' && value.trim() === '') {
                    return validationMessages.required;
                }
                // Para outros valores falsy (mas não para string vazia que já foi tratada)
                if (value === null || value === undefined) {
                    return validationMessages.required;
                }
            }

            // Se o campo não é obrigatório e está vazio, não valida outras regras
            if (!rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
                return null;
            }

            const stringValue = String(value).trim();

            // Validação de email
            if (rule.email && !isValidEmail(stringValue)) {
                return validationMessages.email;
            }

            // Validação de senha forte
            if (rule.password && !isStrongPassword(stringValue)) {
                return validationMessages.password;
            }

            // Validação de CPF
            if (rule.cpf && !isValidCPF(stringValue)) {
                return validationMessages.cpf;
            }

            // Validação de CNPJ
            if (rule.cnpj && !isValidCNPJ(stringValue)) {
                return validationMessages.cnpj;
            }

            // Validação de telefone
            if (rule.phone && !isValidPhone(stringValue)) {
                return validationMessages.phone;
            }

            // Validação de CEP
            if (rule.cep && !isValidCEP(stringValue)) {
                return validationMessages.cep;
            }

            // Validação de data
            if (rule.date && !isValidDate(stringValue)) {
                return validationMessages.date;
            }

            // Validação de comprimento mínimo
            if (rule.minLength && stringValue.length < rule.minLength) {
                return validationMessages.minLength(rule.minLength);
            }

            // Validação de comprimento máximo
            if (rule.maxLength && stringValue.length > rule.maxLength) {
                return validationMessages.maxLength(rule.maxLength);
            }

            // Validação de padrão regex
            if (rule.pattern && !rule.pattern.test(stringValue)) {
                return 'Formato inválido';
            }

            // Validação customizada
            if (rule.custom) {
                const result = rule.custom(value);
                if (typeof result === 'string') {
                    return result;
                }
                if (result === false) {
                    return 'Valor inválido';
                }
            }

            return null;
        },
        [rules],
    );

    const validateForm = useCallback(
        (data: Record<string, any>): boolean => {
            const newErrors: ValidationErrors = {};
            let isValid = true;

            // Valida todos os campos com regras definidas
            Object.keys(rules).forEach((fieldName) => {
                const error = validateField(fieldName, data[fieldName]);
                if (error) {
                    newErrors[fieldName] = error;
                    isValid = false;
                }
            });

            // Validação especial para confirmação de senha
            Object.keys(rules).forEach((fieldName) => {
                const rule = rules[fieldName];
                if (rule.passwordConfirmation) {
                    const passwordValue = data[rule.passwordConfirmation];
                    const confirmationValue = data[fieldName];

                    if (passwordValue !== confirmationValue) {
                        newErrors[fieldName] = validationMessages.passwordConfirmation;
                        isValid = false;
                    }
                }
            });

            setErrors(newErrors);
            return isValid;
        },
        [rules, validateField],
    );

    const clearError = useCallback((fieldName: string) => {
        setErrors((prev) => {
            const newErrors = { ...prev };
            delete newErrors[fieldName];
            return newErrors;
        });
    }, []);

    const clearAllErrors = useCallback(() => {
        setErrors({});
    }, []);

    const setError = useCallback((fieldName: string, message: string) => {
        setErrors((prev) => ({
            ...prev,
            [fieldName]: message,
        }));
    }, []);

    const hasErrors = Object.keys(errors).length > 0;

    return {
        errors,
        validateField,
        validateForm,
        clearError,
        clearAllErrors,
        setError,
        hasErrors,
    };
}
