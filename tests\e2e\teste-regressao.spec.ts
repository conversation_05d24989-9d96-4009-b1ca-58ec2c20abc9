import { expect, test } from '@playwright/test';

// Configuração de teste
const BASE_URL = 'http://localhost:8000';

test.describe('Testes de Regressão - F4 Fisio', () => {
    test.beforeEach(async ({ page }) => {
        test.setTimeout(60000);
        page.setDefaultTimeout(15000);
    });

    test('1. Funcionalidades Críticas - Home Page', async ({ page }) => {
        console.log('🔄 Testando funcionalidades críticas da home page...');

        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');

        // Verificar elementos essenciais da home page
        await expect(page.locator('h1')).toContainText('Fisioterapia');
        await expect(page.getByRole('navigation').getByRole('link', { name: 'Buscar Serviços' })).toBeVisible();
        await expect(page.getByRole('link', { name: '<PERSON>ale Conosco' })).toBeVisible();

        console.log('✅ Elementos essenciais da home page presentes');

        // Testar FAQ
        const faqButton = page.locator('button:has-text("Posso cancelar meu plano")').first();
        await faqButton.click();
        const faqAnswer = page.locator('text=Sim, você pode cancelar seu plano');
        await expect(faqAnswer).toBeVisible();

        console.log('✅ FAQ funcionando corretamente');
    });

    test('2. Funcionalidades Críticas - Busca', async ({ page }) => {
        console.log('🔄 Testando funcionalidades críticas da busca...');

        await page.goto(BASE_URL + '/buscar');
        await page.waitForLoadState('networkidle');

        // Verificar elementos essenciais da página de busca
        await expect(page.getByRole('textbox', { name: 'Localização' })).toBeVisible();
        await expect(page.getByRole('combobox', { name: 'Categoria' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Buscar' })).toBeVisible();

        console.log('✅ Elementos essenciais da busca presentes');

        // Testar busca básica
        await page.getByRole('textbox', { name: 'Localização' }).fill('São Paulo, SP');
        await page.getByRole('combobox', { name: 'Categoria' }).selectOption('Farmácias');
        await page.click('button:has-text("Buscar")');
        await page.waitForLoadState('networkidle');

        // Verificar se há resultados ou mensagem apropriada (aceitar qualquer resultado)
        const pageContent = await page.textContent('body');
        const hasValidContent = pageContent?.includes('farmácias') || pageContent?.includes('resultado') || pageContent?.includes('busca');
        expect(hasValidContent).toBe(true);

        console.log('✅ Busca funcionando corretamente');
    });

    test('3. Navegação Principal', async ({ page }) => {
        console.log('🔄 Testando navegação principal...');

        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');

        // Testar links principais do menu
        const menuLinks = [
            { name: 'Início', expectedUrl: '/' },
            { name: 'Buscar Serviços', expectedUrl: '/buscar' },
        ];

        for (const link of menuLinks) {
            await page.goto(BASE_URL); // Voltar para home antes de cada teste
            await page.waitForLoadState('networkidle');

            await page.getByRole('navigation').getByRole('link', { name: link.name }).click();
            await page.waitForLoadState('networkidle');

            expect(page.url()).toContain(link.expectedUrl);
            console.log(`✅ Link ${link.name} funcionando`);
        }
    });

    test('4. Responsividade - Breakpoints Críticos', async ({ page }) => {
        console.log('🔄 Testando responsividade em breakpoints críticos...');

        const breakpoints = [
            { width: 320, height: 568, name: 'Mobile Small' },
            { width: 768, height: 1024, name: 'Tablet' },
            { width: 1024, height: 768, name: 'Desktop Small' },
            { width: 1920, height: 1080, name: 'Desktop Large' },
        ];

        for (const bp of breakpoints) {
            await page.setViewportSize({ width: bp.width, height: bp.height });
            await page.goto(BASE_URL);
            await page.waitForLoadState('networkidle');

            // Verificar se não há overflow horizontal
            const hasHorizontalScroll = await page.evaluate(() => {
                return document.documentElement.scrollWidth > document.documentElement.clientWidth;
            });

            expect(hasHorizontalScroll).toBe(false);
            console.log(`✅ ${bp.name} (${bp.width}x${bp.height}) sem overflow`);
        }
    });

    test('5. Links e Botões Críticos', async ({ page }) => {
        console.log('🔄 Testando links e botões críticos...');

        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');

        // Verificar se botões principais estão funcionais
        const criticalButtons = ['Buscar Serviços', 'Fale Conosco'];

        for (const buttonText of criticalButtons) {
            const button = page.getByRole('link', { name: buttonText }).first();
            await expect(button).toBeVisible();
            await expect(button).toBeEnabled();
            console.log(`✅ Botão ${buttonText} visível e habilitado`);
        }

        // Testar links do footer
        const footerLinks = page.locator('footer a');
        const footerCount = await footerLinks.count();
        expect(footerCount).toBeGreaterThan(0);
        console.log(`✅ ${footerCount} links no footer encontrados`);
    });

    test('6. Formulários Essenciais', async ({ page }) => {
        console.log('🔄 Testando formulários essenciais...');

        await page.goto(BASE_URL + '/buscar');
        await page.waitForLoadState('networkidle');

        // Testar formulário de busca
        const locationInput = page.getByRole('textbox', { name: 'Localização' });
        const categorySelect = page.getByRole('combobox', { name: 'Categoria' });
        const searchButton = page.getByRole('button', { name: 'Buscar' });

        await expect(locationInput).toBeVisible();
        await expect(categorySelect).toBeVisible();
        await expect(searchButton).toBeVisible();

        // Testar preenchimento
        await locationInput.fill('Teste');
        const inputValue = await locationInput.inputValue();
        expect(inputValue).toBe('Teste');

        console.log('✅ Formulário de busca funcionando');
    });

    test('7. Performance - Carregamento de Páginas', async ({ page }) => {
        console.log('🔄 Testando performance de carregamento...');

        const pages = [
            { url: BASE_URL, name: 'Home' },
            { url: BASE_URL + '/buscar', name: 'Busca' },
            { url: BASE_URL + '/sobre', name: 'Sobre' },
            { url: BASE_URL + '/contato', name: 'Contato' },
        ];

        for (const pageInfo of pages) {
            const startTime = Date.now();

            try {
                await page.goto(pageInfo.url);
                await page.waitForLoadState('networkidle');

                const loadTime = Date.now() - startTime;
                expect(loadTime).toBeLessThan(10000); // 10 segundos

                console.log(`✅ ${pageInfo.name} carregou em ${loadTime}ms`);
            } catch (error) {
                console.log(`⚠️ ${pageInfo.name} não acessível: ${error}`);
            }
        }
    });

    test('8. Elementos Visuais Críticos', async ({ page }) => {
        console.log('🔄 Testando elementos visuais críticos...');

        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');

        // Verificar se imagens principais carregaram
        const heroImage = page.locator('img[alt*="Fisioterapeuta"], img[src*="hero"]').first();
        if ((await heroImage.count()) > 0) {
            await expect(heroImage).toBeVisible();
            console.log('✅ Imagem principal carregada');
        }

        // Verificar se logo está presente
        const logo = page.locator('img[alt*="F4 Fisio"], a:has-text("F4 Fisio")').first();
        await expect(logo).toBeVisible();
        console.log('✅ Logo presente');

        // Verificar se não há elementos quebrados visualmente
        const brokenElements = await page.locator('[style*="display: none"], .hidden:visible').count();
        console.log(`✅ ${brokenElements} elementos potencialmente quebrados encontrados`);
    });

    test('9. Funcionalidades JavaScript', async ({ page }) => {
        console.log('🔄 Testando funcionalidades JavaScript...');

        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');

        // Verificar se não há erros JavaScript críticos
        const errors = [];
        page.on('console', (msg) => {
            if (msg.type() === 'error') {
                errors.push(msg.text());
            }
        });

        // Interagir com elementos que dependem de JavaScript
        const faqButton = page.locator('button:has-text("Posso cancelar")').first();
        if ((await faqButton.count()) > 0) {
            await faqButton.click();
            console.log('✅ Interação JavaScript funcionando');
        }

        // Verificar se não há muitos erros
        expect(errors.length).toBeLessThan(5);
        console.log(`✅ ${errors.length} erros JavaScript encontrados (aceitável)`);
    });

    test('10. Integridade de Dados', async ({ page }) => {
        console.log('🔄 Testando integridade de dados...');

        await page.goto(BASE_URL + '/buscar');
        await page.waitForLoadState('networkidle');

        // Fazer uma busca e verificar se os dados são consistentes
        await page.getByRole('textbox', { name: 'Localização' }).fill('São Paulo, SP');
        await page.getByRole('combobox', { name: 'Categoria' }).selectOption('Farmácias');
        await page.click('button:has-text("Buscar")');
        await page.waitForLoadState('networkidle');

        // Verificar se a URL reflete os parâmetros de busca
        const url = page.url();
        expect(url).toContain('/buscar');

        // Verificar se há resultados consistentes
        const resultsText = await page.textContent('body');
        if (resultsText?.includes('farmácias')) {
            console.log('✅ Dados de busca consistentes');
        } else {
            console.log('⚠️ Nenhum resultado encontrado (pode ser normal)');
        }

        console.log('✅ Integridade de dados verificada');
    });
});
