import { expect, test } from '@playwright/test';

test.describe('Sistema de Afiliados', () => {
    test.beforeEach(async ({ page }) => {
        // Configurar base URL
        await page.goto('/');
    });

    test('deve gerar links de afiliado corretamente', async ({ page }) => {
        // Este teste verificará se os links de afiliado são gerados no formato correto
        // Primeiro precisamos ter um usuário afiliado logado

        // TODO: Implementar login como afiliado
        // Por enquanto, vamos testar se a página de afiliados carrega
        await page.goto('/afiliados');
        await expect(page).toHaveTitle(/F4 Fisio/);
    });

    test('deve capturar parâmetro ref na URL', async ({ page }) => {
        // Testar se o sistema captura o parâmetro ref quando alguém acessa via link de afiliado
        const afiliadoCode = '**********';

        // Acessar com parâmetro ref
        await page.goto(`/?ref=${afiliadoCode}`);

        // Aguardar um pouco para o middleware processar
        await page.waitForTimeout(1000);

        // Verificar se o parâmetro foi capturado (deveria estar em cookie)
        const cookies = await page.context().cookies();
        console.log(
            'Cookies encontrados:',
            cookies.map((c) => ({ name: c.name, value: c.value })),
        );

        // Verificar se existe cookie de referência
        const refCookie = cookies.find((cookie) => cookie.name === 'affiliate_ref');
        console.log('Cookie de referência:', refCookie);

        // Agora deveria ter o cookie
        if (refCookie) {
            expect(refCookie.value).toBe(afiliadoCode);
            console.log('✅ Cookie de afiliado capturado com sucesso!');
        } else {
            console.log('❌ Cookie de afiliado não foi criado');
        }

        expect(page.url()).toContain(`ref=${afiliadoCode}`);
    });

    test('deve preservar referência durante navegação', async ({ page }) => {
        const afiliadoCode = '**********';

        // Acessar com parâmetro ref
        await page.goto(`/?ref=${afiliadoCode}`);

        // Navegar para página de planos
        await page.goto('/planos');

        // Verificar se a referência foi preservada (em cookie ou session)
        const cookies = await page.context().cookies();
        const refCookie = cookies.find(
            (cookie) => cookie.name.includes('ref') || cookie.name.includes('affiliate') || cookie.name.includes('referral'),
        );

        console.log(
            'Cookies após navegação:',
            cookies.map((c) => ({ name: c.name, value: c.value })),
        );
        console.log('Cookie de referência preservado:', refCookie);
    });

    test('deve processar cupom com referência de afiliado', async ({ page }) => {
        const afiliadoCode = '**********';
        const cupomCode = 'DESCONTO10';

        // Acessar com cupom e referência
        await page.goto(`/planos?cupom=${cupomCode}&ref=${afiliadoCode}`);

        // Verificar se ambos os parâmetros estão presentes
        expect(page.url()).toContain(`cupom=${cupomCode}`);
        expect(page.url()).toContain(`ref=${afiliadoCode}`);

        // Verificar se há processamento dos parâmetros
        const cookies = await page.context().cookies();
        console.log(
            'Cookies com cupom e ref:',
            cookies.map((c) => ({ name: c.name, value: c.value })),
        );
    });

    test('deve manter referência durante processo de compra', async ({ page }) => {
        const afiliadoCode = '**********';

        // Simular fluxo completo de compra com referência
        await page.goto(`/planos?ref=${afiliadoCode}`);

        // Verificar se chegou na página de planos
        await expect(page).toHaveURL(/.*planos.*/);

        // Verificar se existem planos na página
        const planosExistem = await page.locator('[data-testid="plano-card"], .plano-card, [class*="plano"], [class*="plan"]').count();
        console.log('Número de planos encontrados:', planosExistem);

        // Documentar estado atual dos cookies
        const cookies = await page.context().cookies();
        console.log(
            'Estado dos cookies durante compra:',
            cookies.map((c) => ({ name: c.name, value: c.value })),
        );
    });

    test('deve verificar estrutura de dados de afiliados', async ({ page }) => {
        // Testar se as rotas de afiliado existem e respondem
        const routes = [
            '/afiliados',
            '/afiliado/dashboard', // Requer autenticação
            '/admin/afiliados', // Requer autenticação admin
        ];

        for (const route of routes) {
            const response = await page.goto(route);
            console.log(`Rota ${route}: Status ${response?.status()}`);

            // Verificar se não é erro 404
            if (response?.status() !== 404) {
                console.log(`✅ Rota ${route} existe`);
            } else {
                console.log(`❌ Rota ${route} não encontrada`);
            }
        }
    });

    test('deve verificar configuração de cookies', async ({ page }) => {
        await page.goto('/');

        // Verificar configurações de cookies do Laravel
        const cookies = await page.context().cookies();
        const laravelSession = cookies.find((c) => c.name.includes('session'));

        console.log('Configuração de cookies Laravel:', {
            sessionCookie: laravelSession,
            totalCookies: cookies.length,
            cookieNames: cookies.map((c) => c.name),
        });

        // Verificar se cookies não criptografados estão funcionando
        // (appearance e sidebar_state devem estar na lista de exceções)
        await page.evaluate(() => {
            document.cookie = 'test_affiliate_ref=**********; path=/; max-age=86400';
        });

        const updatedCookies = await page.context().cookies();
        const testCookie = updatedCookies.find((c) => c.name === 'test_affiliate_ref');
        console.log('Cookie de teste criado:', testCookie);
    });
});
