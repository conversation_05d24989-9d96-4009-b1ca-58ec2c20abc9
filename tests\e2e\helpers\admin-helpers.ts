import { Page, expect } from '@playwright/test';

/**
 * Helper functions for admin tests
 */

export interface AdminCredentials {
    email: string;
    password: string;
}

export const DEFAULT_ADMIN_CREDENTIALS: AdminCredentials = {
    email: '<EMAIL>',
    password: 'admin123',
};

/**
 * Login as admin user
 */
export async function loginAsAdmin(page: Page, credentials: AdminCredentials = DEFAULT_ADMIN_CREDENTIALS) {
    console.log('🔐 Fazendo login como administrador...');

    await page.goto('/login');
    await page.waitForLoadState('networkidle');

    // Fill login form
    await page.getByRole('textbox', { name: 'Endereço de email' }).fill(credentials.email);
    await page.getByRole('textbox', { name: 'Senha' }).fill(credentials.password);

    // Submit form
    await page.getByRole('button', { name: 'Entrar' }).click();

    // Wait for redirect to admin dashboard
    await page.waitForURL('**/admin/dashboard', { timeout: 15000 });

    console.log('✅ Login como admin realizado com sucesso');
}

/**
 * Navigate to admin page
 */
export async function navigateToAdminPage(page: Page, adminPage: string) {
    const url = `/admin/${adminPage}`;
    console.log(`🧭 Navegando para ${url}...`);

    await page.goto(url);
    await page.waitForLoadState('networkidle');

    // Verify we're on the correct page
    expect(page.url()).toContain(url);
    console.log(`✅ Navegação para ${url} bem-sucedida`);
}

/**
 * Wait for page to load completely
 */
export async function waitForPageLoad(page: Page, timeout: number = 10000) {
    await page.waitForLoadState('networkidle', { timeout });

    // Wait for any loading indicators to disappear
    await page
        .waitForFunction(
            () => {
                const loadingElements = document.querySelectorAll('[data-loading="true"], .loading, .spinner');
                return loadingElements.length === 0;
            },
            { timeout: 5000 },
        )
        .catch(() => {
            // Ignore timeout - loading indicators might not exist
        });
}

/**
 * Check if element exists and is visible
 */
export async function checkElementExists(page: Page, selector: string, description: string): Promise<boolean> {
    try {
        const element = page.locator(selector);
        const isVisible = await element.isVisible({ timeout: 5000 });

        if (isVisible) {
            console.log(`✅ ${description} encontrado`);
            return true;
        } else {
            console.log(`⚠️ ${description} não visível`);
            return false;
        }
    } catch (error) {
        console.log(`❌ ${description} não encontrado`);
        return false;
    }
}

/**
 * Click element safely with retry
 */
export async function clickElementSafely(page: Page, selector: string, description: string): Promise<boolean> {
    try {
        const element = page.locator(selector);
        await element.waitFor({ state: 'visible', timeout: 5000 });
        await element.click();
        console.log(`✅ Clicou em: ${description}`);
        return true;
    } catch (error) {
        console.log(`❌ Falha ao clicar em: ${description}`);
        return false;
    }
}

/**
 * Fill form field safely
 */
export async function fillFieldSafely(page: Page, selector: string, value: string, description: string): Promise<boolean> {
    try {
        const element = page.locator(selector);
        await element.waitFor({ state: 'visible', timeout: 5000 });
        await element.fill(value);
        console.log(`✅ Preencheu ${description}: ${value}`);
        return true;
    } catch (error) {
        console.log(`❌ Falha ao preencher ${description}`);
        return false;
    }
}

/**
 * Check if admin has proper permissions
 */
export async function verifyAdminPermissions(page: Page): Promise<boolean> {
    console.log('🔒 Verificando permissões de administrador...');

    // Check if we can access admin dashboard
    await page.goto('/admin/dashboard');
    await waitForPageLoad(page);

    if (!page.url().includes('/admin/dashboard')) {
        console.log('❌ Não foi possível acessar o dashboard admin');
        return false;
    }

    // Check for admin-specific elements
    const adminElements = [
        'heading:has-text("Dashboard Administrativo")',
        'text="Gerenciar Usuários"',
        'text="Fisioterapeutas"',
        'text="Estabelecimentos"',
    ];

    for (const selector of adminElements) {
        const exists = await checkElementExists(page, selector, `Elemento admin: ${selector}`);
        if (!exists) {
            console.log('❌ Elementos administrativos não encontrados');
            return false;
        }
    }

    console.log('✅ Permissões de administrador verificadas');
    return true;
}

/**
 * Test CRUD operations for a resource
 */
export interface CrudTestConfig {
    resourceName: string;
    listUrl: string;
    createUrl: string;
    formFields: { [key: string]: string };
    listItemSelector: string;
    editButtonSelector?: string;
    deleteButtonSelector?: string;
}

export async function testCrudOperations(page: Page, config: CrudTestConfig): Promise<boolean> {
    console.log(`🔧 Testando operações CRUD para ${config.resourceName}...`);

    try {
        // Test LIST
        await navigateToAdminPage(page, config.listUrl.replace('/admin/', ''));
        await waitForPageLoad(page);

        const hasListItems = await checkElementExists(page, config.listItemSelector, `Lista de ${config.resourceName}`);
        console.log(`📋 Lista de ${config.resourceName}: ${hasListItems ? 'OK' : 'Vazia'}`);

        // Test CREATE (navigate to create form)
        const createButton = page.locator('a[href*="create"], button:has-text("Novo"), button:has-text("Criar"), button:has-text("Adicionar")');
        if ((await createButton.count()) > 0) {
            await createButton.first().click();
            await waitForPageLoad(page);
            console.log(`✅ Formulário de criação de ${config.resourceName} acessível`);

            // Go back to list
            await page.goBack();
            await waitForPageLoad(page);
        }

        return true;
    } catch (error) {
        console.log(`❌ Erro ao testar CRUD de ${config.resourceName}: ${error}`);
        return false;
    }
}

/**
 * Test pagination if exists
 */
export async function testPagination(page: Page, resourceName: string): Promise<boolean> {
    console.log(`📄 Testando paginação para ${resourceName}...`);

    const paginationSelectors = [
        '.pagination',
        '[data-testid="pagination"]',
        'nav[aria-label="pagination"]',
        'button:has-text("Próxima")',
        'button:has-text("Anterior")',
        'a:has-text("2")',
    ];

    for (const selector of paginationSelectors) {
        if (await checkElementExists(page, selector, `Paginação (${selector})`)) {
            console.log(`✅ Paginação encontrada para ${resourceName}`);
            return true;
        }
    }

    console.log(`ℹ️ Paginação não encontrada para ${resourceName} (pode ser normal se há poucos itens)`);
    return false;
}

/**
 * Test search functionality
 */
export async function testSearchFunctionality(page: Page, resourceName: string, searchTerm: string = 'test'): Promise<boolean> {
    console.log(`🔍 Testando busca para ${resourceName}...`);

    const searchSelectors = [
        'input[name="search"]',
        'input[placeholder*="buscar"]',
        'input[placeholder*="pesquisar"]',
        'input[type="search"]',
        '.search-input',
    ];

    for (const selector of searchSelectors) {
        if ((await page.locator(selector).count()) > 0) {
            await fillFieldSafely(page, selector, searchTerm, `Campo de busca para ${resourceName}`);

            // Try to submit search
            await page.keyboard.press('Enter');
            await waitForPageLoad(page);

            console.log(`✅ Funcionalidade de busca testada para ${resourceName}`);
            return true;
        }
    }

    console.log(`ℹ️ Campo de busca não encontrado para ${resourceName}`);
    return false;
}

/**
 * Test export functionality
 */
export async function testExportFunctionality(page: Page, resourceName: string): Promise<boolean> {
    console.log(`📤 Testando exportação para ${resourceName}...`);

    const exportSelectors = [
        'button:has-text("Exportar")',
        'button:has-text("Export")',
        'a:has-text("Exportar")',
        'a[href*="export"]',
        '.export-button',
    ];

    for (const selector of exportSelectors) {
        if (await checkElementExists(page, selector, `Botão de exportação para ${resourceName}`)) {
            console.log(`✅ Funcionalidade de exportação encontrada para ${resourceName}`);
            return true;
        }
    }

    console.log(`ℹ️ Funcionalidade de exportação não encontrada para ${resourceName}`);
    return false;
}
