import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AfiliadoLayout from '@/layouts/AfiliadoLayout';
import { Head, Link, router } from '@inertiajs/react';
import { Copy, ExternalLink, Eye, Search } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface Cupom {
    id: number;
    codigo: string;
    nome: string;
    descricao: string;
    tipo: 'publico' | 'afiliado_exclusivo';
    tipo_desconto: 'percentual' | 'valor_fixo';
    valor_desconto: number;
    valor_minimo_pedido?: number;
    data_inicio: string;
    data_fim?: string;
    limite_uso?: number;
    usos_realizados: number;
    ativo: boolean;
    status: string;
    descricao_desconto: string;
    afiliado?: {
        id: number;
        nome: string;
        codigo_afiliado: string;
    };
}

interface Props {
    cupons: {
        data: Cupom[];
        links: any[];
        meta: any;
    };
    stats: {
        total_cupons: number;
        meus_exclusivos: number;
        publicos: number;
    };
    filters: {
        tipo?: string;
        search?: string;
    };
    afiliado: any;
}

export default function Cupons({ cupons, stats, filters, afiliado }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [tipo, setTipo] = useState(filters.tipo || 'all');

    const handleFilter = () => {
        router.get(
            route('afiliado.cupons.index'),
            {
                search: search || undefined,
                tipo: tipo !== 'all' ? tipo : undefined,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    const copyToClipboard = async (text: string) => {
        try {
            await navigator.clipboard.writeText(text);
            toast.success('Código copiado para a área de transferência!');
        } catch (err) {
            toast.error('Erro ao copiar código');
        }
    };

    const getLink = async (cupom: Cupom) => {
        try {
            const response = await fetch(route('afiliado.cupons.link', cupom.id));
            const data = await response.json();

            if (response.ok) {
                await navigator.clipboard.writeText(data.link);
                toast.success('Link copiado para a área de transferência!');
            } else {
                toast.error(data.error || 'Erro ao gerar link');
            }
        } catch (err) {
            toast.error('Erro ao gerar link');
        }
    };

    const getStatusBadge = (cupom: Cupom) => {
        if (!cupom.ativo) {
            return <Badge variant="secondary">Inativo</Badge>;
        }

        if (cupom.status === 'expirado') {
            return <Badge variant="destructive">Expirado</Badge>;
        }

        return <Badge variant="default">Ativo</Badge>;
    };

    const getTipoBadge = (tipo: string) => {
        if (tipo === 'afiliado_exclusivo') {
            return <Badge variant="default">Exclusivo</Badge>;
        }
        return <Badge variant="outline">Público</Badge>;
    };

    return (
        <AfiliadoLayout>
            <Head title="Cupons de Desconto" />

            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Cupons de Desconto</h1>
                    <p className="text-muted-foreground">Gerencie seus cupons de desconto e códigos promocionais</p>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Cupons</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_cupons}</div>
                            <p className="text-xs text-muted-foreground">Cupons disponíveis para você</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Meus Exclusivos</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.meus_exclusivos}</div>
                            <p className="text-xs text-muted-foreground">Cupons exclusivos seus (com comissão)</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Públicos</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.publicos}</div>
                            <p className="text-xs text-muted-foreground">Cupons públicos (sem comissão)</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filtros</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Buscar por código ou nome..."
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    className="max-w-sm"
                                />
                            </div>
                            <Select value={tipo} onValueChange={setTipo}>
                                <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="Tipo de cupom" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todos os cupons</SelectItem>
                                    <SelectItem value="meus_exclusivos">Meus exclusivos</SelectItem>
                                    <SelectItem value="publicos">Públicos</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button onClick={handleFilter}>
                                <Search className="mr-2 h-4 w-4" />
                                Filtrar
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Cupons List */}
                <div className="grid gap-4">
                    {cupons?.data?.map((cupom) => (
                        <Card key={cupom.id}>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                            <h3 className="text-lg font-semibold">{cupom.nome}</h3>
                                            {getTipoBadge(cupom.tipo)}
                                            {getStatusBadge(cupom)}
                                        </div>
                                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                            <span className="rounded bg-muted px-2 py-1 font-mono">{cupom.codigo}</span>
                                            <span>{cupom.descricao_desconto}</span>
                                            {cupom.valor_minimo_pedido && <span>Mín: R$ {Number(cupom.valor_minimo_pedido).toFixed(2)}</span>}
                                        </div>
                                        {cupom.descricao && <p className="text-sm text-muted-foreground">{cupom.descricao}</p>}
                                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                            <span>Válido até: {cupom.data_fim || 'Sem limite'}</span>
                                            {cupom.limite_uso && (
                                                <span>
                                                    Usos: {cupom.usos_realizados}/{cupom.limite_uso}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button variant="outline" size="sm" onClick={() => copyToClipboard(cupom.codigo)}>
                                            <Copy className="mr-2 h-4 w-4" />
                                            Copiar Código
                                        </Button>
                                        <Button variant="outline" size="sm" onClick={() => getLink(cupom)}>
                                            <ExternalLink className="mr-2 h-4 w-4" />
                                            Copiar Link
                                        </Button>
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={route('afiliado.cupons.show', cupom.id)}>
                                                <Eye className="mr-2 h-4 w-4" />
                                                Ver Detalhes
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Pagination */}
                {cupons?.meta?.last_page > 1 && (
                    <div className="flex justify-center">
                        <div className="flex gap-2">
                            {cupons.links?.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => link.url && router.get(link.url)}
                                    disabled={!link.url}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}

                {(!cupons?.data || cupons.data.length === 0) && (
                    <Card>
                        <CardContent className="p-6 text-center">
                            <p className="text-muted-foreground">Nenhum cupom encontrado.</p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AfiliadoLayout>
    );
}
