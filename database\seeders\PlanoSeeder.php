<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Plano;

class PlanoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $planos = [
            [
                'name' => 'Fisio Essencial',
                'description' => 'Plano básico com atendimento domiciliar de qualidade',
                'price' => 129.90,
                'sessions_per_month' => 4,
                'session_duration' => 60,
                'included_services' => [
                    'Fisioterapia Ortopédica',
                    'Fisioterapia Neurológica',
                    'Fisioterapia Respiratória',
                    'Atendimento domiciliar'
                ],
                'benefits' => [
                    'Atendimento no conforto de casa',
                    'Profissionais qualificados',
                    'Relatórios de evolução',
                    'Suporte via WhatsApp'
                ],
                'active' => true,
            ],
            [
                'name' => 'Fisio Vitalidade+',
                'description' => 'Plano completo com benefícios exclusivos',
                'price' => 219.90,
                'sessions_per_month' => 8,
                'session_duration' => 60,
                'included_services' => [
                    'Fisioterapia Ortopédica',
                    'Fisioterapia Neurológica',
                    'Fisioterapia Respiratória',
                    'Fisioterapia Geriátrica',
                    'Fisioterapia Pediátrica',
                    'Atendimento domiciliar',
                    'Avaliação nutricional'
                ],
                'benefits' => [
                    'Atendimento no conforto de casa',
                    'Profissionais qualificados',
                    'Relatórios de evolução detalhados',
                    'Suporte via WhatsApp',
                    'Consulta nutricional mensal',
                    'Programa de exercícios personalizado',
                    'Desconto em equipamentos'
                ],
                'active' => true,
            ],
        ];

        foreach ($planos as $plano) {
            Plano::create($plano);
        }
    }
}
