<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('privacy_profile_visible')->default(true)->comment('Perfil visível para outros usuários');
            $table->boolean('privacy_contact_visible')->default(true)->comment('Informações de contato visíveis');
            $table->boolean('privacy_medical_visible')->default(false)->comment('Informações médicas visíveis para fisioterapeutas');
            $table->boolean('privacy_allow_marketing')->default(false)->comment('Permitir receber comunicações de marketing');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'privacy_profile_visible',
                'privacy_contact_visible',
                'privacy_medical_visible',
                'privacy_allow_marketing'
            ]);
        });
    }
};
