# Guia de Deploy - F4 Fisio

Este guia explica como fazer o deploy da aplicação F4 Fisio em produção, especialmente no HostGator cPanel.

## 📋 Pré-requisitos

- PHP 8.2 ou superior
- Composer
- Node.js e NPM
- Acesso ao cPanel do HostGator
- Domínio configurado

## 🚀 Passos para Deploy

### 1. Preparar a Aplicação Localmente

```bash
# Limpar dados de desenvolvimento
php artisan app:prepare-production

# Instalar dependências de produção
composer install --no-dev --optimize-autoloader

# Compilar assets para produção
npm run build

# Limpar caches
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear
```

### 2. Configurar Ambiente de Produção

1. Copie `.env.production.example` para `.env`
2. Configure as variáveis de ambiente:
   - `APP_URL`: URL do seu domínio
   - `APP_KEY`: Gere com `php artisan key:generate`
   - Configurações de email
   - Configurações do Mercado Pago (se necessário)

### 3. Upload dos Arquivos

#### Via cPanel File Manager:
1. Acesse o cPanel
2. Abra o File Manager
3. Navegue até `public_html`
4. Faça upload de todos os arquivos da aplicação
5. Mova o conteúdo da pasta `public` para `public_html`
6. Ajuste o `index.php` para apontar para a pasta correta

#### Via FTP:
```bash
# Upload da aplicação (exceto pasta public)
# Coloque na pasta acima de public_html (ex: /home/<USER>/)

# Upload do conteúdo da pasta public
# Coloque em public_html
```

### 4. Configurar Banco de Dados

```bash
# Criar arquivo de banco SQLite
touch database/database.sqlite

# Executar migrações
php artisan migrate --force

# Executar seeders de produção
php artisan db:seed --class=ProductionSeeder
```

### 5. Configurar Permissões

```bash
# Definir permissões corretas
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod 644 database/database.sqlite
```

### 6. Configurar .htaccess

Certifique-se de que o arquivo `.htaccess` está na pasta `public_html`:

```apache
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
```

### 7. Configurar Cron Jobs (Opcional)

No cPanel, adicione um cron job para executar tarefas agendadas:

```bash
# A cada minuto
* * * * * cd /home/<USER>/path-to-app && php artisan schedule:run >> /dev/null 2>&1
```

### 8. Configurar SSL

1. No cPanel, vá para "SSL/TLS"
2. Ative "Force HTTPS Redirect"
3. Configure certificado SSL (Let's Encrypt gratuito)

## 🔧 Configurações Específicas do HostGator

### Arquivo .htaccess na Raiz

Crie um `.htaccess` na pasta do usuário (acima de public_html):

```apache
# Redirecionar para public_html se necessário
RewriteEngine On
RewriteCond %{REQUEST_URI} !^/public_html/
RewriteRule ^(.*)$ /public_html/$1 [L]
```

### PHP.ini Personalizado

Crie um arquivo `php.ini` em `public_html`:

```ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M
```

## 🔐 Segurança

### Variáveis de Ambiente Sensíveis

- Nunca commite o arquivo `.env`
- Use senhas fortes
- Configure `APP_DEBUG=false`
- Configure `APP_ENV=production`

### Backup

Configure backups regulares:
- Banco de dados SQLite
- Arquivos de upload
- Configurações

## 📞 Suporte

### Credenciais Padrão
- **Email:** <EMAIL>
- **Senha:** admin123

⚠️ **IMPORTANTE:** Altere a senha após o primeiro login!

### Funcionalidades Principais

1. **Sistema de Usuários**
   - Administradores
   - Fisioterapeutas
   - Pacientes

2. **Sistema de Agendamentos**
   - Agendamento online
   - Confirmação automática
   - Notificações por email

3. **Sistema de Pagamentos**
   - Integração com Mercado Pago
   - Planos mensais e avulsos
   - Controle de assinaturas

4. **Sistema de Busca**
   - Busca de estabelecimentos
   - Geolocalização
   - Contato via WhatsApp

5. **Relatórios e Dashboard**
   - Dashboard administrativo
   - Relatórios financeiros
   - Estatísticas operacionais

## 🐛 Troubleshooting

### Problemas Comuns

1. **Erro 500**
   - Verifique permissões das pastas
   - Verifique logs em `storage/logs`
   - Verifique configuração do `.env`

2. **Assets não carregam**
   - Execute `npm run build`
   - Verifique `APP_URL` no `.env`
   - Verifique permissões da pasta `public`

3. **Banco de dados**
   - Verifique se o arquivo SQLite existe
   - Verifique permissões do arquivo
   - Execute `php artisan migrate`

4. **Email não funciona**
   - Verifique configurações SMTP
   - Teste com `php artisan tinker`
   - Verifique logs de email

### Logs

Monitore os logs em:
- `storage/logs/laravel.log`
- cPanel Error Logs
- Email logs

## 📈 Monitoramento

### Métricas Importantes

- Tempo de resposta
- Uso de memória
- Espaço em disco
- Número de usuários ativos
- Taxa de conversão de planos

### Ferramentas Recomendadas

- Google Analytics
- Uptime monitoring
- Error tracking (Sentry)
- Performance monitoring
