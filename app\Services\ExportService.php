<?php

namespace App\Services;

use App\Models\BackupLog;
use App\Models\User;
use App\Models\Agendamento;
use App\Models\Pagamento;
use App\Models\Avaliacao;
use App\Models\RelatorioSessao;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class ExportService
{
    protected $exportDisk;

    public function __construct()
    {
        $this->exportDisk = Storage::disk('local');
    }

    /**
     * Exportar relatório de usuários
     */
    public function exportUsers(array $filters = [], string $format = 'excel', ?int $userId = null): BackupLog
    {
        $exportLog = BackupLog::create([
            'type' => BackupLog::TYPE_EXPORT,
            'status' => BackupLog::STATUS_PENDING,
            'user_id' => $userId,
            'started_at' => now(),
            'metadata' => [
                'export_type' => 'users',
                'format' => $format,
                'filters' => $filters
            ]
        ]);

        try {
            $exportLog->update(['status' => BackupLog::STATUS_PROCESSING]);

            $query = User::query();
            
            // Aplicar filtros
            if (isset($filters['role'])) {
                $query->where('role', $filters['role']);
            }
            
            if (isset($filters['active'])) {
                $query->where('active', $filters['active']);
            }
            
            if (isset($filters['date_from'])) {
                $query->where('created_at', '>=', $filters['date_from']);
            }
            
            if (isset($filters['date_to'])) {
                $query->where('created_at', '<=', $filters['date_to']);
            }

            $users = $query->get();
            
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $fileName = "relatorio_usuarios_{$timestamp}";
            
            if ($format === 'pdf') {
                $filePath = $this->exportUsersToPdf($users, $fileName, $filters);
            } else {
                $filePath = $this->exportUsersToExcel($users, $fileName, $filters);
            }
            
            $fileSize = $this->exportDisk->size($filePath);
            
            $exportLog->update([
                'status' => BackupLog::STATUS_COMPLETED,
                'file_name' => basename($filePath),
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'completed_at' => now(),
            ]);

            return $exportLog;

        } catch (\Exception $e) {
            $exportLog->update([
                'status' => BackupLog::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * Exportar relatório de agendamentos
     */
    public function exportAgendamentos(array $filters = [], string $format = 'excel', ?int $userId = null): BackupLog
    {
        $exportLog = BackupLog::create([
            'type' => BackupLog::TYPE_EXPORT,
            'status' => BackupLog::STATUS_PENDING,
            'user_id' => $userId,
            'started_at' => now(),
            'metadata' => [
                'export_type' => 'agendamentos',
                'format' => $format,
                'filters' => $filters
            ]
        ]);

        try {
            $exportLog->update(['status' => BackupLog::STATUS_PROCESSING]);

            $query = Agendamento::with(['paciente', 'fisioterapeuta']);
            
            // Aplicar filtros
            if (isset($filters['status'])) {
                $query->where('status', $filters['status']);
            }
            
            if (isset($filters['fisioterapeuta_id'])) {
                $query->where('fisioterapeuta_id', $filters['fisioterapeuta_id']);
            }
            
            if (isset($filters['date_from'])) {
                $query->where('scheduled_at', '>=', $filters['date_from']);
            }
            
            if (isset($filters['date_to'])) {
                $query->where('scheduled_at', '<=', $filters['date_to']);
            }

            $agendamentos = $query->get();
            
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $fileName = "relatorio_agendamentos_{$timestamp}";
            
            if ($format === 'pdf') {
                $filePath = $this->exportAgendamentosToPdf($agendamentos, $fileName, $filters);
            } else {
                $filePath = $this->exportAgendamentosToExcel($agendamentos, $fileName, $filters);
            }
            
            $fileSize = $this->exportDisk->size($filePath);
            
            $exportLog->update([
                'status' => BackupLog::STATUS_COMPLETED,
                'file_name' => basename($filePath),
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'completed_at' => now(),
            ]);

            return $exportLog;

        } catch (\Exception $e) {
            $exportLog->update([
                'status' => BackupLog::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * Exportar relatório financeiro
     */
    public function exportFinanceiro(array $filters = [], string $format = 'excel', ?int $userId = null): BackupLog
    {
        $exportLog = BackupLog::create([
            'type' => BackupLog::TYPE_EXPORT,
            'status' => BackupLog::STATUS_PENDING,
            'user_id' => $userId,
            'started_at' => now(),
            'metadata' => [
                'export_type' => 'financeiro',
                'format' => $format,
                'filters' => $filters
            ]
        ]);

        try {
            $exportLog->update(['status' => BackupLog::STATUS_PROCESSING]);

            $query = Pagamento::with(['assinatura.user']);
            
            // Aplicar filtros
            if (isset($filters['status'])) {
                $query->where('status', $filters['status']);
            }
            
            if (isset($filters['forma_pagamento'])) {
                $query->where('forma_pagamento', $filters['forma_pagamento']);
            }
            
            if (isset($filters['date_from'])) {
                $query->where('created_at', '>=', $filters['date_from']);
            }
            
            if (isset($filters['date_to'])) {
                $query->where('created_at', '<=', $filters['date_to']);
            }

            $pagamentos = $query->get();
            
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $fileName = "relatorio_financeiro_{$timestamp}";
            
            if ($format === 'pdf') {
                $filePath = $this->exportFinanceiroToPdf($pagamentos, $fileName, $filters);
            } else {
                $filePath = $this->exportFinanceiroToExcel($pagamentos, $fileName, $filters);
            }
            
            $fileSize = $this->exportDisk->size($filePath);
            
            $exportLog->update([
                'status' => BackupLog::STATUS_COMPLETED,
                'file_name' => basename($filePath),
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'completed_at' => now(),
            ]);

            return $exportLog;

        } catch (\Exception $e) {
            $exportLog->update([
                'status' => BackupLog::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * Exportar usuários para Excel
     */
    protected function exportUsersToExcel($users, string $fileName, array $filters): string
    {
        $data = [];
        $data[] = ['ID', 'Nome', 'Email', 'Telefone', 'Tipo', 'Ativo', 'Data Cadastro'];
        
        foreach ($users as $user) {
            $data[] = [
                $user->id,
                $user->name,
                $user->email,
                $user->phone ?? 'N/A',
                $user->role,
                $user->active ? 'Sim' : 'Não',
                $user->created_at->format('d/m/Y H:i')
            ];
        }

        $filePath = "exports/{$fileName}.xlsx";
        
        Excel::store(new class($data) implements \Maatwebsite\Excel\Concerns\FromArray {
            private $data;
            
            public function __construct($data) {
                $this->data = $data;
            }
            
            public function array(): array {
                return $this->data;
            }
        }, $filePath, 'local');

        return $filePath;
    }

    /**
     * Exportar usuários para PDF
     */
    protected function exportUsersToPdf($users, string $fileName, array $filters): string
    {
        $pdf = Pdf::loadView('exports.users-pdf', [
            'users' => $users,
            'filters' => $filters,
            'generated_at' => now()
        ]);

        $filePath = "exports/{$fileName}.pdf";
        $this->exportDisk->put($filePath, $pdf->output());

        return $filePath;
    }

    /**
     * Listar exportações
     */
    public function listExports(): array
    {
        return BackupLog::byType(BackupLog::TYPE_EXPORT)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Baixar exportação
     */
    public function downloadExport(int $exportId): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $exportLog = BackupLog::findOrFail($exportId);
        
        if (!$exportLog->isCompleted()) {
            throw new \Exception('Exportação não está completa');
        }

        if (!$this->exportDisk->exists($exportLog->file_path)) {
            throw new \Exception('Arquivo de exportação não encontrado');
        }

        return $this->exportDisk->download($exportLog->file_path, $exportLog->file_name);
    }

    /**
     * Deletar exportação
     */
    public function deleteExport(int $exportId): bool
    {
        $exportLog = BackupLog::findOrFail($exportId);

        try {
            if ($exportLog->file_path && $this->exportDisk->exists($exportLog->file_path)) {
                $this->exportDisk->delete($exportLog->file_path);
            }

            $exportLog->delete();

            return true;
        } catch (\Exception $e) {
            Log::error('Falha ao deletar exportação', [
                'export_id' => $exportId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Exportar agendamentos para Excel
     */
    protected function exportAgendamentosToExcel($agendamentos, string $fileName, array $filters): string
    {
        $data = [];
        $data[] = ['ID', 'Paciente', 'Fisioterapeuta', 'Data/Hora', 'Status', 'Valor', 'Observações'];

        foreach ($agendamentos as $agendamento) {
            $data[] = [
                $agendamento->id,
                $agendamento->paciente->name ?? 'N/A',
                $agendamento->fisioterapeuta->name ?? 'N/A',
                $agendamento->scheduled_at->format('d/m/Y H:i'),
                $agendamento->status,
                'R$ ' . number_format($agendamento->price, 2, ',', '.'),
                $agendamento->notes ?? 'N/A'
            ];
        }

        $filePath = "exports/{$fileName}.xlsx";

        Excel::store(new class($data) implements \Maatwebsite\Excel\Concerns\FromArray {
            private $data;

            public function __construct($data) {
                $this->data = $data;
            }

            public function array(): array {
                return $this->data;
            }
        }, $filePath, 'local');

        return $filePath;
    }

    /**
     * Exportar agendamentos para PDF
     */
    protected function exportAgendamentosToPdf($agendamentos, string $fileName, array $filters): string
    {
        $pdf = Pdf::loadView('exports.agendamentos-pdf', [
            'agendamentos' => $agendamentos,
            'filters' => $filters,
            'generated_at' => now()
        ]);

        $filePath = "exports/{$fileName}.pdf";
        $this->exportDisk->put($filePath, $pdf->output());

        return $filePath;
    }

    /**
     * Exportar financeiro para Excel
     */
    protected function exportFinanceiroToExcel($pagamentos, string $fileName, array $filters): string
    {
        $data = [];
        $data[] = ['ID', 'Cliente', 'Valor', 'Status', 'Forma Pagamento', 'Data Vencimento', 'Data Pagamento'];

        foreach ($pagamentos as $pagamento) {
            $data[] = [
                $pagamento->id,
                $pagamento->assinatura->user->name ?? 'N/A',
                'R$ ' . number_format($pagamento->valor, 2, ',', '.'),
                $pagamento->status,
                $pagamento->forma_pagamento,
                $pagamento->data_vencimento ? $pagamento->data_vencimento->format('d/m/Y') : 'N/A',
                $pagamento->data_pagamento ? $pagamento->data_pagamento->format('d/m/Y') : 'N/A'
            ];
        }

        $filePath = "exports/{$fileName}.xlsx";

        Excel::store(new class($data) implements \Maatwebsite\Excel\Concerns\FromArray {
            private $data;

            public function __construct($data) {
                $this->data = $data;
            }

            public function array(): array {
                return $this->data;
            }
        }, $filePath, 'local');

        return $filePath;
    }

    /**
     * Exportar financeiro para PDF
     */
    protected function exportFinanceiroToPdf($pagamentos, string $fileName, array $filters): string
    {
        $pdf = Pdf::loadView('exports.financeiro-pdf', [
            'pagamentos' => $pagamentos,
            'filters' => $filters,
            'generated_at' => now()
        ]);

        $filePath = "exports/{$fileName}.pdf";
        $this->exportDisk->put($filePath, $pdf->output());

        return $filePath;
    }
}
