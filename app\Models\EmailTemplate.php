<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'nome',
        'assunto',
        'conteudo_html',
        'conteudo_texto',
        'variaveis_disponiveis',
        'tipo',
        'ativo',
        'created_by',
    ];

    protected $casts = [
        'variaveis_disponiveis' => 'array',
        'ativo' => 'boolean',
    ];

    // Relacionamentos
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeAtivos($query)
    {
        return $query->where('ativo', true);
    }

    public function scopePorTipo($query, $tipo)
    {
        return $query->where('tipo', $tipo);
    }

    // Métodos auxiliares
    public function processarTemplate($variaveis = [])
    {
        $assunto = $this->assunto;
        $conteudoHtml = $this->conteudo_html;
        $conteudoTexto = $this->conteudo_texto;

        foreach ($variaveis as $chave => $valor) {
            $placeholder = '{{' . $chave . '}}';
            $assunto = str_replace($placeholder, $valor, $assunto);
            $conteudoHtml = str_replace($placeholder, $valor, $conteudoHtml);
            $conteudoTexto = str_replace($placeholder, $valor, $conteudoTexto);
        }

        return [
            'assunto' => $assunto,
            'conteudo_html' => $conteudoHtml,
            'conteudo_texto' => $conteudoTexto,
        ];
    }

    public static function getTemplateByTipo($tipo)
    {
        return self::where('tipo', $tipo)->where('ativo', true)->first();
    }

    public static function getTiposDisponiveis()
    {
        return [
            'afiliado_aprovacao' => 'Aprovação de Afiliado',
            'afiliado_rejeicao' => 'Rejeição de Afiliado',
            'pagamento_comissao_aprovado' => 'Pagamento de Comissão Aprovado',
            'pagamento_comissao_rejeitado' => 'Pagamento de Comissão Rejeitado',
            'pagamento_comissao_pago' => 'Pagamento de Comissão Realizado',
            'nova_venda_afiliado' => 'Nova Venda para Afiliado',
            'lembrete_pagamento_comissao' => 'Lembrete de Pagamento de Comissão',
        ];
    }

    public function getVariaveisDisponiveisFormatted()
    {
        if (!$this->variaveis_disponiveis) {
            return [];
        }

        return collect($this->variaveis_disponiveis)->map(function ($descricao, $variavel) {
            return [
                'variavel' => '{{' . $variavel . '}}',
                'descricao' => $descricao,
            ];
        })->values()->toArray();
    }
}
