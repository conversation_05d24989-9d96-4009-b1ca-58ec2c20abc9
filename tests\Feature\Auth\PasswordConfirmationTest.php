<?php

use App\Models\User;

test('confirm password screen can be rendered', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)->get('/confirm-password');

    $response->assertStatus(200);
});

test('password can be confirmed', function () {
    $user = User::factory()->create([
        'active' => true,
        'role' => 'paciente'
    ]);

    $response = $this->actingAs($user)
        ->withSession(['_token' => 'test-token'])
        ->post('/confirm-password', [
            'password' => 'password',
            '_token' => 'test-token'
        ]);

    $response->assertRedirect();
    $response->assertSessionHasNoErrors();
});

test('password is not confirmed with invalid password', function () {
    $user = User::factory()->create([
        'active' => true,
        'role' => 'paciente'
    ]);

    $response = $this->actingAs($user)
        ->withSession(['_token' => 'test-token'])
        ->post('/confirm-password', [
            'password' => 'wrong-password',
            '_token' => 'test-token'
        ]);

    $response->assertSessionHasErrors();
});