<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Afiliado;
use App\Models\VendaAfiliado;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class AfiliadoController extends Controller
{
    /**
     * Display a listing of affiliates.
     */
    public function index(Request $request)
    {
        $query = Afiliado::with('aprovadoPor');

        // Filtro por status
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Filtro por ativo/inativo
        if ($request->filled('ativo') && $request->ativo !== 'all') {
            $query->where('ativo', $request->boolean('ativo'));
        }

        // Busca por nome, email ou código
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('nome', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('codigo_afiliado', 'like', '%' . $request->search . '%');
            });
        }

        $afiliados = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        // Estatísticas
        $stats = [
            'total' => Afiliado::count(),
            'pendentes' => Afiliado::where('status', 'pendente')->count(),
            'aprovados' => Afiliado::where('status', 'aprovado')->count(),
            'ativos' => Afiliado::where('ativo', true)->count(),
        ];

        return Inertia::render('admin/afiliados/index', [
            'afiliados' => $afiliados,
            'filters' => $request->only(['status', 'ativo', 'search']),
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('admin/afiliados/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nome' => [
                'required',
                'string',
                'min:2',
                'max:255',
                'regex:/^[a-zA-ZÀ-ÿ\s]+$/'
            ],
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                'unique:afiliados,email'
            ],
            'telefone' => [
                'required',
                'string',
                'max:20',
                new \App\Rules\PhoneValidation()
            ],
            'cpf' => [
                'required',
                'string',
                'unique:afiliados,cpf',
                new \App\Rules\CpfValidation()
            ],
            'endereco' => [
                'required',
                'string',
                'min:10',
                'max:500'
            ],
            'cidade' => [
                'required',
                'string',
                'min:2',
                'max:100',
                'regex:/^[a-zA-ZÀ-ÿ\s]+$/'
            ],
            'estado' => [
                'required',
                'string',
                'size:2',
                'regex:/^[A-Z]{2}$/'
            ],
            'cep' => [
                'required',
                'string',
                new \App\Rules\CepValidation()
            ],
            'experiencia' => [
                'nullable',
                'string',
                'in:nenhuma,iniciante,intermediario,avancado'
            ],
            'motivacao' => [
                'required',
                'string',
                'min:20',
                'max:1000'
            ],
            'canais_divulgacao' => [
                'required',
                'array',
                'min:1',
                'max:10'
            ],
            'canais_divulgacao.*' => [
                'string',
                'max:100'
            ],
            'status' => [
                'required',
                'in:pendente,aprovado,rejeitado'
            ],
            'observacoes_admin' => [
                'nullable',
                'string',
                'max:1000'
            ],
        ]);

        $afiliado = new Afiliado();
        $afiliado->codigo_afiliado = $afiliado->gerarCodigoAfiliado();
        $afiliado->fill($validated);
        
        if ($validated['status'] === 'aprovado') {
            $afiliado->data_aprovacao = now();
            $afiliado->aprovado_por = auth()->id();
            $afiliado->link_afiliado = $afiliado->gerarLinkAfiliado();
        }
        
        $afiliado->save();

        return redirect()->route('admin.afiliados.index')
            ->with('success', 'Afiliado cadastrado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Afiliado $afiliado)
    {
        $afiliado->load(['aprovadoPor', 'vendas.cliente', 'vendas.assinatura']);

        // Estatísticas do afiliado
        $stats = [
            'total_vendas' => $afiliado->vendas()->confirmadas()->count(),
            'vendas_mes' => $afiliado->vendas()->confirmadas()->doMes()->count(),
            'total_comissoes' => $afiliado->vendas()->confirmadas()->sum('comissao'),
            'comissoes_mes' => $afiliado->vendas()->confirmadas()->doMes()->sum('comissao'),
        ];

        // Vendas recentes
        $vendasRecentes = $afiliado->vendas()
            ->with(['cliente', 'assinatura'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return Inertia::render('admin/afiliados/show', [
            'afiliado' => $afiliado,
            'stats' => $stats,
            'vendasRecentes' => $vendasRecentes,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Afiliado $afiliado)
    {
        return Inertia::render('admin/afiliados/edit', [
            'afiliado' => $afiliado,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Afiliado $afiliado)
    {
        $validated = $request->validate([
            'nome' => 'required|string|max:255',
            'email' => 'required|email|unique:afiliados,email,' . $afiliado->id,
            'telefone' => 'required|string|max:20',
            'cpf' => 'required|string|unique:afiliados,cpf,' . $afiliado->id,
            'endereco' => 'required|string|max:500',
            'cidade' => 'required|string|max:100',
            'estado' => 'required|string|size:2',
            'cep' => 'required|string|max:9',
            'experiencia' => 'nullable|string|in:nenhuma,iniciante,intermediario,avancado',
            'motivacao' => 'required|string|max:1000',
            'canais_divulgacao' => 'required|array|min:1',
            'observacoes_admin' => 'nullable|string',
            'ativo' => 'boolean',
        ]);

        $afiliado->update($validated);

        return redirect()->route('admin.afiliados.show', $afiliado)
            ->with('success', 'Afiliado atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Afiliado $afiliado)
    {
        $afiliado->delete();

        return redirect()->route('admin.afiliados.index')
            ->with('success', 'Afiliado removido com sucesso!');
    }

    /**
     * Approve an affiliate.
     */
    public function aprovar(Request $request, Afiliado $afiliado)
    {
        $request->validate([
            'observacoes' => 'nullable|string|max:1000',
        ]);

        $afiliado->aprovar(auth()->id(), $request->observacoes);

        // Se o afiliado estiver associado a um usuário, atualizar o role se necessário
        if ($afiliado->user_id) {
            $user = $afiliado->user;

            // Se o usuário não for admin ou fisioterapeuta, atualizar para afiliado
            if (!in_array($user->role, ['admin', 'fisioterapeuta'])) {
                $user->role = 'afiliado';
                $user->save();
            }
        }

        Log::info('Afiliado aprovado', [
            'afiliado_id' => $afiliado->id,
            'aprovado_por' => auth()->id(),
            'observacoes' => $request->observacoes,
            'user_id' => $afiliado->user_id,
        ]);

        return redirect()->back()
            ->with('success', 'Afiliado aprovado com sucesso! Link exclusivo gerado.');
    }

    /**
     * Reject an affiliate.
     */
    public function rejeitar(Request $request, Afiliado $afiliado)
    {
        $request->validate([
            'observacoes' => 'required|string|max:1000',
        ]);

        $afiliado->rejeitar(auth()->id(), $request->observacoes);

        Log::info('Afiliado rejeitado', [
            'afiliado_id' => $afiliado->id,
            'rejeitado_por' => auth()->id(),
            'observacoes' => $request->observacoes,
        ]);

        return redirect()->back()
            ->with('success', 'Afiliado rejeitado.');
    }

    /**
     * Suspend an affiliate.
     */
    public function suspender(Request $request, Afiliado $afiliado)
    {
        $request->validate([
            'observacoes' => 'required|string|max:1000',
        ]);

        $afiliado->suspender($request->observacoes);

        Log::info('Afiliado suspenso', [
            'afiliado_id' => $afiliado->id,
            'suspenso_por' => auth()->id(),
            'observacoes' => $request->observacoes,
        ]);

        return redirect()->back()
            ->with('success', 'Afiliado suspenso.');
    }

    /**
     * Reactivate an affiliate.
     */
    public function reativar(Afiliado $afiliado)
    {
        $afiliado->reativar();

        Log::info('Afiliado reativado', [
            'afiliado_id' => $afiliado->id,
            'reativado_por' => auth()->id(),
        ]);

        return redirect()->back()
            ->with('success', 'Afiliado reativado com sucesso!');
    }

    /**
     * Generate new affiliate link.
     */
    public function gerarNovoLink(Afiliado $afiliado)
    {
        if ($afiliado->status !== 'aprovado') {
            return redirect()->back()
                ->with('error', 'Apenas afiliados aprovados podem ter links gerados.');
        }

        $afiliado->update([
            'link_afiliado' => $afiliado->gerarLinkAfiliado(),
        ]);

        return redirect()->back()
            ->with('success', 'Novo link gerado com sucesso!');
    }
}
