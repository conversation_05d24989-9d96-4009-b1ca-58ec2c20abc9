<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Models\User;
use App\Models\Avaliacao;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PacienteController extends Controller
{
    public function index(Request $request)
    {
        $fisioterapeuta = auth()->user();
        
        // Buscar pacientes que já tiveram agendamentos com este fisioterapeuta
        $query = User::where('role', 'paciente')
            ->whereHas('agendamentosComoPaciente', function ($q) use ($fisioterapeuta) {
                $q->where('fisioterapeuta_id', $fisioterapeuta->id);
            })
            ->with(['agendamentosComoPaciente' => function ($q) use ($fisioterapeuta) {
                $q->where('fisioterapeuta_id', $fisioterapeuta->id)
                  ->orderBy('scheduled_at', 'desc');
            }]);

        // Filtros
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('status')) {
            $status = $request->status;
            if ($status === 'ativo') {
                // Pacientes com agendamentos nos últimos 30 dias
                $query->whereHas('agendamentosComoPaciente', function ($q) use ($fisioterapeuta) {
                    $q->where('fisioterapeuta_id', $fisioterapeuta->id)
                      ->where('scheduled_at', '>=', Carbon::now()->subDays(30));
                });
            } elseif ($status === 'inativo') {
                // Pacientes sem agendamentos nos últimos 30 dias
                $query->whereDoesntHave('agendamentosComoPaciente', function ($q) use ($fisioterapeuta) {
                    $q->where('fisioterapeuta_id', $fisioterapeuta->id)
                      ->where('scheduled_at', '>=', Carbon::now()->subDays(30));
                });
            }
        }

        $pacientes = $query->orderBy('name')
            ->paginate(15)
            ->withQueryString()
            ->through(function ($paciente) use ($fisioterapeuta) {
                $agendamentos = $paciente->agendamentosComoPaciente;
                $ultimoAgendamento = $agendamentos->first();
                
                return [
                    'id' => $paciente->id,
                    'name' => $paciente->name,
                    'email' => $paciente->email,
                    'phone' => $paciente->phone,
                    'created_at' => $paciente->created_at,
                    'stats' => [
                        'total_sessoes' => $agendamentos->where('status', 'concluido')->count(),
                        'sessoes_canceladas' => $agendamentos->where('status', 'cancelado')->count(),
                        'status_tratamento' => $this->determinarStatusTratamento($agendamentos),
                    ],
                    'ultima_sessao' => $ultimoAgendamento ? [
                        'data' => $ultimoAgendamento->scheduled_at,
                        'status' => $ultimoAgendamento->status,
                        'tipo' => $ultimoAgendamento->service_type,
                    ] : null,
                    'proximo_agendamento' => $agendamentos
                        ->where('scheduled_at', '>', Carbon::now())
                        ->whereIn('status', ['agendado', 'confirmado'])
                        ->first() ? [
                        'data' => $agendamentos
                            ->where('scheduled_at', '>', Carbon::now())
                            ->whereIn('status', ['agendado', 'confirmado'])
                            ->first()->scheduled_at,
                        'status' => $agendamentos
                            ->where('scheduled_at', '>', Carbon::now())
                            ->whereIn('status', ['agendado', 'confirmado'])
                            ->first()->status,
                    ] : null,
                    'status' => $ultimoAgendamento &&
                               $ultimoAgendamento->scheduled_at >= Carbon::now()->subDays(30)
                               ? 'ativo' : 'inativo',
                ];
            });

        // Estatísticas
        $stats = [
            'total_pacientes' => User::where('role', 'paciente')
                ->whereHas('agendamentosComoPaciente', function ($q) use ($fisioterapeuta) {
                    $q->where('fisioterapeuta_id', $fisioterapeuta->id);
                })->count(),
            
            'pacientes_ativos' => User::where('role', 'paciente')
                ->whereHas('agendamentosComoPaciente', function ($q) use ($fisioterapeuta) {
                    $q->where('fisioterapeuta_id', $fisioterapeuta->id)
                      ->where('scheduled_at', '>=', Carbon::now()->subDays(30));
                })->count(),
            
            'novos_mes' => User::where('role', 'paciente')
                ->whereHas('agendamentosComoPaciente', function ($q) use ($fisioterapeuta) {
                    $q->where('fisioterapeuta_id', $fisioterapeuta->id);
                })
                ->whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count(),
        ];

        return Inertia::render('fisioterapeuta/pacientes', [
            'pacientes' => $pacientes,
            'stats' => $stats,
            'filtros' => $request->only(['search', 'status']),
        ]);
    }

    public function show(User $paciente)
    {
        // Verificar se o paciente já teve agendamentos com este fisioterapeuta
        $fisioterapeuta = auth()->user();
        
        if (!$paciente->agendamentosComoPaciente()->where('fisioterapeuta_id', $fisioterapeuta->id)->exists()) {
            abort(404, 'Paciente não encontrado.');
        }

        // Carregar dados do paciente
        $paciente->load([
            'agendamentosComoPaciente' => function ($q) use ($fisioterapeuta) {
                $q->where('fisioterapeuta_id', $fisioterapeuta->id)
                  ->with(['assinatura.plano', 'relatorioSessao'])
                  ->orderBy('scheduled_at', 'desc');
            },
            'avaliacoesFeitas' => function ($q) use ($fisioterapeuta) {
                $q->where('fisioterapeuta_id', $fisioterapeuta->id)
                  ->orderBy('created_at', 'desc');
            }
        ]);

        $agendamentos = $paciente->agendamentosComoPaciente;

        // Estatísticas do paciente
        $stats = [
            'total_sessoes' => $agendamentos->where('status', 'concluido')->count(),
            'sessoes_mes' => $agendamentos->where('status', 'concluido')
                ->where('scheduled_at', '>=', Carbon::now()->startOfMonth())
                ->count(),
            'sessoes_canceladas' => $agendamentos->where('status', 'cancelado')->count(),
            'primeira_sessao' => $agendamentos->last()?->scheduled_at,
            'ultima_sessao' => $agendamentos->where('status', 'concluido')->first()?->finished_at,
            'proxima_sessao' => $agendamentos->where('scheduled_at', '>', Carbon::now())
                ->whereIn('status', ['agendado', 'confirmado'])
                ->first()?->scheduled_at,
            'avaliacao_media' => $paciente->avaliacoesFeitas->avg('rating') ?? 0,
            'total_avaliacoes' => $paciente->avaliacoesFeitas->count(),
            'status_tratamento' => $this->determinarStatusTratamento($agendamentos),
            'valor_total_pago' => (float) $agendamentos->where('status', 'concluido')->sum('valor'),
        ];

        // Histórico de agendamentos
        $historicoAgendamentos = $agendamentos->map(function ($agendamento) {
            return [
                'id' => $agendamento->id,
                'data_hora' => $agendamento->scheduled_at,
                'status' => $agendamento->status,
                'tipo' => $agendamento->service_type,
                'duracao' => $agendamento->duration,
                'preco' => $agendamento->price,
                'endereco' => $agendamento->address,
                'observacoes' => $agendamento->notes,
                'iniciado_em' => $agendamento->started_at,
                'finalizado_em' => $agendamento->finished_at,
                'motivo_cancelamento' => $agendamento->cancellation_reason,
                'plano' => $agendamento->assinatura->plano->name ?? null,
                'tem_relatorio' => $agendamento->relatorioSessao !== null,
                'relatorio' => $agendamento->relatorioSessao ? [
                    'id' => $agendamento->relatorioSessao->id,
                    'observacoes' => $agendamento->relatorioSessao->observations,
                    'exercicios' => $agendamento->relatorioSessao->exercises,
                    'evolucao' => $agendamento->relatorioSessao->progress_notes,
                ] : null,
            ];
        });

        // Avaliações do paciente
        $avaliacoes = $paciente->avaliacoesFeitas->map(function ($avaliacao) {
            return [
                'id' => $avaliacao->id,
                'rating' => $avaliacao->rating,
                'comentario' => $avaliacao->comment,
                'recomenda' => $avaliacao->recommend,
                'data' => $avaliacao->created_at,
                'agendamento_id' => $avaliacao->agendamento_id,
            ];
        });

        // Evolução mensal (últimos 6 meses)
        $evolucaoMensal = [];
        for ($i = 5; $i >= 0; $i--) {
            $mes = Carbon::now()->subMonths($i);
            $sessoesMes = $agendamentos
                ->where('status', 'concluido')
                ->filter(function ($agendamento) use ($mes) {
                    return $agendamento->finished_at &&
                           $agendamento->finished_at->format('Y-m') === $mes->format('Y-m');
                })
                ->count();
            
            $evolucaoMensal[] = [
                'mes' => $mes->format('M/Y'),
                'sessoes' => $sessoesMes,
            ];
        }

        return Inertia::render('fisioterapeuta/pacientes/show', [
            'paciente' => [
                'id' => $paciente->id,
                'name' => $paciente->name,
                'email' => $paciente->email,
                'phone' => $paciente->phone,
                'created_at' => $paciente->created_at,
                'stats' => $stats,
                'agendamentos_recentes' => $historicoAgendamentos,
                'relatorios_recentes' => [],
            ],
            'historicoAgendamentos' => $historicoAgendamentos,
            'avaliacoes' => $avaliacoes,
            'evolucaoMensal' => $evolucaoMensal,
        ]);
    }

    /**
     * Determina o status do tratamento baseado nos agendamentos
     */
    private function determinarStatusTratamento($agendamentos)
    {
        if ($agendamentos->isEmpty()) {
            return 'ativo';
        }

        $ultimoAgendamento = $agendamentos->first();
        $agendamentosRecentes = $agendamentos->where('scheduled_at', '>=', Carbon::now()->subDays(30));

        // Se tem agendamentos futuros ou recentes, está ativo
        if ($agendamentos->where('scheduled_at', '>', Carbon::now())->count() > 0 ||
            $agendamentosRecentes->count() > 0) {
            return 'ativo';
        }

        // Se o último agendamento foi há mais de 60 dias, está pausado
        if ($ultimoAgendamento && Carbon::parse($ultimoAgendamento->scheduled_at)->diffInDays(Carbon::now()) > 60) {
            return 'pausado';
        }

        // Se tem mais de 10 sessões concluídas e nenhuma recente, pode estar concluído
        if ($agendamentos->where('status', 'concluido')->count() >= 10 &&
            $agendamentosRecentes->isEmpty()) {
            return 'concluido';
        }

        return 'ativo';
    }

    public function notas(Request $request, User $paciente)
    {
        // Verificar se o paciente já teve agendamentos com este fisioterapeuta
        $fisioterapeuta = auth()->user();
        
        if (!$paciente->agendamentosComoPaciente()->where('fisioterapeuta_id', $fisioterapeuta->id)->exists()) {
            abort(404, 'Paciente não encontrado.');
        }

        $request->validate([
            'notas' => 'required|string|max:2000',
        ]);

        // Salvar notas em um campo personalizado ou criar um modelo específico
        // Por simplicidade, vamos usar um campo notes no último agendamento
        $ultimoAgendamento = $paciente->agendamentosComoPaciente()
            ->where('fisioterapeuta_id', $fisioterapeuta->id)
            ->latest('scheduled_at')
            ->first();

        if ($ultimoAgendamento) {
            $ultimoAgendamento->update([
                'notes' => $request->notas
            ]);
        }

        return back()->with('success', 'Notas salvas com sucesso!');
    }
}
