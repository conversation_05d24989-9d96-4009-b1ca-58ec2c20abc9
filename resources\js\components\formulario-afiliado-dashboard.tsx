import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useForm, usePage } from '@inertiajs/react';
import { CheckCircle, Loader2, Users } from 'lucide-react';
import { useState } from 'react';

interface FormularioAfiliadoDashboardProps {
    className?: string;
}

interface AfiliadoFormData {
    nome: string;
    email: string;
    telefone: string;
    cpf: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    experiencia: string;
    motivacao: string;
    canais_divulgacao: string[];
    aceita_termos: boolean;
    [key: string]: any;
}

export default function FormularioAfiliadoDashboard({ className = '' }: FormularioAfiliadoDashboardProps) {
    const [showSuccess, setShowSuccess] = useState(false);
    const { auth } = usePage().props as any;
    const user = auth?.user;

    const { data, setData, post, processing, errors, reset } = useForm<AfiliadoFormData>({
        nome: user?.name || '',
        email: user?.email || '',
        telefone: user?.phone || '',
        cpf: '',
        endereco: '',
        cidade: '',
        estado: '',
        cep: '',
        experiencia: '',
        motivacao: '',
        canais_divulgacao: [],
        aceita_termos: false,
    });

    const canaisDisponiveis = ['Instagram', 'Facebook', 'WhatsApp', 'LinkedIn', 'TikTok', 'YouTube', 'Blog/Site', 'Indicação Pessoal', 'Outros'];

    const estados = [
        'AC',
        'AL',
        'AP',
        'AM',
        'BA',
        'CE',
        'DF',
        'ES',
        'GO',
        'MA',
        'MT',
        'MS',
        'MG',
        'PA',
        'PB',
        'PR',
        'PE',
        'PI',
        'RJ',
        'RN',
        'RS',
        'RO',
        'RR',
        'SC',
        'SP',
        'SE',
        'TO',
    ];

    const handleCanalChange = (canal: string, checked: boolean) => {
        if (checked) {
            setData('canais_divulgacao', [...data.canais_divulgacao, canal]);
        } else {
            setData(
                'canais_divulgacao',
                data.canais_divulgacao.filter((c) => c !== canal),
            );
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        post(route('afiliados.store'), {
            onSuccess: () => {
                setShowSuccess(true);
                reset();
                setTimeout(() => setShowSuccess(false), 5000);
            },
        });
    };

    if (showSuccess) {
        return (
            <Card className="border-green-200 bg-green-50">
                <CardContent className="pt-6">
                    <div className="flex items-center gap-3 text-green-800">
                        <CheckCircle className="h-6 w-6" />
                        <div>
                            <h3 className="font-semibold">Cadastro realizado com sucesso!</h3>
                            <p className="text-sm">
                                Seu cadastro foi enviado e está sendo analisado pela nossa equipe. Você receberá um e-mail com o resultado em breve.
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Cadastro de Afiliado
                </CardTitle>
                <CardDescription>Preencha os dados abaixo para se tornar um afiliado F4 Fisio</CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Dados Pessoais */}
                    <div className="space-y-4">
                        <h4 className="text-lg font-medium">Dados Pessoais</h4>

                        <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-2">
                                <Label htmlFor="nome">Nome Completo *</Label>
                                <Input
                                    id="nome"
                                    value={data.nome}
                                    onChange={(e) => setData('nome', e.target.value)}
                                    placeholder="Seu nome completo"
                                    required
                                />
                                {errors.nome && <p className="text-sm text-red-600">{errors.nome}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="email">E-mail *</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    placeholder="<EMAIL>"
                                    required
                                />
                                {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="telefone">Telefone *</Label>
                                <Input
                                    id="telefone"
                                    value={data.telefone}
                                    onChange={(e) => setData('telefone', e.target.value)}
                                    placeholder="(11) 99999-9999"
                                    required
                                />
                                {errors.telefone && <p className="text-sm text-red-600">{errors.telefone}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="cpf">CPF *</Label>
                                <Input
                                    id="cpf"
                                    value={data.cpf}
                                    onChange={(e) => setData('cpf', e.target.value)}
                                    placeholder="000.000.000-00"
                                    required
                                />
                                {errors.cpf && <p className="text-sm text-red-600">{errors.cpf}</p>}
                            </div>
                        </div>
                    </div>

                    {/* Endereço */}
                    <div className="space-y-4">
                        <h4 className="text-lg font-medium">Endereço</h4>

                        <div className="space-y-2">
                            <Label htmlFor="endereco">Endereço Completo *</Label>
                            <Input
                                id="endereco"
                                value={data.endereco}
                                onChange={(e) => setData('endereco', e.target.value)}
                                placeholder="Rua, número, complemento"
                                required
                            />
                            {errors.endereco && <p className="text-sm text-red-600">{errors.endereco}</p>}
                        </div>

                        <div className="grid gap-4 md:grid-cols-3">
                            <div className="space-y-2">
                                <Label htmlFor="cidade">Cidade *</Label>
                                <Input
                                    id="cidade"
                                    value={data.cidade}
                                    onChange={(e) => setData('cidade', e.target.value)}
                                    placeholder="Sua cidade"
                                    required
                                />
                                {errors.cidade && <p className="text-sm text-red-600">{errors.cidade}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="estado">Estado *</Label>
                                <Select value={data.estado} onValueChange={(value) => setData('estado', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Selecione" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {estados.map((estado) => (
                                            <SelectItem key={estado} value={estado}>
                                                {estado}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.estado && <p className="text-sm text-red-600">{errors.estado}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="cep">CEP *</Label>
                                <Input id="cep" value={data.cep} onChange={(e) => setData('cep', e.target.value)} placeholder="00000-000" required />
                                {errors.cep && <p className="text-sm text-red-600">{errors.cep}</p>}
                            </div>
                        </div>
                    </div>

                    {/* Experiência e Motivação */}
                    <div className="space-y-4">
                        <h4 className="text-lg font-medium">Sobre Você</h4>

                        <div className="space-y-2">
                            <Label htmlFor="experiencia">Experiência com Vendas/Marketing</Label>
                            <Select value={data.experiencia} onValueChange={(value) => setData('experiencia', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Selecione seu nível de experiência" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="nenhuma">Nenhuma experiência</SelectItem>
                                    <SelectItem value="iniciante">Iniciante (até 1 ano)</SelectItem>
                                    <SelectItem value="intermediario">Intermediário (1-3 anos)</SelectItem>
                                    <SelectItem value="avancado">Avançado (mais de 3 anos)</SelectItem>
                                </SelectContent>
                            </Select>
                            {errors.experiencia && <p className="text-sm text-red-600">{errors.experiencia}</p>}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="motivacao">Por que quer ser um afiliado? *</Label>
                            <Textarea
                                id="motivacao"
                                value={data.motivacao}
                                onChange={(e) => setData('motivacao', e.target.value)}
                                placeholder="Conte sua motivação para se tornar um afiliado F4 Fisio..."
                                rows={3}
                                required
                            />
                            {errors.motivacao && <p className="text-sm text-red-600">{errors.motivacao}</p>}
                        </div>
                    </div>

                    {/* Canais de Divulgação */}
                    <div className="space-y-4">
                        <h4 className="text-lg font-medium">Canais de Divulgação *</h4>
                        <p className="text-sm text-muted-foreground">Selecione os canais que você pretende usar para divulgar nossos serviços:</p>

                        <div className="grid gap-3 md:grid-cols-3">
                            {canaisDisponiveis.map((canal) => (
                                <div key={canal} className="flex items-center space-x-2">
                                    <Checkbox
                                        id={canal}
                                        checked={data.canais_divulgacao.includes(canal)}
                                        onCheckedChange={(checked) => handleCanalChange(canal, checked as boolean)}
                                    />
                                    <Label htmlFor={canal} className="text-sm">
                                        {canal}
                                    </Label>
                                </div>
                            ))}
                        </div>
                        {errors.canais_divulgacao && <p className="text-sm text-red-600">{errors.canais_divulgacao}</p>}
                    </div>

                    {/* Termos */}
                    <div className="space-y-4">
                        <div className="flex items-start space-x-2">
                            <Checkbox
                                id="aceita_termos"
                                checked={data.aceita_termos}
                                onCheckedChange={(checked) => setData('aceita_termos', checked as boolean)}
                                required
                            />
                            <Label htmlFor="aceita_termos" className="text-sm leading-relaxed">
                                Aceito os{' '}
                                <a href="/termos-afiliados" target="_blank" className="text-primary underline">
                                    termos e condições do programa de afiliados
                                </a>{' '}
                                e autorizo o uso dos meus dados para análise do cadastro. *
                            </Label>
                        </div>
                        {errors.aceita_termos && <p className="text-sm text-red-600">{errors.aceita_termos}</p>}
                    </div>

                    {/* Submit Button */}
                    <Button type="submit" className="w-full" disabled={processing}>
                        {processing ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Enviando...
                            </>
                        ) : (
                            <>
                                <Users className="mr-2 h-4 w-4" />
                                Enviar Cadastro
                            </>
                        )}
                    </Button>
                </form>
            </CardContent>
        </Card>
    );
}
