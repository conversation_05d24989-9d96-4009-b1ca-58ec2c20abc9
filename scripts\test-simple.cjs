const { render } = require('@react-email/render');
const React = require('react');
const { Html, Head, Body, Container, Text } = require('@react-email/components');

async function testSimpleEmail() {
  try {
    const email = React.createElement(Html, {},
      React.createElement(Head),
      React.createElement(Body, { style: { fontFamily: 'Arial, sans-serif' } },
        React.createElement(Container, {},
          React.createElement(Text, {}, 'Ol<PERSON>, este é um teste do React Email!')
        )
      )
    );

    const html = await render(email);
    console.log(html);
  } catch (error) {
    console.error('Erro:', error);
  }
}

testSimpleEmail();
