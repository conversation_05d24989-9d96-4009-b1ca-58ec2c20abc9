<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;

class UpdateExistingUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Atualizar usuários existentes para definir has_subscription
        // Fisioterapeutas e admins têm acesso liberado
        User::whereIn('role', ['admin', 'fisioterapeuta'])
            ->update(['has_subscription' => true]);

        // Pacientes começam sem plano (precisam escolher)
        User::where('role', 'paciente')
            ->update(['has_subscription' => false]);

        $this->command->info('Usuários existentes atualizados com sucesso!');
    }
}
