import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { ArrowLeft, Send, User } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface Usuario {
    id: number;
    nome: string;
    avatar?: string;
    role: string;
}

interface Mensagem {
    id: number;
    conteudo: string;
    tipo: string;
    data: string;
    lida: boolean;
    lida_em?: string;
    eh_remetente: boolean;
    remetente?: {
        id: number;
        nome: string;
        avatar?: string;
    };
    agendamento?: {
        id: number;
        data: string;
    };
    anexos?: any[];
}

interface Props {
    usuario: Usuario;
    mensagens: {
        data: Mensagem[];
        links: any[];
        meta: any;
    };
}

export default function MensagensShow() {
    const { usuario, mensagens } = usePage().props as unknown as Props;
    const [novaMensagem, setNovaMensagem] = useState('');
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const { data, setData, post, processing, reset } = useForm({
        destinatario_id: usuario.id,
        conteudo: '',
        tipo: 'texto',
    });

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [mensagens.data]);

    const handleEnviarMensagem = (e: React.FormEvent) => {
        e.preventDefault();
        if (!novaMensagem.trim()) return;

        setData('conteudo', novaMensagem);

        setData('conteudo', novaMensagem);
        post(route('mensagens.store'), {
            onSuccess: () => {
                setNovaMensagem('');
                reset('conteudo');
                scrollToBottom();
            },
        });
    };

    const formatarData = (data: string) => {
        const dataMsg = new Date(data);
        const hoje = new Date();
        const ontem = new Date(hoje);
        ontem.setDate(hoje.getDate() - 1);

        if (dataMsg.toDateString() === hoje.toDateString()) {
            return dataMsg.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
        } else if (dataMsg.toDateString() === ontem.toDateString()) {
            return 'Ontem ' + dataMsg.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
        } else {
            return dataMsg.toLocaleDateString('pt-BR', {
                day: '2-digit',
                month: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
            });
        }
    };

    const getRoleBadge = (role: string) => {
        const roles = {
            paciente: { label: 'Paciente', variant: 'default' as const },
            fisioterapeuta: { label: 'Fisioterapeuta', variant: 'secondary' as const },
            admin: { label: 'Admin', variant: 'destructive' as const },
        };
        return roles[role as keyof typeof roles] || { label: role, variant: 'outline' as const };
    };

    const breadcrumbs = [
        { title: 'Início', href: '/dashboard' },
        { title: 'Mensagens', href: '/mensagens' },
        { title: usuario.nome, href: `/mensagens/${usuario.id}` },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Conversa com ${usuario.nome}`} />

            <div className="flex h-[calc(100vh-200px)] flex-col">
                {/* Header da Conversa */}
                <Card className="mb-4">
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                                <Button variant="ghost" size="sm" asChild>
                                    <Link href="/mensagens">
                                        <ArrowLeft className="h-4 w-4" />
                                    </Link>
                                </Button>

                                {usuario.avatar ? (
                                    <img src={usuario.avatar} alt={usuario.nome} className="h-10 w-10 rounded-full object-cover" />
                                ) : (
                                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                                        <User className="h-5 w-5 text-muted-foreground" />
                                    </div>
                                )}

                                <div>
                                    <h1 className="font-semibold">{usuario.nome}</h1>
                                    <Badge {...getRoleBadge(usuario.role)} className="text-xs">
                                        {getRoleBadge(usuario.role).label}
                                    </Badge>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Área de Mensagens */}
                <Card className="flex flex-1 flex-col">
                    <CardContent className="flex flex-1 flex-col p-0">
                        {/* Lista de Mensagens */}
                        <div className="flex-1 space-y-4 overflow-y-auto p-4">
                            {mensagens.data.length === 0 ? (
                                <div className="py-8 text-center">
                                    <p className="text-muted-foreground">Nenhuma mensagem ainda. Seja o primeiro a enviar uma mensagem!</p>
                                </div>
                            ) : (
                                mensagens.data.map((mensagem) => (
                                    <div key={mensagem.id} className={`flex ${mensagem.eh_remetente ? 'justify-end' : 'justify-start'}`}>
                                        <div
                                            className={`max-w-[70%] rounded-lg px-4 py-2 ${
                                                mensagem.eh_remetente ? 'bg-primary text-primary-foreground' : 'bg-muted'
                                            }`}
                                        >
                                            {mensagem.tipo === 'sistema' && (
                                                <div className="mb-1 text-xs text-muted-foreground">Mensagem do sistema</div>
                                            )}

                                            <p className="text-sm">{mensagem.conteudo}</p>

                                            <div
                                                className={`mt-1 flex items-center justify-between text-xs ${
                                                    mensagem.eh_remetente ? 'text-primary-foreground/70' : 'text-muted-foreground'
                                                }`}
                                            >
                                                <span>{formatarData(mensagem.data)}</span>
                                                {mensagem.eh_remetente && <span className="ml-2">{mensagem.lida ? '✓✓' : '✓'}</span>}
                                            </div>
                                        </div>
                                    </div>
                                ))
                            )}
                            <div ref={messagesEndRef} />
                        </div>

                        {/* Formulário de Nova Mensagem */}
                        <div className="border-t p-4">
                            <form onSubmit={handleEnviarMensagem} className="flex gap-2">
                                <Input
                                    value={novaMensagem}
                                    onChange={(e) => setNovaMensagem(e.target.value)}
                                    placeholder="Digite sua mensagem..."
                                    className="flex-1"
                                    disabled={processing}
                                />
                                <Button type="submit" disabled={processing || !novaMensagem.trim()} size="sm">
                                    <Send className="h-4 w-4" />
                                </Button>
                            </form>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
