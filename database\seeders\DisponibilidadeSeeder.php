<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Disponibilidade;
use App\Models\User;
use Carbon\Carbon;

class DisponibilidadeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fisioterapeutas = User::where('role', 'fisioterapeuta')->get();

        if ($fisioterapeutas->isEmpty()) {
            return;
        }

        $disponibilidades = [];

        foreach ($fisioterapeutas as $fisioterapeuta) {
            // Disponibilidade padrão de segunda a sexta (recorrente)
            $disponibilidades[] = [
                'fisioterapeuta_id' => $fisioterapeuta->id,
                'tipo' => 'disponivel',
                'data_inicio' => Carbon::now()->format('Y-m-d'),
                'data_fim' => Carbon::now()->addMonths(3)->format('Y-m-d'),
                'hora_inicio' => '08:00:00',
                'hora_fim' => '12:00:00',
                'dias_semana' => json_encode([1, 2, 3, 4, 5]), // Segunda a sexta
                'recorrente' => true,
                'motivo' => null,
                'ativo' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Disponibilidade tarde de segunda a sexta (recorrente)
            $disponibilidades[] = [
                'fisioterapeuta_id' => $fisioterapeuta->id,
                'tipo' => 'disponivel',
                'data_inicio' => Carbon::now()->format('Y-m-d'),
                'data_fim' => Carbon::now()->addMonths(3)->format('Y-m-d'),
                'hora_inicio' => '14:00:00',
                'hora_fim' => '18:00:00',
                'dias_semana' => json_encode([1, 2, 3, 4, 5]), // Segunda a sexta
                'recorrente' => true,
                'motivo' => null,
                'ativo' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Alguns fisioterapeutas trabalham sábado
            if (rand(0, 1) === 1) {
                $disponibilidades[] = [
                    'fisioterapeuta_id' => $fisioterapeuta->id,
                    'tipo' => 'disponivel',
                    'data_inicio' => Carbon::now()->format('Y-m-d'),
                    'data_fim' => Carbon::now()->addMonths(3)->format('Y-m-d'),
                    'hora_inicio' => '08:00:00',
                    'hora_fim' => '14:00:00',
                    'dias_semana' => json_encode([6]), // Sábado
                    'recorrente' => true,
                    'motivo' => null,
                    'ativo' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            // Criar alguns bloqueios específicos
            for ($i = 0; $i < rand(2, 5); $i++) {
                $dataInicio = Carbon::now()->addDays(rand(1, 30));
                $disponibilidades[] = [
                    'fisioterapeuta_id' => $fisioterapeuta->id,
                    'tipo' => 'indisponivel',
                    'data_inicio' => $dataInicio->format('Y-m-d'),
                    'data_fim' => $dataInicio->format('Y-m-d'),
                    'hora_inicio' => '09:00:00',
                    'hora_fim' => '11:00:00',
                    'dias_semana' => null,
                    'recorrente' => false,
                    'motivo' => ['Consulta médica', 'Compromisso pessoal', 'Curso de atualização'][rand(0, 2)],
                    'ativo' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        Disponibilidade::insert($disponibilidades);
    }
}
