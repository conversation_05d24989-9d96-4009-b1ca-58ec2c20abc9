<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Objetivos do tratamento
            $table->string('main_objective')->nullable()->comment('Objetivo principal do tratamento');
            $table->string('pain_level')->default('5')->comment('Nível de dor atual (0-10)');
            $table->json('specific_areas')->nullable()->comment('Áreas específicas de interesse');
            $table->text('treatment_goals')->nullable()->comment('Metas específicas do tratamento');

            // Preferências
            $table->string('preferred_time')->nullable()->comment('Horário preferido para sessões');
            $table->json('preferred_days')->nullable()->comment('Dias da semana preferidos');
            $table->string('communication_preference')->default('whatsapp')->comment('Preferência de comunicação');
            $table->string('reminder_frequency')->default('daily')->comment('Frequência de lembretes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'main_objective',
                'pain_level',
                'specific_areas',
                'treatment_goals',
                'preferred_time',
                'preferred_days',
                'communication_preference',
                'reminder_frequency'
            ]);
        });
    }
};
