import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { CheckCircle, Clock, Edit, Eye, Plus, Search, Trash2, Users } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Planos',
        href: '/admin/planos',
    },
];

interface Plano {
    id: number;
    name: string;
    description: string;
    price: number;
    sessions_per_month: number;
    session_duration: number;
    included_services: string[];
    benefits: string[];
    active: boolean;
    created_at: string;
    assinaturas_count: number;
}

interface Props {
    planos?: {
        data: Plano[];
        links: any[];
        meta: any;
    };
    filters?: {
        active?: boolean;
        search?: string;
    };
}

export default function AdminPlanos({ planos, filters }: Props) {
    const [search, setSearch] = useState(filters?.search || '');
    const { data, setData, get } = useForm({
        active: filters?.active?.toString() || undefined,
        search: filters?.search || '',
    });

    const handleFilter = () => {
        get(route('admin.planos.index'), {
            preserveState: true,
            replace: true,
        });
    };

    const handleDelete = (id: number) => {
        if (confirm('Tem certeza que deseja remover este plano?')) {
            router.delete(route('admin.planos.destroy', id));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Planos" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Planos</h1>
                        <p className="text-gray-600">Gerencie os planos de fisioterapia</p>
                    </div>
                    <Link href={route('admin.planos.create')}>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Novo Plano
                        </Button>
                    </Link>
                </div>

                {/* Filtros */}
                <div className="flex items-end gap-4">
                    <div className="flex-1">
                        <Input
                            placeholder="Buscar por nome do plano..."
                            value={search}
                            onChange={(e) => {
                                setSearch(e.target.value);
                                setData('search', e.target.value);
                            }}
                            className="max-w-sm"
                        />
                    </div>
                    <Select value={data.active || undefined} onValueChange={(value) => setData('active', value || undefined)}>
                        <SelectTrigger className="w-48">
                            <SelectValue placeholder="Filtrar por status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos os status</SelectItem>
                            <SelectItem value="true">Ativo</SelectItem>
                            <SelectItem value="false">Inativo</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button onClick={handleFilter}>
                        <Search className="mr-2 h-4 w-4" />
                        Filtrar
                    </Button>
                </div>

                {/* Cards dos Planos */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {planos?.data?.map((plano) => (
                        <div key={plano.id} className="relative rounded-lg border bg-card p-6">
                            <div className="mb-4 flex items-start justify-between">
                                <div className="flex-1">
                                    <h3 className="text-xl font-bold">{plano.name}</h3>
                                    <p className="mt-2 text-3xl font-bold text-primary">
                                        {new Intl.NumberFormat('pt-BR', {
                                            style: 'currency',
                                            currency: 'BRL',
                                        }).format(plano.price)}
                                        <span className="text-sm font-normal text-muted-foreground">/mês</span>
                                    </p>
                                </div>
                                <div className="flex flex-col items-end gap-2">
                                    <span
                                        className={`rounded-full px-2 py-1 text-xs ${
                                            plano.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                        }`}
                                    >
                                        {plano.active ? 'Ativo' : 'Inativo'}
                                    </span>
                                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                        <Users className="h-4 w-4" />
                                        <span>{plano.assinaturas_count} assinantes</span>
                                    </div>
                                </div>
                            </div>

                            <p className="mb-4 text-sm text-muted-foreground">{plano.description}</p>

                            <div className="mb-6 space-y-3">
                                <div className="flex items-center gap-2">
                                    <Clock className="h-4 w-4 text-blue-600" />
                                    <span className="text-sm">
                                        {plano.sessions_per_month} sessões/mês • {plano.session_duration}min cada
                                    </span>
                                </div>

                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-700">Serviços inclusos:</p>
                                    <div className="space-y-1">
                                        {plano.included_services.map((service, index) => (
                                            <div key={index} className="flex items-center gap-2">
                                                <CheckCircle className="h-3 w-3 text-green-600" />
                                                <span className="text-xs text-muted-foreground">{service}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-700">Benefícios:</p>
                                    <div className="space-y-1">
                                        {plano.benefits.map((benefit, index) => (
                                            <div key={index} className="flex items-center gap-2">
                                                <CheckCircle className="h-3 w-3 text-blue-600" />
                                                <span className="text-xs text-muted-foreground">{benefit}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center justify-between border-t pt-4">
                                <p className="text-xs text-muted-foreground">Criado em {new Date(plano.created_at).toLocaleDateString('pt-BR')}</p>
                                <div className="flex items-center gap-2">
                                    <Link href={route('admin.planos.show', plano.id)}>
                                        <Button variant="ghost" size="sm">
                                            <Eye className="h-4 w-4" />
                                        </Button>
                                    </Link>
                                    <Link href={route('admin.planos.edit', plano.id)}>
                                        <Button variant="ghost" size="sm">
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                    </Link>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleDelete(plano.id)}
                                        className="text-red-600 hover:text-red-700"
                                        disabled={plano.assinaturas_count > 0}
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {(planos?.data?.length ?? 0) === 0 && (
                    <div className="py-12 text-center">
                        <p className="text-muted-foreground">Nenhum plano encontrado</p>
                        <Link href={route('admin.planos.create')} className="mt-4 inline-block">
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Criar Primeiro Plano
                            </Button>
                        </Link>
                    </div>
                )}

                {/* Paginação */}
                {planos?.links && (planos?.data?.length ?? 0) > 0 && (
                    <div className="flex items-center justify-between">
                        <p className="text-sm text-muted-foreground">
                            Mostrando {planos?.meta?.from ?? 0} a {planos?.meta?.to ?? 0} de {planos?.meta?.total ?? 0} resultados
                        </p>
                        <div className="flex gap-2">
                            {planos?.links?.map((link: any, index: number) => (
                                <Button
                                    key={index}
                                    variant={link.active ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => link.url && router.get(link.url)}
                                    disabled={!link.url}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
