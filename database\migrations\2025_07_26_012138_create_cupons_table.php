<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cupons', function (Blueprint $table) {
            $table->id();
            $table->string('codigo')->unique(); // Código do cupom (ex: DESCONTO10)
            $table->enum('tipo', ['publico', 'afiliado_exclusivo']); // Tipo do cupom
            $table->foreignId('afiliado_id')->nullable()->constrained('afiliados')->onDelete('cascade'); // Só preenchido para cupons exclusivos
            $table->string('nome'); // Nome/descrição do cupom
            $table->text('descricao')->nullable(); // Descrição detalhada
            $table->enum('tipo_desconto', ['percentual', 'valor_fixo']); // Tipo do desconto
            $table->decimal('valor_desconto', 8, 2); // Valor do desconto (% ou R$)
            $table->decimal('valor_minimo_pedido', 8, 2)->nullable(); // Valor mínimo do pedido
            $table->date('data_inicio'); // Data de início da validade
            $table->date('data_fim')->nullable(); // Data de fim da validade (null = sem limite)
            $table->integer('limite_uso')->nullable(); // Limite de usos (null = ilimitado)
            $table->integer('usos_realizados')->default(0); // Contador de usos
            $table->boolean('ativo')->default(true); // Se o cupom está ativo
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade'); // Admin que criou
            $table->timestamps();

            // Índices para performance
            $table->index(['codigo', 'ativo']);
            $table->index(['tipo', 'ativo']);
            $table->index(['afiliado_id', 'ativo']);
            $table->index(['data_inicio', 'data_fim']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cupons');
    }
};
