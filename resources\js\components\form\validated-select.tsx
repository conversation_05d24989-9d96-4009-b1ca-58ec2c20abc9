import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { AlertCircle, CheckCircle, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SelectOption {
    value: string;
    label: string;
    disabled?: boolean;
}

export interface ValidatedSelectProps {
    id?: string;
    label?: string;
    placeholder?: string;
    value?: string;
    options: SelectOption[];
    error?: string;
    success?: boolean;
    tooltip?: string;
    required?: boolean;
    disabled?: boolean;
    onChange?: (value: string) => void;
    showValidationIcon?: boolean;
    className?: string;
}

export function ValidatedSelect({
    id,
    label,
    placeholder = "Selecione uma opção",
    value,
    options,
    error,
    success,
    tooltip,
    required,
    disabled,
    onChange,
    showValidationIcon = true,
    className
}: ValidatedSelectProps) {
    const hasError = Boolean(error);
    const hasSuccess = success && !hasError && value;

    return (
        <div className="space-y-2">
            {label && (
                <div className="flex items-center gap-2">
                    <Label htmlFor={id} className={cn(required && "after:content-['*'] after:ml-0.5 after:text-red-500")}>
                        {label}
                    </Label>
                    {tooltip && (
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p className="max-w-xs">{tooltip}</p>
                            </TooltipContent>
                        </Tooltip>
                    )}
                </div>
            )}
            
            <div className="relative">
                <Select
                    value={value}
                    onValueChange={onChange}
                    disabled={disabled}
                >
                    <SelectTrigger
                        id={id}
                        className={cn(
                            hasError && "border-red-500 focus:ring-red-500/20",
                            hasSuccess && "border-green-500 focus:ring-green-500/20",
                            showValidationIcon && (hasError || hasSuccess) && "pr-10",
                            className
                        )}
                        aria-invalid={hasError}
                        aria-describedby={error ? `${id}-error` : undefined}
                    >
                        <SelectValue placeholder={placeholder} />
                    </SelectTrigger>
                    <SelectContent>
                        {options.map((option) => (
                            <SelectItem
                                key={option.value}
                                value={option.value}
                                disabled={option.disabled}
                            >
                                {option.label}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                
                {showValidationIcon && (hasError || hasSuccess) && (
                    <div className="absolute right-8 top-1/2 -translate-y-1/2 pointer-events-none">
                        {hasError && <AlertCircle className="h-4 w-4 text-red-500" />}
                        {hasSuccess && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                )}
            </div>
            
            {error && (
                <p id={`${id}-error`} className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {error}
                </p>
            )}
        </div>
    );
}
