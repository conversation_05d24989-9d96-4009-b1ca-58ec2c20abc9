<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use App\Models\HorarioBase;
use App\Models\HorarioExcecao;
use App\Models\Feriado;
use App\Models\MedicoFeriadosConfig;
use App\Services\HorarioDisponibilidadeService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;

class HorarioConfigController extends Controller
{
    protected $horarioService;

    public function __construct(HorarioDisponibilidadeService $horarioService)
    {
        $this->horarioService = $horarioService;
    }

    /**
     * Página principal de configuração de horários
     */
    public function index()
    {
        $fisioterapeutaId = auth()->id();

        $horariosBase = HorarioBase::porFisioterapeuta($fisioterapeutaId)
            ->ativos()
            ->orderBy('dia_semana')
            ->orderBy('hora_inicio')
            ->get()
            ->groupBy('dia_semana');

        $excecoes = HorarioExcecao::porFisioterapeuta($fisioterapeutaId)
            ->ativos()
            ->orderBy('data_inicio', 'desc')
            ->paginate(10);

        $configFeriados = MedicoFeriadosConfig::where('fisioterapeuta_id', $fisioterapeutaId)->first();

        return Inertia::render('fisioterapeuta/horarios/index', [
            'horariosBase' => $horariosBase,
            'excecoes' => $excecoes,
            'configFeriados' => $configFeriados,
            'diasSemana' => $this->getDiasSemana(),
        ]);
    }

    /**
     * Configurar horários base (padrão semanal)
     */
    public function storeHorarioBase(Request $request)
    {
        $validated = $request->validate([
            'dia_semana' => 'required|integer|min:0|max:6',
            'hora_inicio' => 'required|date_format:H:i',
            'hora_fim' => 'required|date_format:H:i|after:hora_inicio',
            'periodo_nome' => 'nullable|string|max:50',
        ]);

        $fisioterapeutaId = auth()->id();

        // Verificar conflitos
        $conflito = HorarioBase::porFisioterapeuta($fisioterapeutaId)
            ->porDiaSemana($validated['dia_semana'])
            ->ativos()
            ->get()
            ->first(function ($horario) use ($validated) {
                return $horario->conflitaCom($validated['hora_inicio'], $validated['hora_fim']);
            });

        if ($conflito) {
            return back()->withErrors(['conflito' => 'Há conflito com outro horário já cadastrado para este dia.']);
        }

        HorarioBase::create([
            'fisioterapeuta_id' => $fisioterapeutaId,
            ...$validated
        ]);

        return back()->with('success', 'Horário base configurado com sucesso!');
    }

    /**
     * Atualizar horário base
     */
    public function updateHorarioBase(Request $request, HorarioBase $horarioBase)
    {
        if ($horarioBase->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'hora_inicio' => 'required|date_format:H:i',
            'hora_fim' => 'required|date_format:H:i|after:hora_inicio',
            'periodo_nome' => 'nullable|string|max:50',
            'ativo' => 'boolean',
        ]);

        // Verificar conflitos (excluindo o próprio registro)
        $conflito = HorarioBase::porFisioterapeuta($horarioBase->fisioterapeuta_id)
            ->porDiaSemana($horarioBase->dia_semana)
            ->ativos()
            ->where('id', '!=', $horarioBase->id)
            ->get()
            ->first(function ($horario) use ($validated) {
                return $horario->conflitaCom($validated['hora_inicio'], $validated['hora_fim']);
            });

        if ($conflito) {
            return back()->withErrors(['conflito' => 'Há conflito com outro horário já cadastrado para este dia.']);
        }

        $horarioBase->update($validated);

        return back()->with('success', 'Horário base atualizado com sucesso!');
    }

    /**
     * Remover horário base
     */
    public function destroyHorarioBase(HorarioBase $horarioBase)
    {
        if ($horarioBase->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $horarioBase->delete();

        return back()->with('success', 'Horário base removido com sucesso!');
    }

    /**
     * Criar exceção de horário
     */
    public function storeExcecao(Request $request)
    {
        $validated = $request->validate([
            'tipo' => 'required|in:data_especifica,semana,mes,periodo_personalizado',
            'data_inicio' => 'required|date|after_or_equal:today',
            'data_fim' => 'nullable|date|after_or_equal:data_inicio',
            'dia_semana' => 'nullable|integer|min:0|max:6',
            'hora_inicio' => 'nullable|date_format:H:i',
            'hora_fim' => 'nullable|date_format:H:i|after:hora_inicio',
            'periodo_nome' => 'nullable|string|max:50',
            'acao' => 'required|in:disponivel,indisponivel,horario_customizado',
            'motivo' => 'nullable|string|max:500',
        ]);

        // Validações específicas por tipo
        if ($validated['tipo'] === 'data_especifica') {
            $validated['data_fim'] = null;
        }

        if ($validated['acao'] === 'horario_customizado' && (!$validated['hora_inicio'] || !$validated['hora_fim'])) {
            return back()->withErrors(['horario' => 'Horário de início e fim são obrigatórios para horários customizados.']);
        }

        HorarioExcecao::create([
            'fisioterapeuta_id' => auth()->id(),
            ...$validated
        ]);

        return back()->with('success', 'Exceção de horário criada com sucesso!');
    }

    /**
     * Atualizar exceção de horário
     */
    public function updateExcecao(Request $request, HorarioExcecao $excecao)
    {
        if ($excecao->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'data_inicio' => 'required|date',
            'data_fim' => 'nullable|date|after_or_equal:data_inicio',
            'dia_semana' => 'nullable|integer|min:0|max:6',
            'hora_inicio' => 'nullable|date_format:H:i',
            'hora_fim' => 'nullable|date_format:H:i|after:hora_inicio',
            'periodo_nome' => 'nullable|string|max:50',
            'acao' => 'required|in:disponivel,indisponivel,horario_customizado',
            'motivo' => 'nullable|string|max:500',
            'ativo' => 'boolean',
        ]);

        $excecao->update($validated);

        return back()->with('success', 'Exceção de horário atualizada com sucesso!');
    }

    /**
     * Remover exceção de horário
     */
    public function destroyExcecao(HorarioExcecao $excecao)
    {
        if ($excecao->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $excecao->delete();

        return back()->with('success', 'Exceção de horário removida com sucesso!');
    }

    /**
     * Configurar preferências de feriados
     */
    public function updateConfigFeriados(Request $request)
    {
        $validated = $request->validate([
            'trabalha_feriados_nacionais' => 'boolean',
            'trabalha_feriados_estaduais' => 'boolean',
            'trabalha_feriados_municipais' => 'boolean',
            'feriados_excecoes' => 'nullable|array',
            'feriados_excecoes.*' => 'integer|exists:feriados,id',
        ]);

        $fisioterapeutaId = auth()->id();

        MedicoFeriadosConfig::updateOrCreate(
            ['fisioterapeuta_id' => $fisioterapeutaId],
            $validated
        );

        return back()->with('success', 'Configurações de feriados atualizadas com sucesso!');
    }

    /**
     * Preview de disponibilidade para um período
     */
    public function preview(Request $request)
    {
        $validated = $request->validate([
            'data_inicio' => 'required|date',
            'data_fim' => 'required|date|after_or_equal:data_inicio',
        ]);

        $fisioterapeutaId = auth()->id();
        $dataInicio = Carbon::parse($validated['data_inicio']);
        $dataFim = Carbon::parse($validated['data_fim']);

        $preview = [];
        $dataAtual = $dataInicio->copy();

        while ($dataAtual->lte($dataFim)) {
            $horarios = $this->horarioService->calcularHorariosDisponiveis($fisioterapeutaId, $dataAtual);
            
            $preview[] = [
                'data' => $dataAtual->format('Y-m-d'),
                'data_formatada' => $dataAtual->format('d/m/Y'),
                'dia_semana' => $dataAtual->locale('pt_BR')->dayName,
                'horarios' => $horarios,
                'total_horarios' => count($horarios),
            ];

            $dataAtual->addDay();
        }

        return response()->json([
            'success' => true,
            'preview' => $preview,
        ]);
    }

    /**
     * Obter lista de feriados
     */
    public function feriados(Request $request)
    {
        $query = Feriado::ativos()->orderBy('data');

        if ($request->filled('ano')) {
            $query->whereYear('data', $request->ano);
        }

        if ($request->filled('tipo')) {
            $query->porTipo($request->tipo);
        }

        $feriados = $query->get();

        return response()->json([
            'success' => true,
            'feriados' => $feriados,
        ]);
    }

    /**
     * Obter dias da semana
     */
    private function getDiasSemana()
    {
        return [
            0 => 'Domingo',
            1 => 'Segunda-feira',
            2 => 'Terça-feira',
            3 => 'Quarta-feira',
            4 => 'Quinta-feira',
            5 => 'Sexta-feira',
            6 => 'Sábado',
        ];
    }
}
