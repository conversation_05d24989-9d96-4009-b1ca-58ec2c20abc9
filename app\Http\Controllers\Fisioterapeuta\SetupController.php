<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use App\Models\Fisioterapeuta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class SetupController extends Controller
{
    /**
     * Display the fisioterapeuta setup page.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $fisioterapeuta = $user->fisioterapeuta;
        
        // Se não tem perfil de fisioterapeuta, criar um básico
        if (!$fisioterapeuta) {
            $fisioterapeuta = Fisioterapeuta::create([
                'user_id' => $user->id,
                'crefito' => '',
                'specializations' => [],
                'bio' => '',
                'hourly_rate' => 0,
                'available_areas' => [],
                'working_hours' => [],
                'available' => false,
                'rating' => 0,
                'total_reviews' => 0,
            ]);
        }
        
        // Listas para os selects
        $especialidades = [
            'Ortopédica',
            'Neurológica',
            'Respiratória',
            'Cardíaca',
            'Geriátrica',
            'Pediátrica',
            'Esportiva',
            'Dermatofuncional',
            'Uroginecológica',
            'Aquática',
            'Intensiva',
            'Oncológica',
            'Reumatológica',
            'Traumato-Ortopédica',
            'Outras'
        ];
        
        $areas = [
            'São Paulo - Centro',
            'São Paulo - Zona Norte',
            'São Paulo - Zona Sul',
            'São Paulo - Zona Leste',
            'São Paulo - Zona Oeste',
            'ABC Paulista',
            'Guarulhos',
            'Osasco',
            'Barueri',
            'Alphaville',
            'Carapicuíba',
            'Itapevi',
            'Jandira',
            'Cotia',
            'Taboão da Serra',
            'Embu das Artes',
            'São Bernardo do Campo',
            'Santo André',
            'São Caetano do Sul',
            'Diadema',
            'Mauá',
            'Ribeirão Pires',
            'Rio Grande da Serra'
        ];
        
        return Inertia::render('fisioterapeuta/setup', [
            'fisioterapeuta' => $fisioterapeuta,
            'user' => $user,
            'especialidades' => $especialidades,
            'areas' => $areas,
        ]);
    }
    
    /**
     * Store the fisioterapeuta setup.
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $fisioterapeuta = $user->fisioterapeuta;
        
        if (!$fisioterapeuta) {
            return redirect()->route('fisioterapeuta.setup')
                ->with('error', 'Perfil de fisioterapeuta não encontrado.');
        }
        
        $validated = $request->validate([
            'crefito' => 'required|string|max:50|unique:fisioterapeutas,crefito,' . $fisioterapeuta->id,
            'specializations' => 'required|array|min:1',
            'specializations.*' => 'string|max:100',
            'bio' => 'required|string|min:50|max:1000',
            'hourly_rate' => 'required|numeric|min:10|max:999.99',
            'available_areas' => 'required|array|min:1',
            'available_areas.*' => 'string|max:100',
        ], [
            'crefito.required' => 'O número do CREFITO é obrigatório.',
            'crefito.unique' => 'Este CREFITO já está cadastrado no sistema.',
            'specializations.required' => 'Selecione pelo menos uma especialização.',
            'specializations.min' => 'Selecione pelo menos uma especialização.',
            'bio.required' => 'A biografia profissional é obrigatória.',
            'bio.min' => 'A biografia deve ter pelo menos 50 caracteres.',
            'hourly_rate.required' => 'O valor da hora é obrigatório.',
            'hourly_rate.min' => 'O valor da hora deve ser pelo menos R$ 10,00.',
            'available_areas.required' => 'Selecione pelo menos uma área de atendimento.',
            'available_areas.min' => 'Selecione pelo menos uma área de atendimento.',
        ]);
        
        // Validar dados do usuário
        $userValidated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
        ], [
            'name.required' => 'O nome completo é obrigatório.',
            'phone.required' => 'O telefone é obrigatório.',
        ]);
        
        DB::transaction(function () use ($validated, $userValidated, $fisioterapeuta, $user) {
            // Atualizar dados do usuário
            $user->update($userValidated);
            
            // Atualizar dados do fisioterapeuta
            $validated['available'] = true; // Marcar como disponível após setup
            $fisioterapeuta->update($validated);
        });
        
        return redirect()->route('fisioterapeuta.dashboard')
            ->with('success', 'Perfil configurado com sucesso! Agora você pode começar a receber agendamentos.');
    }
}
