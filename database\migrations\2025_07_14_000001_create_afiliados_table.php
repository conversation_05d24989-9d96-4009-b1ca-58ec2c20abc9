<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('afiliados', function (Blueprint $table) {
            $table->id();
            $table->string('codigo_afiliado')->unique(); // AF12345678
            $table->string('nome');
            $table->string('email')->unique();
            $table->string('telefone');
            $table->string('cpf')->unique();
            $table->text('endereco');
            $table->string('cidade');
            $table->string('estado', 2);
            $table->string('cep');
            $table->enum('experiencia', ['nenhuma', 'iniciante', 'intermediario', 'avancado'])->nullable();
            $table->text('motivacao');
            $table->json('canais_divulgacao');
            $table->enum('status', ['pendente', 'aprovado', 'rejeitado', 'suspenso'])->default('pendente');
            $table->string('link_afiliado')->nullable(); // URL exclusiva do afiliado
            $table->text('observacoes_admin')->nullable(); // Observações do admin
            $table->timestamp('data_aprovacao')->nullable();
            $table->timestamp('data_rejeicao')->nullable();
            $table->foreignId('aprovado_por')->nullable()->constrained('users')->onDelete('set null');
            $table->decimal('total_vendas', 10, 2)->default(0);
            $table->decimal('total_comissoes', 10, 2)->default(0);
            $table->integer('vendas_mes_atual')->default(0);
            $table->decimal('comissoes_mes_atual', 8, 2)->default(0);
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Índices para performance
            $table->index(['status', 'ativo']);
            $table->index('codigo_afiliado');
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('afiliados');
    }
};
