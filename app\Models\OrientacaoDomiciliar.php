<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrientacaoDomiciliar extends Model
{
    use HasFactory;

    protected $fillable = [
        'paciente_id',
        'fisioterapeuta_id',
        'agendamento_id',
        'titulo',
        'descricao',
        'exercicios_recomendados',
        'cuidados_posturais',
        'atividades_evitar',
        'dicas_gerais',
        'frequencia_dias',
        'horario_recomendado',
        'prioridade',
        'ativa',
        'observacoes',
    ];

    protected $casts = [
        'horario_recomendado' => 'datetime:H:i',
        'ativa' => 'boolean',
    ];

    // Relacionamentos
    public function paciente()
    {
        return $this->belongsTo(User::class, 'paciente_id');
    }

    public function fisioterapeuta()
    {
        return $this->belongsTo(User::class, 'fisioterapeuta_id');
    }

    public function agendamento()
    {
        return $this->belongsTo(Agendamento::class);
    }
}
