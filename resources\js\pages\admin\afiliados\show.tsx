import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { 
    CheckCircle, 
    XCircle, 
    Clock, 
    Edit, 
    Link as LinkIcon, 
    Mail, 
    Phone, 
    MapPin,
    TrendingUp,
    DollarSign,
    Users,
    Calendar,
    AlertTriangle,
    RefreshCw
} from 'lucide-react';
import { useState } from 'react';

interface Afiliado {
    id: number;
    codigo_afiliado: string;
    nome: string;
    email: string;
    telefone: string;
    cpf: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    experiencia: string;
    motivacao: string;
    canais_divulgacao: string[];
    status: 'pendente' | 'aprovado' | 'rejeitado' | 'suspenso';
    ativo: boolean;
    link_afiliado?: string;
    observacoes_admin?: string;
    total_vendas: number;
    total_comissoes: number;
    vendas_mes_atual: number;
    comissoes_mes_atual: number;
    created_at: string;
    data_aprovacao?: string;
    data_rejeicao?: string;
    aprovado_por?: {
        id: number;
        name: string;
    };
}

interface VendaAfiliado {
    id: number;
    plano_tipo: string;
    valor_venda: number;
    comissao: number;
    status: string;
    created_at: string;
    cliente: {
        id: number;
        name: string;
        email: string;
    };
}

interface Props {
    afiliado: Afiliado;
    stats: {
        total_vendas: number;
        vendas_mes: number;
        total_comissoes: number;
        comissoes_mes: number;
    };
    vendasRecentes: VendaAfiliado[];
}

export default function AfiliadoShow({ afiliado, stats, vendasRecentes }: Props) {
    const [showAprovarForm, setShowAprovarForm] = useState(false);
    const [showRejeitarForm, setShowRejeitarForm] = useState(false);
    const [showSuspenderForm, setShowSuspenderForm] = useState(false);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/admin/dashboard' },
        { title: 'Afiliados', href: '/admin/afiliados' },
        { title: afiliado.nome, href: `/admin/afiliados/${afiliado.id}` },
    ];

    const { data: aprovarData, setData: setAprovarData, post: postAprovar, processing: processingAprovar } = useForm({
        observacoes: '',
    });

    const { data: rejeitarData, setData: setRejeitarData, post: postRejeitar, processing: processingRejeitar } = useForm({
        observacoes: '',
    });

    const { data: suspenderData, setData: setSuspenderData, post: postSuspender, processing: processingSuspender } = useForm({
        observacoes: '',
    });

    const handleAprovar = (e: React.FormEvent) => {
        e.preventDefault();
        postAprovar(route('admin.afiliados.aprovar', afiliado.id), {
            onSuccess: () => setShowAprovarForm(false),
        });
    };

    const handleRejeitar = (e: React.FormEvent) => {
        e.preventDefault();
        postRejeitar(route('admin.afiliados.rejeitar', afiliado.id), {
            onSuccess: () => setShowRejeitarForm(false),
        });
    };

    const handleSuspender = (e: React.FormEvent) => {
        e.preventDefault();
        postSuspender(route('admin.afiliados.suspender', afiliado.id), {
            onSuccess: () => setShowSuspenderForm(false),
        });
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: { variant: 'secondary' as const, icon: Clock, text: 'Pendente' },
            aprovado: { variant: 'default' as const, icon: CheckCircle, text: 'Aprovado' },
            rejeitado: { variant: 'destructive' as const, icon: XCircle, text: 'Rejeitado' },
            suspenso: { variant: 'outline' as const, icon: AlertTriangle, text: 'Suspenso' },
        };

        const config = variants[status as keyof typeof variants] || variants.pendente;
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-3 w-3" />
                {config.text}
            </Badge>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Afiliado: ${afiliado.nome}`} />
            
            <div className="flex h-full flex-1 flex-col gap-6 overflow-x-auto p-4 md:p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">{afiliado.nome}</h1>
                        <p className="text-muted-foreground">
                            Código: {afiliado.codigo_afiliado} • Cadastrado em {new Date(afiliado.created_at).toLocaleDateString('pt-BR')}
                        </p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" asChild>
                            <Link href={route('admin.afiliados.edit', afiliado.id)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Editar
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Status e Ações */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                                <CardTitle>Status do Afiliado</CardTitle>
                                {getStatusBadge(afiliado.status)}
                                {!afiliado.ativo && (
                                    <Badge variant="outline">Inativo</Badge>
                                )}
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-2">
                            {afiliado.status === 'pendente' && (
                                <>
                                    <Button 
                                        onClick={() => setShowAprovarForm(true)}
                                        className="bg-green-600 hover:bg-green-700"
                                    >
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Aprovar
                                    </Button>
                                    <Button 
                                        variant="destructive"
                                        onClick={() => setShowRejeitarForm(true)}
                                    >
                                        <XCircle className="mr-2 h-4 w-4" />
                                        Rejeitar
                                    </Button>
                                </>
                            )}
                            
                            {afiliado.status === 'aprovado' && (
                                <>
                                    <Button 
                                        variant="outline"
                                        onClick={() => setShowSuspenderForm(true)}
                                    >
                                        <AlertTriangle className="mr-2 h-4 w-4" />
                                        Suspender
                                    </Button>
                                    <Button 
                                        variant="outline"
                                        onClick={() => window.location.href = route('admin.afiliados.gerar-novo-link', afiliado.id)}
                                    >
                                        <RefreshCw className="mr-2 h-4 w-4" />
                                        Gerar Novo Link
                                    </Button>
                                </>
                            )}
                            
                            {afiliado.status === 'suspenso' && (
                                <Button 
                                    onClick={() => window.location.href = route('admin.afiliados.reativar', afiliado.id)}
                                    className="bg-blue-600 hover:bg-blue-700"
                                >
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Reativar
                                </Button>
                            )}
                        </div>

                        {/* Formulários de Ação */}
                        {showAprovarForm && (
                            <form onSubmit={handleAprovar} className="mt-4 space-y-4 border-t pt-4">
                                <div>
                                    <Label htmlFor="observacoes-aprovar">Observações (opcional)</Label>
                                    <Textarea
                                        id="observacoes-aprovar"
                                        value={aprovarData.observacoes}
                                        onChange={(e) => setAprovarData('observacoes', e.target.value)}
                                        placeholder="Observações sobre a aprovação..."
                                    />
                                </div>
                                <div className="flex gap-2">
                                    <Button type="submit" disabled={processingAprovar}>
                                        {processingAprovar ? 'Aprovando...' : 'Confirmar Aprovação'}
                                    </Button>
                                    <Button type="button" variant="outline" onClick={() => setShowAprovarForm(false)}>
                                        Cancelar
                                    </Button>
                                </div>
                            </form>
                        )}

                        {showRejeitarForm && (
                            <form onSubmit={handleRejeitar} className="mt-4 space-y-4 border-t pt-4">
                                <div>
                                    <Label htmlFor="observacoes-rejeitar">Motivo da rejeição *</Label>
                                    <Textarea
                                        id="observacoes-rejeitar"
                                        value={rejeitarData.observacoes}
                                        onChange={(e) => setRejeitarData('observacoes', e.target.value)}
                                        placeholder="Explique o motivo da rejeição..."
                                        required
                                    />
                                </div>
                                <div className="flex gap-2">
                                    <Button type="submit" variant="destructive" disabled={processingRejeitar}>
                                        {processingRejeitar ? 'Rejeitando...' : 'Confirmar Rejeição'}
                                    </Button>
                                    <Button type="button" variant="outline" onClick={() => setShowRejeitarForm(false)}>
                                        Cancelar
                                    </Button>
                                </div>
                            </form>
                        )}

                        {showSuspenderForm && (
                            <form onSubmit={handleSuspender} className="mt-4 space-y-4 border-t pt-4">
                                <div>
                                    <Label htmlFor="observacoes-suspender">Motivo da suspensão *</Label>
                                    <Textarea
                                        id="observacoes-suspender"
                                        value={suspenderData.observacoes}
                                        onChange={(e) => setSuspenderData('observacoes', e.target.value)}
                                        placeholder="Explique o motivo da suspensão..."
                                        required
                                    />
                                </div>
                                <div className="flex gap-2">
                                    <Button type="submit" variant="destructive" disabled={processingSuspender}>
                                        {processingSuspender ? 'Suspendendo...' : 'Confirmar Suspensão'}
                                    </Button>
                                    <Button type="button" variant="outline" onClick={() => setShowSuspenderForm(false)}>
                                        Cancelar
                                    </Button>
                                </div>
                            </form>
                        )}
                    </CardContent>
                </Card>

                {/* Stats */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Vendas Total</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_vendas}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Vendas/Mês</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.vendas_mes}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Comissões Total</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                R$ {stats.total_comissoes.toFixed(2).replace('.', ',')}
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Comissões/Mês</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                R$ {stats.comissoes_mes.toFixed(2).replace('.', ',')}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Dados Pessoais */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Dados Pessoais</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-2">
                                <Mail className="h-4 w-4 text-muted-foreground" />
                                <span>{afiliado.email}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4 text-muted-foreground" />
                                <span>{afiliado.telefone}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4 text-muted-foreground" />
                                <span>{afiliado.endereco}, {afiliado.cidade}/{afiliado.estado} - {afiliado.cep}</span>
                            </div>
                            <div>
                                <strong>CPF:</strong> {afiliado.cpf}
                            </div>
                            <div>
                                <strong>Experiência:</strong> {afiliado.experiencia || 'Não informado'}
                            </div>
                            <div>
                                <strong>Canais de Divulgação:</strong>
                                <div className="flex flex-wrap gap-1 mt-1">
                                    {afiliado.canais_divulgacao.map((canal, index) => (
                                        <Badge key={index} variant="outline">{canal}</Badge>
                                    ))}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Link de Afiliado */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Link de Afiliado</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {afiliado.link_afiliado ? (
                                <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <LinkIcon className="h-4 w-4 text-muted-foreground" />
                                        <code className="text-sm bg-muted px-2 py-1 rounded">
                                            {afiliado.link_afiliado}
                                        </code>
                                    </div>
                                    <Button 
                                        variant="outline" 
                                        size="sm"
                                        onClick={() => navigator.clipboard.writeText(afiliado.link_afiliado!)}
                                    >
                                        Copiar Link
                                    </Button>
                                </div>
                            ) : (
                                <p className="text-muted-foreground">
                                    Link será gerado após aprovação do cadastro
                                </p>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Motivação e Observações */}
                <div className="grid gap-6 lg:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Motivação</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm">{afiliado.motivacao}</p>
                        </CardContent>
                    </Card>

                    {afiliado.observacoes_admin && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Observações do Admin</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm">{afiliado.observacoes_admin}</p>
                            </CardContent>
                        </Card>
                    )}
                </div>

                {/* Vendas Recentes */}
                {vendasRecentes.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Vendas Recentes</CardTitle>
                            <CardDescription>Últimas 10 vendas realizadas</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b">
                                            <th className="text-left p-2">Data</th>
                                            <th className="text-left p-2">Cliente</th>
                                            <th className="text-left p-2">Plano</th>
                                            <th className="text-left p-2">Valor</th>
                                            <th className="text-left p-2">Comissão</th>
                                            <th className="text-left p-2">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {vendasRecentes.map((venda) => (
                                            <tr key={venda.id} className="border-b">
                                                <td className="p-2 text-sm">
                                                    {new Date(venda.created_at).toLocaleDateString('pt-BR')}
                                                </td>
                                                <td className="p-2">
                                                    <div>
                                                        <div className="font-medium">{venda.cliente.name}</div>
                                                        <div className="text-sm text-muted-foreground">{venda.cliente.email}</div>
                                                    </div>
                                                </td>
                                                <td className="p-2 text-sm capitalize">{venda.plano_tipo}</td>
                                                <td className="p-2 text-sm">
                                                    R$ {venda.valor_venda.toFixed(2).replace('.', ',')}
                                                </td>
                                                <td className="p-2 text-sm font-medium">
                                                    R$ {venda.comissao.toFixed(2).replace('.', ',')}
                                                </td>
                                                <td className="p-2">
                                                    <Badge variant={venda.status === 'confirmada' ? 'default' : 'secondary'}>
                                                        {venda.status}
                                                    </Badge>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
