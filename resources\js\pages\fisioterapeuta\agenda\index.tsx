import React, { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    Calendar, 
    Clock, 
    MapPin, 
    Phone, 
    User, 
    Filter,
    Search,
    Eye,
    CheckCircle,
    Play,
    Square,
    XCircle,
    AlertTriangle
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface Agendamento {
    id: number;
    data_hora: string;
    status: 'agendado' | 'confirmado' | 'em_andamento' | 'concluido' | 'cancelado';
    endereco: string;
    observacoes?: string;
    valor: number;
    paciente: {
        id: number;
        name: string;
        email: string;
        phone?: string;
    };
    formatted_data_hora: string;
    formatted_valor: string;
    tempo_restante?: string;
    pode_iniciar: boolean;
}

interface Stats {
    total: number;
    hoje: number;
    confirmados: number;
    pendentes: number;
}

interface Props {
    agendamentos: {
        data: Agendamento[];
        links: any[];
        meta: any;
    };
    stats: Stats;
    filtros: {
        search?: string;
        status?: string;
        data?: string;
    };
}

export default function AgendaIndex({ agendamentos, stats, filtros }: Props) {
    const [search, setSearch] = useState(filtros.search || '');
    const [status, setStatus] = useState(filtros.status || '');
    const [data, setData] = useState(filtros.data || '');

    const handleFilter = () => {
        router.get(route('fisioterapeuta.agenda.index'), {
            search: search || undefined,
            status: status || undefined,
            data: data || undefined,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleConfirmar = (agendamento: Agendamento) => {
        router.post(route('fisioterapeuta.agenda.confirmar', agendamento.id), {}, {
            preserveScroll: true,
        });
    };

    const handleIniciar = (agendamento: Agendamento) => {
        router.post(route('fisioterapeuta.agenda.iniciar', agendamento.id), {}, {
            preserveScroll: true,
        });
    };

    const handleFinalizar = (agendamento: Agendamento) => {
        router.post(route('fisioterapeuta.agenda.finalizar', agendamento.id), {}, {
            preserveScroll: true,
        });
    };

    const handleCancelar = (agendamento: Agendamento) => {
        if (confirm('Tem certeza que deseja cancelar este agendamento?')) {
            router.post(route('fisioterapeuta.agenda.cancelar', agendamento.id), {}, {
                preserveScroll: true,
            });
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'agendado':
                return 'bg-blue-100 text-blue-800';
            case 'confirmado':
                return 'bg-green-100 text-green-800';
            case 'em_andamento':
                return 'bg-yellow-100 text-yellow-800';
            case 'concluido':
                return 'bg-gray-100 text-gray-800';
            case 'cancelado':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'agendado':
                return 'Agendado';
            case 'confirmado':
                return 'Confirmado';
            case 'em_andamento':
                return 'Em Andamento';
            case 'concluido':
                return 'Concluído';
            case 'cancelado':
                return 'Cancelado';
            default:
                return status;
        }
    };

    return (
        <AppLayout>
            <Head title="Agenda" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <h2 className="text-3xl font-bold tracking-tight">Agenda</h2>
                        <p className="text-muted-foreground">
                            Gerencie seus agendamentos e sessões
                        </p>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <Calendar className="h-8 w-8 text-blue-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Total</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <Clock className="h-8 w-8 text-green-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Hoje</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.hoje}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <CheckCircle className="h-8 w-8 text-blue-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Confirmados</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.confirmados}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <AlertTriangle className="h-8 w-8 text-yellow-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Pendentes</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.pendentes}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filtros */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Filter className="mr-2 h-5 w-5" />
                                Filtros
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <Input
                                        placeholder="Buscar por paciente..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="w-full"
                                    />
                                </div>

                                <div>
                                    <Select value={status} onValueChange={setStatus}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="">Todos</SelectItem>
                                            <SelectItem value="agendado">Agendado</SelectItem>
                                            <SelectItem value="confirmado">Confirmado</SelectItem>
                                            <SelectItem value="em_andamento">Em Andamento</SelectItem>
                                            <SelectItem value="concluido">Concluído</SelectItem>
                                            <SelectItem value="cancelado">Cancelado</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <Input
                                        type="date"
                                        value={data}
                                        onChange={(e) => setData(e.target.value)}
                                        className="w-full"
                                    />
                                </div>

                                <div>
                                    <Button onClick={handleFilter} className="w-full">
                                        <Search className="mr-2 h-4 w-4" />
                                        Filtrar
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Lista de Agendamentos */}
                    <div className="space-y-4">
                        {agendamentos.data.length === 0 ? (
                            <Card>
                                <CardContent className="p-8 text-center">
                                    <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-4 text-lg font-medium text-gray-900">
                                        Nenhum agendamento encontrado
                                    </h3>
                                    <p className="mt-2 text-gray-500">
                                        Não há agendamentos que correspondam aos filtros selecionados.
                                    </p>
                                </CardContent>
                            </Card>
                        ) : (
                            agendamentos.data.map((agendamento) => (
                                <Card key={agendamento.id} className="hover:shadow-md transition-shadow">
                                    <CardContent className="p-6">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center space-x-4 mb-4">
                                                    <Badge className={getStatusColor(agendamento.status)}>
                                                        {getStatusLabel(agendamento.status)}
                                                    </Badge>
                                                    {agendamento.tempo_restante && (
                                                        <span className="text-sm text-gray-500">
                                                            {agendamento.tempo_restante}
                                                        </span>
                                                    )}
                                                </div>

                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div>
                                                        <div className="flex items-center text-sm text-gray-600 mb-2">
                                                            <User className="mr-2 h-4 w-4" />
                                                            <span className="font-medium">{agendamento.paciente.name}</span>
                                                        </div>
                                                        <div className="flex items-center text-sm text-gray-600 mb-2">
                                                            <Clock className="mr-2 h-4 w-4" />
                                                            <span>{agendamento.formatted_data_hora}</span>
                                                        </div>
                                                        <div className="flex items-center text-sm text-gray-600 mb-2">
                                                            <MapPin className="mr-2 h-4 w-4" />
                                                            <span>{agendamento.endereco}</span>
                                                        </div>
                                                    </div>

                                                    <div>
                                                        {agendamento.paciente.phone && (
                                                            <div className="flex items-center text-sm text-gray-600 mb-2">
                                                                <Phone className="mr-2 h-4 w-4" />
                                                                <span>{agendamento.paciente.phone}</span>
                                                            </div>
                                                        )}
                                                        <div className="text-sm text-gray-600 mb-2">
                                                            <span className="font-medium">Valor: </span>
                                                            <span className="text-green-600 font-bold">
                                                                {agendamento.formatted_valor}
                                                            </span>
                                                        </div>
                                                        {agendamento.observacoes && (
                                                            <div className="text-sm text-gray-600">
                                                                <span className="font-medium">Obs: </span>
                                                                <span>{agendamento.observacoes}</span>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex flex-col space-y-2 ml-6">
                                                <Link href={route('fisioterapeuta.agenda.show', agendamento.id)}>
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="mr-2 h-4 w-4" />
                                                        Ver
                                                    </Button>
                                                </Link>

                                                {agendamento.status === 'agendado' && (
                                                    <Button
                                                        size="sm"
                                                        onClick={() => handleConfirmar(agendamento)}
                                                        className="bg-green-600 hover:bg-green-700"
                                                    >
                                                        <CheckCircle className="mr-2 h-4 w-4" />
                                                        Confirmar
                                                    </Button>
                                                )}

                                                {agendamento.status === 'confirmado' && agendamento.pode_iniciar && (
                                                    <Button
                                                        size="sm"
                                                        onClick={() => handleIniciar(agendamento)}
                                                        className="bg-blue-600 hover:bg-blue-700"
                                                    >
                                                        <Play className="mr-2 h-4 w-4" />
                                                        Iniciar
                                                    </Button>
                                                )}

                                                {agendamento.status === 'em_andamento' && (
                                                    <Button
                                                        size="sm"
                                                        onClick={() => handleFinalizar(agendamento)}
                                                        className="bg-gray-600 hover:bg-gray-700"
                                                    >
                                                        <Square className="mr-2 h-4 w-4" />
                                                        Finalizar
                                                    </Button>
                                                )}

                                                {['agendado', 'confirmado'].includes(agendamento.status) && (
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => handleCancelar(agendamento)}
                                                        className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                                                    >
                                                        <XCircle className="mr-2 h-4 w-4" />
                                                        Cancelar
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))
                        )}
                    </div>

                    {/* Paginação */}
                    {agendamentos.links && agendamentos.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <div className="flex space-x-1">
                                {agendamentos.links.map((link: any, index: number) => (
                                    <Link
                                        key={index}
                                        href={link.url || '#'}
                                        className={`px-3 py-2 text-sm rounded-md ${
                                            link.active
                                                ? 'bg-blue-600 text-white'
                                                : link.url
                                                ? 'bg-white text-gray-700 hover:bg-gray-50 border'
                                                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                        }`}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
