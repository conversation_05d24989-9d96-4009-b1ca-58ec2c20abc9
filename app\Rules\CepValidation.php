<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CepValidation implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->isValidCep($value)) {
            $fail('O campo :attribute deve ser um CEP válido.');
        }
    }

    /**
     * Valida se o CEP é válido
     */
    private function isValidCep(string $cep): bool
    {
        // Remove caracteres não numéricos
        $cep = preg_replace('/\D/', '', $cep);

        // Verifica se tem 8 dígitos
        return strlen($cep) === 8;
    }
}
