import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Head, useForm } from '@inertiajs/react';
import { CheckCircle, Clock, CreditCard, XCircle } from 'lucide-react';
import { useState } from 'react';

interface Pagamento {
    id: number;
    transaction_id: string;
    amount: number;
    formatted_amount: string;
    status: string;
    assinatura: {
        id: number;
        user: {
            name: string;
            email: string;
        };
        plano: {
            name: string;
            price: number;
        };
    };
}

interface Props {
    pagamento: Pagamento;
}

export default function MercadoPagoPayment({ pagamento }: Props) {
    const [isProcessing, setIsProcessing] = useState(false);

    const { post } = useForm();

    const handlePaymentAction = (action: 'approve' | 'reject' | 'cancel') => {
        setIsProcessing(true);

        post(route('mercadopago.simulator.process', pagamento.transaction_id), {
            action,
            onFinish: () => setIsProcessing(false),
        });
    };

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            pendente: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pendente' },
            pago: { color: 'bg-green-100 text-green-800', icon: CheckCircle, text: 'Pago' },
            falhou: { color: 'bg-red-100 text-red-800', icon: XCircle, text: 'Falhou' },
            cancelado: { color: 'bg-gray-100 text-gray-800', icon: XCircle, text: 'Cancelado' },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pendente;
        const Icon = config.icon;

        return (
            <Badge className={config.color}>
                <Icon className="mr-1 h-3 w-3" />
                {config.text}
            </Badge>
        );
    };

    return (
        <>
            <Head title="Simulador Mercado Pago - Pagamento" />

            <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
                <div className="w-full max-w-md">
                    <div className="mb-6 text-center">
                        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-600">
                            <CreditCard className="h-8 w-8 text-white" />
                        </div>
                        <h1 className="mb-2 text-2xl font-bold text-gray-900">Simulador Mercado Pago</h1>
                        <p className="text-gray-600">Ambiente de desenvolvimento</p>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                Detalhes do Pagamento
                                {getStatusBadge(pagamento.status)}
                            </CardTitle>
                            <CardDescription>Transaction ID: {pagamento.transaction_id}</CardDescription>
                        </CardHeader>

                        <CardContent className="space-y-4">
                            <div className="rounded-lg bg-gray-50 p-4">
                                <h3 className="mb-2 text-lg font-semibold">{pagamento.assinatura.plano.name}</h3>
                                <p className="text-2xl font-bold text-green-600">{pagamento.formatted_amount}</p>
                            </div>

                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Cliente:</span>
                                    <span className="font-medium">{pagamento.assinatura.user.name}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Email:</span>
                                    <span className="font-medium">{pagamento.assinatura.user.email}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Assinatura ID:</span>
                                    <span className="font-medium">#{pagamento.assinatura.id}</span>
                                </div>
                            </div>

                            {pagamento.status === 'pendente' && (
                                <div className="space-y-3 border-t pt-4">
                                    <h4 className="font-semibold text-gray-900">Simular Resultado do Pagamento:</h4>

                                    <div className="grid grid-cols-1 gap-2">
                                        <Button
                                            onClick={() => handlePaymentAction('approve')}
                                            disabled={isProcessing}
                                            className="bg-green-600 text-white hover:bg-green-700"
                                        >
                                            <CheckCircle className="mr-2 h-4 w-4" />
                                            {isProcessing ? 'Processando...' : 'Aprovar Pagamento'}
                                        </Button>

                                        <Button onClick={() => handlePaymentAction('reject')} disabled={isProcessing} variant="destructive">
                                            <XCircle className="mr-2 h-4 w-4" />
                                            {isProcessing ? 'Processando...' : 'Rejeitar Pagamento'}
                                        </Button>

                                        <Button onClick={() => handlePaymentAction('cancel')} disabled={isProcessing} variant="outline">
                                            <XCircle className="mr-2 h-4 w-4" />
                                            {isProcessing ? 'Processando...' : 'Cancelar Pagamento'}
                                        </Button>
                                    </div>
                                </div>
                            )}

                            {pagamento.status !== 'pendente' && (
                                <div className="border-t pt-4">
                                    <p className="text-center text-gray-600">Este pagamento já foi processado.</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <div className="mt-6 text-center">
                        <p className="text-sm text-gray-500">
                            ⚠️ Esta é uma simulação para desenvolvimento.
                            <br />
                            Nenhuma transação real será processada.
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
