<?php

namespace App\Http\Controllers;

use App\Services\ProfileCompletionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;

class ProfileStatusController extends Controller
{
    protected ProfileCompletionService $profileService;

    public function __construct(ProfileCompletionService $profileService)
    {
        $this->profileService = $profileService;
    }

    /**
     * Get profile status for current user
     */
    public function status(Request $request): JsonResponse
    {
        $user = auth()->user();
        
        if (!$user) {
            return response()->json(['error' => 'Usuário não autenticado'], 401);
        }

        $status = $this->profileService->getCompletionStatus($user);
        $completionUrl = $this->profileService->getCompletionUrl($user);

        return response()->json([
            'status' => $status,
            'completion_url' => $completionUrl,
        ]);
    }

    /**
     * Get detailed profile analysis
     */
    public function analysis(Request $request)
    {
        $user = auth()->user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        $status = $this->profileService->getCompletionStatus($user);
        $completionUrl = $this->profileService->getCompletionUrl($user);
        $requiredFields = $this->profileService->getRequiredFields($user->role);

        return Inertia::render('profile/analysis', [
            'status' => $status,
            'completion_url' => $completionUrl,
            'required_fields' => $requiredFields,
            'user_role' => $user->role,
        ]);
    }

    /**
     * Check if profile meets minimum requirements
     */
    public function check(Request $request): JsonResponse
    {
        $user = auth()->user();
        
        if (!$user) {
            return response()->json(['error' => 'Usuário não autenticado'], 401);
        }

        $minPercentage = $request->input('min_percentage', 80);
        $enforceComplete = $request->boolean('enforce_complete', false);

        $status = $this->profileService->getCompletionStatus($user);
        
        $meetsRequirements = $enforceComplete 
            ? $status['is_complete']
            : $status['percentage'] >= $minPercentage;

        return response()->json([
            'meets_requirements' => $meetsRequirements,
            'status' => $status,
            'requirements' => [
                'min_percentage' => $minPercentage,
                'enforce_complete' => $enforceComplete,
            ],
        ]);
    }

    /**
     * Get profile completion suggestions
     */
    public function suggestions(Request $request): JsonResponse
    {
        $user = auth()->user();
        
        if (!$user) {
            return response()->json(['error' => 'Usuário não autenticado'], 401);
        }

        $status = $this->profileService->getCompletionStatus($user);
        
        $suggestions = [
            'priority_fields' => array_slice($status['missing_fields'], 0, 3),
            'next_steps' => $status['next_steps'],
            'completion_url' => $this->profileService->getCompletionUrl($user),
            'estimated_time' => $this->estimateCompletionTime($status['missing_count']),
        ];

        return response()->json($suggestions);
    }

    /**
     * Estimate completion time based on missing fields
     */
    private function estimateCompletionTime(int $missingCount): string
    {
        if ($missingCount === 0) {
            return '0 minutos';
        }

        $minutes = $missingCount * 2; // 2 minutos por campo em média

        if ($minutes < 5) {
            return 'menos de 5 minutos';
        } elseif ($minutes < 15) {
            return '5-15 minutos';
        } elseif ($minutes < 30) {
            return '15-30 minutos';
        } else {
            return 'mais de 30 minutos';
        }
    }
}
