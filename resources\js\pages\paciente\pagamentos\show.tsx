import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { AlertTriangle, ArrowLeft, Calendar, CheckCircle, Clock, CreditCard, DollarSign, Download, FileText, QrCode } from 'lucide-react';
import React from 'react';

interface Pagamento {
    id: number;
    valor: number;
    status: 'pendente' | 'pago' | 'falhou' | 'cancelado';
    forma_pagamento: string;
    data_vencimento: string;
    data_pagamento?: string;
    transaction_id?: string;
    observacoes?: string;
    created_at: string;
    formatted_valor: string;
    formatted_data_vencimento: string;
    formatted_data_pagamento?: string;
    assinatura: {
        id: number;
        status: string;
        plano: {
            id: number;
            name: string;
            description: string;
            price: number;
            sessions_per_month: number;
        };
    };
}

interface Props {
    pagamento: Pagamento;
}

export default function PagamentoShow({ pagamento }: Props) {
    const [showPaymentOptions, setShowPaymentOptions] = React.useState(false);
    const [selectedPaymentMethod, setSelectedPaymentMethod] = React.useState('');
    const [pixData, setPixData] = React.useState<any>(null);

    const { data, setData, post, processing } = useForm({
        forma_pagamento: '',
        dados_pagamento: {},
    });

    const handlePayment = (method: string) => {
        setSelectedPaymentMethod(method);
        setData('forma_pagamento', method);

        if (method === 'pix') {
            // Gerar PIX
            fetch(route('paciente.pagamentos.pix', pagamento.id))
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        setPixData(data.pix);
                    }
                });
        } else if (method === 'boleto') {
            // Redirecionar para boleto
            window.open(route('paciente.pagamentos.boleto', pagamento.id), '_blank');
        } else {
            // Simular pagamento com cartão
            setData({
                forma_pagamento: method,
                dados_pagamento: {
                    // Dados simulados do cartão
                    numero: '4111111111111111',
                    cvv: '123',
                    validade: '12/25',
                    nome: 'Titular do Cartão',
                },
            });

            post(route('paciente.pagamentos.process', pagamento.id));
        }
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
            pago: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
            falhou: { variant: 'destructive' as const, icon: AlertTriangle, color: 'text-red-600' },
            cancelado: { variant: 'outline' as const, icon: AlertTriangle, color: 'text-gray-600' },
        };

        const config = variants[status as keyof typeof variants] || variants.pendente;
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-4 w-4" />
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getFormaPagamentoLabel = (forma: string) => {
        const formas = {
            cartao_credito: 'Cartão de Crédito',
            cartao_debito: 'Cartão de Débito',
            pix: 'PIX',
            boleto: 'Boleto',
        };
        return formas[forma as keyof typeof formas] || forma;
    };

    const isVencido = () => {
        return pagamento.status === 'pendente' && new Date(pagamento.data_vencimento) < new Date();
    };

    const canPay = () => {
        return pagamento.status === 'pendente';
    };

    return (
        <AppLayout>
            <Head title={`Pagamento #${pagamento.id}`} />

            <div className="py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <Link href={route('paciente.pagamentos.index')}>
                                    <Button variant="ghost" size="sm">
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        Voltar
                                    </Button>
                                </Link>
                                <div>
                                    <h2 className="text-3xl font-bold tracking-tight">Pagamento #{pagamento.id}</h2>
                                    <p className="text-muted-foreground">{pagamento.assinatura.plano.name}</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                {pagamento.status === 'pago' && (
                                    <Link href={route('paciente.pagamentos.comprovante', pagamento.id)}>
                                        <Button variant="outline">
                                            <Download className="mr-2 h-4 w-4" />
                                            Comprovante
                                        </Button>
                                    </Link>
                                )}
                                {canPay() && (
                                    <Button onClick={() => setShowPaymentOptions(true)} className={isVencido() ? 'bg-red-600 hover:bg-red-700' : ''}>
                                        <DollarSign className="mr-2 h-4 w-4" />
                                        {isVencido() ? 'Pagar Agora (Vencido)' : 'Pagar Agora'}
                                    </Button>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="grid gap-6 md:grid-cols-2">
                        {/* Informações do Pagamento */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <DollarSign className="mr-2 h-5 w-5" />
                                    Detalhes do Pagamento
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Status:</span>
                                    <div className="flex items-center gap-2">
                                        {getStatusBadge(pagamento.status)}
                                        {isVencido() && (
                                            <Badge variant="destructive">
                                                <AlertTriangle className="mr-1 h-3 w-3" />
                                                Vencido
                                            </Badge>
                                        )}
                                    </div>
                                </div>

                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Valor:</span>
                                    <span className="text-2xl font-bold text-green-600">{pagamento.formatted_valor}</span>
                                </div>

                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Data de Vencimento:</span>
                                    <span className={`flex items-center text-sm ${isVencido() ? 'text-red-600' : ''}`}>
                                        <Calendar className="mr-1 h-4 w-4" />
                                        {pagamento.formatted_data_vencimento}
                                    </span>
                                </div>

                                {pagamento.data_pagamento && (
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium text-gray-600">Data do Pagamento:</span>
                                        <span className="flex items-center text-sm text-green-600">
                                            <CheckCircle className="mr-1 h-4 w-4" />
                                            {pagamento.formatted_data_pagamento}
                                        </span>
                                    </div>
                                )}

                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Forma de Pagamento:</span>
                                    <Badge variant="outline" className="flex items-center gap-1">
                                        <CreditCard className="h-3 w-3" />
                                        {getFormaPagamentoLabel(pagamento.forma_pagamento)}
                                    </Badge>
                                </div>

                                {pagamento.transaction_id && (
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium text-gray-600">ID da Transação:</span>
                                        <span className="rounded bg-gray-100 px-2 py-1 font-mono text-sm">{pagamento.transaction_id}</span>
                                    </div>
                                )}

                                {pagamento.observacoes && (
                                    <div>
                                        <span className="text-sm font-medium text-gray-600">Observações:</span>
                                        <p className="mt-1 rounded bg-gray-50 p-2 text-sm text-gray-700">{pagamento.observacoes}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Informações do Plano */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <FileText className="mr-2 h-5 w-5" />
                                    Informações do Plano
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <span className="text-sm font-medium text-gray-600">Plano:</span>
                                    <div className="mt-1">
                                        <p className="font-medium">{pagamento.assinatura.plano.name}</p>
                                        <p className="text-sm text-gray-600">{pagamento.assinatura.plano.description}</p>
                                    </div>
                                </div>

                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Sessões por Mês:</span>
                                    <span className="font-medium">{pagamento.assinatura.plano.sessions_per_month}</span>
                                </div>

                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Valor Mensal:</span>
                                    <span className="font-medium text-green-600">
                                        R$ {pagamento.assinatura.plano.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                    </span>
                                </div>

                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-600">Status da Assinatura:</span>
                                    <Badge variant="outline">{pagamento.assinatura.status}</Badge>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Opções de Pagamento */}
                    {showPaymentOptions && canPay() && (
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle>Escolha a Forma de Pagamento</CardTitle>
                                <CardDescription>Selecione como deseja pagar este valor</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                    <Button
                                        variant="outline"
                                        className="flex h-20 flex-col items-center justify-center"
                                        onClick={() => handlePayment('cartao_credito')}
                                        disabled={processing}
                                    >
                                        <CreditCard className="mb-2 h-6 w-6" />
                                        <span>Cartão de Crédito</span>
                                    </Button>

                                    <Button
                                        variant="outline"
                                        className="flex h-20 flex-col items-center justify-center"
                                        onClick={() => handlePayment('cartao_debito')}
                                        disabled={processing}
                                    >
                                        <CreditCard className="mb-2 h-6 w-6" />
                                        <span>Cartão de Débito</span>
                                    </Button>

                                    <Button
                                        variant="outline"
                                        className="flex h-20 flex-col items-center justify-center"
                                        onClick={() => handlePayment('pix')}
                                        disabled={processing}
                                    >
                                        <QrCode className="mb-2 h-6 w-6" />
                                        <span>PIX</span>
                                    </Button>

                                    <Button
                                        variant="outline"
                                        className="flex h-20 flex-col items-center justify-center"
                                        onClick={() => handlePayment('boleto')}
                                        disabled={processing}
                                    >
                                        <FileText className="mb-2 h-6 w-6" />
                                        <span>Boleto</span>
                                    </Button>
                                </div>

                                {processing && (
                                    <div className="mt-4 text-center">
                                        <p className="text-sm text-gray-600">Processando pagamento...</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    )}

                    {/* PIX QR Code */}
                    {pixData && (
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <QrCode className="mr-2 h-5 w-5" />
                                    Pagamento via PIX
                                </CardTitle>
                                <CardDescription>Escaneie o QR Code ou copie o código PIX</CardDescription>
                            </CardHeader>
                            <CardContent className="text-center">
                                <div className="mb-4">
                                    <img src={pixData.qr_code} alt="QR Code PIX" className="mx-auto h-48 w-48 rounded-lg border" />
                                </div>

                                <div className="space-y-2 text-sm">
                                    <p>
                                        <strong>Valor:</strong> R$ {pixData.valor.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                    </p>
                                    <p>
                                        <strong>Válido até:</strong> {format(new Date(pixData.data_expiracao), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                                    </p>
                                </div>

                                <div className="mt-4">
                                    <Button onClick={() => navigator.clipboard.writeText(pixData.codigo_pix)} variant="outline" className="w-full">
                                        Copiar Código PIX
                                    </Button>
                                </div>

                                <p className="mt-4 text-xs text-gray-500">
                                    Após o pagamento, a confirmação pode levar até 5 minutos para aparecer no sistema.
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
