<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Notificacao extends Model
{
    use HasFactory;

    protected $table = 'notificacoes';

    protected $fillable = [
        'user_id',
        'agendamento_id',
        'tipo',
        'titulo',
        'mensagem',
        'lida',
        'data_envio',
        'data_leitura',
    ];

    protected $casts = [
        'lida' => 'boolean',
        'data_envio' => 'datetime',
        'data_leitura' => 'datetime',
    ];

    // Relacionamentos
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function agendamento()
    {
        return $this->belongsTo(Agendamento::class);
    }

    // Scopes
    public function scopeNaoLidas($query)
    {
        return $query->where('lida', false);
    }

    public function scopeLidas($query)
    {
        return $query->where('lida', true);
    }

    public function scopePorTipo($query, $tipo)
    {
        return $query->where('tipo', $tipo);
    }

    // Métodos auxiliares
    public function marcarComoLida()
    {
        $this->update([
            'lida' => true,
            'data_leitura' => now(),
        ]);
    }

    public static function criarNotificacao($userId, $agendamentoId, $tipo, $titulo, $mensagem)
    {
        return self::create([
            'user_id' => $userId,
            'agendamento_id' => $agendamentoId,
            'tipo' => $tipo,
            'titulo' => $titulo,
            'mensagem' => $mensagem,
            'lida' => false,
            'data_envio' => now(),
        ]);
    }

    // Tipos de notificação
    const TIPO_NOVO_AGENDAMENTO = 'novo_agendamento';
    const TIPO_AGENDAMENTO_CONFIRMADO = 'agendamento_confirmado';
    const TIPO_AGENDAMENTO_REAGENDADO = 'agendamento_reagendado';
    const TIPO_AGENDAMENTO_CANCELADO = 'agendamento_cancelado';
    const TIPO_SESSAO_INICIADA = 'sessao_iniciada';
    const TIPO_SESSAO_FINALIZADA = 'sessao_finalizada';
    const TIPO_LEMBRETE_SESSAO = 'lembrete_sessao';
}
