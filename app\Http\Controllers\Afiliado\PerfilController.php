<?php

namespace App\Http\Controllers\Afiliado;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PerfilController extends Controller
{
    /**
     * Display the affiliate profile.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;
        
        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }
        
        return Inertia::render('afiliado/perfil', [
            'afiliado' => $afiliado,
        ]);
    }
    
    /**
     * Update the affiliate profile.
     */
    public function update(Request $request)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;
        
        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }
        
        $validated = $request->validate([
            'telefone' => 'required|string|max:20',
            'endereco' => 'required|string|max:500',
            'cidade' => 'required|string|max:100',
            'estado' => 'required|string|size:2',
            'cep' => 'required|string|max:9',
            'experiencia' => 'nullable|string|in:nenhuma,iniciante,intermediario,avancado',
            'motivacao' => 'required|string|max:1000',
            'canais_divulgacao' => 'required|array|min:1',
        ]);
        
        $afiliado->update($validated);
        
        return redirect()->back()->with('success', 'Perfil atualizado com sucesso!');
    }
}
