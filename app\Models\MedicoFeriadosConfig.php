<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MedicoFeriadosConfig extends Model
{
    use HasFactory;

    protected $table = 'medico_feriados_config';

    protected $fillable = [
        'fisioterapeuta_id',
        'trabalha_feriados_nacionais',
        'trabalha_feriados_estaduais',
        'trabalha_feriados_municipais',
        'feriados_excecoes',
    ];

    protected $casts = [
        'trabalha_feriados_nacionais' => 'boolean',
        'trabalha_feriados_estaduais' => 'boolean',
        'trabalha_feriados_municipais' => 'boolean',
        'feriados_excecoes' => 'array',
    ];

    // Relacionamentos
    public function fisioterapeuta()
    {
        return $this->belongsTo(User::class, 'fisioterapeuta_id');
    }

    // Métodos auxiliares
    public function trabalhaEmFeriado($feriado)
    {
        // Verifica se há exceção específica para este feriado
        if ($this->feriados_excecoes && in_array($feriado->id, $this->feriados_excecoes)) {
            // Se está nas exceções, inverte a configuração padrão
            switch ($feriado->tipo) {
                case 'nacional':
                    return !$this->trabalha_feriados_nacionais;
                case 'estadual':
                    return !$this->trabalha_feriados_estaduais;
                case 'municipal':
                    return !$this->trabalha_feriados_municipais;
            }
        }

        // Usa a configuração padrão
        switch ($feriado->tipo) {
            case 'nacional':
                return $this->trabalha_feriados_nacionais;
            case 'estadual':
                return $this->trabalha_feriados_estaduais;
            case 'municipal':
                return $this->trabalha_feriados_municipais;
            default:
                return false;
        }
    }

    public function adicionarExcecaoFeriado($feriadoId)
    {
        $excecoes = $this->feriados_excecoes ?? [];
        
        if (!in_array($feriadoId, $excecoes)) {
            $excecoes[] = $feriadoId;
            $this->feriados_excecoes = $excecoes;
            $this->save();
        }
    }

    public function removerExcecaoFeriado($feriadoId)
    {
        $excecoes = $this->feriados_excecoes ?? [];
        
        if (($key = array_search($feriadoId, $excecoes)) !== false) {
            unset($excecoes[$key]);
            $this->feriados_excecoes = array_values($excecoes);
            $this->save();
        }
    }

    public function getConfiguracoesResumoAttribute()
    {
        $resumo = [];
        
        if ($this->trabalha_feriados_nacionais) {
            $resumo[] = 'Trabalha em feriados nacionais';
        }
        
        if ($this->trabalha_feriados_estaduais) {
            $resumo[] = 'Trabalha em feriados estaduais';
        }
        
        if ($this->trabalha_feriados_municipais) {
            $resumo[] = 'Trabalha em feriados municipais';
        }

        if (empty($resumo)) {
            $resumo[] = 'Não trabalha em feriados';
        }

        if ($this->feriados_excecoes && count($this->feriados_excecoes) > 0) {
            $resumo[] = count($this->feriados_excecoes) . ' exceção(ões) específica(s)';
        }

        return implode(', ', $resumo);
    }
}
