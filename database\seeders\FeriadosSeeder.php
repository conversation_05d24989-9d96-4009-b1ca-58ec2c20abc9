<?php

namespace Database\Seeders;

use App\Models\Feriado;
use Illuminate\Database\Seeder;

class FeriadosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $feriadosNacionais = [
            [
                'nome' => 'Confraternização Universal',
                'data' => '2025-01-01',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Ano Novo',
            ],
            [
                'nome' => 'Tiradentes',
                'data' => '2025-04-21',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia de Tiradentes',
            ],
            [
                'nome' => 'Dia do Trabalhador',
                'data' => '2025-05-01',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia do Trabalho',
            ],
            [
                'nome' => 'Independência do Brasil',
                'data' => '2025-09-07',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia da Independência',
            ],
            [
                'nome' => 'Nossa Senhora Aparecida',
                'data' => '2025-10-12',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Padroeira do Brasil',
            ],
            [
                'nome' => 'Finados',
                'data' => '2025-11-02',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Dia de Finados',
            ],
            [
                'nome' => 'Proclamação da República',
                'data' => '2025-11-15',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Proclamação da República',
            ],
            [
                'nome' => 'Natal',
                'data' => '2025-12-25',
                'tipo' => 'nacional',
                'recorrente' => true,
                'descricao' => 'Natal',
            ],
        ];

        $feriadosEstaduaisSP = [
            [
                'nome' => 'Revolução Constitucionalista',
                'data' => '2025-07-09',
                'tipo' => 'estadual',
                'estado' => 'SP',
                'recorrente' => true,
                'descricao' => 'Revolução Constitucionalista de 1932',
            ],
        ];

        $feriadosMunicipaisSP = [
            [
                'nome' => 'Aniversário de São Paulo',
                'data' => '2025-01-25',
                'tipo' => 'municipal',
                'estado' => 'SP',
                'cidade' => 'São Paulo',
                'recorrente' => true,
                'descricao' => 'Fundação da cidade de São Paulo',
            ],
            [
                'nome' => 'Consciência Negra',
                'data' => '2025-11-20',
                'tipo' => 'municipal',
                'estado' => 'SP',
                'cidade' => 'São Paulo',
                'recorrente' => true,
                'descricao' => 'Dia da Consciência Negra',
            ],
        ];

        // Feriados móveis de 2025 (calculados)
        $feriadosMoveis2025 = [
            [
                'nome' => 'Carnaval',
                'data' => '2025-03-03',
                'tipo' => 'nacional',
                'recorrente' => false,
                'descricao' => 'Segunda-feira de Carnaval',
            ],
            [
                'nome' => 'Carnaval',
                'data' => '2025-03-04',
                'tipo' => 'nacional',
                'recorrente' => false,
                'descricao' => 'Terça-feira de Carnaval',
            ],
            [
                'nome' => 'Sexta-feira Santa',
                'data' => '2025-04-18',
                'tipo' => 'nacional',
                'recorrente' => false,
                'descricao' => 'Paixão de Cristo',
            ],
            [
                'nome' => 'Corpus Christi',
                'data' => '2025-06-19',
                'tipo' => 'nacional',
                'recorrente' => false,
                'descricao' => 'Corpus Christi',
            ],
        ];

        // Inserir feriados nacionais
        foreach ($feriadosNacionais as $feriado) {
            Feriado::updateOrCreate(
                [
                    'nome' => $feriado['nome'],
                    'tipo' => $feriado['tipo'],
                ],
                $feriado
            );
        }

        // Inserir feriados estaduais de SP
        foreach ($feriadosEstaduaisSP as $feriado) {
            Feriado::updateOrCreate(
                [
                    'nome' => $feriado['nome'],
                    'tipo' => $feriado['tipo'],
                    'estado' => $feriado['estado'],
                ],
                $feriado
            );
        }

        // Inserir feriados municipais de SP
        foreach ($feriadosMunicipaisSP as $feriado) {
            Feriado::updateOrCreate(
                [
                    'nome' => $feriado['nome'],
                    'tipo' => $feriado['tipo'],
                    'estado' => $feriado['estado'],
                    'cidade' => $feriado['cidade'],
                ],
                $feriado
            );
        }

        // Inserir feriados móveis de 2025
        foreach ($feriadosMoveis2025 as $feriado) {
            Feriado::updateOrCreate(
                [
                    'nome' => $feriado['nome'],
                    'data' => $feriado['data'],
                ],
                $feriado
            );
        }

        $this->command->info('Feriados brasileiros inseridos com sucesso!');
    }
}
