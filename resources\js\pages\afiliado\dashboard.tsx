import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { BarChart3, Calendar, CheckCircle, Copy, DollarSign, Download, Link as LinkIcon, Share2, TrendingUp, Users } from 'lucide-react';
import { useState } from 'react';

interface Props {
    afiliado: {
        id: number;
        codigo_afiliado: string;
        nome: string;
        email: string;
        status: string;
        link_afiliado?: string;
        total_vendas: number;
        total_comissoes: number;
        vendas_mes_atual: number;
        comissoes_mes_atual: number;
        created_at: string;
    };
    vendas: Array<{
        id: number;
        plano_tipo: string;
        valor_venda: number;
        comissao: number;
        status: string;
        created_at: string;
        cliente: {
            name: string;
            email: string;
        };
    }>;
}

export default function AfiliadoDashboard({ afiliado, vendas = [] }: Props) {
    const [copiedLink, setCopiedLink] = useState(false);

    // Se não há dados do afiliado, mostrar estado de carregamento ou erro
    if (!afiliado) {
        return (
            <AppLayout breadcrumbs={[{ title: 'Dashboard', href: '/afiliado/dashboard' }]}>
                <Head title="Dashboard do Afiliado" />
                <div className="flex min-h-screen items-center justify-center bg-background">
                    <div className="text-center">
                        <h1 className="mb-4 text-2xl font-bold">Carregando dados do afiliado...</h1>
                        <p className="text-muted-foreground">Se o problema persistir, entre em contato com o suporte.</p>
                    </div>
                </div>
            </AppLayout>
        );
    }

    const handleCopyLink = () => {
        if (afiliado?.link_afiliado) {
            navigator.clipboard.writeText(afiliado.link_afiliado);
            setCopiedLink(true);
            setTimeout(() => setCopiedLink(false), 2000);
        }
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: { variant: 'secondary' as const, text: 'Pendente' },
            aprovado: { variant: 'default' as const, text: 'Aprovado' },
            rejeitado: { variant: 'destructive' as const, text: 'Rejeitado' },
            suspenso: { variant: 'outline' as const, text: 'Suspenso' },
        };

        const config = variants[status as keyof typeof variants] || variants.pendente;

        return <Badge variant={config.variant}>{config.text}</Badge>;
    };

    const breadcrumbs = [{ title: 'Dashboard', href: '/afiliado/dashboard', current: true }];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard do Afiliado" />
            <div className="min-h-screen bg-background">
                {/* Header */}
                <section className="bg-gradient-to-b from-background to-muted/30 py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 className="text-3xl font-bold">Olá, {afiliado?.nome || 'Afiliado'}!</h1>
                                <p className="text-muted-foreground">
                                    Código: {afiliado?.codigo_afiliado || 'N/A'} • Status: {getStatusBadge(afiliado?.status || 'pendente')}
                                </p>
                            </div>
                            <div className="flex gap-2">
                                <Button variant="outline" asChild>
                                    <a href="/materiais-divulgacao">
                                        <Download className="mr-2 h-4 w-4" />
                                        Materiais
                                    </a>
                                </Button>
                                <Button variant="outline">
                                    <Share2 className="mr-2 h-4 w-4" />
                                    Compartilhar
                                </Button>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Stats Cards */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-6 md:grid-cols-4">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Vendas Total</CardTitle>
                                    <Users className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{afiliado?.total_vendas || 0}</div>
                                    <p className="text-xs text-muted-foreground">Desde o cadastro</p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Vendas/Mês</CardTitle>
                                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{afiliado?.vendas_mes_atual || 0}</div>
                                    <p className="text-xs text-muted-foreground">Mês atual</p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Comissões Total</CardTitle>
                                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">
                                        R$ {(Number(afiliado?.total_comissoes) || 0).toFixed(2).replace('.', ',')}
                                    </div>
                                    <p className="text-xs text-muted-foreground">Total acumulado</p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Comissões/Mês</CardTitle>
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">
                                        R$ {(Number(afiliado?.comissoes_mes_atual) || 0).toFixed(2).replace('.', ',')}
                                    </div>
                                    <p className="text-xs text-muted-foreground">Mês atual</p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* Link de Afiliado */}
                {afiliado?.link_afiliado && (
                    <section className="py-8">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <LinkIcon className="h-5 w-5" />
                                        Seu Link de Afiliado
                                    </CardTitle>
                                    <CardDescription>Use este link para divulgar os serviços da F4 Fisio e ganhar comissões</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                                        <div className="flex-1 rounded-lg bg-muted p-3">
                                            <code className="text-sm break-all">{afiliado?.link_afiliado}</code>
                                        </div>
                                        <Button onClick={handleCopyLink} variant="outline">
                                            {copiedLink ? (
                                                <>
                                                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                                    Copiado!
                                                </>
                                            ) : (
                                                <>
                                                    <Copy className="mr-2 h-4 w-4" />
                                                    Copiar
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </section>
                )}

                {/* Status do Cadastro */}
                {afiliado?.status === 'pendente' && (
                    <section className="py-8">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <Card className="border-yellow-200 bg-yellow-50">
                                <CardHeader>
                                    <CardTitle className="text-yellow-800">Cadastro em Análise</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-yellow-700">
                                        Seu cadastro está sendo analisado pela nossa equipe. Você receberá um e-mail com a confirmação e seu link
                                        exclusivo em até 24 horas.
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                    </section>
                )}

                {/* Vendas Recentes */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5" />
                                    Vendas Recentes
                                </CardTitle>
                                <CardDescription>Suas últimas vendas e comissões</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {vendas.length > 0 ? (
                                    <div className="overflow-x-auto">
                                        <table className="w-full">
                                            <thead>
                                                <tr className="border-b">
                                                    <th className="p-2 text-left">Data</th>
                                                    <th className="p-2 text-left">Cliente</th>
                                                    <th className="p-2 text-left">Plano</th>
                                                    <th className="p-2 text-left">Valor</th>
                                                    <th className="p-2 text-left">Comissão</th>
                                                    <th className="p-2 text-left">Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {vendas.map((venda) => (
                                                    <tr key={venda.id} className="border-b">
                                                        <td className="p-2 text-sm">{new Date(venda.created_at).toLocaleDateString('pt-BR')}</td>
                                                        <td className="p-2">
                                                            <div>
                                                                <div className="font-medium">{venda.cliente?.name || 'N/A'}</div>
                                                                <div className="text-sm text-muted-foreground">{venda.cliente?.email || 'N/A'}</div>
                                                            </div>
                                                        </td>
                                                        <td className="p-2 text-sm capitalize">{venda.plano_tipo}</td>
                                                        <td className="p-2 text-sm">
                                                            R$ {(Number(venda.valor_venda) || 0).toFixed(2).replace('.', ',')}
                                                        </td>
                                                        <td className="p-2 text-sm font-medium text-green-600">
                                                            R$ {(Number(venda.comissao) || 0).toFixed(2).replace('.', ',')}
                                                        </td>
                                                        <td className="p-2">
                                                            <Badge variant={venda.status === 'confirmada' ? 'default' : 'secondary'}>
                                                                {venda.status}
                                                            </Badge>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                ) : (
                                    <div className="py-8 text-center">
                                        <Users className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                        <h3 className="mb-2 text-lg font-medium">Nenhuma venda ainda</h3>
                                        <p className="mb-4 text-muted-foreground">Comece a divulgar seu link para gerar suas primeiras vendas!</p>
                                        <Button asChild>
                                            <a href="/materiais-divulgacao">
                                                <Download className="mr-2 h-4 w-4" />
                                                Baixar Materiais
                                            </a>
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Dicas Rápidas */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle>💡 Dicas para Aumentar suas Vendas</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <h4 className="font-medium">Divulgação Efetiva</h4>
                                        <ul className="space-y-1 text-sm text-muted-foreground">
                                            <li>• Compartilhe em grupos de WhatsApp relevantes</li>
                                            <li>• Poste regularmente nas redes sociais</li>
                                            <li>• Use os materiais prontos disponíveis</li>
                                        </ul>
                                    </div>
                                    <div className="space-y-2">
                                        <h4 className="font-medium">Relacionamento</h4>
                                        <ul className="space-y-1 text-sm text-muted-foreground">
                                            <li>• Responda dúvidas rapidamente</li>
                                            <li>• Compartilhe benefícios reais</li>
                                            <li>• Seja transparente sobre ser afiliado</li>
                                        </ul>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Guia de Primeiros Passos */}
                {vendas.length === 0 && (
                    <section className="py-8">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100">
                                        <CheckCircle className="h-5 w-5" />
                                        Primeiros Passos como Afiliado
                                    </CardTitle>
                                    <CardDescription className="text-blue-700 dark:text-blue-300">
                                        Siga este guia para começar a gerar suas primeiras vendas
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                        <div className="rounded-lg border border-blue-200 bg-white p-4 dark:border-blue-800 dark:bg-blue-950/40">
                                            <div className="mb-2 flex items-center gap-2">
                                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-xs font-bold text-white">
                                                    1
                                                </div>
                                                <h4 className="font-medium">Copie seu Link</h4>
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                Use o botão "Copiar Link" acima para copiar seu link exclusivo de afiliado
                                            </p>
                                        </div>

                                        <div className="rounded-lg border border-blue-200 bg-white p-4 dark:border-blue-800 dark:bg-blue-950/40">
                                            <div className="mb-2 flex items-center gap-2">
                                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-xs font-bold text-white">
                                                    2
                                                </div>
                                                <h4 className="font-medium">Baixe Materiais</h4>
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                Acesse a seção "Materiais" para baixar banners e textos prontos
                                            </p>
                                        </div>

                                        <div className="rounded-lg border border-blue-200 bg-white p-4 dark:border-blue-800 dark:bg-blue-950/40">
                                            <div className="mb-2 flex items-center gap-2">
                                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-xs font-bold text-white">
                                                    3
                                                </div>
                                                <h4 className="font-medium">Comece a Divulgar</h4>
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                Compartilhe seu link nas redes sociais, WhatsApp e outros canais
                                            </p>
                                        </div>
                                    </div>

                                    <div className="mt-6 flex flex-wrap gap-3">
                                        <Button asChild>
                                            <a href="/afiliado/materiais">
                                                <Download className="mr-2 h-4 w-4" />
                                                Ver Materiais
                                            </a>
                                        </Button>
                                        <Button variant="outline" asChild>
                                            <a href="/contato" target="_blank">
                                                <Users className="mr-2 h-4 w-4" />
                                                Falar com Suporte
                                            </a>
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </section>
                )}
            </div>
        </AppLayout>
    );
}
