import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { CheckCircle, Copy, Download, ExternalLink, FileText, Image, Link as LinkIcon, Share2, Video } from 'lucide-react';
import { useState } from 'react';

interface Material {
    id: number;
    titulo: string;
    descricao: string;
    tipo: 'banner' | 'texto' | 'video';
    formato: string;
    tamanho: string;
    url: string;
    categoria: string;
}

interface Props {
    materiais: Material[];
    afiliado: {
        codigo_afiliado: string;
        link_afiliado: string;
        nome: string;
    };
}

export default function AfiliadoMateriais({ materiais, afiliado }: Props) {
    const [copiedItem, setCopiedItem] = useState<string | null>(null);
    const [copiedLink, setCopiedLink] = useState(false);

    const breadcrumbs = [
        { title: 'Dashboard', href: '/afiliado/dashboard' },
        { title: 'Materiais', href: '/afiliado/materiais' },
    ];

    const handleCopyText = (text: string, itemId: string) => {
        // Substituir placeholder pelo link real do afiliado
        const textWithLink = text.replace('[SEU_LINK_AFILIADO]', afiliado.link_afiliado);
        navigator.clipboard.writeText(textWithLink);
        setCopiedItem(itemId);
        setTimeout(() => setCopiedItem(null), 2000);
    };

    const handleCopyLink = () => {
        navigator.clipboard.writeText(afiliado.link_afiliado);
        setCopiedLink(true);
        setTimeout(() => setCopiedLink(false), 2000);
    };

    const getTypeIcon = (tipo: string) => {
        switch (tipo) {
            case 'banner':
                return <Image className="h-5 w-5" />;
            case 'texto':
                return <FileText className="h-5 w-5" />;
            case 'video':
                return <Video className="h-5 w-5" />;
            default:
                return <FileText className="h-5 w-5" />;
        }
    };

    const banners = materiais.filter((m) => m.tipo === 'banner');
    const textos = materiais.filter((m) => m.tipo === 'texto');
    const videos = materiais.filter((m) => m.tipo === 'video');

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Materiais de Divulgação" />

            <div className="min-h-screen bg-background">
                {/* Header */}
                <section className="bg-gradient-to-b from-background to-muted/30 py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 className="text-3xl font-bold">Materiais de Divulgação</h1>
                                <p className="text-muted-foreground">Baixe materiais personalizados com seu link de afiliado</p>
                            </div>
                            <div className="flex gap-2">
                                <Button variant="outline" asChild>
                                    <a href="/materiais-divulgacao" target="_blank">
                                        <ExternalLink className="mr-2 h-4 w-4" />
                                        Ver Página Pública
                                    </a>
                                </Button>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Link do Afiliado */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card className="border-primary/20 bg-primary/5">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <LinkIcon className="h-5 w-5" />
                                    Seu Link de Afiliado
                                </CardTitle>
                                <CardDescription>Use este link em todas as suas divulgações para receber comissões</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                                    <div className="flex-1">
                                        <Label htmlFor="link-afiliado" className="sr-only">
                                            Link de Afiliado
                                        </Label>
                                        <Input id="link-afiliado" value={afiliado.link_afiliado} readOnly className="font-mono" />
                                    </div>
                                    <Button onClick={handleCopyLink} className="shrink-0">
                                        {copiedLink ? (
                                            <>
                                                <CheckCircle className="mr-2 h-4 w-4" />
                                                Copiado!
                                            </>
                                        ) : (
                                            <>
                                                <Copy className="mr-2 h-4 w-4" />
                                                Copiar Link
                                            </>
                                        )}
                                    </Button>
                                </div>
                                <div className="mt-4 rounded-lg bg-muted/50 p-4">
                                    <h4 className="font-medium">Como usar:</h4>
                                    <ul className="mt-2 space-y-1 text-sm text-muted-foreground">
                                        <li>• Compartilhe este link em suas redes sociais</li>
                                        <li>• Inclua em seus materiais de divulgação</li>
                                        <li>• Envie para clientes interessados</li>
                                        <li>• Use em campanhas de marketing</li>
                                    </ul>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Banners */}
                {banners.length > 0 && (
                    <section className="py-8">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mb-6">
                                <h2 className="text-2xl font-bold">Banners</h2>
                                <p className="text-muted-foreground">Imagens prontas para suas redes sociais e campanhas</p>
                            </div>
                            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                {banners.map((material) => (
                                    <Card key={material.id}>
                                        <CardHeader>
                                            <div className="flex items-center justify-between">
                                                <CardTitle className="flex items-center gap-2 text-lg">
                                                    {getTypeIcon(material.tipo)}
                                                    {material.titulo}
                                                </CardTitle>
                                                <Badge variant="outline">{material.categoria}</Badge>
                                            </div>
                                            <CardDescription>{material.descricao}</CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="mb-4 space-y-2 text-sm text-muted-foreground">
                                                <p>Formato: {material.formato}</p>
                                                <p>Tamanho: {material.tamanho}</p>
                                            </div>
                                            <Button asChild className="w-full">
                                                <a href={material.url} download>
                                                    <Download className="mr-2 h-4 w-4" />
                                                    Baixar Banner
                                                </a>
                                            </Button>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </div>
                    </section>
                )}

                {/* Textos Prontos */}
                {textos.length > 0 && (
                    <section className="py-8">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mb-6">
                                <h2 className="text-2xl font-bold">Textos Prontos</h2>
                                <p className="text-muted-foreground">Textos personalizados com seu link de afiliado</p>
                            </div>
                            <div className="grid gap-6 lg:grid-cols-2">
                                {textos.map((material) => (
                                    <Card key={material.id}>
                                        <CardHeader>
                                            <div className="flex items-center justify-between">
                                                <CardTitle className="flex items-center gap-2 text-lg">
                                                    {getTypeIcon(material.tipo)}
                                                    {material.titulo}
                                                </CardTitle>
                                                <Badge variant="outline">{material.categoria}</Badge>
                                            </div>
                                            <CardDescription>{material.descricao}</CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="mb-4 rounded-lg bg-muted p-4">
                                                <pre className="font-sans text-sm whitespace-pre-wrap">
                                                    {material.url.replace('[SEU_LINK_AFILIADO]', afiliado.link_afiliado)}
                                                </pre>
                                            </div>
                                            <Button
                                                variant="outline"
                                                className="w-full"
                                                onClick={() => handleCopyText(material.url, `texto-${material.id}`)}
                                            >
                                                {copiedItem === `texto-${material.id}` ? (
                                                    <>
                                                        <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                                        Copiado!
                                                    </>
                                                ) : (
                                                    <>
                                                        <Copy className="mr-2 h-4 w-4" />
                                                        Copiar Texto
                                                    </>
                                                )}
                                            </Button>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </div>
                    </section>
                )}

                {/* Vídeos */}
                {videos.length > 0 && (
                    <section className="py-8">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mb-6">
                                <h2 className="text-2xl font-bold">Vídeos</h2>
                                <p className="text-muted-foreground">Conteúdo em vídeo para suas campanhas</p>
                            </div>
                            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                {videos.map((material) => (
                                    <Card key={material.id}>
                                        <CardHeader>
                                            <div className="flex items-center justify-between">
                                                <CardTitle className="flex items-center gap-2 text-lg">
                                                    {getTypeIcon(material.tipo)}
                                                    {material.titulo}
                                                </CardTitle>
                                                <Badge variant="outline">{material.categoria}</Badge>
                                            </div>
                                            <CardDescription>{material.descricao}</CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="mb-4 space-y-2 text-sm text-muted-foreground">
                                                <p>Formato: {material.formato}</p>
                                                <p>Tamanho: {material.tamanho}</p>
                                            </div>
                                            <Button asChild className="w-full">
                                                <a href={material.url} download>
                                                    <Download className="mr-2 h-4 w-4" />
                                                    Baixar Vídeo
                                                </a>
                                            </Button>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </div>
                    </section>
                )}

                {/* Dicas de Uso */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Share2 className="h-5 w-5" />
                                    Dicas para Maximizar suas Vendas
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-6 md:grid-cols-2">
                                    <div>
                                        <h4 className="mb-3 font-medium">Estratégias de Divulgação</h4>
                                        <ul className="space-y-2 text-sm text-muted-foreground">
                                            <li>• Publique regularmente nas redes sociais</li>
                                            <li>• Compartilhe depoimentos de clientes</li>
                                            <li>• Use stories e posts interativos</li>
                                            <li>• Crie conteúdo educativo sobre fisioterapia</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 className="mb-3 font-medium">Melhores Práticas</h4>
                                        <ul className="space-y-2 text-sm text-muted-foreground">
                                            <li>• Seja transparente sobre ser afiliado</li>
                                            <li>• Foque nos benefícios para o cliente</li>
                                            <li>• Use linguagem clara e acessível</li>
                                            <li>• Responda rapidamente às dúvidas</li>
                                        </ul>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </section>
            </div>
        </AppLayout>
    );
}
