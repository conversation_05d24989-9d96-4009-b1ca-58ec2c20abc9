import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { AlertTriangle, ArrowLeft, Calendar, CheckCircle, Clock, FileText, Mail, MapPin, Phone, User, XCircle } from 'lucide-react';
import { useState } from 'react';

interface Props {
    agendamento: {
        id: number;
        data_agendamento: string;
        horario: string;
        status: string;
        tipo: string;
        observacoes?: string;
        endereco_atendimento: {
            logradouro: string;
            numero: string;
            complemento?: string;
            bairro: string;
            cidade: string;
            cep: string;
        };
        fisioterapeuta: {
            id: number;
            user: {
                name: string;
                email: string;
                phone?: string;
            };
            especialidades: string[];
            crefito: string;
        };
        created_at: string;
        updated_at: string;
    };
    podeEditar: boolean;
    podeCancelar: boolean;
}

export default function PacienteAgendamentoShow() {
    const pageProps = usePage().props as any;
    const { agendamento, podeEditar, podeCancelar } = pageProps;
    const [showCancelDialog, setShowCancelDialog] = useState(false);

    const { post, processing } = useForm();

    const handleCancel = () => {
        post(route('paciente.agendamentos.cancel', agendamento.id));
        setShowCancelDialog(false);
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            agendado: 'default',
            concluido: 'default',
            cancelado: 'destructive',
            em_andamento: 'secondary',
        } as const;

        const labels = {
            agendado: 'Agendado',
            concluido: 'Concluído',
            cancelado: 'Cancelado',
            em_andamento: 'Em Andamento',
        };

        const icons = {
            agendado: <Clock className="h-4 w-4" />,
            concluido: <CheckCircle className="h-4 w-4" />,
            cancelado: <XCircle className="h-4 w-4" />,
            em_andamento: <Clock className="h-4 w-4" />,
        };

        return (
            <Badge variant={variants[status as keyof typeof variants] || 'default'} className="flex items-center gap-1">
                {icons[status as keyof typeof icons]}
                {labels[status as keyof typeof labels] || status}
            </Badge>
        );
    };

    const formatDate = (dateString: string | null | undefined) => {
        if (!dateString) return 'Data não informada';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Data inválida';
            return date.toLocaleDateString('pt-BR', {
                day: '2-digit',
                month: 'long',
                year: 'numeric',
                weekday: 'long',
            });
        } catch {
            return 'Data inválida';
        }
    };

    const formatTime = (timeString: string | null | undefined) => {
        if (!timeString) return '--:--';
        return timeString.substring(0, 5);
    };

    const formatDateTime = (dateTimeString: string) => {
        return new Date(dateTimeString).toLocaleString('pt-BR');
    };

    const formatAddress = (endereco: any) => {
        const { logradouro, numero, complemento, bairro, cidade, cep } = endereco;
        return `${logradouro}, ${numero}${complemento ? ` - ${complemento}` : ''}, ${bairro}, ${cidade} - CEP: ${cep}`;
    };

    return (
        <AppLayout>
            <Head title={`Agendamento - ${formatDate(agendamento.data_agendamento)}`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                <div className="flex items-center gap-4">
                    <Link href={route('paciente.agendamentos.index')}>
                        <Button variant="ghost" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar aos Agendamentos
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">{agendamento.tipo === 'sessao' ? 'Sessão de Fisioterapia' : 'Avaliação'}</h1>
                        <p className="text-muted-foreground">
                            {formatDate(agendamento.data_agendamento)} às {formatTime(agendamento.horario)}
                        </p>
                    </div>
                </div>

                {/* Alertas baseados no status */}
                {agendamento.status === 'cancelado' && (
                    <Alert>
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>Este agendamento foi cancelado.</AlertDescription>
                    </Alert>
                )}

                {agendamento.status === 'agendado' && podeCancelar && (
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>Você pode cancelar este agendamento até 24 horas antes do horário marcado.</AlertDescription>
                    </Alert>
                )}

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Informações do Agendamento */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <span className="flex items-center gap-2">
                                    <Calendar className="h-5 w-5" />
                                    Detalhes do Agendamento
                                </span>
                                {getStatusBadge(agendamento.status)}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm text-muted-foreground">Data</p>
                                    <p className="font-medium">{formatDate(agendamento.data_agendamento)}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-muted-foreground">Horário</p>
                                    <p className="font-medium">{formatTime(agendamento.horario)}</p>
                                </div>
                            </div>

                            <div>
                                <p className="text-sm text-muted-foreground">Tipo</p>
                                <p className="font-medium">{agendamento.tipo === 'sessao' ? 'Sessão de Fisioterapia' : 'Avaliação'}</p>
                            </div>

                            {agendamento.observacoes && (
                                <div>
                                    <p className="text-sm text-muted-foreground">Observações</p>
                                    <p className="rounded-md bg-muted p-3 text-sm">{agendamento.observacoes}</p>
                                </div>
                            )}

                            <Separator />

                            <div className="space-y-1 text-xs text-muted-foreground">
                                <p>Agendado em: {formatDateTime(agendamento.created_at)}</p>
                                {agendamento.updated_at !== agendamento.created_at && (
                                    <p>Última atualização: {formatDateTime(agendamento.updated_at)}</p>
                                )}
                            </div>

                            {/* Ações */}
                            {agendamento.status === 'agendado' && (
                                <div className="flex gap-2 pt-4">
                                    {podeCancelar && (
                                        <Button variant="destructive" size="sm" onClick={() => setShowCancelDialog(true)} disabled={processing}>
                                            Cancelar Agendamento
                                        </Button>
                                    )}
                                </div>
                            )}

                            {agendamento.status === 'concluido' && (
                                <div className="pt-4">
                                    <Link href={route('paciente.historico.show', agendamento.id)}>
                                        <Button variant="outline" size="sm">
                                            <FileText className="mr-2 h-4 w-4" />
                                            Ver Relatório
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Informações do Fisioterapeuta */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Fisioterapeuta
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <p className="text-lg font-medium">{agendamento.fisioterapeuta.user.name}</p>
                                <p className="text-sm text-muted-foreground">CREFITO: {agendamento.fisioterapeuta.crefito}</p>
                            </div>

                            {agendamento.fisioterapeuta.especialidades.length > 0 && (
                                <div>
                                    <p className="mb-2 text-sm text-muted-foreground">Especialidades</p>
                                    <div className="flex flex-wrap gap-1">
                                        {agendamento.fisioterapeuta.especialidades.map((especialidade: string, index: number) => (
                                            <Badge key={index} variant="secondary" className="text-xs">
                                                {especialidade}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                            )}

                            <Separator />

                            <div className="space-y-3">
                                <div className="flex items-center gap-2">
                                    <Mail className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm">{agendamento.fisioterapeuta.user.email}</span>
                                </div>

                                {agendamento.fisioterapeuta.user.phone && (
                                    <div className="flex items-center gap-2">
                                        <Phone className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">{agendamento.fisioterapeuta.user.phone}</span>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Endereço do Atendimento */}
                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <MapPin className="h-5 w-5" />
                                Local do Atendimento
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm">{formatAddress(agendamento.endereco_atendimento)}</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Dialog de Confirmação de Cancelamento */}
                {showCancelDialog && (
                    <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
                        <Card className="w-full max-w-md">
                            <CardHeader>
                                <CardTitle>Confirmar Cancelamento</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-sm text-muted-foreground">
                                    Tem certeza que deseja cancelar este agendamento? Esta ação não pode ser desfeita.
                                </p>
                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setShowCancelDialog(false)} disabled={processing}>
                                        Não, manter agendamento
                                    </Button>
                                    <Button variant="destructive" onClick={handleCancel} disabled={processing}>
                                        {processing ? 'Cancelando...' : 'Sim, cancelar'}
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
