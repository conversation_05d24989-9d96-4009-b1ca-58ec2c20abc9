import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Calendar, CheckCircle, Clock, Eye, FileText, Plus, RotateCcw, Search, Star, User, X } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Início',
        href: '/paciente/dashboard',
    },
    {
        title: 'Agendamentos',
        href: '/paciente/agendamentos',
    },
];

interface Agendamento {
    id: number;
    data_hora: string;
    duracao: number;
    status: string;
    observacoes?: string;
    relatorio_sessao?: any;
    avaliacao?: any;
    fisioterapeuta: {
        id: number;
        user: {
            name: string;
            email: string;
        };
    };
}

interface Props {
    agendamentos: {
        data: Agendamento[];
        links: any[];
        meta: any;
    };
    filters: {
        status?: string;
        periodo?: string;
    };
    stats: {
        total: number;
        agendados: number;
        concluidos: number;
        cancelados: number;
    };
}

export default function PacienteAgendamentos({ agendamentos, filters, stats }: Props) {
    const [search, setSearch] = useState('');
    const [showQuickScheduleModal, setShowQuickScheduleModal] = useState(false);
    const { data, setData, get } = useForm({
        status: filters.status || undefined,
        periodo: filters.periodo || undefined,
    });

    const formatDate = (dateString: string | null | undefined) => {
        if (!dateString) return 'Data não informada';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Data inválida';
            return date.toLocaleDateString('pt-BR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
            });
        } catch {
            return 'Data inválida';
        }
    };

    const formatTime = (dateString: string | null | undefined) => {
        if (!dateString) return '--:--';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '--:--';
            return date.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit',
            });
        } catch {
            return '--:--';
        }
    };

    const handleFilter = () => {
        get(route('paciente.agendamentos.index'), {
            preserveState: true,
            replace: true,
        });
    };

    const handleCancel = (id: number) => {
        if (confirm('Tem certeza que deseja cancelar este agendamento?')) {
            router.post(route('paciente.agendamentos.cancel', id));
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'agendado':
                return 'bg-blue-100 text-blue-800';
            case 'confirmado':
                return 'bg-green-100 text-green-800';
            case 'concluido':
                return 'bg-gray-100 text-gray-800';
            case 'cancelado':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'agendado':
                return 'Agendado';
            case 'confirmado':
                return 'Confirmado';
            case 'concluido':
                return 'Concluído';
            case 'cancelado':
                return 'Cancelado';
            default:
                return status;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Meus Agendamentos" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">Meus Agendamentos</h1>
                        <p className="text-gray-600">Gerencie suas consultas e sessões</p>
                    </div>
                    <div className="flex flex-col gap-2 sm:flex-row">
                        <Link href={route('paciente.fisioterapeutas.index')}>
                            <Button variant="outline" className="w-full sm:w-auto">
                                <User className="mr-2 h-4 w-4" />
                                <span className="hidden sm:inline">Buscar Fisioterapeutas</span>
                                <span className="sm:hidden">Buscar</span>
                            </Button>
                        </Link>
                        <Button className="w-full sm:w-auto" onClick={() => setShowQuickScheduleModal(true)}>
                            <Plus className="mr-2 h-4 w-4" />
                            <span className="hidden sm:inline">Agendamento Rápido</span>
                            <span className="sm:hidden">Agendar</span>
                        </Button>
                        <Link href={route('paciente.agendamentos.create')}>
                            <Button variant="outline" className="w-full sm:w-auto">
                                <Calendar className="mr-2 h-4 w-4" />
                                <span className="hidden sm:inline">Agendamento Completo</span>
                                <span className="sm:hidden">Completo</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Estatísticas */}
                <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
                    {/* Total */}
                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                        <div className="mb-4 flex items-center gap-3">
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <Calendar className="h-5 w-5" />
                            </Badge>
                            <div>
                                <p className="text-sm font-medium text-foreground">Total</p>
                                <p className="text-xs text-muted-foreground">Agendamentos</p>
                            </div>
                        </div>
                        <div>
                            <div className="mb-1 text-2xl font-semibold text-primary">{stats.total}</div>
                            <p className="text-sm text-muted-foreground">Total de agendamentos</p>
                        </div>
                    </div>

                    {/* Agendados */}
                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                        <div className="mb-4 flex items-center gap-3">
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <Clock className="h-5 w-5" />
                            </Badge>
                            <div>
                                <p className="text-sm font-medium text-foreground">Agendados</p>
                                <p className="text-xs text-muted-foreground">Pendentes</p>
                            </div>
                        </div>
                        <div>
                            <div className="mb-1 text-2xl font-semibold text-blue-600">{stats.agendados}</div>
                            <p className="text-sm text-muted-foreground">Aguardando atendimento</p>
                        </div>
                    </div>

                    {/* Concluídos */}
                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                        <div className="mb-4 flex items-center gap-3">
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <CheckCircle className="h-5 w-5" />
                            </Badge>
                            <div>
                                <p className="text-sm font-medium text-foreground">Concluídos</p>
                                <p className="text-xs text-muted-foreground">Finalizados</p>
                            </div>
                        </div>
                        <div>
                            <div className="mb-1 text-2xl font-semibold text-green-600">{stats.concluidos}</div>
                            <p className="text-sm text-muted-foreground">Sessões realizadas</p>
                        </div>
                    </div>

                    {/* Cancelados */}
                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                        <div className="mb-4 flex items-center gap-3">
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <X className="h-5 w-5" />
                            </Badge>
                            <div>
                                <p className="text-sm font-medium text-foreground">Cancelados</p>
                                <p className="text-xs text-muted-foreground">Não realizados</p>
                            </div>
                        </div>
                        <div>
                            <div className="mb-1 text-2xl font-semibold text-red-600">{stats.cancelados}</div>
                            <p className="text-sm text-muted-foreground">Agendamentos cancelados</p>
                        </div>
                    </div>
                </div>

                {/* Filtros */}
                <div className="flex items-end gap-4">
                    <Select value={data.status || undefined} onValueChange={(value) => setData('status', value || undefined)}>
                        <SelectTrigger className="w-48">
                            <SelectValue placeholder="Filtrar por status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos os status</SelectItem>
                            <SelectItem value="agendado">Agendado</SelectItem>
                            <SelectItem value="confirmado">Confirmado</SelectItem>
                            <SelectItem value="concluido">Concluído</SelectItem>
                            <SelectItem value="cancelado">Cancelado</SelectItem>
                        </SelectContent>
                    </Select>

                    <Select value={data.periodo || undefined} onValueChange={(value) => setData('periodo', value || undefined)}>
                        <SelectTrigger className="w-48">
                            <SelectValue placeholder="Filtrar por período" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos os períodos</SelectItem>
                            <SelectItem value="proximos">Próximos</SelectItem>
                            <SelectItem value="passados">Passados</SelectItem>
                            <SelectItem value="mes_atual">Mês atual</SelectItem>
                        </SelectContent>
                    </Select>

                    <Button onClick={handleFilter}>
                        <Search className="mr-2 h-4 w-4" />
                        Filtrar
                    </Button>
                </div>

                {/* Lista de Agendamentos */}
                <div className="space-y-4">
                    {agendamentos.data.map((agendamento) => (
                        <Card key={agendamento.id}>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                                            <User className="h-6 w-6 text-blue-600" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold">{agendamento.fisioterapeuta.user.name}</h3>
                                            <p className="text-sm text-muted-foreground">{formatDate(agendamento.data_hora)}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatTime(agendamento.data_hora)} • {agendamento.duracao} minutos
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3">
                                        <span className={`rounded-full px-3 py-1 text-xs ${getStatusColor(agendamento.status)}`}>
                                            {getStatusText(agendamento.status)}
                                        </span>

                                        <div className="flex items-center gap-2">
                                            <Link href={route('paciente.agendamentos.show', agendamento.id)}>
                                                <Button variant="ghost" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </Link>

                                            {agendamento.status === 'concluido' && agendamento.relatorio_sessao && (
                                                <Link href={route('paciente.agendamentos.report', agendamento.id)}>
                                                    <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700">
                                                        <FileText className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            )}

                                            {agendamento.status === 'concluido' && !agendamento.avaliacao && (
                                                <Link href={route('paciente.agendamentos.evaluate', agendamento.id)}>
                                                    <Button variant="ghost" size="sm" className="text-yellow-600 hover:text-yellow-700">
                                                        <Star className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            )}

                                            {(agendamento.status === 'agendado' || agendamento.status === 'confirmado') && (
                                                <Link href={route('paciente.agendamentos.reschedule', agendamento.id)}>
                                                    <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                                                        <RotateCcw className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            )}

                                            {agendamento.status === 'agendado' && (
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleCancel(agendamento.id)}
                                                    className="text-red-600 hover:text-red-700"
                                                >
                                                    <X className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {agendamento.observacoes && (
                                    <div className="mt-4 border-t pt-4">
                                        <p className="text-sm text-muted-foreground">
                                            <strong>Observações:</strong> {agendamento.observacoes}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {agendamentos.data.length === 0 && (
                    <div className="py-12 text-center">
                        <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="mt-2 text-muted-foreground">Nenhum agendamento encontrado</p>
                        <Link href={route('paciente.agendamentos.create')} className="mt-4 inline-block">
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Fazer Primeiro Agendamento
                            </Button>
                        </Link>
                    </div>
                )}

                {/* Paginação */}
                {agendamentos.links && agendamentos.data.length > 0 && (
                    <div className="flex items-center justify-between">
                        <p className="text-sm text-muted-foreground">
                            Mostrando {agendamentos.meta?.from || 1} a {agendamentos.meta?.to || agendamentos.data.length} de{' '}
                            {agendamentos.meta?.total || agendamentos.data.length} resultados
                        </p>
                        <div className="flex gap-2">
                            {agendamentos.links.map((link: any, index: number) => (
                                <Button
                                    key={index}
                                    variant={link.active ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => link.url && router.get(link.url)}
                                    disabled={!link.url}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>

            {/* Modal de Agendamento Rápido */}
            {showQuickScheduleModal && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
                    <div className="mx-4 w-full max-w-md rounded-lg bg-white p-6">
                        <div className="mb-4 flex items-center justify-between">
                            <h2 className="text-lg font-semibold">Agendamento Rápido</h2>
                            <Button variant="ghost" size="sm" onClick={() => setShowQuickScheduleModal(false)}>
                                <X className="h-4 w-4" />
                            </Button>
                        </div>

                        <div className="space-y-4">
                            <p className="text-sm text-muted-foreground">Para um agendamento mais rápido, você pode:</p>

                            <div className="space-y-3">
                                <Link href={route('paciente.fisioterapeutas.index')}>
                                    <Button variant="outline" className="w-full justify-start">
                                        <User className="mr-2 h-4 w-4" />
                                        Escolher Fisioterapeuta e Agendar
                                    </Button>
                                </Link>

                                <Link href={route('paciente.agendamentos.create')}>
                                    <Button className="w-full justify-start">
                                        <Calendar className="mr-2 h-4 w-4" />
                                        Agendamento Completo
                                    </Button>
                                </Link>
                            </div>

                            <div className="border-t pt-2">
                                <Button variant="ghost" className="w-full" onClick={() => setShowQuickScheduleModal(false)}>
                                    Cancelar
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </AppLayout>
    );
}
