<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('medico_feriados_config', function (Blueprint $table) {
            $table->id();
            $table->foreignId('fisioterapeuta_id')->constrained('users')->onDelete('cascade');
            $table->boolean('trabalha_feriados_nacionais')->default(false);
            $table->boolean('trabalha_feriados_estaduais')->default(false);
            $table->boolean('trabalha_feriados_municipais')->default(false);
            $table->json('feriados_excecoes')->nullable(); // Array de IDs de feriados específicos com configuração diferente
            $table->timestamps();
            
            // Constraint para garantir um registro por fisioterapeuta
            $table->unique('fisioterapeuta_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('medico_feriados_config');
    }
};
