<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PagamentoComissao extends Model
{
    use HasFactory;

    protected $table = 'pagamentos_comissao';

    protected $fillable = [
        'afiliado_id',
        'valor_solicitado',
        'status',
        'data_solicitacao',
        'data_aprovacao',
        'data_pagamento',
        'aprovado_por',
        'metodo_pagamento',
        'dados_pagamento',
        'observacoes_admin',
        'observacoes_afiliado',
        'comprovante_pagamento',
    ];

    protected $casts = [
        'valor_solicitado' => 'decimal:2',
        'data_solicitacao' => 'datetime',
        'data_aprovacao' => 'datetime',
        'data_pagamento' => 'datetime',
        'dados_pagamento' => 'array',
    ];

    // Relacionamentos
    public function afiliado()
    {
        return $this->belongsTo(Afiliado::class);
    }

    public function aprovadoPor()
    {
        return $this->belongsTo(User::class, 'aprovado_por');
    }

    // Scopes
    public function scopePendentes($query)
    {
        return $query->where('status', 'pendente');
    }

    public function scopeAprovados($query)
    {
        return $query->where('status', 'aprovado');
    }

    public function scopePagos($query)
    {
        return $query->where('status', 'pago');
    }

    public function scopeRejeitados($query)
    {
        return $query->where('status', 'rejeitado');
    }

    // Métodos auxiliares
    public function aprovar($adminId, $observacoes = null)
    {
        $this->update([
            'status' => 'aprovado',
            'data_aprovacao' => now(),
            'aprovado_por' => $adminId,
            'observacoes_admin' => $observacoes,
        ]);
    }

    public function rejeitar($adminId, $observacoes)
    {
        $this->update([
            'status' => 'rejeitado',
            'data_aprovacao' => now(),
            'aprovado_por' => $adminId,
            'observacoes_admin' => $observacoes,
        ]);
    }

    public function marcarComoPago($comprovante = null)
    {
        $this->update([
            'status' => 'pago',
            'data_pagamento' => now(),
            'comprovante_pagamento' => $comprovante,
        ]);
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pendente' => ['class' => 'bg-yellow-100 text-yellow-800', 'text' => 'Pendente'],
            'aprovado' => ['class' => 'bg-blue-100 text-blue-800', 'text' => 'Aprovado'],
            'pago' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Pago'],
            'rejeitado' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Rejeitado'],
        ];

        return $badges[$this->status] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => 'Desconhecido'];
    }

    public function getFormattedValueAttribute()
    {
        return 'R$ ' . number_format($this->valor_solicitado, 2, ',', '.');
    }
}
