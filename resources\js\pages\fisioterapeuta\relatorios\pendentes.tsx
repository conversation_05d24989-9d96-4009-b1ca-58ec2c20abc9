import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import { Calendar, Clock, FileText, Plus, User } from 'lucide-react';

interface Agendamento {
    id: number;
    scheduled_at: string;
    status: string;
    observacoes: string;
    paciente: {
        id: number;
        name: string;
        email: string;
        telefone: string;
    };
    endereco: string;
    valor: number;
    plano: {
        nome: string;
    };
}

interface Props {
    agendamentosPendentes: Agendamento[];
}

export default function RelatoriosPendentes({ agendamentosPendentes }: Props) {
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    return (
        <AppLayout>
            <Head title="Relatórios Pendentes" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-3xl font-bold tracking-tight">Relatórios Pendentes</h2>
                        <p className="text-muted-foreground">Sessões concluídas que precisam de relatório</p>
                    </div>
                </div>

                {agendamentosPendentes.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <FileText className="mb-4 h-12 w-12 text-muted-foreground" />
                            <h3 className="mb-2 text-lg font-semibold">Nenhum relatório pendente</h3>
                            <p className="text-center text-muted-foreground">Todas as suas sessões concluídas já possuem relatórios.</p>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="grid gap-6">
                        {agendamentosPendentes.map((agendamento) => (
                            <Card key={agendamento.id} className="transition-shadow hover:shadow-md">
                                <CardContent className="p-6">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1 space-y-4">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center space-x-3">
                                                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                                        <User className="h-5 w-5 text-primary" />
                                                    </div>
                                                    <div>
                                                        <h3 className="text-lg font-semibold">{agendamento.paciente.name}</h3>
                                                        <div className="flex items-center text-sm text-muted-foreground">
                                                            <Calendar className="mr-1 h-4 w-4" />
                                                            {formatDate(agendamento.scheduled_at)}
                                                        </div>
                                                    </div>
                                                </div>
                                                <Badge variant="secondary">{agendamento.status}</Badge>
                                            </div>

                                            <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
                                                <div className="flex items-center">
                                                    <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                                                    <span>60 minutos</span>
                                                </div>
                                                <div className="flex items-center">
                                                    <User className="mr-2 h-4 w-4 text-muted-foreground" />
                                                    <span>{agendamento.paciente.telefone}</span>
                                                </div>
                                                <div className="flex items-center">
                                                    <FileText className="mr-2 h-4 w-4 text-muted-foreground" />
                                                    <span>{agendamento.endereco}</span>
                                                </div>
                                            </div>

                                            {agendamento.observacoes && <p className="text-sm text-muted-foreground">{agendamento.observacoes}</p>}
                                        </div>

                                        <div className="ml-6 space-y-2 text-right">
                                            <div className="text-lg font-semibold">{formatCurrency(agendamento.valor)}</div>
                                            <div className="text-sm text-muted-foreground">{agendamento.plano?.nome || 'Plano não informado'}</div>
                                            <Link href={route('fisioterapeuta.relatorios.create', agendamento.id)} className="inline-flex">
                                                <Button size="sm">
                                                    <Plus className="mr-2 h-4 w-4" />
                                                    Criar Relatório
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
