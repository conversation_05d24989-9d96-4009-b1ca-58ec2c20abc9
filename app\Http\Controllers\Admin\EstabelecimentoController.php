<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Estabelecimento;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EstabelecimentoController extends Controller
{
    /**
     * Display a listing of establishments.
     */
    public function index(Request $request)
    {
        $query = Estabelecimento::with('user');

        // Filtro por status ativo
        if ($request->filled('ativo') && $request->ativo !== 'all') {
            $query->where('ativo', $request->boolean('ativo'));
        }

        // Filtro por plano ativo
        if ($request->filled('plano_ativo') && $request->plano_ativo !== 'all') {
            $query->where('plano_ativo', $request->boolean('plano_ativo'));
        }

        // Filtro por categoria
        if ($request->filled('categoria') && $request->categoria !== 'all') {
            $query->where('categoria', $request->categoria);
        }

        // Busca por nome, cidade ou email do usuário
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('nome', 'like', '%' . $request->search . '%')
                  ->orWhere('cidade', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function ($userQuery) use ($request) {
                      $userQuery->where('email', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $estabelecimentos = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        // Estatísticas
        $stats = [
            'total' => Estabelecimento::count(),
            'ativos' => Estabelecimento::where('ativo', true)->count(),
            'com_plano' => Estabelecimento::where('plano_ativo', true)->count(),
            'sem_plano' => Estabelecimento::where('plano_ativo', false)->count(),
        ];

        return Inertia::render('admin/estabelecimentos/index', [
            'estabelecimentos' => $estabelecimentos,
            'filters' => $request->only(['ativo', 'plano_ativo', 'categoria', 'search']),
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for creating a new establishment.
     */
    public function create()
    {
        // Buscar usuários com role empresa que ainda não têm estabelecimento
        $usuariosDisponiveis = User::where('role', 'empresa')
            ->whereDoesntHave('estabelecimento')
            ->get(['id', 'name', 'email']);

        return Inertia::render('admin/estabelecimentos/create', [
            'usuariosDisponiveis' => $usuariosDisponiveis,
        ]);
    }

    /**
     * Store a newly created establishment.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'nome' => 'required|string|max:255',
            'categoria' => 'required|in:dentista,farmacia,fisioterapia,outros',
            'descricao' => 'nullable|string|max:1000',
            'telefone' => 'nullable|string|max:20',
            'whatsapp' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'endereco' => 'required|string|max:255',
            'cidade' => 'required|string|max:100',
            'estado' => 'required|string|size:2',
            'cep' => 'required|string|size:9',
            'servicos_oferecidos' => 'nullable|string|max:1000',
            'site' => 'nullable|url|max:255',
            'instagram' => 'nullable|string|max:255',
            'facebook' => 'nullable|string|max:255',
            'ativo' => 'boolean',
            'plano_ativo' => 'boolean',
        ]);

        $estabelecimento = Estabelecimento::create($validated);

        return redirect()->route('admin.estabelecimentos.index')
            ->with('success', 'Estabelecimento criado com sucesso!');
    }

    /**
     * Display the specified establishment.
     */
    public function show(Estabelecimento $estabelecimento)
    {
        $estabelecimento->load('user');

        return Inertia::render('admin/estabelecimentos/show', [
            'estabelecimento' => $estabelecimento,
        ]);
    }

    /**
     * Show the form for editing the specified establishment.
     */
    public function edit(Estabelecimento $estabelecimento)
    {
        $estabelecimento->load('user');

        // Buscar usuários empresa disponíveis (incluindo o atual)
        $usuariosDisponiveis = User::where('role', 'empresa')
            ->where(function ($query) use ($estabelecimento) {
                $query->whereDoesntHave('estabelecimento')
                      ->orWhere('id', $estabelecimento->user_id);
            })
            ->get(['id', 'name', 'email']);

        return Inertia::render('admin/estabelecimentos/edit', [
            'estabelecimento' => $estabelecimento,
            'usuariosDisponiveis' => $usuariosDisponiveis,
        ]);
    }

    /**
     * Update the specified establishment.
     */
    public function update(Request $request, Estabelecimento $estabelecimento)
    {
        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'nome' => 'required|string|max:255',
            'categoria' => 'required|in:dentista,farmacia,fisioterapia,outros',
            'descricao' => 'nullable|string|max:1000',
            'telefone' => 'nullable|string|max:20',
            'whatsapp' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'endereco' => 'required|string|max:255',
            'cidade' => 'required|string|max:100',
            'estado' => 'required|string|size:2',
            'cep' => 'required|string|size:9',
            'servicos_oferecidos' => 'nullable|string|max:1000',
            'site' => 'nullable|url|max:255',
            'instagram' => 'nullable|string|max:255',
            'facebook' => 'nullable|string|max:255',
            'ativo' => 'boolean',
            'plano_ativo' => 'boolean',
        ]);

        $estabelecimento->update($validated);

        return redirect()->route('admin.estabelecimentos.index')
            ->with('success', 'Estabelecimento atualizado com sucesso!');
    }

    /**
     * Remove the specified establishment.
     */
    public function destroy(Estabelecimento $estabelecimento)
    {
        $estabelecimento->delete();

        return redirect()->route('admin.estabelecimentos.index')
            ->with('success', 'Estabelecimento removido com sucesso!');
    }

    /**
     * Toggle establishment active status.
     */
    public function toggleAtivo(Estabelecimento $estabelecimento)
    {
        $estabelecimento->update([
            'ativo' => !$estabelecimento->ativo
        ]);

        $status = $estabelecimento->ativo ? 'ativado' : 'desativado';

        return back()->with('success', "Estabelecimento {$status} com sucesso!");
    }

    /**
     * Toggle establishment plan status.
     */
    public function togglePlano(Estabelecimento $estabelecimento)
    {
        $estabelecimento->update([
            'plano_ativo' => !$estabelecimento->plano_ativo,
            'plano_vencimento' => $estabelecimento->plano_ativo ? null : now()->addMonth()
        ]);

        $status = $estabelecimento->plano_ativo ? 'ativado' : 'desativado';

        return back()->with('success', "Plano {$status} com sucesso!");
    }
}
