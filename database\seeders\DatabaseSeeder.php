<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Executar seeders na ordem correta devido às dependências
        $this->call([
            PlanoSeeder::class,
            FisioterapeutaSeeder::class,
            PacienteSeeder::class,
            AssinaturaSeeder::class,
            PagamentoSeeder::class,
            AgendamentoSeeder::class,
            DisponibilidadeSeeder::class,
            NotificacaoSeeder::class,
            EstabelecimentoSeeder::class,
        ]);

        // Criar usuário admin padrão
        User::create([
            'name' => 'Administrador',
            'email' => '<EMAIL>',
            'password' => bcrypt('admin123'),
            'role' => 'admin',
            'active' => true,
        ]);

        $this->command->info('✅ Seeders executados com sucesso!');
        $this->command->info('📊 Dados de teste criados:');
        $this->command->info('   - 1 Administrador');
        $this->command->info('   - 5 Fisioterapeutas');
        $this->command->info('   - 8 Pacientes');
        $this->command->info('   - 2 Planos');
        $this->command->info('   - ~6 Assinaturas');
        $this->command->info('   - ~40 Pagamentos (histórico de 6 meses)');
        $this->command->info('   - ~30 Agendamentos');
        $this->command->info('   - Disponibilidades para 4 semanas');
        $this->command->info('   - Notificações variadas');
        $this->command->info('');
        $this->command->info('🔑 Credenciais de acesso:');
        $this->command->info('   Admin: <EMAIL> / admin123');
        $this->command->info('   Fisioterapeutas: [nome]@f4fisio.com / fisio123');
        $this->command->info('   Pacientes: [nome]@email.com / paciente123');
    }
}
