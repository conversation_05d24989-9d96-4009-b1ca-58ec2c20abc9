<?php

use App\Http\Middleware\CheckCompleteProfile;
use App\Http\Middleware\CheckFisioterapeutaProfile;
use App\Http\Middleware\CheckPacienteMedicalData;
use App\Http\Middleware\CheckSubscription;
use App\Http\Middleware\HandleAppearance;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\RoleMiddleware;
use App\Http\Middleware\VerifyCsrfToken;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: ['appearance', 'sidebar_state', 'affiliate_ref', 'coupon_code']);

        $middleware->validateCsrfTokens(except: [
            '/api/estabelecimentos/buscar',
            '/api/estabelecimento/*/contato',
            '/webhook/mercadopago',
        ]);

        $middleware->web(append: [
            HandleAppearance::class,
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
            \App\Http\Middleware\HandleUserMode::class,
            \App\Http\Middleware\TrackAffiliateReferral::class,
        ]);

        $middleware->alias([
            'role' => RoleMiddleware::class,
            'custom.throttle' => \App\Http\Middleware\CustomRateLimiter::class,
            'check.subscription' => CheckSubscription::class,
            'user.mode' => \App\Http\Middleware\HandleUserMode::class,
            'check.afiliado' => \App\Http\Middleware\CheckAfiliadoAccess::class,
            'check.empresa' => \App\Http\Middleware\CheckEmpresaAccess::class,
            'check.fisioterapeuta' => CheckFisioterapeutaProfile::class,
            'check.paciente.medical' => CheckPacienteMedicalData::class,
            'check.profile' => CheckCompleteProfile::class,
            'sanitize.input' => \App\Http\Middleware\SanitizeInput::class,
            'form.rate.limit' => \App\Http\Middleware\FormRateLimit::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
