import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PublicLayout from '@/layouts/public-layout';
import { Link } from '@inertiajs/react';
import { Check, MapPin, Search, Users, Zap } from 'lucide-react';

export default function PlanosPublicos() {
    const planos = [
        {
            id: 'busca',
            nome: 'Plano Busca',
            preco: 14.80,
            periodo: 'mês',
            descricao: 'Encontre fisioterapeutas, farmácias e dentistas próximos a você',
            popular: false,
            recursos: [
                'Busca ilimitada de profissionais',
                'Filtros avançados por especialidade',
                'Visualização de avaliações',
                'Informações de contato',
                'Localização no mapa',
                'Horários de funcionamento',
                'Suporte por chat',
            ],
            icone: Search,
            cor: 'blue',
            ctaText: 'Come<PERSON>r <PERSON>',
        },
        {
            id: 'pessoal',
            nome: 'Plano Pessoal',
            preco: 180.00,
            periodo: 'mês',
            descricao: 'Atendimento fisioterapêutico domiciliar personalizado',
            popular: true,
            recursos: [
                'Todos os recursos do Plano Busca',
                'Agendamento de consultas domiciliares',
                'Fisioterapeutas qualificados',
                'Atendimento personalizado',
                'Relatórios de evolução',
                'Suporte prioritário',
                'Cancelamento flexível',
                'Histórico médico digital',
            ],
            icone: Users,
            cor: 'green',
            ctaText: 'Contratar Plano',
        },
        {
            id: 'empresarial',
            nome: 'Plano Empresarial',
            preco: 640.00,
            periodo: 'mês',
            descricao: 'Solução completa de fisioterapia para sua empresa',
            popular: false,
            recursos: [
                'Todos os recursos do Plano Pessoal',
                'Atendimento para até 20 funcionários',
                'Gestão centralizada',
                'Relatórios empresariais',
                'Programa de prevenção',
                'Treinamentos ergonômicos',
                'Suporte dedicado',
                'Desconto em consultas extras',
                'Dashboard administrativo',
            ],
            icone: Zap,
            cor: 'purple',
            ctaText: 'Solicitar Proposta',
        },
    ];

    const getCorClasses = (cor: string) => {
        const cores = {
            blue: {
                bg: 'bg-blue-50',
                border: 'border-blue-200',
                text: 'text-blue-600',
                button: 'bg-blue-600 hover:bg-blue-700',
                icon: 'text-blue-600',
            },
            green: {
                bg: 'bg-green-50',
                border: 'border-green-200',
                text: 'text-green-600',
                button: 'bg-green-600 hover:bg-green-700',
                icon: 'text-green-600',
            },
            purple: {
                bg: 'bg-purple-50',
                border: 'border-purple-200',
                text: 'text-purple-600',
                button: 'bg-purple-600 hover:bg-purple-700',
                icon: 'text-purple-600',
            },
        };
        return cores[cor as keyof typeof cores] || cores.blue;
    };

    return (
        <PublicLayout
            title="Planos e Preços - F4 Fisio"
            description="Conheça nossos planos de fisioterapia domiciliar e encontre o ideal para você ou sua empresa."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-32">
                    <div className="relative z-10 mx-auto w-full max-w-6xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-4xl font-medium text-balance md:text-5xl">
                                Escolha o Plano Ideal
                                <span className="block text-primary">Para Sua Saúde</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Desde busca de profissionais até atendimento domiciliar completo. 
                                Temos a solução perfeita para suas necessidades.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Planos */}
            <section className="bg-background py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="grid gap-8 lg:grid-cols-3">
                        {planos.map((plano) => {
                            const cores = getCorClasses(plano.cor);
                            const IconeComponent = plano.icone;

                            return (
                                <Card 
                                    key={plano.id} 
                                    className={`relative ${plano.popular ? 'ring-2 ring-primary shadow-lg scale-105' : ''}`}
                                >
                                    {plano.popular && (
                                        <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                                            <Badge className="bg-primary text-primary-foreground">
                                                Mais Popular
                                            </Badge>
                                        </div>
                                    )}

                                    <CardHeader className="text-center">
                                        <div className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full ${cores.bg}`}>
                                            <IconeComponent className={`h-8 w-8 ${cores.icon}`} />
                                        </div>
                                        <CardTitle className="text-2xl">{plano.nome}</CardTitle>
                                        <p className="text-muted-foreground">{plano.descricao}</p>
                                    </CardHeader>

                                    <CardContent className="space-y-6">
                                        {/* Preço */}
                                        <div className="text-center">
                                            <div className="flex items-baseline justify-center gap-1">
                                                <span className="text-sm text-muted-foreground">R$</span>
                                                <span className="text-4xl font-bold">
                                                    {plano.preco.toFixed(2).replace('.', ',')}
                                                </span>
                                                <span className="text-sm text-muted-foreground">/{plano.periodo}</span>
                                            </div>
                                        </div>

                                        {/* Recursos */}
                                        <div className="space-y-3">
                                            {plano.recursos.map((recurso, index) => (
                                                <div key={index} className="flex items-start gap-3">
                                                    <Check className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-600" />
                                                    <span className="text-sm">{recurso}</span>
                                                </div>
                                            ))}
                                        </div>

                                        {/* CTA */}
                                        <div className="pt-4">
                                            <Button 
                                                asChild 
                                                className={`w-full ${cores.button} text-white`}
                                                size="lg"
                                            >
                                                <Link href="/register">
                                                    {plano.ctaText}
                                                </Link>
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            );
                        })}
                    </div>
                </div>
            </section>

            {/* Comparação */}
            <section className="bg-muted/30 py-20">
                <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">
                            Compare os Planos
                        </h2>
                        <p className="mx-auto mt-4 max-w-2xl text-xl text-balance text-muted-foreground">
                            Veja em detalhes o que cada plano oferece
                        </p>
                    </div>

                    <div className="mt-12 overflow-hidden rounded-lg border bg-card shadow-sm">
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-muted/50">
                                    <tr>
                                        <th className="px-6 py-4 text-left font-medium">Recursos</th>
                                        <th className="px-6 py-4 text-center font-medium">Busca</th>
                                        <th className="px-6 py-4 text-center font-medium">Pessoal</th>
                                        <th className="px-6 py-4 text-center font-medium">Empresarial</th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y">
                                    <tr>
                                        <td className="px-6 py-4 font-medium">Busca de profissionais</td>
                                        <td className="px-6 py-4 text-center"><Check className="mx-auto h-5 w-5 text-green-600" /></td>
                                        <td className="px-6 py-4 text-center"><Check className="mx-auto h-5 w-5 text-green-600" /></td>
                                        <td className="px-6 py-4 text-center"><Check className="mx-auto h-5 w-5 text-green-600" /></td>
                                    </tr>
                                    <tr>
                                        <td className="px-6 py-4 font-medium">Agendamento domiciliar</td>
                                        <td className="px-6 py-4 text-center">-</td>
                                        <td className="px-6 py-4 text-center"><Check className="mx-auto h-5 w-5 text-green-600" /></td>
                                        <td className="px-6 py-4 text-center"><Check className="mx-auto h-5 w-5 text-green-600" /></td>
                                    </tr>
                                    <tr>
                                        <td className="px-6 py-4 font-medium">Relatórios de evolução</td>
                                        <td className="px-6 py-4 text-center">-</td>
                                        <td className="px-6 py-4 text-center"><Check className="mx-auto h-5 w-5 text-green-600" /></td>
                                        <td className="px-6 py-4 text-center"><Check className="mx-auto h-5 w-5 text-green-600" /></td>
                                    </tr>
                                    <tr>
                                        <td className="px-6 py-4 font-medium">Gestão empresarial</td>
                                        <td className="px-6 py-4 text-center">-</td>
                                        <td className="px-6 py-4 text-center">-</td>
                                        <td className="px-6 py-4 text-center"><Check className="mx-auto h-5 w-5 text-green-600" /></td>
                                    </tr>
                                    <tr>
                                        <td className="px-6 py-4 font-medium">Número de usuários</td>
                                        <td className="px-6 py-4 text-center">1</td>
                                        <td className="px-6 py-4 text-center">1</td>
                                        <td className="px-6 py-4 text-center">Até 20</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ */}
            <section className="bg-background py-20">
                <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">
                            Dúvidas Frequentes
                        </h2>
                        <p className="mx-auto mt-4 max-w-2xl text-xl text-balance text-muted-foreground">
                            Tire suas dúvidas sobre nossos planos
                        </p>
                    </div>

                    <div className="mt-12 space-y-6">
                        <Card>
                            <CardContent className="p-6">
                                <h3 className="font-semibold">Posso cancelar meu plano a qualquer momento?</h3>
                                <p className="mt-2 text-muted-foreground">
                                    Sim, todos os nossos planos podem ser cancelados a qualquer momento sem multa. 
                                    O cancelamento é efetivado no final do período já pago.
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <h3 className="font-semibold">Como funciona o atendimento domiciliar?</h3>
                                <p className="mt-2 text-muted-foreground">
                                    Nossos fisioterapeutas vão até sua casa no horário agendado, levando todos os 
                                    equipamentos necessários para o atendimento. Você só precisa ter um espaço adequado.
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <h3 className="font-semibold">O Plano Empresarial tem desconto para mais funcionários?</h3>
                                <p className="mt-2 text-muted-foreground">
                                    Sim, para empresas com mais de 20 funcionários, oferecemos planos personalizados 
                                    com descontos progressivos. Entre em contato para uma proposta customizada.
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            {/* CTA Final */}
            <section className="bg-primary py-20 text-primary-foreground">
                <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl font-medium text-balance md:text-4xl">
                        Pronto para Começar?
                    </h2>
                    <p className="mx-auto mt-4 max-w-2xl text-xl text-balance opacity-90">
                        Cadastre-se agora e comece a cuidar da sua saúde com a F4 Fisio
                    </p>
                    <div className="mt-8 flex flex-col gap-3 sm:flex-row sm:justify-center">
                        <Button size="lg" variant="secondary" asChild>
                            <Link href="/register">
                                Criar Conta Grátis
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" asChild>
                            <Link href="/contato">
                                Falar com Especialista
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
