<?php

use App\Models\User;
use Illuminate\Support\Facades\Auth;

test('login screen can be rendered', function () {
    $response = $this->get('/login');

    $response->assertStatus(200);
});

test('users can authenticate using the login screen', function () {
    $user = User::factory()->create([
        'active' => true,
        'role' => 'paciente',
        'email' => '<EMAIL>',
        'password' => 'password',
        'email_verified_at' => now()
    ]);

    // Test direct authentication first
    expect(Auth::attempt(['email' => '<EMAIL>', 'password' => 'password']))->toBeTrue();
    Auth::logout(); // Clean up

    // Test the login endpoint with session
    $response = $this->withSession(['_token' => 'test-token'])
        ->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            '_token' => 'test-token'
        ]);

    $this->assertAuthenticated();
    $response->assertStatus(302);
});

test('users can not authenticate with invalid password', function () {
    $user = User::factory()->create();

    $this->post('/login', [
        'email' => $user->email,
        'password' => 'wrong-password',
    ]);

    $this->assertGuest();
});

test('users can logout', function () {
    $user = User::factory()->create([
        'active' => true,
        'role' => 'paciente'
    ]);

    $response = $this->actingAs($user)
        ->withSession(['_token' => 'test-token'])
        ->post('/logout', ['_token' => 'test-token']);

    $this->assertGuest();
    $response->assertRedirect('/');
});