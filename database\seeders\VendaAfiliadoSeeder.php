<?php

namespace Database\Seeders;

use App\Models\VendaAfiliado;
use App\Models\Afiliado;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VendaAfiliadoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buscar um afiliado aprovado
        $afiliado = Afiliado::where('status', 'aprovado')->first();

        if (!$afiliado) {
            $this->command->info('Nenhum afiliado aprovado encontrado. Execute o seeder de afiliados primeiro.');
            return;
        }

        // Buscar um usuário para as vendas
        $usuario = User::where('role', 'paciente')->first();
        if (!$usuario) {
            $usuario = User::first(); // Fallback para qualquer usuário
        }

        // Criar algumas vendas de exemplo
        $vendas = [
            [
                'valor_venda' => 299.90,
                'comissao_percentual' => 10,
                'valor_comissao' => 29.99,
                'status' => 'confirmada',
                'data_venda' => now()->subDays(30),
            ],
            [
                'valor_venda' => 199.90,
                'comissao_percentual' => 10,
                'valor_comissao' => 19.99,
                'status' => 'confirmada',
                'data_venda' => now()->subDays(25),
            ],
            [
                'valor_venda' => 399.90,
                'comissao_percentual' => 10,
                'valor_comissao' => 39.99,
                'status' => 'confirmada',
                'data_venda' => now()->subDays(20),
            ],
            [
                'valor_venda' => 149.90,
                'comissao_percentual' => 10,
                'valor_comissao' => 14.99,
                'status' => 'confirmada',
                'data_venda' => now()->subDays(15),
            ],
            [
                'valor_venda' => 249.90,
                'comissao_percentual' => 10,
                'valor_comissao' => 24.99,
                'status' => 'confirmada',
                'data_venda' => now()->subDays(10),
            ],
        ];

        foreach ($vendas as $venda) {
            VendaAfiliado::create([
                'afiliado_id' => $afiliado->id,
                'user_id' => $usuario->id,
                'assinatura_id' => null, // Pode ser null para vendas diretas
                'plano_tipo' => 'mensal',
                'valor_venda' => $venda['valor_venda'],
                'comissao' => $venda['valor_comissao'],
                'percentual_comissao' => $venda['comissao_percentual'],
                'status' => $venda['status'],
                'data_confirmacao' => $venda['data_venda'],
                'observacoes' => 'Venda de teste para demonstração',
            ]);
        }

        $this->command->info('Vendas de afiliado criadas com sucesso!');
        $this->command->info('Total de comissões: R$ ' . number_format(array_sum(array_column($vendas, 'valor_comissao')), 2, ',', '.'));
    }
}
