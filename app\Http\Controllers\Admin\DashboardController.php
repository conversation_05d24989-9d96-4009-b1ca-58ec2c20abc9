<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Fisioterapeuta;
use App\Models\Agendamento;
use App\Models\Assinatura;
use App\Models\Estabelecimento;
use App\Models\Pagamento;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Estatísticas gerais
        $totalPacientes = User::where('role', 'paciente')->where('active', true)->count();
        $totalFisioterapeutas = User::where('role', 'fisioterapeuta')->where('active', true)->count();
        $totalEstabelecimentos = Estabelecimento::where('ativo', true)->count();
        $agendamentosHoje = Agendamento::whereDate('scheduled_at', Carbon::today())->count();
        $receitaMensal = Pagamento::where('status', 'pago')
            ->whereMonth('paid_at', Carbon::now()->month)
            ->whereYear('paid_at', Carbon::now()->year)
            ->sum('amount');

        // Agendamentos recentes
        $agendamentosRecentes = Agendamento::with(['paciente', 'fisioterapeuta'])
            ->orderBy('scheduled_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($agendamento) {
                return [
                    'id' => $agendamento->id,
                    'paciente' => $agendamento->paciente ? $agendamento->paciente->name : 'N/A',
                    'fisioterapeuta' => $agendamento->fisioterapeuta ? $agendamento->fisioterapeuta->name : 'N/A',
                    'data_hora' => $agendamento->scheduled_at ? $agendamento->scheduled_at->format('d/m/Y H:i') : 'N/A',
                    'status' => $agendamento->status,
                    'endereco' => $agendamento->address ?? 'N/A',
                ];
            });

        // Estatísticas mensais (últimos 6 meses)
        $estatisticasMensais = [];
        for ($i = 5; $i >= 0; $i--) {
            $mes = Carbon::now()->subMonths($i);
            $estatisticasMensais[] = [
                'mes' => $mes->format('M/Y'),
                'agendamentos' => Agendamento::whereMonth('created_at', $mes->month)
                    ->whereYear('created_at', $mes->year)
                    ->count(),
                'receita' => Pagamento::where('status', 'pago')
                    ->whereMonth('paid_at', $mes->month)
                    ->whereYear('paid_at', $mes->year)
                    ->sum('amount'),
            ];
        }

        return Inertia::render('admin/dashboard', [
            'stats' => [
                'totalPacientes' => $totalPacientes,
                'totalFisioterapeutas' => $totalFisioterapeutas,
                'totalEstabelecimentos' => $totalEstabelecimentos,
                'agendamentosHoje' => $agendamentosHoje,
                'receitaMensal' => $receitaMensal,
            ],
            'agendamentosRecentes' => $agendamentosRecentes,
            'estatisticasMensais' => $estatisticasMensais,
        ]);
    }
}
