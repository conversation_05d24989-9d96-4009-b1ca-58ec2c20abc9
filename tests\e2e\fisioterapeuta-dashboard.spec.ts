import { test, expect } from '@playwright/test';
import { 
  ensureFisioterapeutaAuthenticated,
  navigateToFisioterapeutaPage,
  verifyFisioterapeutaLayout,
  waitForPageLoad,
  checkResponsiveness
} from './helpers/fisioterapeuta-helpers';

test.describe('Fisioterapeuta - Dashboard', () => {
  
  test.beforeEach(async ({ page }) => {
    await ensureFisioterapeutaAuthenticated(page);
    await navigateToFisioterapeutaPage(page, 'dashboard');
  });

  test('deve carregar dashboard com estatísticas principais', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Verificar se o título da página está correto
    await expect(page.locator('h1, h2')).toContainText(/dashboard|painel/i);
    
    // Verificar se as estatísticas principais estão visíveis
    const statsCards = page.locator('[data-testid="stats-card"], .stats-card, .card');
    await expect(statsCards).toHaveCountGreaterThan(0);
    
    // Verificar se há indicadores numéricos (estatísticas)
    const numbers = page.locator('text=/\\d+/');
    await expect(numbers).toHaveCountGreaterThan(0);
  });

  test('deve exibir próximos agendamentos', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar seção de próximos agendamentos
    const agendamentosSection = page.locator('text="Próximos Agendamentos", text="Agendamentos", [data-testid="proximos-agendamentos"]');
    
    if (await agendamentosSection.count() > 0) {
      await expect(agendamentosSection).toBeVisible();
      
      // Verificar se há lista de agendamentos ou mensagem de "nenhum agendamento"
      const agendamentosList = page.locator('.agendamento-item, .appointment-item, [data-testid="agendamento"]');
      const noAgendamentosMessage = page.locator('text="Nenhum agendamento", text="Sem agendamentos"');
      
      const hasAgendamentos = await agendamentosList.count() > 0;
      const hasNoAgendamentosMessage = await noAgendamentosMessage.count() > 0;
      
      expect(hasAgendamentos || hasNoAgendamentosMessage).toBeTruthy();
    }
  });

  test('deve exibir agendamentos de hoje', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar seção de agendamentos de hoje
    const agendamentosHojeSection = page.locator('text="Hoje", text="Agendamentos de Hoje", [data-testid="agendamentos-hoje"]');
    
    if (await agendamentosHojeSection.count() > 0) {
      await expect(agendamentosHojeSection).toBeVisible();
    }
  });

  test('deve exibir pacientes recentes', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar seção de pacientes recentes
    const pacientesSection = page.locator('text="Pacientes Recentes", text="Últimos Pacientes", [data-testid="pacientes-recentes"]');
    
    if (await pacientesSection.count() > 0) {
      await expect(pacientesSection).toBeVisible();
      
      // Verificar se há lista de pacientes ou mensagem de "nenhum paciente"
      const pacientesList = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
      const noPacientesMessage = page.locator('text="Nenhum paciente", text="Sem pacientes"');
      
      const hasPacientes = await pacientesList.count() > 0;
      const hasNoPacientesMessage = await noPacientesMessage.count() > 0;
      
      expect(hasPacientes || hasNoPacientesMessage).toBeTruthy();
    }
  });

  test('deve exibir avaliações recentes', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar seção de avaliações recentes
    const avaliacoesSection = page.locator('text="Avaliações Recentes", text="Últimas Avaliações", [data-testid="avaliacoes-recentes"]');
    
    if (await avaliacoesSection.count() > 0) {
      await expect(avaliacoesSection).toBeVisible();
    }
  });

  test('deve exibir estatísticas da semana', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar gráficos ou estatísticas da semana
    const estatisticasSection = page.locator('text="Estatísticas", text="Semana", [data-testid="estatisticas-semana"]');
    
    if (await estatisticasSection.count() > 0) {
      await expect(estatisticasSection).toBeVisible();
    }
  });

  test('deve exibir alertas importantes', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar seção de alertas
    const alertasSection = page.locator('text="Alertas", text="Avisos", [data-testid="alertas"], .alert, .warning');
    
    if (await alertasSection.count() > 0) {
      await expect(alertasSection).toBeVisible();
    }
  });

  test('deve permitir navegação para outras páginas através do menu', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Testar links de navegação principais
    const navigationLinks = [
      { text: 'Agenda', expectedUrl: '/agenda' },
      { text: 'Pacientes', expectedUrl: '/pacientes' },
      { text: 'Perfil', expectedUrl: '/perfil' },
      { text: 'Relatórios', expectedUrl: '/relatorios' }
    ];
    
    for (const link of navigationLinks) {
      // Voltar para dashboard
      await navigateToFisioterapeutaPage(page, 'dashboard');
      
      // Procurar e clicar no link
      const linkElement = page.locator(`a:has-text("${link.text}"), button:has-text("${link.text}")`);
      
      if (await linkElement.count() > 0) {
        await linkElement.first().click();
        await waitForPageLoad(page);
        
        // Verificar se navegou para a página correta
        expect(page.url()).toContain(link.expectedUrl);
      }
    }
  });

  test('deve permitir ações rápidas nos agendamentos', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar agendamentos com botões de ação
    const agendamentoItems = page.locator('.agendamento-item, .appointment-item, [data-testid="agendamento"]');
    
    if (await agendamentoItems.count() > 0) {
      const firstAgendamento = agendamentoItems.first();
      
      // Procurar botões de ação (confirmar, iniciar, etc.)
      const actionButtons = firstAgendamento.locator('button, a');
      
      if (await actionButtons.count() > 0) {
        // Verificar se os botões estão visíveis
        await expect(actionButtons.first()).toBeVisible();
      }
    }
  });

  test('deve atualizar dados em tempo real', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Aguardar um tempo para verificar se há atualizações automáticas
    await page.waitForTimeout(5000);
    
    // Verificar se a página ainda está responsiva
    await expect(page.locator('body')).toBeVisible();
  });

  test('deve ser responsivo em diferentes tamanhos de tela', async ({ page }) => {
    await checkResponsiveness(page);
    
    // Verificar se elementos principais ainda estão visíveis em mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await waitForPageLoad(page);
    
    await expect(page.locator('h1, h2')).toBeVisible();
    await verifyFisioterapeutaLayout(page);
  });

  test('deve carregar sem erros de JavaScript', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.reload();
    await waitForPageLoad(page);
    
    // Verificar se não há erros críticos de JavaScript
    const criticalErrors = errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_')
    );
    
    expect(criticalErrors.length).toBe(0);
  });

  test('deve exibir informações do perfil do fisioterapeuta', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar informações do perfil (nome, avatar, etc.)
    const profileInfo = page.locator('[data-testid="profile-info"], .profile, .user-info');
    
    if (await profileInfo.count() > 0) {
      await expect(profileInfo).toBeVisible();
    }
    
    // Verificar se há nome do fisioterapeuta visível
    const userName = page.locator('text=/Dr\.|Dra\.|Fisioterapeuta/');
    if (await userName.count() > 0) {
      await expect(userName.first()).toBeVisible();
    }
  });

  test('deve permitir acesso rápido a funcionalidades principais', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar botões de ação rápida
    const quickActions = page.locator('button:has-text("Novo"), button:has-text("Criar"), button:has-text("Adicionar")');
    
    if (await quickActions.count() > 0) {
      await expect(quickActions.first()).toBeVisible();
    }
  });

  test('deve exibir métricas de performance', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar métricas como rating, número de sessões, etc.
    const metricas = page.locator('text=/\d+\.\d+/, text=/\d+%/, text=/R\$\s*\d+/');
    
    if (await metricas.count() > 0) {
      await expect(metricas.first()).toBeVisible();
    }
  });
});
