import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import PublicLayout from '@/layouts/public-layout';

export default function Servicos() {
    return (
        <PublicLayout title="Serviços - F4 Fisio" description="Conheça todos os serviços de fisioterapia domiciliar oferecidos pela F4 Fisio.">
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-36">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-5xl font-medium text-balance md:text-6xl">
                                Serviços de
                                <span className="block text-primary">Fisioterapia Domiciliar</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Tratamentos especializados no conforto da sua casa, com equipamentos profissionais e fisioterapeutas credenciados para
                                sua recuperação completa.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Principais Serviços */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Especialidades em Fisioterapia Domiciliar</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Tratamentos especializados adaptados para o ambiente domiciliar, com a mesma eficácia das clínicas tradicionais
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                        {/* Fisioterapia Ortopédica Domiciliar */}
                        <div className="relative rounded-2xl border border-primary/20 bg-primary/5 p-8 shadow-sm">
                            <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-primary px-4 py-1 text-sm font-semibold text-primary-foreground">
                                Mais Popular
                            </div>
                            <div className="mb-6">
                                <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                                        />
                                    </svg>
                                </Badge>
                            </div>
                            <h3 className="mb-4 text-xl font-medium text-foreground">Reabilitação Ortopédica</h3>
                            <p className="mb-4 text-sm leading-relaxed text-muted-foreground">
                                Recuperação pós-cirúrgica e tratamento de lesões ortopédicas no conforto do seu lar, com equipamentos especializados e
                                técnicas avançadas.
                            </p>
                            <ul className="space-y-2 text-sm text-muted-foreground">
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Pós-operatório de joelho, quadril e ombro</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Fraturas e imobilizações</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Artrose e artrite</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Dores lombares e cervicais</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Tendinites e bursites</span>
                                </li>
                            </ul>
                        </div>

                        {/* Fisioterapia Neurológica Domiciliar */}
                        <div className="rounded-2xl border bg-card p-8 shadow-sm">
                            <div className="mb-6">
                                <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                                        />
                                    </svg>
                                </Badge>
                            </div>
                            <h3 className="mb-4 text-xl font-medium text-foreground">Reabilitação Neurológica</h3>
                            <p className="mb-4 text-sm leading-relaxed text-muted-foreground">
                                Tratamento especializado para condições neurológicas no ambiente familiar, promovendo maior adesão e resultados mais
                                efetivos.
                            </p>
                            <ul className="space-y-2 text-sm text-muted-foreground">
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Sequelas de AVC (Derrame)</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Doença de Parkinson</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Esclerose múltipla</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Paralisia cerebral</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Lesões medulares</span>
                                </li>
                            </ul>
                        </div>

                        {/* Fisioterapia Respiratória Domiciliar */}
                        <div className="relative rounded-2xl border bg-card p-8 shadow-sm">
                            <div className="absolute -top-3 right-4 rounded-full bg-green-600 px-3 py-1 text-xs font-semibold text-white">Novo</div>
                            <div className="mb-6">
                                <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                    </svg>
                                </Badge>
                            </div>
                            <h3 className="mb-4 text-xl font-medium text-foreground">Fisioterapia Respiratória</h3>
                            <p className="mb-4 text-sm leading-relaxed text-muted-foreground">
                                Reabilitação pulmonar especializada em casa, ideal para pacientes com dificuldades respiratórias e recuperação
                                pós-COVID.
                            </p>
                            <ul className="space-y-2 text-sm text-muted-foreground">
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>DPOC e enfisema</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Sequelas pós-COVID</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Asma e bronquite</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Pneumonia</span>
                                </li>
                                <li className="flex items-start gap-2">
                                    <span className="text-primary">•</span>
                                    <span>Preparação pré-cirúrgica</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            {/* Como Funciona o Processo */}
            <section className="bg-muted/30 py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Como Funciona Nosso Atendimento</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Processo profissional e seguro para iniciar sua fisioterapia domiciliar
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
                        {/* Passo 1 */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                    />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Contato Inicial</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Entre em contato via WhatsApp ou telefone. Coletamos informações sobre sua condição e necessidades.
                            </p>
                        </div>

                        {/* Passo 2 */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                                    />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Avaliação Domiciliar</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Fisioterapeuta credenciado realiza avaliação completa no seu domicílio, com anamnese e testes funcionais.
                            </p>
                        </div>

                        {/* Passo 3 */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                                    />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Plano Terapêutico</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Desenvolvimento de plano individualizado com objetivos, frequência e técnicas específicas para seu caso.
                            </p>
                        </div>

                        {/* Passo 4 */}
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                    />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Tratamento e Evolução</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Sessões regulares com acompanhamento da evolução e ajustes no plano conforme necessário.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Equipamentos e Recursos */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Equipamentos Profissionais</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Tecnologia de ponta transportada até sua casa para tratamentos eficazes e seguros
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Eletroterapia</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                TENS, FES, ultrassom terapêutico e correntes analgésicas portáteis.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                    />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Exercícios Terapêuticos</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Therabands, bolas suíças, pesos e acessórios para reabilitação.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2a2 2 0 002-2V5a2 2 0 00-2-2z"
                                    />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Terapia Manual</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Técnicas especializadas de mobilização articular e liberação miofascial.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="size-12" className="h-12 w-12">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                                    />
                                </svg>
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Avaliação Completa</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Goniômetros, dinamômetros e escalas de avaliação funcional.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="bg-muted/30 py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mx-auto max-w-4xl rounded-2xl border bg-card p-6 text-center shadow-sm sm:p-8">
                        <h2 className="text-2xl font-medium text-card-foreground">Pronto para Iniciar seu Tratamento?</h2>
                        <p className="mt-4 text-xl text-balance text-muted-foreground">
                            Agende sua avaliação domiciliar gratuita e descubra como podemos ajudar na sua recuperação.
                        </p>
                        <div className="mt-8 flex flex-col gap-3 sm:flex-row sm:justify-center">
                            <Button asChild>
                                <a
                                    href="https://wa.me/5511978196207?text=Gostaria de agendar uma avaliação domiciliar gratuita."
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                        />
                                    </svg>
                                    Agendar Avaliação Gratuita
                                </a>
                            </Button>
                            <Button variant="outline" asChild>
                                <a href="/#planos">
                                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                                        />
                                    </svg>
                                    Ver Planos e Preços
                                </a>
                            </Button>
                        </div>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
