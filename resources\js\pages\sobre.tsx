import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import PublicLayout from '@/layouts/public-layout';
import { Clock, Eye, Heart, Shield, Stethoscope, Target, Zap } from 'lucide-react';

export default function Sobre() {
    return (
        <PublicLayout
            title="Sobre - F4 Fisio"
            description="Conheça a F4 Fisio, nossa missão, valores e equipe especializada em fisioterapia domiciliar."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-36">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-5xl font-medium text-balance md:text-6xl">
                                Sobre a<span className="block text-primary">F4 Fisio</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Somos uma empresa especializada em fisioterapia domiciliar, comprometida em levar cuidado de qualidade até você, no
                                conforto e segurança da sua casa.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Nossa História */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="grid gap-12 lg:grid-cols-2 lg:items-center">
                        <div>
                            <h2 className="text-3xl font-medium text-balance md:text-4xl">Nossa História</h2>
                            <p className="mt-6 text-base leading-relaxed text-muted-foreground">
                                A F4 Fisio foi fundada em 2019 com o objetivo de revolucionar o atendimento fisioterapêutico domiciliar. Identificamos
                                que muitos pacientes, especialmente idosos, pós-cirúrgicos e pessoas com mobilidade reduzida, enfrentavam grandes
                                dificuldades para acessar tratamentos de qualidade.
                            </p>
                            <p className="mt-4 text-base leading-relaxed text-muted-foreground">
                                Nossa proposta é levar fisioterapia especializada diretamente ao seu lar, eliminando barreiras de deslocamento e
                                proporcionando um ambiente familiar e acolhedor para sua recuperação. Acreditamos que o tratamento em casa
                                potencializa os resultados terapêuticos.
                            </p>
                            <p className="mt-4 text-base leading-relaxed text-muted-foreground">
                                Todos os nossos fisioterapeutas são registrados no CREFITO, possuem especialização em atendimento domiciliar e
                                contamos com seguro de responsabilidade civil. Utilizamos equipamentos portáteis de última geração para garantir a
                                mesma eficácia dos tratamentos clínicos.
                            </p>
                        </div>
                        <div className="relative">
                            <div className="aspect-square rounded-2xl bg-gradient-to-br from-primary/10 to-muted/20 p-8 shadow-sm">
                                <div className="flex h-full items-center justify-center">
                                    <div className="text-center">
                                        <div className="mb-4 text-5xl font-medium text-primary">500+</div>
                                        <p className="text-lg font-medium text-foreground">Pacientes Atendidos</p>
                                        <div className="mt-8 grid grid-cols-2 gap-6 text-sm">
                                            <div>
                                                <div className="text-2xl font-medium text-primary">95%</div>
                                                <p className="text-muted-foreground">Satisfação</p>
                                            </div>
                                            <div>
                                                <div className="text-2xl font-medium text-primary">5</div>
                                                <p className="text-muted-foreground">Anos</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Missão, Visão e Valores */}
            <section className="bg-muted/30 py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Nossos Pilares</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Os valores que nos guiam em cada atendimento
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 md:grid-cols-3">
                        {/* Missão */}
                        <div className="rounded-2xl bg-card p-8 text-center shadow-sm">
                            <Badge variant="gradient" size="icon-lg" className="mx-auto mb-6 h-12 w-12">
                                <Zap className="h-6 w-6" />
                            </Badge>
                            <h3 className="mb-4 text-xl font-medium text-card-foreground">Missão</h3>
                            <p className="text-sm leading-relaxed text-muted-foreground">
                                Democratizar o acesso à fisioterapia de qualidade, levando tratamentos especializados ao domicílio com segurança,
                                eficácia e humanização, respeitando as necessidades individuais de cada paciente.
                            </p>
                        </div>

                        {/* Visão */}
                        <div className="rounded-2xl bg-card p-8 text-center shadow-sm">
                            <Badge variant="gradient" size="icon-lg" className="mx-auto mb-6 h-12 w-12">
                                <Eye className="h-6 w-6" />
                            </Badge>
                            <h3 className="mb-4 text-xl font-medium text-card-foreground">Visão</h3>
                            <p className="text-sm leading-relaxed text-muted-foreground">
                                Ser a principal referência em fisioterapia domiciliar na Grande São Paulo, reconhecida pela excelência técnica,
                                inovação e compromisso com a recuperação integral dos pacientes.
                            </p>
                        </div>

                        {/* Valores */}
                        <div className="rounded-2xl bg-card p-8 text-center shadow-sm">
                            <Badge variant="gradient" size="icon-lg" className="mx-auto mb-6 h-12 w-12">
                                <Heart className="h-6 w-6" />
                            </Badge>
                            <h3 className="mb-4 text-xl font-medium text-card-foreground">Valores</h3>
                            <div className="text-left text-sm leading-relaxed text-muted-foreground">
                                <ul className="space-y-2">
                                    <li className="flex items-start gap-2">
                                        <span className="text-primary">•</span>
                                        <span>
                                            <strong className="text-foreground">Humanização:</strong> Cuidado centrado na pessoa
                                        </span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-primary">•</span>
                                        <span>
                                            <strong className="text-foreground">Excelência:</strong> Qualidade técnica comprovada
                                        </span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-primary">•</span>
                                        <span>
                                            <strong className="text-foreground">Ética:</strong> Transparência e responsabilidade
                                        </span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-primary">•</span>
                                        <span>
                                            <strong className="text-foreground">Inovação:</strong> Tecnologia a serviço da saúde
                                        </span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="text-primary">•</span>
                                        <span>
                                            <strong className="text-foreground">Compromisso:</strong> Resultados efetivos
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Diferenciais */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Nossos Diferenciais</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            O que nos torna únicos no mercado de fisioterapia domiciliar
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                <Shield className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Segurança Garantida</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Fisioterapeutas credenciados CREFITO com seguro de responsabilidade civil e protocolos de segurança.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                <Stethoscope className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Equipamentos Portáteis</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Aparelhos de eletroterapia, ultrassom e recursos modernos transportados até sua residência.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                <Clock className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Horários Flexíveis</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Atendimento de segunda a sábado, incluindo manhã, tarde e noite conforme sua disponibilidade.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                                <svg className="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                                    />
                                </svg>
                            </div>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Acompanhamento Detalhado</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Relatórios de evolução, reavaliações periódicas e comunicação constante sobre seu progresso.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                <Target className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Suporte Contínuo</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Comunicação direta via WhatsApp para dúvidas, orientações e suporte entre as sessões.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="bg-muted/30 py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mx-auto max-w-4xl rounded-2xl border bg-card p-6 text-center shadow-sm sm:p-8">
                        <h2 className="text-2xl font-medium text-card-foreground">Pronto para Começar?</h2>
                        <p className="mt-4 text-xl text-balance text-muted-foreground">
                            Entre em contato conosco e descubra como podemos ajudar na sua recuperação e bem-estar.
                        </p>
                        <div className="mt-8 flex flex-col gap-3 sm:flex-row sm:justify-center">
                            <Button asChild>
                                <a
                                    href="https://wa.me/5511978196207?text=Olá! Gostaria de agendar uma avaliação domiciliar gratuita."
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <Heart className="h-4 w-4" />
                                    Agendar Avaliação
                                </a>
                            </Button>
                            <Button variant="outline" asChild>
                                <a href="/contato">
                                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                        />
                                    </svg>
                                    Falar no WhatsApp
                                </a>
                            </Button>
                        </div>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
