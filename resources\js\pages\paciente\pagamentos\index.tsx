import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { AlertTriangle, Calendar, CheckCircle, Clock, CreditCard, Download, Eye, Filter } from 'lucide-react';

interface Pagamento {
    id: number;
    valor: number;
    status: 'pendente' | 'pago' | 'falhou' | 'cancelado';
    forma_pagamento: string;
    data_vencimento: string;
    data_pagamento?: string;
    transaction_id?: string;
    formatted_valor: string;
    formatted_data_vencimento: string;
    formatted_data_pagamento?: string;
    assinatura: {
        id: number;
        plano: {
            id: number;
            name: string;
            price: number;
        };
    };
}

interface Stats {
    total_pago: number;
    total_pendente: number;
    total_vencido: number;
    count_pendente: number;
}

interface Props {
    pagamentos: {
        data: Pagamento[];
        links: any[];
        meta?: {
            total?: number;
            current_page?: number;
            last_page?: number;
            per_page?: number;
            from?: number;
            to?: number;
        };
    };
    filters: {
        status?: string;
        periodo?: string;
    };
    stats: Stats;
    proximosVencimentos: Pagamento[];
}

export default function PagamentosIndex({ pagamentos, filters, stats, proximosVencimentos }: Props) {
    const handleFilterChange = (key: string, value: string) => {
        router.get(
            route('paciente.pagamentos.index'),
            {
                ...filters,
                [key]: value === filters[key as keyof typeof filters] ? '' : value,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
            pago: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
            falhou: { variant: 'destructive' as const, icon: AlertTriangle, color: 'text-red-600' },
            cancelado: { variant: 'outline' as const, icon: AlertTriangle, color: 'text-gray-600' },
        };

        const config = variants[status as keyof typeof variants] || variants.pendente;
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-3 w-3" />
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getFormaPagamentoBadge = (forma: string) => {
        const formas = {
            cartao_credito: 'Cartão de Crédito',
            cartao_debito: 'Cartão de Débito',
            pix: 'PIX',
            boleto: 'Boleto',
        };

        return (
            <Badge variant="outline" className="flex items-center gap-1">
                <CreditCard className="h-3 w-3" />
                {formas[forma as keyof typeof formas] || forma}
            </Badge>
        );
    };

    const isVencido = (dataVencimento: string, status: string) => {
        return status === 'pendente' && new Date(dataVencimento) < new Date();
    };

    return (
        <AppLayout>
            <Head title="Meus Pagamentos" />

            <div className="px-4 py-6 sm:py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold tracking-tight sm:text-3xl">Meus Pagamentos</h2>
                        <p className="text-muted-foreground">Acompanhe seus pagamentos e faturas</p>
                    </div>

                    {/* Estatísticas */}
                    <div className="mb-8 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Pago</CardTitle>
                                <CheckCircle className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-600">
                                    R$ {stats.total_pago.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                </div>
                                <p className="text-xs text-muted-foreground">Valor total já pago</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
                                <Clock className="h-4 w-4 text-yellow-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-yellow-600">
                                    R$ {stats.total_pendente.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                </div>
                                <p className="text-xs text-muted-foreground">{stats.count_pendente} pagamentos pendentes</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Vencidos</CardTitle>
                                <AlertTriangle className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-red-600">
                                    R$ {stats.total_vencido.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                </div>
                                <p className="text-xs text-muted-foreground">Pagamentos em atraso</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Próximos</CardTitle>
                                <Calendar className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-blue-600">{proximosVencimentos.length}</div>
                                <p className="text-xs text-muted-foreground">Próximos 30 dias</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Próximos Vencimentos */}
                    {proximosVencimentos.length > 0 && (
                        <Card className="mb-6">
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Calendar className="mr-2 h-5 w-5" />
                                    Próximos Vencimentos
                                </CardTitle>
                                <CardDescription>Pagamentos que vencem nos próximos 30 dias</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {proximosVencimentos.map((pagamento) => (
                                        <div key={pagamento.id} className="flex items-center justify-between rounded-lg border p-3">
                                            <div className="flex items-center gap-4">
                                                <div>
                                                    <p className="font-medium">{pagamento.assinatura.plano.name}</p>
                                                    <p className="text-sm text-gray-600">Vence em {pagamento.formatted_data_vencimento}</p>
                                                </div>
                                                {getStatusBadge(pagamento.status)}
                                            </div>
                                            <div className="flex items-center gap-4">
                                                <span className="text-lg font-bold">{pagamento.formatted_valor}</span>
                                                <Link href={route('paciente.pagamentos.show', pagamento.id)}>
                                                    <Button size="sm">Pagar</Button>
                                                </Link>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Filtros */}
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Filter className="mr-2 h-5 w-5" />
                                Filtros
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-2">
                                <Button
                                    variant={filters.status === 'pendente' ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleFilterChange('status', 'pendente')}
                                >
                                    Pendentes
                                </Button>
                                <Button
                                    variant={filters.status === 'pago' ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleFilterChange('status', 'pago')}
                                >
                                    Pagos
                                </Button>
                                <Button
                                    variant={filters.periodo === 'mes_atual' ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleFilterChange('periodo', 'mes_atual')}
                                >
                                    Este Mês
                                </Button>
                                <Button
                                    variant={filters.periodo === 'ultimos_3_meses' ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleFilterChange('periodo', 'ultimos_3_meses')}
                                >
                                    Últimos 3 Meses
                                </Button>
                                <Button
                                    variant={filters.periodo === 'ano_atual' ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleFilterChange('periodo', 'ano_atual')}
                                >
                                    Este Ano
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Ações da Assinatura */}
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <CreditCard className="mr-2 h-5 w-5" />
                                Gerenciar Assinatura
                            </CardTitle>
                            <CardDescription>Ações relacionadas à sua assinatura</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <h4 className="font-medium">Solicitar Reembolso</h4>
                                    <p className="text-sm text-muted-foreground">Solicite o reembolso de pagamentos realizados nos últimos 30 dias</p>
                                    <Button variant="outline" size="sm">
                                        Solicitar Reembolso
                                    </Button>
                                </div>
                                <div className="space-y-2">
                                    <h4 className="font-medium">Cancelar Assinatura</h4>
                                    <p className="text-sm text-muted-foreground">Cancele sua assinatura a qualquer momento</p>
                                    <Button variant="destructive" size="sm">
                                        Cancelar Assinatura
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Lista de Pagamentos */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Histórico de Pagamentos</CardTitle>
                            <CardDescription>{pagamentos.meta?.total ?? pagamentos.data.length} pagamentos encontrados</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {pagamentos.data.map((pagamento) => (
                                    <div
                                        key={pagamento.id}
                                        className={`flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50 ${
                                            isVencido(pagamento.data_vencimento, pagamento.status) ? 'border-red-200 bg-red-50' : ''
                                        }`}
                                    >
                                        <div className="flex-1">
                                            <div className="mb-2 flex items-center gap-4">
                                                <div className="font-medium">{pagamento.assinatura.plano.name}</div>
                                                {getStatusBadge(pagamento.status)}
                                                {getFormaPagamentoBadge(pagamento.forma_pagamento)}
                                                {isVencido(pagamento.data_vencimento, pagamento.status) && (
                                                    <Badge variant="destructive">
                                                        <AlertTriangle className="mr-1 h-3 w-3" />
                                                        Vencido
                                                    </Badge>
                                                )}
                                            </div>

                                            <div className="flex items-center gap-6 text-sm text-gray-600">
                                                <div className="flex items-center">
                                                    <Calendar className="mr-1 h-4 w-4" />
                                                    <span>Venc: {pagamento.formatted_data_vencimento}</span>
                                                </div>
                                                {pagamento.data_pagamento && (
                                                    <div className="flex items-center">
                                                        <CheckCircle className="mr-1 h-4 w-4 text-green-600" />
                                                        <span>Pago: {pagamento.formatted_data_pagamento}</span>
                                                    </div>
                                                )}
                                                <div className="text-lg font-bold">{pagamento.formatted_valor}</div>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            <Link href={route('paciente.pagamentos.show', pagamento.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </Link>
                                            {pagamento.status === 'pago' && (
                                                <Link href={route('paciente.pagamentos.comprovante', pagamento.id)}>
                                                    <Button variant="outline" size="sm">
                                                        <Download className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            )}
                                        </div>
                                    </div>
                                ))}

                                {pagamentos.data.length === 0 && (
                                    <div className="py-8 text-center text-gray-500">Nenhum pagamento encontrado com os filtros aplicados.</div>
                                )}
                            </div>

                            {/* Paginação */}
                            {pagamentos.links && pagamentos.links.length > 3 && (
                                <div className="mt-6 flex justify-center">
                                    <div className="flex gap-2">
                                        {pagamentos.links.map((link, index) => (
                                            <Link
                                                key={index}
                                                href={link.url || '#'}
                                                className={`rounded-md px-3 py-2 text-sm ${
                                                    link.active ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                                } ${!link.url ? 'cursor-not-allowed opacity-50' : ''}`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
