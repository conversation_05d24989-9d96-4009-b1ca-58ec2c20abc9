import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { AlertTriangle, Check, MessageCircle } from 'lucide-react';
import { useState } from 'react';

interface Plano {
    id: string;
    name: string;
    price: number | null;
    original_price?: number;
    description: string;
    savings?: number;
    features: string[];
    type: string;
    popular?: boolean;
}

interface PlanoAtual {
    type: string;
    name: string;
    status: string;
}

interface PlanosProps {
    planos: Plano[];
    planoAtual?: PlanoAtual;
    hasSubscription: boolean;
}

export default function Planos({ planos, planoAtual, hasSubscription }: PlanosProps) {
    const { data, setData, post, processing } = useForm({
        plano_type: '',
    });

    const [showAvulsaDialog, setShowAvulsaDialog] = useState(false);
    const [showMercadoPagoDialog, setShowMercadoPagoDialog] = useState(false);

    const handleSelectPlan = (planoType: string) => {
        if (planoType === 'avulsa') {
            setShowAvulsaDialog(true);
        } else if (planoType === 'mensal') {
            // Verificar se as APIs do Mercado Pago estão configuradas
            setShowMercadoPagoDialog(true);
        } else if (planoType === 'empresarial') {
            // Redirecionar para WhatsApp
            const message = encodeURIComponent(
                'Olá! Gostaria de saber mais sobre o plano empresarial de fisioterapia domiciliar para grupos/empresas.',
            );
            window.open(`https://wa.me/5511978196207?text=${message}`, '_blank');
        }
    };

    const handleConfirmAvulsa = () => {
        setData('plano_type', 'avulsa');
        post(route('paciente.planos.store'));
        setShowAvulsaDialog(false);
    };

    const handleMercadoPago = () => {
        // Simular que não tem APIs configuradas
        setShowMercadoPagoDialog(false);
        // Aqui seria a integração real com Mercado Pago
        // Por enquanto, apenas mostra o dialog de aviso
    };

    const formatPrice = (price: number | null) => {
        if (price === null) return 'Sob Consulta';
        return `R$ ${price.toFixed(2).replace('.', ',')}`;
    };

    const breadcrumbs = [
        {
            title: 'Início',
            href: '/paciente/dashboard',
        },
        {
            title: 'Escolher Plano',
            href: '/paciente/planos',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Escolher Plano" />

            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30 py-12 sm:py-8">
                <div className="relative">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-3xl font-medium text-balance sm:text-4xl md:text-5xl">
                                Escolha seu <span className="text-primary">Plano</span>
                            </h1>
                            <p className="mx-auto my-6 max-w-3xl text-lg text-balance text-muted-foreground sm:my-8 sm:text-xl">
                                Selecione o plano que melhor atende às suas necessidades de fisioterapia domiciliar.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Plano Atual Section */}
            {hasSubscription && planoAtual && (
                <section className="bg-background py-12">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mx-auto max-w-2xl">
                            <div className="rounded-lg border bg-card p-6 shadow-sm">
                                <div className="flex items-center gap-3">
                                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                        <Check className="h-5 w-5 text-primary" />
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold">Plano Atual</h3>
                                        <p className="text-sm text-muted-foreground">
                                            Você possui o plano <strong>{planoAtual.name}</strong> ativo.
                                            <br />
                                            Você pode trocar de plano a qualquer momento selecionando uma das opções abaixo.
                                        </p>
                                    </div>
                                </div>
                                <div className="mt-4 flex gap-2">
                                    <Button variant="outline" asChild>
                                        <Link href="/paciente/dashboard">Ir para Dashboard</Link>
                                    </Button>
                                    {planoAtual.type !== 'avulsa' && (
                                        <Button variant="outline" asChild>
                                            <Link href={route('paciente.plano.index')}>Gerenciar Plano</Link>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            )}

            {/* Pricing Section */}
            <div className="relative bg-muted/30 py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mx-auto max-w-2xl text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Planos que se adaptam às suas necessidades</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Escolha o plano ideal para seu tratamento de fisioterapia domiciliar. Todos incluem avaliação gratuita.
                        </p>
                    </div>
                    <div className="@container relative mt-12 md:mt-20">
                        <Card className="relative mx-auto max-w-sm @4xl:max-w-full">
                            <div className="grid gap-0 @4xl:min-h-[600px] @4xl:grid-cols-3">
                                {/* Plano Sessão Avulsa */}
                                <div className="flex flex-col @4xl:min-h-full">
                                    <CardHeader className="flex-shrink-0 p-8">
                                        <CardTitle className="font-medium">Sessão Avulsa</CardTitle>
                                        <span className="mt-2 mb-0.5 block text-2xl font-semibold">R$ 180</span>
                                        <CardDescription className="text-sm">Por sessão</CardDescription>
                                    </CardHeader>

                                    <ul role="list" className="flex-grow space-y-3 px-8">
                                        {[
                                            'Sessão de 60 minutos',
                                            'Avaliação básica incluída',
                                            'Equipamentos profissionais',
                                            'Orientações domiciliares',
                                            'Pagamento no ato',
                                        ].map((item, index) => (
                                            <li key={index} className="flex items-center gap-2">
                                                <Check className="size-3 text-primary" strokeWidth={3.5} />
                                                <span className="text-sm">{item}</span>
                                            </li>
                                        ))}
                                    </ul>

                                    <div className="mt-auto border-t px-8 py-4">
                                        {hasSubscription && planoAtual?.type === 'avulsa' ? (
                                            <Button disabled className="w-full" variant="outline">
                                                <Check className="mr-2 h-4 w-4" />
                                                Plano Atual
                                            </Button>
                                        ) : (
                                            <Button
                                                onClick={() => handleSelectPlan('avulsa')}
                                                disabled={processing}
                                                className="w-full"
                                                variant="outline"
                                            >
                                                {processing ? 'Processando...' : hasSubscription ? 'Trocar para este Plano' : 'Confirmar Seleção'}
                                            </Button>
                                        )}
                                    </div>
                                </div>

                                {/* Plano Mensal - Destacado */}
                                <div className="-mx-1 rounded-(--radius) border-transparent bg-background shadow ring-1 ring-foreground/10 @3xl:mx-0 @3xl:-my-3 @4xl:min-h-full">
                                    <div className="relative flex flex-col px-1 @3xl:px-0 @3xl:py-3 @4xl:min-h-full">
                                        <div className="absolute -top-4 left-1/2 -translate-x-1/2 rounded-full bg-primary px-4 py-1 text-sm font-semibold text-primary-foreground">
                                            Mais Popular
                                        </div>
                                        <CardHeader className="flex-shrink-0 p-8">
                                            <CardTitle className="font-medium">Plano Mensal</CardTitle>
                                            <span className="mt-2 mb-0.5 block text-2xl font-semibold">R$ 640</span>
                                            <CardDescription className="text-sm">Por mês</CardDescription>
                                            <div className="flex items-center gap-2 text-sm">
                                                <span className="text-muted-foreground line-through">R$ 720</span>
                                                <Badge variant="secondary" className="text-xs">
                                                    Economize R$ 80
                                                </Badge>
                                            </div>
                                        </CardHeader>

                                        <ul role="list" className="flex-grow space-y-3 px-8">
                                            {[
                                                '4 sessões de 60min por mês',
                                                'Avaliação inicial gratuita',
                                                'Plano terapêutico personalizado',
                                                'Relatórios quinzenais',
                                                'Suporte via WhatsApp',
                                                'Horários flexíveis',
                                                'Sem taxa de adesão',
                                                'Cancelamento flexível',
                                            ].map((item, index) => (
                                                <li key={index} className="flex items-center gap-2">
                                                    <Check className="size-3 text-primary" strokeWidth={3.5} />
                                                    <span className="text-sm">{item}</span>
                                                </li>
                                            ))}
                                        </ul>

                                        <div className="-mx-1 mt-auto border-t px-8 py-4 @3xl:mx-0">
                                            {hasSubscription && planoAtual?.type === 'mensal' ? (
                                                <Button disabled className="w-full">
                                                    <Check className="mr-2 h-4 w-4" />
                                                    Plano Atual
                                                </Button>
                                            ) : (
                                                <Button onClick={() => handleSelectPlan('mensal')} disabled={processing} className="w-full">
                                                    {processing ? 'Processando...' : hasSubscription ? 'Trocar para este Plano' : 'Escolher Plano'}
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Plano Empresarial/Grupos */}
                                <div className="flex flex-col @4xl:min-h-full">
                                    <CardHeader className="flex-shrink-0 p-8">
                                        <CardTitle className="font-medium">Plano Empresarial</CardTitle>
                                        <span className="mt-2 mb-0.5 block text-2xl font-semibold">Sob Consulta</span>
                                        <CardDescription className="text-sm">Para grupos e empresas</CardDescription>
                                        <div className="flex items-center gap-2 text-sm">
                                            <span className="rounded bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                                                Condições especiais
                                            </span>
                                        </div>
                                    </CardHeader>

                                    <ul role="list" className="flex-grow space-y-3 px-8">
                                        {[
                                            'Atendimento para grupos',
                                            'Planos corporativos',
                                            'Desconto por volume',
                                            'Gestão centralizada',
                                            'Relatórios gerenciais',
                                            'Suporte dedicado',
                                            'Flexibilidade de horários',
                                            'Contrato personalizado',
                                        ].map((item, index) => (
                                            <li key={index} className="flex items-center gap-2">
                                                <Check className="size-3 text-primary" strokeWidth={3.5} />
                                                <span className="text-sm">{item}</span>
                                            </li>
                                        ))}
                                    </ul>

                                    <div className="mt-auto border-t px-8 py-4">
                                        {hasSubscription && planoAtual?.type === 'empresarial' ? (
                                            <Button disabled className="w-full" variant="outline">
                                                <Check className="mr-2 h-4 w-4" />
                                                Plano Atual
                                            </Button>
                                        ) : (
                                            <Button
                                                onClick={() => handleSelectPlan('empresarial')}
                                                disabled={processing}
                                                className="w-full"
                                                variant="outline"
                                            >
                                                {processing ? 'Processando...' : 'Entrar em Contato'}
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </Card>
                    </div>

                    <div className="mt-12 text-center">
                        <p className="text-sm text-muted-foreground">
                            Tem dúvidas sobre qual plano escolher?
                            <a
                                href="https://wa.me/5511978196207?text=Olá! Tenho dúvidas sobre os planos de fisioterapia domiciliar."
                                target="_blank"
                                rel="noopener noreferrer"
                                className="ml-1 text-primary hover:underline"
                            >
                                Entre em contato conosco
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            {/* Dialog de Confirmação para Sessão Avulsa */}
            <AlertDialog open={showAvulsaDialog} onOpenChange={setShowAvulsaDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirmar Sessão Avulsa</AlertDialogTitle>
                        <AlertDialogDescription>
                            Você está escolhendo o plano de Sessão Avulsa por R$ 180,00.
                            <br />
                            <br />
                            <strong>Mas que tal economizar R$ 80 por mês?</strong>
                            <br />
                            <br />
                            Com o Plano Mensal você tem 4 sessões por apenas R$ 640,00 (ao invés de R$ 720,00 no avulso), além de avaliação inicial
                            gratuita, plano terapêutico personalizado e suporte via WhatsApp.
                            <br />
                            <br />
                            Você pagará apenas quando agendar suas consultas no plano avulso.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                        <Button
                            variant="outline"
                            onClick={() => {
                                setShowAvulsaDialog(false);
                                handleSelectPlan('mensal');
                            }}
                        >
                            <MessageCircle className="mr-2 h-4 w-4" />
                            Escolher Plano Mensal
                        </Button>
                        <AlertDialogAction onClick={handleConfirmAvulsa}>Confirmar Sessão Avulsa</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Dialog de Aviso - APIs não configuradas */}
            <AlertDialog open={showMercadoPagoDialog} onOpenChange={setShowMercadoPagoDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5 text-amber-500" />
                            APIs não configuradas
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            As APIs do Mercado Pago ainda não foram configuradas no sistema.
                            <br />
                            <br />
                            Entre em contato conosco via WhatsApp para finalizar sua assinatura do plano mensal.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Fechar</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => {
                                const message = encodeURIComponent(
                                    'Olá! Gostaria de assinar o Plano Mensal de fisioterapia domiciliar por R$ 640,00/mês.',
                                );
                                window.open(`https://wa.me/5511978196207?text=${message}`, '_blank');
                                setShowMercadoPagoDialog(false);
                            }}
                        >
                            <MessageCircle className="mr-2 h-4 w-4" />
                            Contatar via WhatsApp
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
