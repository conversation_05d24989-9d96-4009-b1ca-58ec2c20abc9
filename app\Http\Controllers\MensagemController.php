<?php

namespace App\Http\Controllers;

use App\Models\Mensagem;
use App\Models\User;
use App\Http\Requests\MensagemRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class MensagemController extends Controller
{
    /**
     * Listar conversas do usuário
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Buscar conversas do usuário
        $conversas = Mensagem::getConversas($user->id);
        
        // Formatar dados para o frontend
        $conversasFormatadas = $conversas->map(function ($mensagem) use ($user) {
            $outroUsuario = $mensagem->remetente_id == $user->id 
                ? $mensagem->destinatario 
                : $mensagem->remetente;
                
            return [
                'id' => $outroUsuario->id,
                'nome' => $outroUsuario->name,
                'avatar' => $outroUsuario->avatar,
                'role' => $outroUsuario->role,
                'ultima_mensagem' => [
                    'conteudo' => $mensagem->conteudo,
                    'data' => $mensagem->created_at,
                    'lida' => $mensagem->lida,
                    'tipo' => $mensagem->tipo,
                    'eh_remetente' => $mensagem->remetente_id == $user->id,
                ],
                'nao_lidas' => Mensagem::where('destinatario_id', $user->id)
                    ->where('remetente_id', $outroUsuario->id)
                    ->naoLidas()
                    ->count(),
            ];
        })->values();

        // Contar total de mensagens não lidas
        $totalNaoLidas = Mensagem::contarNaoLidas($user->id);

        return Inertia::render('mensagens/index', [
            'conversas' => $conversasFormatadas,
            'totalNaoLidas' => $totalNaoLidas,
        ]);
    }

    /**
     * Exibir conversa específica
     */
    public function show(Request $request, User $usuario)
    {
        $user = Auth::user();
        
        // Verificar se o usuário pode conversar com este usuário
        if (!$this->podeConversar($user, $usuario)) {
            abort(403, 'Você não pode conversar com este usuário.');
        }

        // Buscar mensagens entre os usuários
        $mensagens = Mensagem::entre($user->id, $usuario->id)
            ->with(['remetente', 'destinatario', 'agendamento'])
            ->orderBy('created_at', 'asc')
            ->paginate(50);

        // Marcar mensagens como lidas
        Mensagem::where('destinatario_id', $user->id)
            ->where('remetente_id', $usuario->id)
            ->naoLidas()
            ->update([
                'lida' => true,
                'lida_em' => now(),
            ]);

        // Formatar mensagens para o frontend
        $mensagensFormatadas = $mensagens->getCollection()->map(function ($mensagem) use ($user) {
            return [
                'id' => $mensagem->id,
                'conteudo' => $mensagem->conteudo,
                'tipo' => $mensagem->tipo,
                'data' => $mensagem->created_at,
                'lida' => $mensagem->lida,
                'lida_em' => $mensagem->lida_em,
                'eh_remetente' => $mensagem->remetente_id == $user->id,
                'remetente' => $mensagem->remetente ? [
                    'id' => $mensagem->remetente->id,
                    'nome' => $mensagem->remetente->name,
                    'avatar' => $mensagem->remetente->avatar,
                ] : null,
                'agendamento' => $mensagem->agendamento ? [
                    'id' => $mensagem->agendamento->id,
                    'data' => $mensagem->agendamento->scheduled_at,
                ] : null,
                'anexos' => $mensagem->anexos,
            ];
        });

        $mensagens->setCollection($mensagensFormatadas);

        return Inertia::render('mensagens/show', [
            'usuario' => [
                'id' => $usuario->id,
                'nome' => $usuario->name,
                'avatar' => $usuario->avatar,
                'role' => $usuario->role,
            ],
            'mensagens' => $mensagens,
        ]);
    }

    /**
     * Enviar nova mensagem
     */
    public function store(MensagemRequest $request)
    {
        $user = Auth::user();
        $dados = $request->validated();
        
        $destinatario = User::findOrFail($dados['destinatario_id']);
        
        // Verificar se pode conversar
        if (!$this->podeConversar($user, $destinatario)) {
            return response()->json(['error' => 'Você não pode enviar mensagens para este usuário.'], 403);
        }

        try {
            $mensagem = Mensagem::enviarMensagem(
                $user->id,
                $dados['destinatario_id'],
                $dados['conteudo'],
                $dados['tipo'] ?? Mensagem::TIPO_TEXTO,
                $dados['agendamento_id'] ?? null
            );

            $mensagem->load(['remetente', 'destinatario']);

            // Log para auditoria
            Log::info('Mensagem enviada', [
                'remetente_id' => $user->id,
                'destinatario_id' => $dados['destinatario_id'],
                'tipo' => $mensagem->tipo,
            ]);

            return response()->json([
                'success' => true,
                'mensagem' => [
                    'id' => $mensagem->id,
                    'conteudo' => $mensagem->conteudo,
                    'tipo' => $mensagem->tipo,
                    'data' => $mensagem->created_at,
                    'eh_remetente' => true,
                    'remetente' => [
                        'id' => $user->id,
                        'nome' => $user->name,
                        'avatar' => $user->avatar,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao enviar mensagem', [
                'error' => $e->getMessage(),
                'remetente_id' => $user->id,
                'destinatario_id' => $dados['destinatario_id'],
            ]);

            return response()->json(['error' => 'Erro ao enviar mensagem.'], 500);
        }
    }

    /**
     * Marcar mensagem como lida
     */
    public function marcarLida(Mensagem $mensagem)
    {
        $user = Auth::user();
        
        if ($mensagem->destinatario_id !== $user->id) {
            abort(403, 'Você não pode marcar esta mensagem como lida.');
        }

        $mensagem->marcarComoLida();

        return response()->json(['success' => true]);
    }

    /**
     * Buscar usuários para conversar
     */
    public function buscarUsuarios(Request $request)
    {
        $user = Auth::user();
        $busca = $request->get('q', '');

        $query = User::where('id', '!=', $user->id)
            ->where('active', true);

        if ($busca) {
            $query->where('name', 'like', '%' . $busca . '%');
        }

        // Filtrar por tipo de usuário baseado no role atual
        if ($user->role === 'paciente') {
            $query->where('role', 'fisioterapeuta');
        } elseif ($user->role === 'fisioterapeuta') {
            $query->where('role', 'paciente');
        }

        $usuarios = $query->select('id', 'name', 'avatar', 'role')
            ->limit(10)
            ->get();

        return response()->json($usuarios);
    }

    /**
     * Verificar se dois usuários podem conversar
     */
    private function podeConversar(User $usuario1, User $usuario2)
    {
        // Admins podem conversar com qualquer um
        if ($usuario1->role === 'admin' || $usuario2->role === 'admin') {
            return true;
        }

        // Pacientes só podem conversar com fisioterapeutas (e vice-versa)
        if ($usuario1->role === 'paciente' && $usuario2->role === 'fisioterapeuta') {
            return true;
        }

        if ($usuario1->role === 'fisioterapeuta' && $usuario2->role === 'paciente') {
            return true;
        }

        return false;
    }

    /**
     * Contar mensagens não lidas
     */
    public function contarNaoLidas()
    {
        $user = Auth::user();
        $count = Mensagem::contarNaoLidas($user->id);
        
        return response()->json(['count' => $count]);
    }
}
