import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import PublicLayout from '@/layouts/public-layout';
import { useForm, usePage } from '@inertiajs/react';
import { CheckCircle, Mail, MapPin, MessageCircle, Phone, XCircle } from 'lucide-react';

interface FlashMessages {
    success?: string;
    error?: string;
}

export default function Contato() {
    const { flash } = usePage<{ flash: FlashMessages }>().props;
    const { data, setData, post, processing, errors, reset } = useForm({
        nome: '',
        email: '',
        telefone: '',
        assunto: '',
        mensagem: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('contato.store'), {
            onSuccess: () => {
                reset();
            },
        });
    };

    return (
        <PublicLayout
            title="Contato - F4 Fisio"
            description="Entre em contato com a F4 Fisio. Estamos prontos para esclarecer suas dúvidas e agendar sua consulta."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-36">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-5xl font-medium text-balance md:text-6xl">
                                Entre em
                                <span className="block text-primary">Contato</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Estamos aqui para ajudar você. Entre em contato conosco e descubra como podemos cuidar da sua saúde no conforto da sua
                                casa.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Informações de Contato e Formulário */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="grid gap-12 lg:grid-cols-2">
                        {/* Informações de Contato */}
                        <div>
                            <h2 className="text-3xl font-medium text-foreground">Fale Conosco</h2>

                            <div className="mt-8 space-y-6">
                                {/* Telefone */}
                                <div className="flex items-start gap-4">
                                    <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                        <Phone className="h-6 w-6" />
                                    </Badge>
                                    <div>
                                        <h3 className="text-base font-semibold text-foreground">Telefone</h3>
                                        <p className="text-muted-foreground">(11) 97819-6207</p>
                                        <p className="text-sm text-muted-foreground">Segunda a sexta, 8h às 18h</p>
                                    </div>
                                </div>

                                {/* WhatsApp */}
                                <div className="flex items-start gap-4">
                                    <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                        <MessageCircle className="h-6 w-6" />
                                    </Badge>
                                    <div>
                                        <h3 className="text-base font-semibold text-foreground">WhatsApp</h3>
                                        <p className="text-muted-foreground">(11) 97819-6207</p>
                                        <p className="text-sm text-muted-foreground">Atendimento rápido e direto</p>
                                    </div>
                                </div>

                                {/* Email */}
                                <div className="flex items-start gap-4">
                                    <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                        <Mail className="h-6 w-6" />
                                    </Badge>
                                    <div>
                                        <h3 className="text-base font-semibold text-foreground">Email</h3>
                                        <p className="text-muted-foreground"><EMAIL></p>
                                        <p className="text-sm text-muted-foreground">Resposta em até 24 horas</p>
                                    </div>
                                </div>

                                {/* Endereço */}
                                <div className="flex items-start gap-4">
                                    <Badge variant="gradient" size="icon-lg" className="h-12 w-12">
                                        <MapPin className="h-6 w-6" />
                                    </Badge>
                                    <div>
                                        <h3 className="text-base font-semibold text-foreground">Endereço</h3>
                                        <p className="text-muted-foreground">São Paulo, SP</p>
                                        <p className="text-sm text-muted-foreground">Atendemos toda a região metropolitana</p>
                                    </div>
                                </div>
                            </div>

                            {/* Horários de Atendimento */}
                            <div className="mt-12 rounded-2xl border bg-card p-6 shadow-sm">
                                <h3 className="mb-4 text-xl font-medium text-card-foreground">Horários de Atendimento</h3>
                                <div className="space-y-2 text-sm text-muted-foreground">
                                    <div className="flex justify-between">
                                        <span>Segunda a Sexta:</span>
                                        <span className="font-medium text-foreground">8h às 18h</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Sábado:</span>
                                        <span className="font-medium text-foreground">8h às 14h</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Domingo:</span>
                                        <span className="font-medium text-foreground">Emergências</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Formulário de Contato */}
                        <div>
                            <h2 className="text-3xl font-medium text-foreground">Envie sua Mensagem</h2>

                            {/* Flash Messages */}
                            {flash?.success && (
                                <Alert className="mt-6 border-green-200 bg-green-50">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    <AlertDescription className="text-green-800">{flash.success}</AlertDescription>
                                </Alert>
                            )}

                            {flash?.error && (
                                <Alert className="mt-6 border-red-200 bg-red-50">
                                    <XCircle className="h-4 w-4 text-red-600" />
                                    <AlertDescription className="text-red-800">{flash.error}</AlertDescription>
                                </Alert>
                            )}

                            <form onSubmit={handleSubmit} className="mt-8 space-y-6">
                                <div>
                                    <label htmlFor="nome" className="block text-sm font-medium text-foreground">
                                        Nome Completo *
                                    </label>
                                    <input
                                        type="text"
                                        id="nome"
                                        name="nome"
                                        required
                                        value={data.nome}
                                        onChange={(e) => setData('nome', e.target.value)}
                                        className="mt-2 block w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none"
                                        placeholder="Seu nome completo"
                                    />
                                    {errors.nome && <p className="mt-1 text-sm text-red-600">{errors.nome}</p>}
                                </div>

                                <div className="grid gap-6 md:grid-cols-2">
                                    <div>
                                        <label htmlFor="email" className="block text-sm font-medium text-foreground">
                                            Email *
                                        </label>
                                        <input
                                            type="email"
                                            id="email"
                                            name="email"
                                            required
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            className="mt-2 block w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none"
                                            placeholder="<EMAIL>"
                                        />
                                        {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                                    </div>

                                    <div>
                                        <label htmlFor="telefone" className="block text-sm font-medium text-foreground">
                                            Telefone *
                                        </label>
                                        <input
                                            type="tel"
                                            id="telefone"
                                            name="telefone"
                                            required
                                            value={data.telefone}
                                            onChange={(e) => setData('telefone', e.target.value)}
                                            className="mt-2 block w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none"
                                            placeholder="(11) 99999-9999"
                                        />
                                        {errors.telefone && <p className="mt-1 text-sm text-red-600">{errors.telefone}</p>}
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="assunto" className="block text-sm font-medium text-foreground">
                                        Assunto *
                                    </label>
                                    <select
                                        id="assunto"
                                        name="assunto"
                                        required
                                        value={data.assunto}
                                        onChange={(e) => setData('assunto', e.target.value)}
                                        className="mt-2 block w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none"
                                    >
                                        <option value="">Selecione um assunto</option>
                                        <option value="agendamento">Agendamento de Consulta</option>
                                        <option value="planos">Informações sobre Planos</option>
                                        <option value="duvidas">Dúvidas Gerais</option>
                                        <option value="orcamento">Solicitação de Orçamento</option>
                                        <option value="suporte">Suporte Técnico</option>
                                        <option value="outros">Outros</option>
                                    </select>
                                    {errors.assunto && <p className="mt-1 text-sm text-red-600">{errors.assunto}</p>}
                                </div>

                                <div>
                                    <label htmlFor="mensagem" className="block text-sm font-medium text-foreground">
                                        Mensagem *
                                    </label>
                                    <textarea
                                        id="mensagem"
                                        name="mensagem"
                                        required
                                        rows={6}
                                        value={data.mensagem}
                                        onChange={(e) => setData('mensagem', e.target.value)}
                                        className="mt-2 block w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none"
                                        placeholder="Descreva como podemos ajudar você..."
                                    />
                                    {errors.mensagem && <p className="mt-1 text-sm text-red-600">{errors.mensagem}</p>}
                                </div>

                                <Button type="submit" className="w-full" disabled={processing}>
                                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                    </svg>
                                    {processing ? 'Enviando...' : 'Enviar Mensagem'}
                                </Button>
                            </form>
                        </div>
                    </div>
                </div>
            </section>

            {/* Áreas de Atendimento */}
            <section className="bg-muted/30 py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Áreas de Atendimento</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Atendemos diversas regiões da Grande São Paulo
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 md:grid-cols-3">
                        <div className="rounded-2xl border bg-card p-8 text-center shadow-sm">
                            <div className="flex flex-col items-center">
                                <Badge variant="gradient" size="icon-lg" className="mb-6 h-12 w-12">
                                    <MapPin className="h-6 w-6" />
                                </Badge>
                                <h3 className="mb-4 text-xl font-medium text-card-foreground">São Paulo</h3>
                                <p className="text-sm leading-relaxed text-muted-foreground">
                                    Zona Norte, Sul, Leste, Oeste e Centro. Cobertura completa da capital paulista.
                                </p>
                            </div>
                        </div>

                        <div className="rounded-2xl border bg-card p-8 text-center shadow-sm">
                            <div className="flex flex-col items-center">
                                <Badge variant="gradient" size="icon-lg" className="mb-6 h-12 w-12">
                                    <MapPin className="h-6 w-6" />
                                </Badge>
                                <h3 className="mb-4 text-xl font-medium text-card-foreground">Barueri / Alphaville</h3>
                                <p className="text-sm leading-relaxed text-muted-foreground">
                                    Atendimento especializado na região de Barueri, Alphaville e adjacências.
                                </p>
                            </div>
                        </div>

                        <div className="rounded-2xl border bg-card p-8 text-center shadow-sm">
                            <div className="flex flex-col items-center">
                                <Badge variant="gradient" size="icon-lg" className="mb-6 h-12 w-12">
                                    <MapPin className="h-6 w-6" />
                                </Badge>
                                <h3 className="mb-4 text-xl font-medium text-card-foreground">Osasco / Carapicuíba / Itapevi</h3>
                                <p className="text-sm leading-relaxed text-muted-foreground">
                                    Cobertura completa da região oeste da Grande São Paulo.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mx-auto max-w-4xl rounded-2xl border bg-card p-6 text-center shadow-sm sm:p-8">
                        <h2 className="text-2xl font-medium text-card-foreground">Pronto para Começar?</h2>
                        <p className="mt-4 text-xl text-balance text-muted-foreground">
                            Entre em contato agora mesmo e agende sua primeira consulta.
                        </p>
                        <div className="mt-8 flex flex-col gap-3 sm:flex-row sm:justify-center">
                            <Button asChild>
                                <a href="tel:+5511978196207">
                                    <Phone className="h-4 w-4" />
                                    Ligar Agora
                                </a>
                            </Button>
                            <Button variant="outline" asChild>
                                <a
                                    href="https://wa.me/5511978196207?text=Olá! Gostaria de agendar uma consulta."
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <MessageCircle className="h-4 w-4" />
                                    WhatsApp
                                </a>
                            </Button>
                        </div>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
