<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContatoRecebido extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public array $dados;

    /**
     * Create a new message instance.
     */
    public function __construct(array $dados)
    {
        $this->dados = $dados;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $assuntos = [
            'agendamento' => 'Agendamento de Consulta',
            'planos' => 'Informações sobre Planos',
            'duvidas' => 'Dúvidas Gerais',
            'orcamento' => 'Solicitação de Orçamento',
            'suporte' => 'Suporte Técnico',
            'outros' => 'Outros'
        ];

        $assuntoTexto = $assuntos[$this->dados['assunto']] ?? 'Contato';

        return new Envelope(
            subject: "Novo Contato: {$assuntoTexto} - {$this->dados['nome']}",
            replyTo: [$this->dados['email'] => $this->dados['nome']]
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.contato-recebido',
            with: [
                'dados' => $this->dados,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
