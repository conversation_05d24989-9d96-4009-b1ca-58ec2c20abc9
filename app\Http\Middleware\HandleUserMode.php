<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HandleUserMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            $user = auth()->user();
            
            // Verificar se o modo atual é válido
            $currentMode = session('user_mode', 'normal');
            
            // Se o usuário está em modo afiliado mas não pode mais acessar
            if ($currentMode === 'afiliado' && !$user->canSwitchToAfiliadoMode()) {
                session(['user_mode' => 'normal']);
                $currentMode = 'normal';
            }
            
            // Compartilhar o modo atual com todas as views
            view()->share('currentUserMode', $currentMode);
            
            // Adicionar o modo atual ao request para uso nos controllers
            $request->merge(['current_user_mode' => $currentMode]);
        }

        return $next($request);
    }
}
