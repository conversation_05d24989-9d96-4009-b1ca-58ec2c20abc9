<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Avaliacao extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'avaliacoes';

    protected $fillable = [
        'agendamento_id',
        'paciente_id',
        'fisioterapeuta_id',
        'nota_geral',
        'nota_pontualidade',
        'nota_profissionalismo',
        'nota_eficacia',
        'comentario',
        'recomendaria',
        'pontos_positivos',
        'pontos_melhorar',
        'anonima',
        'aprovada',
        'aprovada_em',
        'aprovada_por',
        // Manter compatibilidade com campos antigos
        'rating',
        'comment',
        'recommend',
    ];

    protected $casts = [
        'nota_geral' => 'integer',
        'nota_pontualidade' => 'integer',
        'nota_profissionalismo' => 'integer',
        'nota_eficacia' => 'integer',
        'recomendaria' => 'boolean',
        'anonima' => 'boolean',
        'aprovada' => 'boolean',
        'aprovada_em' => 'datetime',
        'pontos_positivos' => 'array',
        'pontos_melhorar' => 'array',
        // Compatibilidade
        'recommend' => 'boolean',
        'rating' => 'integer',
    ];

    // Relacionamentos
    public function agendamento()
    {
        return $this->belongsTo(Agendamento::class);
    }

    public function paciente()
    {
        return $this->belongsTo(User::class, 'paciente_id');
    }

    public function fisioterapeuta()
    {
        return $this->belongsTo(User::class, 'fisioterapeuta_id');
    }

    public function aprovadaPor()
    {
        return $this->belongsTo(User::class, 'aprovada_por');
    }

    // Scopes
    public function scopeAprovadas($query)
    {
        return $query->where('aprovada', true);
    }

    public function scopePendentes($query)
    {
        return $query->where('aprovada', false);
    }

    public function scopePublicas($query)
    {
        return $query->where('anonima', false)->aprovadas();
    }

    public function scopePorFisioterapeuta($query, $fisioterapeutaId)
    {
        return $query->where('fisioterapeuta_id', $fisioterapeutaId);
    }

    public function scopePorPaciente($query, $pacienteId)
    {
        return $query->where('paciente_id', $pacienteId);
    }

    public function scopeComNotaMinima($query, $nota)
    {
        return $query->where('nota_geral', '>=', $nota);
    }

    // Métodos auxiliares
    public function getNotaMediaAttribute()
    {
        $notas = [
            $this->nota_pontualidade,
            $this->nota_profissionalismo,
            $this->nota_eficacia
        ];

        $notasValidas = array_filter($notas, fn($nota) => $nota !== null);

        return count($notasValidas) > 0 ? round(array_sum($notasValidas) / count($notasValidas), 1) : 0;
    }

    public function getEstrelas($nota)
    {
        return str_repeat('⭐', $nota) . str_repeat('☆', 5 - $nota);
    }

    public function aprovar($aprovadoPor = null)
    {
        $this->update([
            'aprovada' => true,
            'aprovada_em' => now(),
            'aprovada_por' => $aprovadoPor,
        ]);
    }

    public function reprovar()
    {
        $this->update([
            'aprovada' => false,
            'aprovada_em' => null,
            'aprovada_por' => null,
        ]);
    }

    // Constantes para pontos positivos e negativos
    const PONTOS_POSITIVOS = [
        'pontual' => 'Pontual',
        'profissional' => 'Profissional',
        'atencioso' => 'Atencioso',
        'competente' => 'Competente',
        'paciencia' => 'Paciente',
        'explicativo' => 'Explicativo',
        'organizado' => 'Organizado',
        'motivador' => 'Motivador',
        'respeitoso' => 'Respeitoso',
        'dedicado' => 'Dedicado',
    ];

    const PONTOS_MELHORAR = [
        'pontualidade' => 'Pontualidade',
        'comunicacao' => 'Comunicação',
        'paciencia' => 'Paciência',
        'organizacao' => 'Organização',
        'explicacoes' => 'Explicações',
        'atencao' => 'Atenção',
        'motivacao' => 'Motivação',
        'profissionalismo' => 'Profissionalismo',
        'dedicacao' => 'Dedicação',
        'flexibilidade' => 'Flexibilidade',
    ];

    public static function getPontosPositivos()
    {
        return self::PONTOS_POSITIVOS;
    }

    public static function getPontosMelhorar()
    {
        return self::PONTOS_MELHORAR;
    }

    // Métodos estáticos para estatísticas
    public static function getEstatisticasFisioterapeuta($fisioterapeutaId)
    {
        $avaliacoes = self::porFisioterapeuta($fisioterapeutaId)->aprovadas();

        return [
            'total' => $avaliacoes->count(),
            'nota_media' => round($avaliacoes->avg('nota_geral'), 1),
            'nota_pontualidade' => round($avaliacoes->avg('nota_pontualidade'), 1),
            'nota_profissionalismo' => round($avaliacoes->avg('nota_profissionalismo'), 1),
            'nota_eficacia' => round($avaliacoes->avg('nota_eficacia'), 1),
            'recomendacoes' => $avaliacoes->where('recomendaria', true)->count(),
            'percentual_recomendacao' => $avaliacoes->count() > 0
                ? round(($avaliacoes->where('recomendaria', true)->count() / $avaliacoes->count()) * 100, 1)
                : 0,
            'distribuicao_notas' => [
                5 => $avaliacoes->where('nota_geral', 5)->count(),
                4 => $avaliacoes->where('nota_geral', 4)->count(),
                3 => $avaliacoes->where('nota_geral', 3)->count(),
                2 => $avaliacoes->where('nota_geral', 2)->count(),
                1 => $avaliacoes->where('nota_geral', 1)->count(),
            ],
        ];
    }

    public static function getAvaliacoesRecentes($fisioterapeutaId, $limite = 5)
    {
        return self::porFisioterapeuta($fisioterapeutaId)
            ->aprovadas()
            ->with(['paciente', 'agendamento'])
            ->orderBy('created_at', 'desc')
            ->limit($limite)
            ->get();
    }

    public static function verificarPodeAvaliar($agendamentoId, $pacienteId)
    {
        $agendamento = Agendamento::find($agendamentoId);

        if (!$agendamento) {
            return false;
        }

        // Verificar se o agendamento pertence ao paciente
        if ($agendamento->user_id !== $pacienteId) {
            return false;
        }

        // Verificar se o agendamento foi concluído
        if ($agendamento->status !== 'concluido') {
            return false;
        }

        // Verificar se já existe avaliação
        $avaliacaoExistente = self::where('agendamento_id', $agendamentoId)
            ->where('paciente_id', $pacienteId)
            ->exists();

        return !$avaliacaoExistente;
    }
}
