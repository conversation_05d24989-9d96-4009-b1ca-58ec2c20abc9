<?php

namespace App\Http\Controllers\Empresa;

use App\Http\Controllers\Controller;
use App\Models\Estabelecimento;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;

class SetupController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        // Se já tem estabelecimento, redirecionar para dashboard
        if ($user->estabelecimento) {
            return redirect()->route('empresa.dashboard');
        }

        return Inertia::render('empresa/setup');
    }

    public function store(Request $request)
    {
        $user = auth()->user();
        
        // Verificar se já tem estabelecimento
        if ($user->estabelecimento) {
            return redirect()->route('empresa.dashboard')
                ->with('info', 'Você já possui um estabelecimento cadastrado.');
        }

        $validated = $request->validate([
            'nome' => 'required|string|max:255',
            'categoria' => 'required|in:dentista,farmacia,fisioterapia,outros',
            'descricao' => 'nullable|string|max:1000',
            'telefone' => 'nullable|string|max:20',
            'whatsapp' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'endereco' => 'required|string|max:255',
            'cidade' => 'required|string|max:100',
            'estado' => 'required|string|size:2',
            'cep' => 'required|string|size:9',
            'horario_funcionamento' => 'nullable|array',
            'servicos_oferecidos' => 'nullable|string|max:1000',
            'site' => 'nullable|url|max:255',
            'instagram' => 'nullable|string|max:255',
            'facebook' => 'nullable|string|max:255',
        ]);

        // Obter coordenadas
        $coordenadas = $this->obterCoordenadas($validated['endereco'], $validated['cidade'], $validated['cep']);

        $estabelecimento = Estabelecimento::create([
            'user_id' => $user->id,
            'nome' => $validated['nome'],
            'categoria' => $validated['categoria'],
            'descricao' => $validated['descricao'],
            'telefone' => $validated['telefone'],
            'whatsapp' => $validated['whatsapp'],
            'email' => $validated['email'],
            'endereco' => $validated['endereco'],
            'cidade' => $validated['cidade'],
            'estado' => $validated['estado'],
            'cep' => $validated['cep'],
            'latitude' => $coordenadas['lat'] ?? null,
            'longitude' => $coordenadas['lng'] ?? null,
            'horario_funcionamento' => $validated['horario_funcionamento'],
            'servicos_oferecidos' => $validated['servicos_oferecidos'],
            'site' => $validated['site'],
            'instagram' => $validated['instagram'],
            'facebook' => $validated['facebook'],
            'ativo' => true,
            'plano_ativo' => false, // Será ativado após pagamento
        ]);

        // Gerar slug
        $estabelecimento->slug = $estabelecimento->generateSlug();
        $estabelecimento->save();

        return redirect()->route('empresa.dashboard')
            ->with('success', 'Estabelecimento configurado com sucesso! Agora você pode ativar seu plano.');
    }

    private function obterCoordenadas($endereco, $cidade, $cep)
    {
        try {
            $enderecoCompleto = "{$endereco}, {$cidade}, {$cep}";
            $response = Http::get('https://maps.googleapis.com/maps/api/geocode/json', [
                'address' => $enderecoCompleto,
                'key' => config('services.google.maps_api_key')
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (!empty($data['results'])) {
                    $location = $data['results'][0]['geometry']['location'];
                    return [
                        'lat' => $location['lat'],
                        'lng' => $location['lng']
                    ];
                }
            }
        } catch (\Exception $e) {
            \Log::error('Erro ao obter coordenadas: ' . $e->getMessage());
        }

        return ['lat' => null, 'lng' => null];
    }
}
