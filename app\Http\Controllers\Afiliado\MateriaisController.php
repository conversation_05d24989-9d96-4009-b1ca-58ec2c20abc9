<?php

namespace App\Http\Controllers\Afiliado;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class MateriaisController extends Controller
{
    /**
     * Display the affiliate marketing materials.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;
        
        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }
        
        // Materiais de marketing disponíveis
        $materiais = [
            [
                'id' => 1,
                'titulo' => 'Banner Principal',
                'descricao' => 'Banner para uso em sites e blogs',
                'tipo' => 'imagem',
                'formato' => 'jpg',
                'tamanho' => '1200x628px',
                'url' => '/storage/materiais/banner-principal.jpg',
                'link' => $afiliado->link_afiliado,
            ],
            [
                'id' => 2,
                'titulo' => 'Banner Lateral',
                'descricao' => 'Banner para uso em sidebars',
                'tipo' => 'imagem',
                'formato' => 'jpg',
                'tamanho' => '300x600px',
                'url' => '/storage/materiais/banner-lateral.jpg',
                'link' => $afiliado->link_afiliado,
            ],
            [
                'id' => 3,
                'titulo' => 'E-mail Marketing',
                'descricao' => 'Template para campanhas de e-mail',
                'tipo' => 'html',
                'formato' => 'html',
                'tamanho' => '600px',
                'url' => '/storage/materiais/email-template.html',
                'link' => $afiliado->link_afiliado,
            ],
            [
                'id' => 4,
                'titulo' => 'Post para Redes Sociais',
                'descricao' => 'Imagem para compartilhamento em redes sociais',
                'tipo' => 'imagem',
                'formato' => 'jpg',
                'tamanho' => '1080x1080px',
                'url' => '/storage/materiais/post-social.jpg',
                'link' => $afiliado->link_afiliado,
            ],
            [
                'id' => 5,
                'titulo' => 'Texto para Blog',
                'descricao' => 'Artigo pronto para publicação em blogs',
                'tipo' => 'texto',
                'formato' => 'txt',
                'tamanho' => '2500 palavras',
                'url' => '/storage/materiais/artigo-blog.txt',
                'link' => $afiliado->link_afiliado,
            ],
        ];
        
        return Inertia::render('afiliado/materiais', [
            'afiliado' => $afiliado,
            'materiais' => $materiais,
        ]);
    }
}
