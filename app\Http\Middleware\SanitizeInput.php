<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SanitizeInput
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $input = $request->all();
        
        array_walk_recursive($input, function (&$value) {
            if (is_string($value)) {
                // Remove tags HTML perigosos
                $value = strip_tags($value);
                
                // Converte caracteres especiais para entidades HTML
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                
                // Remove espaços em branco no início e fim
                $value = trim($value);
                
                // Remove caracteres de controle
                $value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);
            }
        });

        $request->merge($input);

        return $next($request);
    }
}
