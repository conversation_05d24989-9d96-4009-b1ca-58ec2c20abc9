<?php

namespace App\Console\Commands;

use App\Jobs\ProcessarLembretesAgendamento;
use Illuminate\Console\Command;

class ProcessarLembretes extends Command
{
    protected $signature = 'agendamentos:lembretes {--horas=24 : Horas de antecedência para envio}';
    protected $description = 'Processar e enviar lembretes de agendamentos';

    public function handle()
    {
        $horas = (int) $this->option('horas');
        
        $this->info("Processando lembretes com {$horas} horas de antecedência...");
        
        ProcessarLembretesAgendamento::dispatch($horas);
        
        $this->info('Job de processamento de lembretes adicionado à fila.');
        
        return Command::SUCCESS;
    }
}
