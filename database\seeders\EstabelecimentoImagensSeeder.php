<?php

namespace Database\Seeders;

use App\Models\Estabelecimento;
use App\Models\EstabelecimentoImagem;
use Illuminate\Database\Seeder;

class EstabelecimentoImagensSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $estabelecimentos = Estabelecimento::all();

        // URLs de imagens de exemplo do Unsplash para diferentes tipos de estabelecimentos
        $imagensDentista = [
            [
                'url' => 'https://images.unsplash.com/photo-1629909613654-28e377c37b09?w=800&h=600&fit=crop',
                'descricao' => 'Consultório odontológico moderno'
            ],
            [
                'url' => 'https://images.unsplash.com/photo-1606811841689-23dfddce3e95?w=800&h=600&fit=crop',
                'descricao' => 'Equipamentos odontológicos de última geração'
            ],
            [
                'url' => 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=800&h=600&fit=crop',
                'descricao' => 'Sala de espera confortável'
            ]
        ];

        $imagensFarmacia = [
            [
                'url' => 'https://images.unsplash.com/photo-1576602976047-174e57a47881?w=800&h=600&fit=crop',
                'descricao' => 'Interior da farmácia com medicamentos organizados'
            ],
            [
                'url' => 'https://images.unsplash.com/photo-1585435557343-3b092031d4c1?w=800&h=600&fit=crop',
                'descricao' => 'Balcão de atendimento'
            ],
            [
                'url' => 'https://images.unsplash.com/photo-1631549916768-4119b2e5f926?w=800&h=600&fit=crop',
                'descricao' => 'Área de manipulação'
            ]
        ];

        $imagensFisioterapia = [
            [
                'url' => 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop',
                'descricao' => 'Sala de fisioterapia com equipamentos'
            ],
            [
                'url' => 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=600&fit=crop',
                'descricao' => 'Área de exercícios terapêuticos'
            ],
            [
                'url' => 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=800&h=600&fit=crop',
                'descricao' => 'Equipamentos de reabilitação'
            ]
        ];

        foreach ($estabelecimentos as $estabelecimento) {
            $imagens = [];

            switch ($estabelecimento->categoria) {
                case 'dentista':
                    $imagens = $imagensDentista;
                    break;
                case 'farmacia':
                    $imagens = $imagensFarmacia;
                    break;
                case 'fisioterapia':
                    $imagens = $imagensFisioterapia;
                    break;
            }

            foreach ($imagens as $index => $imagem) {
                EstabelecimentoImagem::create([
                    'estabelecimento_id' => $estabelecimento->id,
                    'nome_arquivo' => 'exemplo_' . $estabelecimento->id . '_' . ($index + 1) . '.jpg',
                    'nome_original' => $imagem['descricao'] . '.jpg',
                    'tipo_mime' => 'image/jpeg',
                    'tamanho' => 1024000, // 1MB exemplo
                    'descricao' => $imagem['descricao'],
                    'principal' => $index === 0,
                    'ordem' => $index,
                ]);
            }
        }
    }
}
