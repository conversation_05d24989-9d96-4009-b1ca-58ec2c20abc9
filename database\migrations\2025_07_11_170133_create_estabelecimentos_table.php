<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('estabelecimentos', function (Blueprint $table) {
            $table->id();
            $table->string('nome');
            $table->enum('categoria', ['dentista', 'farmacia', 'fisioterapia', 'outros']);
            $table->text('descricao')->nullable();
            $table->string('telefone')->nullable();
            $table->string('whatsapp');
            $table->string('email')->nullable();
            $table->string('endereco');
            $table->string('cidade');
            $table->string('estado', 2);
            $table->string('cep', 9);
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->json('horario_funcionamento')->nullable();
            $table->text('servicos_oferecidos')->nullable();
            $table->string('site')->nullable();
            $table->string('instagram')->nullable();
            $table->string('facebook')->nullable();
            $table->boolean('ativo')->default(true);
            $table->boolean('plano_ativo')->default(false);
            $table->timestamp('plano_vencimento')->nullable();
            $table->decimal('avaliacao_media', 3, 2)->default(0);
            $table->integer('total_avaliacoes')->default(0);
            $table->timestamps();

            // Índices para busca geográfica
            $table->index(['latitude', 'longitude']);
            $table->index(['categoria', 'ativo', 'plano_ativo']);
            $table->index(['cidade', 'estado']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('estabelecimentos');
    }
};
