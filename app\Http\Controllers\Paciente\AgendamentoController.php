<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Models\Avaliacao;
use App\Models\Fisioterapeuta;
use App\Models\Assinatura;
use App\Models\User;
use App\Services\NotificacaoService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AgendamentoController extends Controller
{
    /**
     * Display a listing of patient's appointments.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        $query = Agendamento::where('paciente_id', $user->id)
            ->with(['fisioterapeuta.user', 'relatorioSessao', 'avaliacao']);

        // Filtro por status
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Filtro por período
        if ($request->filled('periodo') && $request->periodo !== 'all') {
            switch ($request->periodo) {
                case 'proximos':
                    $query->where('scheduled_at', '>=', Carbon::now());
                    break;
                case 'passados':
                    $query->where('scheduled_at', '<', Carbon::now());
                    break;
                case 'mes_atual':
                    $query->whereMonth('scheduled_at', Carbon::now()->month)
                          ->whereYear('scheduled_at', Carbon::now()->year);
                    break;
            }
        }

        $agendamentos = $query->orderBy('scheduled_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        // Estatísticas
        $stats = [
            'total' => Agendamento::where('paciente_id', $user->id)->count(),
            'agendados' => Agendamento::where('paciente_id', $user->id)->where('status', 'agendado')->count(),
            'concluidos' => Agendamento::where('paciente_id', $user->id)->where('status', 'concluido')->count(),
            'cancelados' => Agendamento::where('paciente_id', $user->id)->where('status', 'cancelado')->count(),
        ];

        return Inertia::render('paciente/agendamentos', [
            'agendamentos' => $agendamentos,
            'filters' => $request->only(['status', 'periodo']),
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for creating a new appointment.
     */
    public function create(Request $request)
    {
        $user = auth()->user();

        // Verificar se o paciente tem plano ativo
        if (!$user->has_subscription) {
            return redirect()->route('paciente.planos')
                ->with('error', 'Você precisa ter um plano ativo para agendar consultas.');
        }

        // Buscar assinatura ativa (pode não existir para planos avulsos)
        $assinatura = Assinatura::where('user_id', $user->id)
            ->where('status', 'ativa')
            ->with('plano')
            ->first();

        // Para planos mensais, verificar sessões restantes
        if ($assinatura && $assinatura->plano) {
            $sessoesUsadas = Agendamento::where('paciente_id', $user->id)
                ->where('status', 'concluido')
                ->whereMonth('scheduled_at', Carbon::now()->month)
                ->whereYear('scheduled_at', Carbon::now()->year)
                ->count();

            $sessoesRestantes = $assinatura->plano->sessions_per_month - $sessoesUsadas;

            if ($sessoesRestantes <= 0) {
                return redirect()->route('paciente.agendamentos.index')
                    ->with('error', 'Você já utilizou todas as sessões do seu plano neste mês.');
            }
        }

        // Fisioterapeuta pré-selecionado (vindo da busca)
        $fisioterapeutaSelecionado = null;
        if ($request->filled('fisioterapeuta')) {
            $fisioterapeutaSelecionado = User::with('fisioterapeuta')
                ->where('id', $request->fisioterapeuta)
                ->whereHas('fisioterapeuta')
                ->first();
        }

        // Buscar fisioterapeutas disponíveis
        $fisioterapeutas = User::with('fisioterapeuta')
            ->whereHas('fisioterapeuta', function ($query) {
                $query->where('available', true);
            })
            ->where('active', true)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'user' => [
                        'name' => $user->name,
                        'avatar' => $user->avatar,
                    ],
                    'crefito' => $user->fisioterapeuta->crefito,
                    'specializations' => $user->fisioterapeuta->specializations ?? [],
                    'hourly_rate' => (float) ($user->fisioterapeuta->hourly_rate ?? 0),
                    'rating' => $user->fisioterapeuta->rating ?? 0,
                    'total_reviews' => $user->fisioterapeuta->total_reviews ?? 0,
                ];
            });

        return Inertia::render('paciente/agendamentos/create', [
            'fisioterapeutas' => $fisioterapeutas,
            'fisioterapeutaSelecionado' => $fisioterapeutaSelecionado ? [
                'id' => $fisioterapeutaSelecionado->id,
                'user' => [
                    'name' => $fisioterapeutaSelecionado->name,
                    'avatar' => $fisioterapeutaSelecionado->avatar,
                ],
                'crefito' => $fisioterapeutaSelecionado->fisioterapeuta->crefito,
                'specializations' => $fisioterapeutaSelecionado->fisioterapeuta->specializations ?? [],
                'hourly_rate' => $fisioterapeutaSelecionado->fisioterapeuta->hourly_rate,
                'rating' => $fisioterapeutaSelecionado->fisioterapeuta->rating ?? 0,
                'total_reviews' => $fisioterapeutaSelecionado->fisioterapeuta->total_reviews ?? 0,
            ] : null,
            'assinaturaAtual' => $assinatura ? [
                'sessoes_restantes' => $sessoesRestantes ?? null,
                'plano' => [
                    'nome' => $assinatura->plano->name,
                    'sessions_per_month' => $assinatura->plano->sessions_per_month,
                ],
            ] : null,
            'isPlanoAvulso' => !$assinatura, // Indica se é plano avulso
        ]);
    }

    /**
     * Store a newly created appointment.
     */
    public function store(Request $request)
    {
        $user = auth()->user();

        $validated = $request->validate([
            'fisioterapeuta_id' => 'required|exists:users,id',
            'data_hora' => 'required|date|after:now|before:' . Carbon::now()->addDays(60)->toDateString(),
            'observacoes' => 'nullable|string|max:500',
        ], [
            'fisioterapeuta_id.required' => 'Selecione um fisioterapeuta.',
            'fisioterapeuta_id.exists' => 'Fisioterapeuta não encontrado.',
            'data_hora.required' => 'Selecione uma data e horário.',
            'data_hora.date' => 'Data e horário inválidos.',
            'data_hora.after' => 'A data deve ser futura.',
            'data_hora.before' => 'Não é possível agendar com mais de 60 dias de antecedência.',
            'observacoes.max' => 'As observações não podem ter mais de 500 caracteres.',
        ]);

        $dataHora = Carbon::parse($validated['data_hora']);

        // Validações adicionais de negócio

        // 1. Verificar se não é domingo
        if ($dataHora->dayOfWeek === 0) {
            return back()->withErrors(['data_hora' => 'Não é possível agendar aos domingos.']);
        }

        // 2. Verificar horário comercial (8h às 18h)
        $hora = $dataHora->hour;
        if ($hora < 8 || $hora >= 18) {
            return back()->withErrors(['data_hora' => 'Horário deve ser entre 8h e 18h.']);
        }

        // 3. Verificar se o fisioterapeuta existe e está ativo
        $fisioterapeuta = User::with('fisioterapeuta')
            ->where('id', $validated['fisioterapeuta_id'])
            ->whereHas('fisioterapeuta', function($q) {
                $q->where('available', true);
            })
            ->first();

        if (!$fisioterapeuta) {
            return back()->withErrors(['fisioterapeuta_id' => 'Fisioterapeuta não disponível.']);
        }

        // 4. Verificar se o horário ainda está disponível
        $conflito = Agendamento::where('fisioterapeuta_id', $validated['fisioterapeuta_id'])
            ->where('data_hora', $validated['data_hora'])
            ->where('status', '!=', 'cancelado')
            ->exists();

        if ($conflito) {
            return back()->withErrors(['data_hora' => 'Este horário não está mais disponível.']);
        }

        // 5. Verificar se o paciente não tem outro agendamento no mesmo horário
        $conflitoPaciente = Agendamento::where('paciente_id', $user->id)
            ->where('data_hora', $validated['data_hora'])
            ->where('status', '!=', 'cancelado')
            ->exists();

        if ($conflitoPaciente) {
            return back()->withErrors(['data_hora' => 'Você já possui um agendamento neste horário.']);
        }

        // 6. Verificar limite de agendamentos por dia (máximo 3)
        $agendamentosDia = Agendamento::where('paciente_id', $user->id)
            ->whereDate('data_hora', $dataHora->toDateString())
            ->where('status', '!=', 'cancelado')
            ->count();

        if ($agendamentosDia >= 3) {
            return back()->withErrors(['data_hora' => 'Limite máximo de 3 agendamentos por dia atingido.']);
        }

        // Buscar assinatura (pode não existir para planos avulsos)
        $assinatura = Assinatura::where('user_id', $user->id)
            ->where('status', 'ativa')
            ->with('plano')
            ->first();

        // Para planos mensais, verificar sessões restantes
        if ($assinatura && $assinatura->plano) {
            $sessoesUsadas = Agendamento::where('paciente_id', $user->id)
                ->where('status', 'concluido')
                ->whereMonth('data_hora', Carbon::now()->month)
                ->whereYear('data_hora', Carbon::now()->year)
                ->count();

            if ($sessoesUsadas >= $assinatura->plano->sessions_per_month) {
                return back()->withErrors(['data_hora' => 'Você já utilizou todas as sessões do seu plano neste mês.']);
            }
        }

        $agendamento = null;

        DB::transaction(function () use ($validated, $user, $assinatura, &$agendamento) {
            // Para planos avulsos, usar valores padrão
            $duration = $assinatura && $assinatura->plano ? $assinatura->plano->session_duration : 60;
            $price = $assinatura && $assinatura->plano ? $assinatura->plano->price : 120.00; // Preço padrão para avulso

            $agendamento = Agendamento::create([
                'paciente_id' => $user->id,
                'fisioterapeuta_id' => $validated['fisioterapeuta_id'],
                'assinatura_id' => $assinatura ? $assinatura->id : null,
                'scheduled_at' => $validated['data_hora'],
                'duration' => $duration,
                'status' => 'agendado',
                'service_type' => 'Fisioterapia',
                'notes' => $validated['observacoes'],
                'address' => $user->address ?? 'Endereço não informado',
                'price' => $price,
            ]);

            // Enviar notificação para o fisioterapeuta
            $notificacaoService = new NotificacaoService();
            $notificacaoService->notificarNovoAgendamento($agendamento);
        });

        return redirect()->route('paciente.agendamentos.index')
            ->with('success', 'Agendamento realizado com sucesso!');
    }

    /**
     * Display the specified appointment.
     */
    public function show(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        $agendamento->load(['fisioterapeuta.user', 'avaliacao']);

        // Formatar dados para o frontend
        $agendamentoFormatado = [
            'id' => $agendamento->id,
            'data_agendamento' => $agendamento->scheduled_at ? $agendamento->scheduled_at->format('Y-m-d') : null,
            'horario' => $agendamento->scheduled_at ? $agendamento->scheduled_at->format('H:i:s') : null,
            'status' => $agendamento->status,
            'tipo' => 'sessao', // Assumindo que é sempre sessão por enquanto
            'observacoes' => $agendamento->notes,
            'endereco_atendimento' => [
                'logradouro' => 'Rua Exemplo', // Dados fictícios por enquanto
                'numero' => '123',
                'bairro' => 'Centro',
                'cidade' => 'São Paulo',
                'cep' => '01000-000',
            ],
            'fisioterapeuta' => [
                'id' => $agendamento->fisioterapeuta->id ?? null,
                'user' => [
                    'name' => $agendamento->fisioterapeuta->user->name ?? 'Fisioterapeuta',
                    'email' => $agendamento->fisioterapeuta->user->email ?? '',
                    'phone' => $agendamento->fisioterapeuta->user->phone ?? '',
                ],
                'especialidades' => ['Ortopédica', 'Esportiva'], // Dados fictícios por enquanto
                'crefito' => $agendamento->fisioterapeuta->crefito ?? 'CREFITO-3/123456-F',
            ],
            'created_at' => $agendamento->created_at->toISOString(),
            'updated_at' => $agendamento->updated_at->toISOString(),
        ];

        return Inertia::render('paciente/agendamentos/show', [
            'agendamento' => $agendamentoFormatado,
            'podeEditar' => $agendamento->status === 'agendado',
            'podeCancelar' => in_array($agendamento->status, ['agendado', 'confirmado']),
        ]);
    }

    /**
     * Cancel an appointment.
     */
    public function cancel(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se pode cancelar (pelo menos 24h de antecedência)
        if (Carbon::parse($agendamento->data_hora)->diffInHours(Carbon::now()) < 24) {
            return back()->withErrors(['cancel' => 'Agendamentos só podem ser cancelados com pelo menos 24 horas de antecedência.']);
        }

        if ($agendamento->status !== 'agendado') {
            return back()->withErrors(['cancel' => 'Este agendamento não pode ser cancelado.']);
        }

        $agendamento->update([
            'status' => 'cancelado',
            'notes' => ($agendamento->notes ?? '') . "\n\nCancelado pelo paciente em " . Carbon::now()->format('d/m/Y H:i'),
        ]);

        // Enviar notificação para o fisioterapeuta
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarAgendamentoCancelado($agendamento, 'Cancelado pelo paciente');

        return redirect()->route('paciente.agendamentos.index')
            ->with('success', 'Agendamento cancelado com sucesso.');
    }

    /**
     * Show reschedule form
     */
    public function reschedule(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se pode reagendar (pelo menos 24h de antecedência)
        if (Carbon::parse($agendamento->data_hora)->diffInHours(Carbon::now()) < 24) {
            return back()->withErrors(['reschedule' => 'Agendamentos só podem ser reagendados com pelo menos 24 horas de antecedência.']);
        }

        if (!in_array($agendamento->status, ['agendado', 'confirmado'])) {
            return back()->withErrors(['reschedule' => 'Este agendamento não pode ser reagendado.']);
        }

        $agendamento->load(['fisioterapeuta.user', 'fisioterapeuta.fisioterapeuta']);

        return Inertia::render('paciente/agendamentos/reschedule', [
            'agendamento' => $agendamento,
            'fisioterapeuta' => $agendamento->fisioterapeuta,
        ]);
    }

    /**
     * Update reschedule
     */
    public function updateReschedule(Request $request, Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se pode reagendar
        if (Carbon::parse($agendamento->data_hora)->diffInHours(Carbon::now()) < 24) {
            return back()->withErrors(['reschedule' => 'Agendamentos só podem ser reagendados com pelo menos 24 horas de antecedência.']);
        }

        if (!in_array($agendamento->status, ['agendado', 'confirmado'])) {
            return back()->withErrors(['reschedule' => 'Este agendamento não pode ser reagendado.']);
        }

        $validated = $request->validate([
            'data_hora' => 'required|date|after:now',
            'observacoes' => 'nullable|string|max:1000',
        ]);

        // Verificar disponibilidade do fisioterapeuta
        $conflito = Agendamento::where('fisioterapeuta_id', $agendamento->fisioterapeuta_id)
            ->where('id', '!=', $agendamento->id)
            ->where('scheduled_at', $validated['data_hora'])
            ->whereIn('status', ['agendado', 'confirmado', 'em_andamento'])
            ->exists();

        if ($conflito) {
            return back()->withErrors(['data_hora' => 'Este horário não está disponível.']);
        }

        $dataHoraAntiga = $agendamento->scheduled_at;

        $agendamento->update([
            'scheduled_at' => $validated['data_hora'],
            'status' => 'agendado', // Resetar para agendado se estava confirmado
            'notes' => $validated['observacoes'] ?? $agendamento->notes,
        ]);

        // Enviar notificação para o fisioterapeuta
        $notificacaoService = new NotificacaoService();
        $notificacaoService->notificarAgendamentoReagendado($agendamento, $dataHoraAntiga);

        return redirect()->route('paciente.agendamentos.index')
            ->with('success', 'Agendamento reagendado com sucesso!');
    }

    /**
     * Buscar horários disponíveis via AJAX
     */
    public function horariosDisponiveis(Request $request)
    {
        $validated = $request->validate([
            'fisioterapeuta_id' => 'required|exists:users,id',
            'data_agendamento' => 'required|date|after_or_equal:today',
        ]);

        $fisioterapeutaId = $validated['fisioterapeuta_id'];
        $dataAgendamento = Carbon::parse($validated['data_agendamento']);

        // Implementação temporária simplificada
        $horarios = [];

        // Gerar horários de 8h às 18h em intervalos de 1 hora
        for ($hora = 8; $hora <= 17; $hora++) {
            $horarios[] = sprintf('%02d:00', $hora);
        }

        return response()->json([
            'success' => true,
            'horarios' => $horarios,
            'data' => $dataAgendamento->format('d/m/Y'),
            'dia_semana' => $dataAgendamento->locale('pt_BR')->dayName,
        ]);
    }

    /**
     * Get available time slots for appointments
     */
    private function getHorariosDisponiveisOld()
    {
        $horarios = [];
        $dataInicio = Carbon::now()->addDay();
        $dataFim = Carbon::now()->addDays(30);

        while ($dataInicio <= $dataFim) {
            // Pular finais de semana
            if ($dataInicio->isWeekend()) {
                $dataInicio->addDay();
                continue;
            }

            // Horários de 8h às 18h
            for ($hora = 8; $hora <= 17; $hora++) {
                $horario = $dataInicio->copy()->setTime($hora, 0);
                
                // Verificar se não há conflitos
                $ocupado = Agendamento::where('scheduled_at', $horario)
                    ->where('status', '!=', 'cancelado')
                    ->exists();

                if (!$ocupado) {
                    $horarios[] = [
                        'data_hora' => $horario->toISOString(),
                        'data_formatada' => $horario->format('d/m/Y'),
                        'hora_formatada' => $horario->format('H:i'),
                        'dia_semana' => $horario->locale('pt_BR')->dayName,
                    ];
                }
            }

            $dataInicio->addDay();
        }

        return collect($horarios)->groupBy('data_formatada')->toArray();
    }

    /**
     * Show session report
     */
    public function showReport(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se a sessão foi concluída e tem relatório
        if ($agendamento->status !== 'concluido' || !$agendamento->relatorioSessao) {
            return back()->withErrors(['report' => 'Relatório não disponível para esta sessão.']);
        }

        $agendamento->load(['fisioterapeuta.user', 'relatorioSessao', 'avaliacao']);

        return Inertia::render('paciente/agendamentos/report', [
            'agendamento' => $agendamento,
            'relatorio' => $agendamento->relatorioSessao,
            'avaliacao' => $agendamento->avaliacao,
        ]);
    }

    /**
     * Show evaluation form
     */
    public function showEvaluation(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se a sessão foi concluída
        if ($agendamento->status !== 'concluido') {
            return back()->withErrors(['evaluation' => 'Só é possível avaliar sessões concluídas.']);
        }

        // Verificar se já foi avaliada
        if ($agendamento->avaliacao) {
            return redirect()->route('paciente.agendamentos.report', $agendamento)
                ->with('info', 'Esta sessão já foi avaliada.');
        }

        $agendamento->load(['fisioterapeuta.user', 'relatorioSessao']);

        return Inertia::render('paciente/agendamentos/evaluate', [
            'agendamento' => $agendamento,
        ]);
    }

    /**
     * Store evaluation
     */
    public function storeEvaluation(Request $request, Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao paciente logado
        if ($agendamento->paciente_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se a sessão foi concluída
        if ($agendamento->status !== 'concluido') {
            return back()->withErrors(['evaluation' => 'Só é possível avaliar sessões concluídas.']);
        }

        // Verificar se já foi avaliada
        if ($agendamento->avaliacao) {
            return back()->withErrors(['evaluation' => 'Esta sessão já foi avaliada.']);
        }

        $validated = $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
            'recommend' => 'required|boolean',
        ], [
            'rating.required' => 'A avaliação é obrigatória.',
            'rating.integer' => 'A avaliação deve ser um número.',
            'rating.min' => 'A avaliação deve ser no mínimo 1 estrela.',
            'rating.max' => 'A avaliação deve ser no máximo 5 estrelas.',
            'comment.max' => 'O comentário não pode ter mais de 1000 caracteres.',
            'recommend.required' => 'Informe se recomenda o fisioterapeuta.',
            'recommend.boolean' => 'Valor inválido para recomendação.',
        ]);

        // Criar avaliação
        Avaliacao::create([
            'agendamento_id' => $agendamento->id,
            'paciente_id' => $agendamento->paciente_id,
            'fisioterapeuta_id' => $agendamento->fisioterapeuta_id,
            'rating' => $validated['rating'],
            'comment' => $validated['comment'],
            'recommend' => $validated['recommend'],
        ]);

        // Atualizar rating médio do fisioterapeuta
        $this->updateFisioterapeutaRating($agendamento->fisioterapeuta_id);

        return redirect()->route('paciente.agendamentos.report', $agendamento)
            ->with('success', 'Avaliação enviada com sucesso! Obrigado pelo seu feedback.');
    }

    /**
     * Update fisioterapeuta average rating
     */
    private function updateFisioterapeutaRating($fisioterapeutaId)
    {
        $averageRating = Avaliacao::where('fisioterapeuta_id', $fisioterapeutaId)->avg('rating');

        $fisioterapeuta = \App\Models\Fisioterapeuta::where('user_id', $fisioterapeutaId)->first();
        if ($fisioterapeuta) {
            $fisioterapeuta->update(['rating' => round($averageRating, 2)]);
        }
    }

    /**
     * Buscar fisioterapeutas disponíveis via AJAX
     */
    public function fisioterapeutasDisponiveis(Request $request)
    {
        $validated = $request->validate([
            'data' => 'required|date|after:today',
            'horario' => 'required|date_format:H:i',
            'area' => 'nullable|string',
            'especializacao' => 'nullable|string',
        ]);

        // Usar o novo sistema de agendamento
        $agendamentoController = new \App\Http\Controllers\AgendamentoController();
        $response = $agendamentoController->fisioterapeutasDisponiveis($request);

        return $response;
    }

    /**
     * Verificar disponibilidade via AJAX
     */
    public function verificarDisponibilidadeApi(Request $request)
    {
        $validated = $request->validate([
            'fisioterapeuta_id' => 'required|exists:users,id',
            'data_hora' => 'required|date|after:now',
        ]);

        // Usar o novo sistema de agendamento
        $agendamentoController = new \App\Http\Controllers\AgendamentoController();
        $response = $agendamentoController->verificarDisponibilidadeApi($request);

        return $response;
    }
}
