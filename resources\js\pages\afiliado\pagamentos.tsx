import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AfiliadoLayout from '@/layouts/AfiliadoLayout';
import { Head, Link, router } from '@inertiajs/react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CheckCircle, Clock, DollarSign, Eye, Plus, XCircle } from 'lucide-react';
import { useState } from 'react';

interface PagamentoComissao {
    id: number;
    valor_solicitado: number;
    status: 'pendente' | 'aprovado' | 'pago' | 'rejeitado';
    data_solicitacao: string;
    data_aprovacao?: string;
    data_pagamento?: string;
    metodo_pagamento: 'pix' | 'transferencia_bancaria';
    dados_pagamento: any;
    observacoes_admin?: string;
    observacoes_afiliado?: string;
    comprovante_pagamento?: string;
    aprovado_por?: {
        id: number;
        name: string;
    };
    status_badge: {
        class: string;
        text: string;
    };
    formatted_value: string;
}

interface Props {
    pagamentos: {
        data: PagamentoComissao[];
        links: any[];
        meta: any;
    };
    stats: {
        total_solicitado: number;
        total_pago: number;
        pendentes: number;
        saldo_disponivel: number;
    };
    filters: {
        status?: string;
    };
    afiliado: any;
}

export default function Pagamentos({ pagamentos, stats, filters, afiliado }: Props) {
    const [status, setStatus] = useState(filters.status || 'all');

    const handleFilter = () => {
        router.get(
            route('afiliado.pagamentos.index'),
            {
                status: status !== 'all' ? status : undefined,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pendente':
                return <Clock className="h-4 w-4" />;
            case 'aprovado':
                return <CheckCircle className="h-4 w-4" />;
            case 'pago':
                return <CheckCircle className="h-4 w-4" />;
            case 'rejeitado':
                return <XCircle className="h-4 w-4" />;
            default:
                return <Clock className="h-4 w-4" />;
        }
    };

    const getStatusBadge = (pagamento: PagamentoComissao) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            pendente: 'secondary',
            aprovado: 'default',
            pago: 'default',
            rejeitado: 'destructive',
        };

        const statusTexts: Record<string, string> = {
            pendente: 'Pendente',
            aprovado: 'Aprovado',
            pago: 'Pago',
            rejeitado: 'Rejeitado',
        };

        return (
            <Badge variant={variants[pagamento.status] || 'outline'}>
                {getStatusIcon(pagamento.status)}
                <span className="ml-1">{pagamento.status_badge?.text || statusTexts[pagamento.status] || pagamento.status}</span>
            </Badge>
        );
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const formatDate = (dateString: string) => {
        return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: ptBR });
    };

    return (
        <AfiliadoLayout>
            <Head title="Pagamentos de Comissão" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Pagamentos de Comissão</h1>
                        <p className="text-muted-foreground">Gerencie suas solicitações de pagamento de comissões</p>
                    </div>
                    <Button asChild>
                        <Link href={route('afiliado.pagamentos.create')}>
                            <Plus className="mr-2 h-4 w-4" />
                            Solicitar Pagamento
                        </Link>
                    </Button>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Saldo Disponível</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.saldo_disponivel)}</div>
                            <p className="text-xs text-muted-foreground">Disponível para saque</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Solicitado</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_solicitado)}</div>
                            <p className="text-xs text-muted-foreground">Valor total já solicitado</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Pago</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.total_pago)}</div>
                            <p className="text-xs text-muted-foreground">Valor total já recebido</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600">{stats.pendentes}</div>
                            <p className="text-xs text-muted-foreground">Solicitações pendentes</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filtros</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <Select value={status} onValueChange={setStatus}>
                                <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todos os status</SelectItem>
                                    <SelectItem value="pendente">Pendente</SelectItem>
                                    <SelectItem value="aprovado">Aprovado</SelectItem>
                                    <SelectItem value="pago">Pago</SelectItem>
                                    <SelectItem value="rejeitado">Rejeitado</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button onClick={handleFilter}>Filtrar</Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Pagamentos List */}
                <div className="grid gap-4">
                    {pagamentos?.data?.map((pagamento) => (
                        <Card key={pagamento.id}>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                            <h3 className="text-lg font-semibold">{pagamento.formatted_value}</h3>
                                            {getStatusBadge(pagamento)}
                                        </div>
                                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                            <span>Método: {pagamento.metodo_pagamento === 'pix' ? 'PIX' : 'Transferência Bancária'}</span>
                                            <span>Solicitado em: {formatDate(pagamento.data_solicitacao)}</span>
                                        </div>
                                        {pagamento.data_aprovacao && (
                                            <div className="text-sm text-muted-foreground">Aprovado em: {formatDate(pagamento.data_aprovacao)}</div>
                                        )}
                                        {pagamento.data_pagamento && (
                                            <div className="text-sm text-green-600">Pago em: {formatDate(pagamento.data_pagamento)}</div>
                                        )}
                                        {pagamento.observacoes_admin && (
                                            <div className="text-sm">
                                                <span className="font-medium">Observações do admin:</span> {pagamento.observacoes_admin}
                                            </div>
                                        )}
                                        {pagamento.observacoes_afiliado && (
                                            <div className="text-sm">
                                                <span className="font-medium">Suas observações:</span> {pagamento.observacoes_afiliado}
                                            </div>
                                        )}
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={route('afiliado.pagamentos.show', pagamento.id)}>
                                                <Eye className="mr-2 h-4 w-4" />
                                                Ver Detalhes
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Pagination */}
                {pagamentos?.meta?.last_page > 1 && (
                    <div className="flex justify-center">
                        <div className="flex gap-2">
                            {pagamentos.links?.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => link.url && router.get(link.url)}
                                    disabled={!link.url}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}

                {(!pagamentos?.data || pagamentos.data.length === 0) && (
                    <Card>
                        <CardContent className="p-6 text-center">
                            <p className="mb-4 text-muted-foreground">Nenhuma solicitação de pagamento encontrada.</p>
                            <Button asChild>
                                <Link href={route('afiliado.pagamentos.create')}>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Fazer Primeira Solicitação
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AfiliadoLayout>
    );
}
