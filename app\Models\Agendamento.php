<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Agendamento extends Model
{
    use HasFactory;

    protected $fillable = [
        'paciente_id',
        'fisioterapeuta_id',
        'assinatura_id',
        'scheduled_at',
        'duration',
        'status',
        'service_type',
        'notes',
        'address',
        'price',
        'started_at',
        'finished_at',
        'cancellation_reason',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'price' => 'decimal:2',
    ];

    // Relacionamentos
    public function paciente()
    {
        return $this->belongsTo(User::class, 'paciente_id');
    }

    public function fisioterapeuta()
    {
        return $this->belongsTo(Fisioterapeuta::class, 'fisioterapeuta_id', 'user_id');
    }

    public function assinatura()
    {
        return $this->belongsTo(Assinatura::class);
    }

    public function relatorioSessao()
    {
        return $this->hasOne(RelatorioSessao::class);
    }

    public function avaliacao()
    {
        return $this->hasOne(Avaliacao::class);
    }

    // Scopes
    public function scopeAgendados($query)
    {
        return $query->where('status', 'agendado');
    }

    public function scopeConcluidos($query)
    {
        return $query->where('status', 'concluido');
    }
}
