import { Badge } from '@/components/ui/badge';
import { Award, Clock, Heart, MapPin, Shield, Users } from 'lucide-react';
import React from 'react';

export default function BenefitsSection() {
    return (
        <section id="beneficios" className="bg-muted/30 py-20">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="mb-16 text-center">
                    <h2 className="text-3xl font-medium text-balance md:text-4xl">Por que Escolher a F4 Fisio?</h2>
                    <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                        Conectamos você aos melhores profissionais de saúde da sua região com qualidade e confiança.
                    </p>
                </div>

                <div className="grid gap-12 sm:grid-cols-2 lg:grid-cols-3">
                    <FeatureCard
                        title="Profissionais Qualificados"
                        description="Todos os profissionais são verificados e possuem certificações válidas para garantir o melhor atendimento."
                        icon={Award}
                    />

                    <FeatureCard
                        title="Localização Inteligente"
                        description="Encontre facilmente profissionais próximos a você com nossa busca por localização avançada."
                        icon={MapPin}
                    />

                    <FeatureCard
                        title="Atendimento Humanizado"
                        description="Priorizamos o cuidado personalizado e a atenção individual para cada paciente."
                        icon={Heart}
                    />

                    <FeatureCard
                        title="Horários Flexíveis"
                        description="Agende consultas nos horários que melhor se adequam à sua rotina, incluindo fins de semana."
                        icon={Clock}
                    />

                    <FeatureCard
                        title="Segurança e Confiança"
                        description="Plataforma segura com avaliações reais de pacientes para sua tranquilidade."
                        icon={Shield}
                    />

                    <FeatureCard
                        title="Comunidade Ativa"
                        description="Faça parte de uma comunidade que se preocupa com seu bem-estar e qualidade de vida."
                        icon={Users}
                    />
                </div>
            </div>
        </section>
    );
}

const FeatureCard = ({
    title,
    description,
    icon: Icon,
}: {
    title: string;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
}) => {
    return (
        <div className="flex items-start gap-6">
            <Badge variant="gradient" size="icon-lg" className="h-12 w-12 flex-shrink-0">
                <Icon className="h-7 w-7" />
            </Badge>
            <div className="flex-1">
                <h3 className="text-lg font-semibold text-foreground">{title}</h3>
                <p className="mt-3 text-sm leading-relaxed text-muted-foreground">{description}</p>
            </div>
        </div>
    );
};
