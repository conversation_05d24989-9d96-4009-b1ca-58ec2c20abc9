<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckAfiliadoAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $currentMode = session('user_mode', 'normal');

        // Verificar se o usuário pode acessar o modo afiliado
        if ($currentMode === 'afiliado') {
            if (!$user->canSwitchToAfiliadoMode()) {
                // Resetar para modo normal e redirecionar
                session(['user_mode' => 'normal']);
                return redirect()->route('dashboard')
                    ->with('error', 'Você não tem acesso ao painel de afiliado.');
            }
        } else {
            // Se não estiver em modo afiliado, redirecionar para dashboard normal
            return redirect()->route('dashboard')
                ->with('error', 'Acesso negado. Alterne para o modo afiliado.');
        }

        return $next($request);
    }
}
