<?php

namespace App\Http\Controllers;

use App\Http\Requests\ContatoRequest;
use App\Mail\ContatoRecebido;
use App\Mail\ContatoConfirmacao;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class ContatoController extends Controller
{
    /**
     * Exibir página de contato
     */
    public function index()
    {
        return Inertia::render('contato');
    }

    /**
     * Processar envio do formulário de contato
     */
    public function store(ContatoRequest $request)
    {
        try {
            $dados = $request->validated();

            // Adicionar informações extras
            $dados['ip'] = $request->ip();
            $dados['user_agent'] = $request->userAgent();
            $dados['data_envio'] = now();

            // Tentar enviar emails (com fallback para demonstração)
            try {
                // Enviar email para a equipe
                Mail::to(config('mail.contact.to', '<EMAIL>'))
                    ->send(new ContatoRecebido($dados));

                // Enviar email de confirmação para o remetente
                Mail::to($dados['email'])
                    ->send(new ContatoConfirmacao($dados));

                $emailStatus = 'enviado';
            } catch (\Exception $emailError) {
                // Se o email falhar, ainda assim registramos o contato
                Log::warning('Erro ao enviar email de contato', [
                    'error' => $emailError->getMessage(),
                    'dados' => $dados
                ]);
                $emailStatus = 'pendente';
            }

            // Log para auditoria
            Log::info('Formulário de contato processado', [
                'nome' => $dados['nome'],
                'email' => $dados['email'],
                'assunto' => $dados['assunto'],
                'ip' => $dados['ip'],
                'email_status' => $emailStatus
            ]);

            return back()->with('success', 'Mensagem enviada com sucesso! Entraremos em contato em breve. Você também receberá um email de confirmação.');

        } catch (\Exception $e) {
            Log::error('Erro ao processar formulário de contato', [
                'error' => $e->getMessage(),
                'dados' => $request->only(['nome', 'email', 'assunto'])
            ]);

            return back()
                ->withErrors(['error' => 'Erro ao enviar mensagem. Tente novamente ou entre em contato via WhatsApp: (11) 97819-6207'])
                ->withInput();
        }
    }

    /**
     * Página de sucesso (opcional)
     */
    public function sucesso()
    {
        return Inertia::render('contato-sucesso');
    }
}
