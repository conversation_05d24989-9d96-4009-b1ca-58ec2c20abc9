import { expect, test } from '@playwright/test';

test.describe('Diagnóstico do Fluxo do Paciente', () => {
    let patientEmail: string;
    let patientPassword = 'password123';

    test.beforeAll(async () => {
        const timestamp = Date.now();
        patientEmail = `paciente.diagnostico.${timestamp}@example.com`;
    });

    test.describe('1. Verificação de Páginas Básicas', () => {
        test('deve carregar página inicial', async ({ page }) => {
            await page.goto('/');
            
            // Verificar se a página carrega
            await expect(page).toHaveTitle(/F4 Fisio|Laravel/);
            
            // Verificar se há elementos básicos
            const header = page.locator('header, nav, [role="banner"]');
            if (await header.count() > 0) {
                await expect(header.first()).toBeVisible();
            }
            
            console.log('✅ Página inicial carrega corretamente');
        });

        test('deve acessar página de registro', async ({ page }) => {
            await page.goto('/register');
            
            // Verificar título correto (em português)
            await expect(page).toHaveTitle(/Cadastro|Register/);
            
            // Verificar se há formulário de registro
            await expect(page.locator('input[name="name"]')).toBeVisible();
            await expect(page.locator('input[name="email"]')).toBeVisible();
            await expect(page.locator('input[name="password"]')).toBeVisible();
            await expect(page.locator('select[name="role"]')).toBeVisible();
            
            console.log('✅ Página de registro carrega corretamente');
        });

        test('deve acessar página de login', async ({ page }) => {
            await page.goto('/login');
            
            // Verificar se há formulário de login
            await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
            await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
            await expect(page.locator('button[type="submit"]')).toBeVisible();
            
            console.log('✅ Página de login carrega corretamente');
        });
    });

    test.describe('2. Teste de Registro', () => {
        test('deve registrar novo paciente', async ({ page }) => {
            await page.goto('/register');
            
            // Preencher formulário
            await page.fill('input[name="name"]', 'Paciente Diagnóstico');
            await page.fill('input[name="email"]', patientEmail);
            await page.fill('input[name="password"]', patientPassword);
            await page.fill('input[name="password_confirmation"]', patientPassword);
            await page.selectOption('select[name="role"]', 'paciente');
            
            // Submeter formulário
            await page.click('button[type="submit"]');
            
            // Aguardar redirecionamento
            await page.waitForLoadState('networkidle');
            
            // Verificar para onde foi redirecionado
            const currentUrl = page.url();
            console.log('URL após registro:', currentUrl);
            
            // Verificar se não há erros de validação
            const errors = page.locator('.error, .text-red-500, .text-danger, .invalid-feedback');
            const errorCount = await errors.count();
            if (errorCount > 0) {
                const errorTexts = await errors.allTextContents();
                console.log('Erros encontrados:', errorTexts);
            }
            
            // Deve redirecionar para alguma página do paciente ou verificação de email
            expect(currentUrl).toMatch(/paciente|verify|dashboard|planos/);
            
            console.log('✅ Registro realizado com sucesso');
        });
    });

    test.describe('3. Teste de Login', () => {
        test('deve fazer login com paciente registrado', async ({ page }) => {
            await page.goto('/login');
            
            // Preencher credenciais
            await page.fill('input[type="email"], input[name="email"]', patientEmail);
            await page.fill('input[type="password"], input[name="password"]', patientPassword);
            
            // Submeter formulário
            await page.click('button[type="submit"]');
            
            // Aguardar redirecionamento
            await page.waitForLoadState('networkidle');
            
            // Verificar URL atual
            const currentUrl = page.url();
            console.log('URL após login:', currentUrl);
            
            // Verificar se não ficou na página de login (indicaria erro)
            if (currentUrl.includes('/login')) {
                // Verificar se há mensagens de erro
                const errors = page.locator('.error, .text-red-500, .text-danger, .alert-danger');
                const errorCount = await errors.count();
                if (errorCount > 0) {
                    const errorTexts = await errors.allTextContents();
                    console.log('Erros de login:', errorTexts);
                }
                
                // Verificar se precisa verificar email
                const verifyMessage = page.locator('text=verificar, text=verify, text=email');
                if (await verifyMessage.count() > 0) {
                    console.log('⚠️ Verificação de email necessária');
                }
            }
            
            // Deve redirecionar para área do paciente
            expect(currentUrl).not.toContain('/login');
            
            console.log('✅ Login realizado com sucesso');
        });
    });

    test.describe('4. Verificação de Redirecionamentos', () => {
        test('deve verificar fluxo de redirecionamento do paciente', async ({ page }) => {
            // Fazer login primeiro
            await page.goto('/login');
            await page.fill('input[type="email"], input[name="email"]', patientEmail);
            await page.fill('input[type="password"], input[name="password"]', patientPassword);
            await page.click('button[type="submit"]');
            await page.waitForLoadState('networkidle');
            
            const currentUrl = page.url();
            console.log('URL após login:', currentUrl);
            
            // Verificar se foi para planos (paciente sem assinatura)
            if (currentUrl.includes('/paciente/planos')) {
                console.log('✅ Redirecionado para seleção de planos (correto para paciente sem assinatura)');
                
                // Verificar se a página de planos carrega
                await expect(page.locator('h1, h2')).toContainText(/plano/i);
                
                // Verificar se há opções de planos
                const planoOptions = page.locator('button:has-text("Escolher"), button:has-text("Selecionar"), .plano-card, [data-testid="plano"]');
                if (await planoOptions.count() > 0) {
                    console.log('✅ Opções de planos disponíveis');
                } else {
                    console.log('⚠️ Nenhuma opção de plano encontrada');
                }
            }
            
            // Verificar se foi para onboarding
            else if (currentUrl.includes('/paciente/onboarding')) {
                console.log('✅ Redirecionado para onboarding (dados médicos incompletos)');
                await expect(page.locator('h1, h2')).toContainText(/onboarding|dados|informações/i);
            }
            
            // Verificar se foi para dashboard
            else if (currentUrl.includes('/paciente/dashboard')) {
                console.log('✅ Redirecionado para dashboard (paciente completo)');
                await expect(page.locator('h1, h2')).toContainText(/dashboard|início/i);
            }
            
            // Verificar se foi para verificação de email
            else if (currentUrl.includes('/verify')) {
                console.log('⚠️ Redirecionado para verificação de email');
                await expect(page.locator('text=verificar, text=verify')).toBeVisible();
            }
            
            else {
                console.log('❌ Redirecionamento inesperado para:', currentUrl);
            }
        });
    });

    test.describe('5. Teste de Navegação Básica', () => {
        test('deve navegar pelas páginas do paciente', async ({ page }) => {
            // Fazer login
            await page.goto('/login');
            await page.fill('input[type="email"], input[name="email"]', patientEmail);
            await page.fill('input[type="password"], input[name="password"]', patientPassword);
            await page.click('button[type="submit"]');
            await page.waitForLoadState('networkidle');
            
            // Tentar acessar páginas principais do paciente
            const pagesToTest = [
                '/paciente/dashboard',
                '/paciente/perfil',
                '/paciente/planos',
                '/paciente/agendamentos',
                '/paciente/historico',
                '/paciente/pagamentos'
            ];
            
            for (const pageUrl of pagesToTest) {
                try {
                    await page.goto(pageUrl);
                    await page.waitForLoadState('networkidle');
                    
                    const currentUrl = page.url();
                    
                    if (currentUrl.includes(pageUrl)) {
                        console.log(`✅ ${pageUrl} - Acesso permitido`);
                        
                        // Verificar se a página carrega conteúdo
                        const hasContent = await page.locator('h1, h2, main, .content').count() > 0;
                        if (hasContent) {
                            console.log(`✅ ${pageUrl} - Conteúdo carregado`);
                        } else {
                            console.log(`⚠️ ${pageUrl} - Página vazia`);
                        }
                    } else {
                        console.log(`❌ ${pageUrl} - Redirecionado para: ${currentUrl}`);
                        
                        // Verificar se foi redirecionado por falta de permissão
                        if (currentUrl.includes('/planos')) {
                            console.log(`   Motivo: Sem assinatura ativa`);
                        } else if (currentUrl.includes('/onboarding')) {
                            console.log(`   Motivo: Dados médicos incompletos`);
                        } else if (currentUrl.includes('/login')) {
                            console.log(`   Motivo: Não autenticado`);
                        }
                    }
                } catch (error) {
                    console.log(`❌ ${pageUrl} - Erro: ${error.message}`);
                }
                
                // Pequena pausa entre requisições
                await page.waitForTimeout(500);
            }
        });
    });

    test.describe('6. Verificação de Sidebar e Navegação', () => {
        test('deve verificar sidebar do paciente', async ({ page }) => {
            // Fazer login e ir para uma página do paciente
            await page.goto('/login');
            await page.fill('input[type="email"], input[name="email"]', patientEmail);
            await page.fill('input[type="password"], input[name="password"]', patientPassword);
            await page.click('button[type="submit"]');
            await page.waitForLoadState('networkidle');
            
            // Tentar acessar dashboard
            await page.goto('/paciente/dashboard');
            await page.waitForLoadState('networkidle');
            
            // Verificar se há sidebar
            const sidebar = page.locator('aside, nav[role="navigation"], [data-testid="sidebar"]');
            if (await sidebar.count() > 0) {
                console.log('✅ Sidebar encontrada');
                
                // Verificar links de navegação
                const navLinks = sidebar.locator('a, button');
                const linkCount = await navLinks.count();
                console.log(`✅ ${linkCount} links de navegação encontrados`);
                
                // Listar os links disponíveis
                for (let i = 0; i < Math.min(linkCount, 10); i++) {
                    const link = navLinks.nth(i);
                    const text = await link.textContent();
                    const href = await link.getAttribute('href');
                    if (text && text.trim()) {
                        console.log(`   - ${text.trim()} ${href ? `(${href})` : ''}`);
                    }
                }
            } else {
                console.log('❌ Sidebar não encontrada');
            }
        });
    });

    test.describe('7. Verificação de Formulários', () => {
        test('deve verificar formulário de perfil', async ({ page }) => {
            // Fazer login
            await page.goto('/login');
            await page.fill('input[type="email"], input[name="email"]', patientEmail);
            await page.fill('input[type="password"], input[name="password"]', patientPassword);
            await page.click('button[type="submit"]');
            await page.waitForLoadState('networkidle');
            
            // Tentar acessar perfil
            await page.goto('/paciente/perfil');
            await page.waitForLoadState('networkidle');
            
            const currentUrl = page.url();
            if (currentUrl.includes('/paciente/perfil')) {
                console.log('✅ Página de perfil acessível');
                
                // Verificar campos do formulário
                const formFields = page.locator('input, select, textarea');
                const fieldCount = await formFields.count();
                console.log(`✅ ${fieldCount} campos de formulário encontrados`);
                
                // Verificar campos específicos
                const expectedFields = ['name', 'email', 'phone', 'birth_date'];
                for (const fieldName of expectedFields) {
                    const field = page.locator(`input[name="${fieldName}"]`);
                    if (await field.count() > 0) {
                        console.log(`✅ Campo ${fieldName} encontrado`);
                    } else {
                        console.log(`⚠️ Campo ${fieldName} não encontrado`);
                    }
                }
                
                // Verificar botão de salvar
                const saveButton = page.locator('button[type="submit"], button:has-text("Salvar")');
                if (await saveButton.count() > 0) {
                    console.log('✅ Botão de salvar encontrado');
                } else {
                    console.log('⚠️ Botão de salvar não encontrado');
                }
            } else {
                console.log(`❌ Redirecionado de perfil para: ${currentUrl}`);
            }
        });
    });
});
