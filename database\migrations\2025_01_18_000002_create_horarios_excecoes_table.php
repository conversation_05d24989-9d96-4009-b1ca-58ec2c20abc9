<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('horarios_excecoes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('fisioterapeuta_id')->constrained('users')->onDelete('cascade');
            $table->enum('tipo', ['data_especifica', 'semana', 'mes', 'periodo_personalizado']);
            $table->date('data_inicio');
            $table->date('data_fim')->nullable(); // Para períodos
            $table->tinyInteger('dia_semana')->nullable(); // Para exceções que se aplicam a dias específicos da semana
            $table->time('hora_inicio')->nullable(); // Null = dia todo indisponível
            $table->time('hora_fim')->nullable();
            $table->string('periodo_nome')->nullable(); // Ex: "Manhã", "Tarde", "Noite"
            $table->enum('acao', ['disponivel', 'indisponivel', 'horario_customizado'])->default('horario_customizado');
            $table->text('motivo')->nullable(); // Motivo da exceção
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Índices para performance
            $table->index(['fisioterapeuta_id', 'data_inicio', 'data_fim']);
            $table->index(['fisioterapeuta_id', 'tipo', 'ativo']);
            $table->index(['data_inicio', 'data_fim']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('horarios_excecoes');
    }
};
