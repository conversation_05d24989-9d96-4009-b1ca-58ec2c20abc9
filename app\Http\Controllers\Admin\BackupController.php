<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\BackupService;
use App\Services\ExportService;
use App\Services\ImportService;
use App\Jobs\ProcessBackupJob;
use App\Jobs\CleanupOldBackupsJob;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;

class BackupController extends Controller
{
    protected $backupService;
    protected $exportService;
    protected $importService;

    public function __construct(
        BackupService $backupService,
        ExportService $exportService,
        ImportService $importService
    ) {
        $this->backupService = $backupService;
        $this->exportService = $exportService;
        $this->importService = $importService;
    }

    /**
     * Página principal de backups
     */
    public function index()
    {
        $backups = $this->backupService->listBackupsWithLogs();
        $exports = $this->exportService->listExports();
        $imports = $this->importService->listImports();
        $stats = $this->backupService->getBackupStats();

        return Inertia::render('admin/backup/index', [
            'backups' => $backups,
            'exports' => $exports,
            'imports' => $imports,
            'stats' => $stats,
        ]);
    }

    /**
     * Criar backup manual
     */
    public function createBackup(Request $request)
    {
        $request->validate([
            'type' => 'required|in:full,database'
        ]);

        try {
            // Executar backup em background
            ProcessBackupJob::dispatch($request->type, auth()->id());

            return redirect()->back()->with('success', 'Backup iniciado! Você será notificado quando estiver pronto.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao iniciar backup: ' . $e->getMessage());
        }
    }

    /**
     * Baixar backup
     */
    public function downloadBackup(int $backupId)
    {
        try {
            return $this->backupService->downloadBackupById($backupId);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao baixar backup: ' . $e->getMessage());
        }
    }

    /**
     * Deletar backup
     */
    public function deleteBackup(int $backupId)
    {
        try {
            $success = $this->backupService->deleteBackupById($backupId);

            if ($success) {
                return redirect()->back()->with('success', 'Backup deletado com sucesso!');
            } else {
                return redirect()->back()->with('error', 'Erro ao deletar backup.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao deletar backup: ' . $e->getMessage());
        }
    }

    /**
     * Exportar relatórios
     */
    public function exportReport(Request $request)
    {
        $request->validate([
            'type' => 'required|in:users,agendamentos,financeiro',
            'format' => 'required|in:excel,pdf',
            'filters' => 'nullable|array'
        ]);

        try {
            $filters = $request->filters ?? [];

            switch ($request->type) {
                case 'users':
                    $export = $this->exportService->exportUsers($filters, $request->format, auth()->id());
                    break;
                case 'agendamentos':
                    $export = $this->exportService->exportAgendamentos($filters, $request->format, auth()->id());
                    break;
                case 'financeiro':
                    $export = $this->exportService->exportFinanceiro($filters, $request->format, auth()->id());
                    break;
            }

            return redirect()->back()->with('success', 'Exportação iniciada! O arquivo estará disponível em breve.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao exportar: ' . $e->getMessage());
        }
    }

    /**
     * Baixar exportação
     */
    public function downloadExport(int $exportId)
    {
        try {
            return $this->exportService->downloadExport($exportId);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao baixar exportação: ' . $e->getMessage());
        }
    }

    /**
     * Deletar exportação
     */
    public function deleteExport(int $exportId)
    {
        try {
            $success = $this->exportService->deleteExport($exportId);

            if ($success) {
                return redirect()->back()->with('success', 'Exportação deletada com sucesso!');
            } else {
                return redirect()->back()->with('error', 'Erro ao deletar exportação.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao deletar exportação: ' . $e->getMessage());
        }
    }

    /**
     * Upload de arquivo para importação
     */
    public function uploadImportFile(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
            'type' => 'required|in:users,estabelecimentos'
        ]);

        try {
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('imports', $fileName, 'local');

            // Validar arquivo
            $validation = $this->importService->validateImportFile($filePath, $request->type);

            if (!$validation['valid']) {
                Storage::disk('local')->delete($filePath);
                return redirect()->back()->with('error', 'Arquivo inválido: ' . $validation['error']);
            }

            return redirect()->back()->with([
                'success' => 'Arquivo carregado com sucesso!',
                'import_file' => $filePath,
                'import_type' => $request->type,
                'validation' => $validation
            ]);

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao carregar arquivo: ' . $e->getMessage());
        }
    }

    /**
     * Processar importação
     */
    public function processImport(Request $request)
    {
        $request->validate([
            'file_path' => 'required|string',
            'type' => 'required|in:users,estabelecimentos',
            'options' => 'nullable|array'
        ]);

        try {
            $options = $request->options ?? [];

            switch ($request->type) {
                case 'users':
                    $import = $this->importService->importUsers($request->file_path, $options, auth()->id());
                    break;
                case 'estabelecimentos':
                    $import = $this->importService->importEstabelecimentos($request->file_path, $options, auth()->id());
                    break;
            }

            // Limpar arquivo temporário
            Storage::disk('local')->delete($request->file_path);

            return redirect()->back()->with('success', 'Importação concluída com sucesso!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro na importação: ' . $e->getMessage());
        }
    }

    /**
     * Gerar template de importação
     */
    public function generateTemplate(Request $request)
    {
        $request->validate([
            'type' => 'required|in:users,estabelecimentos'
        ]);

        try {
            $filePath = $this->importService->generateImportTemplate($request->type);

            return Storage::disk('local')->download($filePath);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao gerar template: ' . $e->getMessage());
        }
    }

    /**
     * Executar limpeza de backups antigos
     */
    public function cleanupOldBackups(Request $request)
    {
        $request->validate([
            'days_to_keep' => 'required|integer|min:1|max:365'
        ]);

        try {
            CleanupOldBackupsJob::dispatch($request->days_to_keep);

            return redirect()->back()->with('success', 'Limpeza de backups antigos iniciada!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao iniciar limpeza: ' . $e->getMessage());
        }
    }

    /**
     * Configurações de backup automático
     */
    public function configureAutoBackup(Request $request)
    {
        $request->validate([
            'enabled' => 'required|boolean',
            'frequency' => 'required|in:daily,weekly,monthly',
            'time' => 'required|date_format:H:i',
            'type' => 'required|in:full,database',
            'keep_days' => 'required|integer|min:1|max:365'
        ]);

        try {
            // Salvar configurações no cache ou banco
            $config = [
                'enabled' => $request->enabled,
                'frequency' => $request->frequency,
                'time' => $request->time,
                'type' => $request->type,
                'keep_days' => $request->keep_days
            ];

            cache()->put('backup_auto_config', $config, now()->addYear());

            return redirect()->back()->with('success', 'Configurações de backup automático salvas!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erro ao salvar configurações: ' . $e->getMessage());
        }
    }

    /**
     * Obter configurações de backup automático
     */
    public function getAutoBackupConfig()
    {
        $config = cache()->get('backup_auto_config', [
            'enabled' => false,
            'frequency' => 'daily',
            'time' => '02:00',
            'type' => 'full',
            'keep_days' => 30
        ]);

        return response()->json($config);
    }
}
