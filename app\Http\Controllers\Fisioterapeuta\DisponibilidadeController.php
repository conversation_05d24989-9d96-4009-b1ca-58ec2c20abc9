<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use App\Models\Disponibilidade;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DisponibilidadeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $fisioterapeutaId = auth()->id();

        $query = Disponibilidade::where('fisioterapeuta_id', $fisioterapeutaId)
            ->orderBy('data_inicio', 'desc')
            ->orderBy('hora_inicio', 'asc');

        // Filtros
        if ($request->filled('tipo')) {
            $query->where('tipo', $request->tipo);
        }

        if ($request->filled('ativo')) {
            $query->where('ativo', $request->boolean('ativo'));
        }

        if ($request->filled('periodo')) {
            $hoje = Carbon::now();
            switch ($request->periodo) {
                case 'futuras':
                    $query->where('data_inicio', '>=', $hoje->toDateString());
                    break;
                case 'passadas':
                    $query->where('data_fim', '<', $hoje->toDateString())
                          ->orWhere(function ($q) use ($hoje) {
                              $q->whereNull('data_fim')
                                ->where('data_inicio', '<', $hoje->toDateString())
                                ->where('recorrente', false);
                          });
                    break;
                case 'ativas':
                    $query->where(function ($q) use ($hoje) {
                        $q->where('data_inicio', '<=', $hoje->toDateString())
                          ->where(function ($subQ) use ($hoje) {
                              $subQ->whereNull('data_fim')
                                   ->orWhere('data_fim', '>=', $hoje->toDateString());
                          });
                    });
                    break;
            }
        }

        $disponibilidades = $query->paginate(15)->withQueryString();

        // Estatísticas
        $stats = [
            'total' => Disponibilidade::where('fisioterapeuta_id', $fisioterapeutaId)->count(),
            'ativas' => Disponibilidade::where('fisioterapeuta_id', $fisioterapeutaId)->where('ativo', true)->count(),
            'bloqueios' => Disponibilidade::where('fisioterapeuta_id', $fisioterapeutaId)
                ->whereIn('tipo', ['indisponivel', 'bloqueio'])
                ->where('ativo', true)
                ->count(),
            'recorrentes' => Disponibilidade::where('fisioterapeuta_id', $fisioterapeutaId)
                ->where('recorrente', true)
                ->where('ativo', true)
                ->count(),
        ];

        // Buscar horários base também
        $horariosBase = \App\Models\HorarioBase::where('fisioterapeuta_id', $fisioterapeutaId)
            ->orderBy('dia_semana')
            ->orderBy('hora_inicio')
            ->get();

        return Inertia::render('fisioterapeuta/disponibilidade', [
            'disponibilidades' => $disponibilidades->items(),
            'horariosBase' => $horariosBase,
            'filters' => $request->only(['tipo', 'ativo', 'periodo']),
            'stats' => $stats,
            'fisioterapeuta' => [
                'available' => auth()->user()->fisioterapeuta?->available ?? true,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('fisioterapeuta/disponibilidade/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'tipo' => 'required|in:disponivel,indisponivel,bloqueio',
            'data_inicio' => 'required|date|after_or_equal:today',
            'data_fim' => 'nullable|date|after_or_equal:data_inicio',
            'hora_inicio' => 'required|date_format:H:i',
            'hora_fim' => 'required|date_format:H:i|after:hora_inicio',
            'dias_semana' => 'nullable|array',
            'dias_semana.*' => 'integer|min:0|max:6',
            'recorrente' => 'boolean',
            'motivo' => 'nullable|string|max:500',
        ]);

        // Validações adicionais
        if ($validated['recorrente'] && empty($validated['dias_semana'])) {
            return back()->withErrors(['dias_semana' => 'Dias da semana são obrigatórios para disponibilidades recorrentes.']);
        }

        if (!$validated['recorrente'] && !empty($validated['dias_semana'])) {
            return back()->withErrors(['recorrente' => 'Marque como recorrente para especificar dias da semana.']);
        }

        // Verificar conflitos
        $conflitos = $this->verificarConflitos(
            auth()->id(),
            $validated['data_inicio'],
            $validated['data_fim'],
            $validated['hora_inicio'],
            $validated['hora_fim'],
            $validated['dias_semana'] ?? null,
            $validated['recorrente']
        );

        if ($conflitos->isNotEmpty()) {
            return back()->withErrors(['conflito' => 'Há conflitos com outras disponibilidades já cadastradas.']);
        }

        Disponibilidade::create([
            'fisioterapeuta_id' => auth()->id(),
            'tipo' => $validated['tipo'],
            'data_inicio' => $validated['data_inicio'],
            'data_fim' => $validated['data_fim'],
            'hora_inicio' => $validated['hora_inicio'],
            'hora_fim' => $validated['hora_fim'],
            'dias_semana' => $validated['dias_semana'],
            'recorrente' => $validated['recorrente'],
            'motivo' => $validated['motivo'],
            'ativo' => true,
        ]);

        return redirect()->route('fisioterapeuta.disponibilidade.index')
            ->with('success', 'Disponibilidade cadastrada com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Disponibilidade $disponibilidade)
    {
        if ($disponibilidade->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        return Inertia::render('fisioterapeuta/disponibilidade/show', [
            'disponibilidade' => $disponibilidade,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Disponibilidade $disponibilidade)
    {
        if ($disponibilidade->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        return Inertia::render('fisioterapeuta/disponibilidade/edit', [
            'disponibilidade' => $disponibilidade,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Disponibilidade $disponibilidade)
    {
        if ($disponibilidade->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'tipo' => 'required|in:disponivel,indisponivel,bloqueio',
            'data_inicio' => 'required|date',
            'data_fim' => 'nullable|date|after_or_equal:data_inicio',
            'hora_inicio' => 'required|date_format:H:i',
            'hora_fim' => 'required|date_format:H:i|after:hora_inicio',
            'dias_semana' => 'nullable|array',
            'dias_semana.*' => 'integer|min:0|max:6',
            'recorrente' => 'boolean',
            'motivo' => 'nullable|string|max:500',
            'ativo' => 'boolean',
        ]);

        // Validações adicionais
        if ($validated['recorrente'] && empty($validated['dias_semana'])) {
            return back()->withErrors(['dias_semana' => 'Dias da semana são obrigatórios para disponibilidades recorrentes.']);
        }

        // Verificar conflitos (excluindo a própria disponibilidade)
        $conflitos = $this->verificarConflitos(
            auth()->id(),
            $validated['data_inicio'],
            $validated['data_fim'],
            $validated['hora_inicio'],
            $validated['hora_fim'],
            $validated['dias_semana'] ?? null,
            $validated['recorrente'],
            $disponibilidade->id
        );

        if ($conflitos->isNotEmpty()) {
            return back()->withErrors(['conflito' => 'Há conflitos com outras disponibilidades já cadastradas.']);
        }

        $disponibilidade->update($validated);

        return redirect()->route('fisioterapeuta.disponibilidade.index')
            ->with('success', 'Disponibilidade atualizada com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Disponibilidade $disponibilidade)
    {
        if ($disponibilidade->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $disponibilidade->delete();

        return redirect()->route('fisioterapeuta.disponibilidade.index')
            ->with('success', 'Disponibilidade removida com sucesso!');
    }

    /**
     * Toggle ativo status
     */
    public function toggle(Disponibilidade $disponibilidade)
    {
        if ($disponibilidade->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $disponibilidade->update(['ativo' => !$disponibilidade->ativo]);

        $status = $disponibilidade->ativo ? 'ativada' : 'desativada';
        
        return back()->with('success', "Disponibilidade {$status} com sucesso!");
    }

    /**
     * Verificar conflitos de disponibilidade
     */
    private function verificarConflitos($fisioterapeutaId, $dataInicio, $dataFim, $horaInicio, $horaFim, $diasSemana, $recorrente, $excludeId = null)
    {
        $query = Disponibilidade::where('fisioterapeuta_id', $fisioterapeutaId)
            ->where('ativo', true);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        // Verificar sobreposição de datas
        $query->where(function ($q) use ($dataInicio, $dataFim) {
            $q->where(function ($subQ) use ($dataInicio, $dataFim) {
                // Conflitos de período
                $subQ->whereBetween('data_inicio', [$dataInicio, $dataFim ?? $dataInicio]);
            })->orWhere(function ($subQ) use ($dataInicio, $dataFim) {
                $subQ->where('data_inicio', '<=', $dataInicio)
                     ->where(function ($dateQ) use ($dataInicio) {
                         $dateQ->whereNull('data_fim')
                               ->orWhere('data_fim', '>=', $dataInicio);
                     });
            });
        });

        // Verificar sobreposição de horários
        $query->where(function ($q) use ($horaInicio, $horaFim) {
            $q->where(function ($subQ) use ($horaInicio, $horaFim) {
                $subQ->where('hora_inicio', '<', $horaFim)
                     ->where('hora_fim', '>', $horaInicio);
            });
        });

        // Se for recorrente, verificar dias da semana
        if ($recorrente && $diasSemana) {
            $query->where(function ($q) use ($diasSemana) {
                $q->where('recorrente', false) // Conflita com qualquer não-recorrente
                  ->orWhere(function ($subQ) use ($diasSemana) {
                      // Ou conflita com recorrente que tem dias em comum
                      foreach ($diasSemana as $dia) {
                          $subQ->orWhereJsonContains('dias_semana', $dia);
                      }
                  });
            });
        }

        return $query->get();
    }
}
