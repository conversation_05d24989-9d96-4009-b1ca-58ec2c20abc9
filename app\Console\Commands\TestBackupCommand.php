<?php

namespace App\Console\Commands;

use App\Services\BackupService;
use App\Services\ExportService;
use App\Services\ImportService;
use Illuminate\Console\Command;

class TestBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:test {action=backup : Action to test (backup, export, import)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test backup, export and import functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');
        
        switch ($action) {
            case 'backup':
                return $this->testBackup();
            case 'export':
                return $this->testExport();
            case 'import':
                return $this->testImport();
            case 'stats':
                return $this->showStats();
            default:
                $this->error('Invalid action. Use: backup, export, import, or stats');
                return 1;
        }
    }

    protected function testBackup()
    {
        $this->info('Testing backup functionality...');
        
        try {
            $backupService = app(BackupService::class);
            
            $this->info('Creating database backup...');
            $backup = $backupService->createDatabaseOnlyBackup();
            
            $this->info('Backup created successfully!');
            $this->line('Backup ID: ' . $backup->id);
            $this->line('File: ' . $backup->file_name);
            $this->line('Size: ' . $backup->getFileSizeFormatted());
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Backup test failed: ' . $e->getMessage());
            return 1;
        }
    }

    protected function testExport()
    {
        $this->info('Testing export functionality...');
        
        try {
            $exportService = app(ExportService::class);
            
            $this->info('Exporting users to Excel...');
            $export = $exportService->exportUsers([], 'excel');
            
            $this->info('Export created successfully!');
            $this->line('Export ID: ' . $export->id);
            $this->line('File: ' . $export->file_name);
            $this->line('Size: ' . $export->getFileSizeFormatted());
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Export test failed: ' . $e->getMessage());
            return 1;
        }
    }

    protected function testImport()
    {
        $this->info('Testing import functionality...');
        
        try {
            $importService = app(ImportService::class);
            
            $this->info('Generating import template...');
            $templatePath = $importService->generateImportTemplate('users');
            
            $this->info('Template generated successfully!');
            $this->line('Template path: ' . $templatePath);
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Import test failed: ' . $e->getMessage());
            return 1;
        }
    }

    protected function showStats()
    {
        $this->info('Backup System Statistics');
        $this->line('========================');
        
        try {
            $backupService = app(BackupService::class);
            $stats = $backupService->getBackupStats();
            
            $this->line('Total Backups: ' . $stats['total_backups']);
            $this->line('Completed: ' . $stats['completed_backups']);
            $this->line('Failed: ' . $stats['failed_backups']);
            $this->line('Success Rate: ' . $stats['success_rate'] . '%');
            $this->line('Total Size: ' . $stats['total_size_formatted']);
            $this->line('Last Backup: ' . ($stats['last_backup'] ? $stats['last_backup']->format('d/m/Y H:i') : 'Never'));
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Failed to get stats: ' . $e->getMessage());
            return 1;
        }
    }
}
