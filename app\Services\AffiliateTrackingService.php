<?php

namespace App\Services;

use App\Models\Afiliado;
use App\Models\VendaAfiliado;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;

class AffiliateTrackingService
{
    /**
     * Get current affiliate referral from session or cookie
     */
    public function getCurrentAffiliateRef(Request $request = null): ?string
    {
        // Primeiro, tentar da sessão (mais recente)
        $sessionRef = session('affiliate_ref');
        if ($sessionRef) {
            return $sessionRef;
        }
        
        // Se não houver na sessão, tentar do cookie
        if ($request) {
            $cookieRef = $request->cookie('affiliate_ref');
            if ($cookieRef) {
                // Restaurar na sessão para próximas consultas
                session(['affiliate_ref' => $cookieRef]);
                return $cookieRef;
            }
        }
        
        return null;
    }
    
    /**
     * Get affiliate model from current referral
     */
    public function getCurrentAffiliate(Request $request = null): ?Afiliado
    {
        $refCode = $this->getCurrentAffiliateRef($request);
        
        if (!$refCode) {
            return null;
        }
        
        return Afiliado::where('codigo_afiliado', $refCode)
            ->where('status', 'aprovado')
            ->where('ativo', true)
            ->first();
    }
    
    /**
     * Get current coupon code from session or cookie
     */
    public function getCurrentCouponCode(Request $request = null): ?string
    {
        // Primeiro, tentar da sessão
        $sessionCoupon = session('coupon_code');
        if ($sessionCoupon) {
            return $sessionCoupon;
        }
        
        // Se não houver na sessão, tentar do cookie
        if ($request) {
            $cookieCoupon = $request->cookie('coupon_code');
            if ($cookieCoupon) {
                session(['coupon_code' => $cookieCoupon]);
                return $cookieCoupon;
            }
        }
        
        return null;
    }
    
    /**
     * Create affiliate sale when user makes a purchase
     */
    public function createAffiliateSale(
        User $user,
        $assinaturaId,
        string $planoTipo,
        float $valorVenda,
        Request $request = null
    ): ?VendaAfiliado {
        try {
            $afiliado = $this->getCurrentAffiliate($request);
            
            if (!$afiliado) {
                Log::info('No affiliate referral found for sale', [
                    'user_id' => $user->id,
                    'valor_venda' => $valorVenda,
                ]);
                return null;
            }
            
            // Verificar se não é o próprio afiliado comprando
            if ($afiliado->user_id === $user->id) {
                Log::info('Affiliate cannot earn commission from own purchase', [
                    'afiliado_id' => $afiliado->id,
                    'user_id' => $user->id,
                ]);
                return null;
            }
            
            // Criar venda de afiliado
            $vendaAfiliado = VendaAfiliado::criarVenda(
                $afiliado->id,
                $user->id,
                $planoTipo,
                $valorVenda,
                $assinaturaId
            );
            
            // Atualizar totais do afiliado
            $this->updateAffiliateStats($afiliado);
            
            // Limpar referência da sessão (opcional - depende da estratégia)
            // session()->forget('affiliate_ref');
            
            Log::info('Affiliate sale created', [
                'venda_afiliado_id' => $vendaAfiliado->id,
                'afiliado_id' => $afiliado->id,
                'user_id' => $user->id,
                'valor_venda' => $valorVenda,
                'comissao' => $vendaAfiliado->comissao,
            ]);
            
            return $vendaAfiliado;
            
        } catch (\Exception $e) {
            Log::error('Error creating affiliate sale', [
                'user_id' => $user->id,
                'valor_venda' => $valorVenda,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }
    
    /**
     * Update affiliate statistics
     */
    public function updateAffiliateStats(Afiliado $afiliado): void
    {
        try {
            // Recalcular totais
            $totalVendas = $afiliado->vendas()->confirmadas()->count();
            $totalComissoes = $afiliado->vendas()->confirmadas()->sum('comissao');
            
            // Vendas do mês atual
            $vendasMes = $afiliado->vendas()
                ->confirmadas()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count();
                
            $comissoesMes = $afiliado->vendas()
                ->confirmadas()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('comissao');
            
            // Atualizar afiliado
            $afiliado->update([
                'total_vendas' => $totalVendas,
                'total_comissoes' => $totalComissoes,
                'vendas_mes_atual' => $vendasMes,
                'comissoes_mes_atual' => $comissoesMes,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error updating affiliate stats', [
                'afiliado_id' => $afiliado->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
    
    /**
     * Clear affiliate referral (useful after purchase or timeout)
     */
    public function clearAffiliateRef(): void
    {
        session()->forget('affiliate_ref');
        session()->forget('coupon_code');
        
        // Note: Cookies will expire naturally or can be cleared with:
        // Cookie::queue(Cookie::forget('affiliate_ref'));
        // Cookie::queue(Cookie::forget('coupon_code'));
    }
    
    /**
     * Get tracking info for debugging
     */
    public function getTrackingInfo(Request $request = null): array
    {
        return [
            'affiliate_ref' => $this->getCurrentAffiliateRef($request),
            'coupon_code' => $this->getCurrentCouponCode($request),
            'affiliate' => $this->getCurrentAffiliate($request)?->only([
                'id', 'codigo_afiliado', 'nome', 'status', 'ativo'
            ]),
            'session_data' => [
                'affiliate_ref' => session('affiliate_ref'),
                'coupon_code' => session('coupon_code'),
            ],
            'cookie_data' => $request ? [
                'affiliate_ref' => $request->cookie('affiliate_ref'),
                'coupon_code' => $request->cookie('coupon_code'),
            ] : null,
        ];
    }
}
