<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fisioterapeutas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('crefito')->unique(); // Registro profissional
            $table->json('specializations'); // Especializações
            $table->text('bio')->nullable();
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->json('available_areas'); // Áreas de atendimento
            $table->json('working_hours')->nullable(); // Horários de trabalho
            $table->boolean('available')->default(true);
            $table->decimal('rating', 3, 2)->default(0); // Avaliação média
            $table->integer('total_reviews')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fisioterapeutas');
    }
};
