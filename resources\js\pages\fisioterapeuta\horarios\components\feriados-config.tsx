import { useState, useEffect } from 'react';
import { useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, MapPin, Clock, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface ConfigFeriados {
    id: number;
    fisioterapeuta_id: number;
    trabalha_feriados_nacionais: boolean;
    trabalha_feriados_estaduais: boolean;
    trabalha_feriados_municipais: boolean;
    feriados_excecoes?: number[];
    configuracoes_resumo: string;
}

interface Feriado {
    id: number;
    nome: string;
    data: string;
    tipo: 'nacional' | 'estadual' | 'municipal';
    estado?: string;
    cidade?: string;
    recorrente: boolean;
    descricao?: string;
    formatted_data: string;
    tipo_texto: string;
    localizacao: string;
}

interface Props {
    configFeriados?: ConfigFeriados;
}

export function FeriadosConfig({ configFeriados }: Props) {
    const [feriados, setFeriados] = useState<Feriado[]>([]);
    const [filtroAno, setFiltroAno] = useState(new Date().getFullYear().toString());
    const [filtroTipo, setFiltroTipo] = useState('');
    const [loading, setLoading] = useState(false);

    const { data, setData, put, processing } = useForm({
        trabalha_feriados_nacionais: configFeriados?.trabalha_feriados_nacionais || false,
        trabalha_feriados_estaduais: configFeriados?.trabalha_feriados_estaduais || false,
        trabalha_feriados_municipais: configFeriados?.trabalha_feriados_municipais || false,
        feriados_excecoes: configFeriados?.feriados_excecoes || [],
    });

    const carregarFeriados = async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams();
            if (filtroAno) params.append('ano', filtroAno);
            if (filtroTipo) params.append('tipo', filtroTipo);

            const response = await fetch(route('fisioterapeuta.horarios.feriados.list') + '?' + params.toString());
            const result = await response.json();
            
            if (result.success) {
                setFeriados(result.feriados);
            }
        } catch (error) {
            console.error('Erro ao carregar feriados:', error);
            toast.error('Erro ao carregar feriados');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        carregarFeriados();
    }, [filtroAno, filtroTipo]);

    const handleSubmit = () => {
        put(route('fisioterapeuta.horarios.feriados.update'), {
            onSuccess: () => {
                toast.success('Configurações de feriados atualizadas!');
            },
            onError: () => {
                toast.error('Erro ao atualizar configurações');
            },
        });
    };

    const toggleExcecaoFeriado = (feriadoId: number) => {
        const excecoes = [...data.feriados_excecoes];
        const index = excecoes.indexOf(feriadoId);
        
        if (index > -1) {
            excecoes.splice(index, 1);
        } else {
            excecoes.push(feriadoId);
        }
        
        setData('feriados_excecoes', excecoes);
    };

    const isExcecao = (feriadoId: number) => {
        return data.feriados_excecoes.includes(feriadoId);
    };

    const trabalhaNoFeriado = (feriado: Feriado) => {
        const isExcecaoFeriado = isExcecao(feriado.id);
        
        switch (feriado.tipo) {
            case 'nacional':
                return isExcecaoFeriado ? !data.trabalha_feriados_nacionais : data.trabalha_feriados_nacionais;
            case 'estadual':
                return isExcecaoFeriado ? !data.trabalha_feriados_estaduais : data.trabalha_feriados_estaduais;
            case 'municipal':
                return isExcecaoFeriado ? !data.trabalha_feriados_municipais : data.trabalha_feriados_municipais;
            default:
                return false;
        }
    };

    const getTipoColor = (tipo: string) => {
        switch (tipo) {
            case 'nacional':
                return 'bg-blue-100 text-blue-800';
            case 'estadual':
                return 'bg-green-100 text-green-800';
            case 'municipal':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const anos = Array.from({ length: 5 }, (_, i) => (new Date().getFullYear() + i).toString());

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-2xl font-bold">Configuração de Feriados</h2>
                <p className="text-muted-foreground">
                    Configure se você trabalha ou não em feriados nacionais, estaduais e municipais
                </p>
            </div>

            {/* Configurações Gerais */}
            <Card>
                <CardHeader>
                    <CardTitle>Configurações Gerais</CardTitle>
                    <CardDescription>
                        Defina sua disponibilidade padrão para cada tipo de feriado
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <Label className="text-base">Feriados Nacionais</Label>
                            <p className="text-sm text-muted-foreground">
                                Trabalhar em feriados nacionais (Natal, Ano Novo, etc.)
                            </p>
                        </div>
                        <Switch
                            checked={data.trabalha_feriados_nacionais}
                            onCheckedChange={(checked) => setData('trabalha_feriados_nacionais', checked)}
                        />
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <Label className="text-base">Feriados Estaduais</Label>
                            <p className="text-sm text-muted-foreground">
                                Trabalhar em feriados estaduais
                            </p>
                        </div>
                        <Switch
                            checked={data.trabalha_feriados_estaduais}
                            onCheckedChange={(checked) => setData('trabalha_feriados_estaduais', checked)}
                        />
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <Label className="text-base">Feriados Municipais</Label>
                            <p className="text-sm text-muted-foreground">
                                Trabalhar em feriados municipais
                            </p>
                        </div>
                        <Switch
                            checked={data.trabalha_feriados_municipais}
                            onCheckedChange={(checked) => setData('trabalha_feriados_municipais', checked)}
                        />
                    </div>

                    <div className="pt-4">
                        <Button onClick={handleSubmit} disabled={processing}>
                            Salvar Configurações
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Lista de Feriados */}
            <Card>
                <CardHeader>
                    <CardTitle>Feriados Específicos</CardTitle>
                    <CardDescription>
                        Configure exceções para feriados específicos
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Filtros */}
                    <div className="flex gap-4">
                        <div className="flex-1">
                            <Label htmlFor="filtro-ano">Ano</Label>
                            <Select value={filtroAno} onValueChange={setFiltroAno}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Selecione o ano" />
                                </SelectTrigger>
                                <SelectContent>
                                    {anos.map((ano) => (
                                        <SelectItem key={ano} value={ano}>
                                            {ano}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex-1">
                            <Label htmlFor="filtro-tipo">Tipo</Label>
                            <Select value={filtroTipo} onValueChange={setFiltroTipo}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Todos os tipos" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">Todos</SelectItem>
                                    <SelectItem value="nacional">Nacional</SelectItem>
                                    <SelectItem value="estadual">Estadual</SelectItem>
                                    <SelectItem value="municipal">Municipal</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Lista de Feriados */}
                    <div className="space-y-2">
                        {loading ? (
                            <div className="text-center py-8">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                                <p className="text-sm text-muted-foreground mt-2">Carregando feriados...</p>
                            </div>
                        ) : feriados.length === 0 ? (
                            <div className="text-center py-8">
                                <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                                <p className="text-muted-foreground">Nenhum feriado encontrado</p>
                            </div>
                        ) : (
                            feriados.map((feriado) => {
                                const trabalha = trabalhaNoFeriado(feriado);
                                const isExcecaoAtual = isExcecao(feriado.id);
                                
                                return (
                                    <div
                                        key={feriado.id}
                                        className="flex items-center justify-between p-3 border rounded-lg"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <h4 className="font-medium">{feriado.nome}</h4>
                                                <Badge className={getTipoColor(feriado.tipo)}>
                                                    {feriado.tipo_texto}
                                                </Badge>
                                                {trabalha ? (
                                                    <Badge className="bg-green-100 text-green-800">
                                                        Trabalha
                                                    </Badge>
                                                ) : (
                                                    <Badge className="bg-red-100 text-red-800">
                                                        Não trabalha
                                                    </Badge>
                                                )}
                                                {isExcecaoAtual && (
                                                    <Badge variant="outline">
                                                        Exceção
                                                    </Badge>
                                                )}
                                            </div>
                                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                                <span className="flex items-center gap-1">
                                                    <Calendar className="h-3 w-3" />
                                                    {feriado.formatted_data}
                                                </span>
                                                <span className="flex items-center gap-1">
                                                    <MapPin className="h-3 w-3" />
                                                    {feriado.localizacao}
                                                </span>
                                            </div>
                                            {feriado.descricao && (
                                                <p className="text-sm text-muted-foreground mt-1">
                                                    {feriado.descricao}
                                                </p>
                                            )}
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Label htmlFor={`feriado-${feriado.id}`} className="text-sm">
                                                Exceção
                                            </Label>
                                            <Checkbox
                                                id={`feriado-${feriado.id}`}
                                                checked={isExcecaoAtual}
                                                onCheckedChange={() => toggleExcecaoFeriado(feriado.id)}
                                            />
                                        </div>
                                    </div>
                                );
                            })
                        )}
                    </div>

                    {feriados.length > 0 && (
                        <div className="pt-4 border-t">
                            <div className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                                <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
                                <div className="text-sm">
                                    <p className="font-medium text-blue-900">Como funcionam as exceções:</p>
                                    <p className="text-blue-700">
                                        Marque "Exceção" para inverter sua configuração padrão para um feriado específico.
                                        Por exemplo, se você não trabalha em feriados nacionais, mas quer trabalhar no Natal,
                                        marque o Natal como exceção.
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
