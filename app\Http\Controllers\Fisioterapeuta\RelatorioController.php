<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Models\RelatorioSessao;
use App\Models\RelatorioAnexo;
use App\Services\FileUploadService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class RelatorioController extends Controller
{
    public function index(Request $request)
    {
        $fisioterapeuta = auth()->user();
        
        // Filtros
        $dataInicio = $request->get('data_inicio', Carbon::now()->subYear()->format('Y-m-d'));
        $dataFim = $request->get('data_fim', Carbon::now()->addYear()->format('Y-m-d'));
        $paciente = $request->get('paciente');
        $status = $request->get('status', 'todos'); // todos, com_relatorio, sem_relatorio

        // Query base - agendamentos concluídos
        $query = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->where('status', 'concluido')
            ->with(['paciente', 'relatorioSessao'])
            ->whereBetween('finished_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ]);

        // Filtro por paciente
        if ($paciente) {
            $query->where('paciente_id', $paciente);
        }

        // Filtro por status do relatório
        if ($status === 'com_relatorio') {
            $query->whereHas('relatorioSessao');
        } elseif ($status === 'sem_relatorio') {
            $query->whereDoesntHave('relatorioSessao');
        }

        $agendamentos = $query->orderBy('finished_at', 'desc')
            ->paginate(15)
            ->withQueryString()
            ->through(function ($agendamento) {
                return [
                    'id' => $agendamento->id,
                    'data_hora' => $agendamento->scheduled_at,
                    'finalizado_em' => $agendamento->finished_at,
                    'tipo' => $agendamento->service_type,
                    'duracao' => $agendamento->duration,
                    'paciente' => [
                        'id' => $agendamento->paciente->id,
                        'name' => $agendamento->paciente->name,
                    ],
                    'tem_relatorio' => $agendamento->relatorioSessao !== null,
                    'relatorio_id' => $agendamento->relatorioSessao?->id,
                    'relatorio_criado_em' => $agendamento->relatorioSessao?->created_at,
                ];
            });

        // Estatísticas
        $totalSessoes = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->where('status', 'concluido')
            ->whereBetween('finished_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])->count();

        $comRelatorio = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->where('status', 'concluido')
            ->whereHas('relatorioSessao')
            ->whereBetween('finished_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])->count();

        // Estatísticas de relatórios
        $totalRelatorios = RelatorioSessao::whereHas('agendamento', function($query) use ($fisioterapeuta) {
            $query->where('fisioterapeuta_id', $fisioterapeuta->id);
        })->count();

        $relatoriosMes = RelatorioSessao::whereHas('agendamento', function($query) use ($fisioterapeuta) {
            $query->where('fisioterapeuta_id', $fisioterapeuta->id);
        })->whereMonth('created_at', Carbon::now()->month)
          ->whereYear('created_at', Carbon::now()->year)
          ->count();

        $pacientesAtendidos = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->where('status', 'concluido')
            ->whereHas('relatorioSessao')
            ->distinct('paciente_id')
            ->count();

        $receitaMes = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->where('status', 'concluido')
            ->whereMonth('finished_at', Carbon::now()->month)
            ->whereYear('finished_at', Carbon::now()->year)
            ->sum('valor');

        // Estatísticas de dor
        $relatoriosComDor = RelatorioSessao::whereHas('agendamento', function($query) use ($fisioterapeuta) {
            $query->where('fisioterapeuta_id', $fisioterapeuta->id);
        })->whereNotNull('pain_level_before')
          ->whereNotNull('pain_level_after');

        $mediaDorAntes = $relatoriosComDor->avg('pain_level_before') ?? 0;
        $mediaDorDepois = $relatoriosComDor->avg('pain_level_after') ?? 0;
        $melhoriaDor = $mediaDorAntes > 0 ? (($mediaDorAntes - $mediaDorDepois) / $mediaDorAntes) * 100 : 0;

        $stats = [
            'total_relatorios' => $totalRelatorios,
            'relatorios_mes' => $relatoriosMes,
            'pacientes_atendidos' => $pacientesAtendidos,
            'sessoes_concluidas' => $totalSessoes,
            'receita_mes' => $receitaMes,
            'media_dor_antes' => round($mediaDorAntes, 1),
            'media_dor_depois' => round($mediaDorDepois, 1),
            'melhoria_dor' => round($melhoriaDor, 1),
        ];

        // Lista de pacientes para filtro
        $pacientesOptions = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->where('status', 'concluido')
            ->with('paciente')
            ->distinct('paciente_id')
            ->get()
            ->pluck('paciente')
            ->map(function ($paciente) {
                return [
                    'id' => $paciente->id,
                    'name' => $paciente->name,
                ];
            })
            ->sortBy('name')
            ->values();

        return Inertia::render('fisioterapeuta/relatorios', [
            'agendamentos' => $agendamentos,
            'stats' => $stats,
            'filtros' => $request->only(['data_inicio', 'data_fim', 'paciente', 'status']),
            'pacientesOptions' => $pacientesOptions,
        ]);
    }

    public function create(Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao fisioterapeuta
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se o agendamento foi concluído
        if ($agendamento->status !== 'concluido') {
            return redirect()->route('fisioterapeuta.agenda.index')
                ->withErrors(['message' => 'Só é possível criar relatório para sessões concluídas.']);
        }

        // Verificar se já existe relatório
        if ($agendamento->relatorioSessao) {
            return redirect()->route('fisioterapeuta.relatorios.edit', $agendamento->relatorioSessao);
        }

        $agendamento->load(['paciente', 'assinatura.plano']);

        return Inertia::render('fisioterapeuta/relatorios/create', [
            'agendamento' => [
                'id' => $agendamento->id,
                'data_hora' => $agendamento->scheduled_at,
                'finalizado_em' => $agendamento->finished_at,
                'tipo' => $agendamento->service_type,
                'duracao' => $agendamento->duration,
                'observacoes' => $agendamento->notes,
                'paciente' => [
                    'id' => $agendamento->paciente->id,
                    'name' => $agendamento->paciente->name,
                ],
                'plano' => $agendamento->assinatura->plano->name ?? null,
            ],
        ]);
    }

    public function store(Request $request, Agendamento $agendamento)
    {
        // Verificar se o agendamento pertence ao fisioterapeuta
        if ($agendamento->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        // Verificar se já existe relatório
        if ($agendamento->relatorioSessao) {
            return back()->withErrors(['message' => 'Já existe um relatório para esta sessão.']);
        }

        $validated = $request->validate([
            'observacoes' => 'required|string|max:2000',
            'exercicios_realizados' => 'nullable|array',
            'exercicios_realizados.*' => 'string|max:500',
            'evolucao' => 'nullable|string|max:2000',
            'proximos_passos' => 'nullable|string|max:2000',
            'dor_inicial' => 'nullable|integer|min:0|max:10',
            'dor_final' => 'nullable|integer|min:0|max:10',
            'mobilidade' => 'nullable|string|in:limitada,parcial,normal,melhorada',
            'satisfacao_paciente' => 'nullable|integer|min:1|max:5',
        ]);

        RelatorioSessao::create([
            'agendamento_id' => $agendamento->id,
            'fisioterapeuta_id' => auth()->id(),
            'paciente_id' => $agendamento->paciente_id,
            'objective' => 'Sessão de fisioterapia - ' . $agendamento->service_type,
            'activities_performed' => isset($validated['exercicios_realizados']) ? implode("\n", $validated['exercicios_realizados']) : 'Atividades conforme observações',
            'patient_response' => 'Conforme observações registradas',
            'observations' => $validated['observacoes'],
            'exercises' => isset($validated['exercicios_realizados']) ? implode("\n", $validated['exercicios_realizados']) : null,
            'progress_notes' => $validated['evolucao'] ?? null,
            'next_steps' => $validated['proximos_passos'] ?? null,
            'pain_level_before' => $validated['dor_inicial'] ?? null,
            'pain_level_after' => $validated['dor_final'] ?? null,
            'mobility_assessment' => $validated['mobilidade'] ?? null,
            'patient_satisfaction' => $validated['satisfacao_paciente'] ?? null,
        ]);

        return redirect()->route('fisioterapeuta.relatorios.index')
            ->with('success', 'Relatório criado com sucesso!');
    }

    public function show(RelatorioSessao $relatorio)
    {
        // Verificar se o relatório pertence ao fisioterapeuta
        if ($relatorio->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $relatorio->load(['agendamento.paciente', 'agendamento.assinatura.plano', 'anexos']);

        return Inertia::render('fisioterapeuta/relatorios/show', [
            'relatorio' => [
                'id' => $relatorio->id,
                'observacoes' => $relatorio->observations,
                'exercicios' => $relatorio->exercises,
                'evolucao' => $relatorio->progress_notes,
                'proximos_passos' => $relatorio->next_steps,
                'dor_inicial' => $relatorio->pain_level_before,
                'dor_final' => $relatorio->pain_level_after,
                'mobilidade' => $relatorio->mobility_assessment,
                'satisfacao_paciente' => $relatorio->patient_satisfaction,
                'created_at' => $relatorio->created_at,
                'updated_at' => $relatorio->updated_at,
                'anexos' => $relatorio->anexos->map(function ($anexo) {
                    return [
                        'id' => $anexo->id,
                        'nome_original' => $anexo->nome_original,
                        'nome_arquivo' => $anexo->nome_arquivo,
                        'tamanho_formatado' => $this->formatFileSize($anexo->tamanho_arquivo),
                        'tipo_anexo' => $anexo->tipo_anexo,
                        'descricao' => $anexo->descricao,
                        'url' => Storage::url($anexo->caminho_arquivo),
                        'created_at' => $anexo->created_at,
                    ];
                }),
                'agendamento' => [
                    'id' => $relatorio->agendamento->id,
                    'data_hora' => $relatorio->agendamento->scheduled_at,
                    'finalizado_em' => $relatorio->agendamento->finished_at,
                    'tipo' => $relatorio->agendamento->service_type,
                    'duracao' => $relatorio->agendamento->duration,
                    'paciente' => [
                        'id' => $relatorio->agendamento->paciente->id,
                        'name' => $relatorio->agendamento->paciente->name,
                    ],
                    'plano' => $relatorio->agendamento->assinatura->plano->name ?? null,
                ],
            ],
        ]);
    }

    public function edit(RelatorioSessao $relatorio)
    {
        // Verificar se o relatório pertence ao fisioterapeuta
        if ($relatorio->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $relatorio->load(['agendamento.paciente', 'agendamento.assinatura.plano']);

        return Inertia::render('fisioterapeuta/relatorios/edit', [
            'relatorio' => [
                'id' => $relatorio->id,
                'observacoes' => $relatorio->observations,
                'exercicios' => $relatorio->exercises,
                'evolucao' => $relatorio->progress_notes,
                'proximos_passos' => $relatorio->next_steps,
                'dor_inicial' => $relatorio->pain_level_before,
                'dor_final' => $relatorio->pain_level_after,
                'mobilidade' => $relatorio->mobility_assessment,
                'satisfacao_paciente' => $relatorio->patient_satisfaction,
                'agendamento' => [
                    'id' => $relatorio->agendamento->id,
                    'data_hora' => $relatorio->agendamento->scheduled_at,
                    'finalizado_em' => $relatorio->agendamento->finished_at,
                    'tipo' => $relatorio->agendamento->service_type,
                    'duracao' => $relatorio->agendamento->duration,
                    'paciente' => [
                        'id' => $relatorio->agendamento->paciente->id,
                        'name' => $relatorio->agendamento->paciente->name,
                    ],
                    'plano' => $relatorio->agendamento->plano->name ?? null,
                ],
            ],
        ]);
    }

    public function update(Request $request, RelatorioSessao $relatorio)
    {
        // Verificar se o relatório pertence ao fisioterapeuta
        if ($relatorio->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'observacoes' => 'required|string|max:2000',
            'exercicios' => 'nullable|string|max:2000',
            'evolucao' => 'nullable|string|max:2000',
            'proximos_passos' => 'nullable|string|max:2000',
            'dor_inicial' => 'nullable|integer|min:0|max:10',
            'dor_final' => 'nullable|integer|min:0|max:10',
            'mobilidade' => 'nullable|string|in:limitada,parcial,normal,melhorada',
            'satisfacao_paciente' => 'nullable|integer|min:1|max:5',
        ]);

        $relatorio->update([
            'observations' => $validated['observacoes'],
            'exercises' => $validated['exercicios'],
            'progress_notes' => $validated['evolucao'],
            'next_steps' => $validated['proximos_passos'],
            'pain_level_before' => $validated['dor_inicial'],
            'pain_level_after' => $validated['dor_final'],
            'mobility_assessment' => $validated['mobilidade'],
            'patient_satisfaction' => $validated['satisfacao_paciente'],
        ]);

        return redirect()->route('fisioterapeuta.relatorios.show', $relatorio)
            ->with('success', 'Relatório atualizado com sucesso!');
    }

    public function pendentes()
    {
        $fisioterapeuta = auth()->user();
        
        $agendamentosPendentes = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->where('status', 'concluido')
            ->whereDoesntHave('relatorioSessao')
            ->with(['paciente'])
            ->orderBy('finished_at', 'desc')
            ->get()
            ->map(function ($agendamento) {
                return [
                    'id' => $agendamento->id,
                    'data_hora' => $agendamento->scheduled_at,
                    'finalizado_em' => $agendamento->finished_at,
                    'tipo' => $agendamento->service_type,
                    'paciente' => [
                        'id' => $agendamento->paciente->id,
                        'name' => $agendamento->paciente->name,
                    ],
                    'dias_pendente' => Carbon::now()->diffInDays($agendamento->finished_at),
                ];
            });

        return Inertia::render('fisioterapeuta/relatorios/pendentes', [
            'agendamentosPendentes' => $agendamentosPendentes,
        ]);
    }

    /**
     * Upload de anexo para relatório
     */
    public function uploadAnexo(Request $request, RelatorioSessao $relatorio)
    {
        // Verificar se o relatório pertence ao fisioterapeuta
        if ($relatorio->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        $request->validate([
            'arquivo' => 'required|file|max:10240', // 10MB max
            'descricao' => 'nullable|string|max:255',
        ]);

        try {
            $file = $request->file('arquivo');
            $fileUploadService = new FileUploadService();

            // Determinar tipo de anexo baseado no MIME type
            $mimeType = $file->getMimeType();
            $tipoAnexo = 'documento';

            if (str_starts_with($mimeType, 'image/')) {
                $tipoAnexo = 'imagem';
            } elseif (str_starts_with($mimeType, 'video/')) {
                $tipoAnexo = 'video';
            } elseif (str_starts_with($mimeType, 'audio/')) {
                $tipoAnexo = 'audio';
            }

            // Upload do arquivo
            $uploadResult = $fileUploadService->uploadDocument($file, 'relatorio', auth()->id());

            // Salvar no banco
            $anexo = RelatorioAnexo::create([
                'relatorio_sessao_id' => $relatorio->id,
                'nome_arquivo' => $uploadResult['filename'],
                'nome_original' => $file->getClientOriginalName(),
                'caminho_arquivo' => $uploadResult['path'],
                'tamanho_arquivo' => $uploadResult['size'],
                'tipo_mime' => $mimeType,
                'tipo_anexo' => $tipoAnexo,
                'descricao' => $request->descricao,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Arquivo enviado com sucesso!',
                'anexo' => [
                    'id' => $anexo->id,
                    'nome_original' => $anexo->nome_original,
                    'tamanho_formatado' => $anexo->tamanho_formatado,
                    'tipo_anexo' => $anexo->tipo_anexo,
                    'url' => $anexo->url,
                    'descricao' => $anexo->descricao,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao enviar arquivo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remover anexo do relatório
     */
    public function removeAnexo(RelatorioAnexo $anexo)
    {
        // Verificar se o anexo pertence a um relatório do fisioterapeuta
        if ($anexo->relatorioSessao->fisioterapeuta_id !== auth()->id()) {
            abort(403);
        }

        try {
            // Deletar arquivo do storage
            Storage::disk('public')->delete($anexo->caminho_arquivo);

            // Deletar registro do banco
            $anexo->delete();

            return response()->json([
                'success' => true,
                'message' => 'Arquivo removido com sucesso!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao remover arquivo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Formatar tamanho do arquivo
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
}
