<?php

namespace App\Services;

use App\Models\BackupLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use ZipArchive;

class BackupService
{
    private string $backupDisk = 'local';
    private string $backupPath = 'backups';

    public function __construct()
    {
        $this->backupDisk = config('backup.disk', 'local');
        $this->backupPath = config('backup.path', 'backups');
    }

    /**
     * Criar backup completo (banco + arquivos) com logging
     */
    public function createFullBackup(?int $userId = null): BackupLog
    {
        $backupLog = BackupLog::create([
            'type' => BackupLog::TYPE_BACKUP,
            'status' => BackupLog::STATUS_PENDING,
            'user_id' => $userId,
            'started_at' => now(),
            'metadata' => [
                'backup_type' => 'full',
                'includes' => ['database', 'files', 'uploads']
            ]
        ]);

        $timestamp = now()->format('Y-m-d_H-i-s');
        $backupName = "backup_full_{$timestamp}";

        try {
            $backupLog->update(['status' => BackupLog::STATUS_PROCESSING]);

            Log::info('Iniciando backup completo', [
                'backup_id' => $backupLog->id,
                'backup_name' => $backupName
            ]);

            // Criar backup do banco de dados
            $dbBackupPath = $this->createDatabaseBackup($backupName);

            // Criar backup dos arquivos
            $filesBackupPath = $this->createFilesBackup($backupName);

            // Criar arquivo ZIP com tudo
            $zipPath = $this->createZipBackup($backupName, [$dbBackupPath, $filesBackupPath]);

            // Limpar arquivos temporários
            $this->cleanupTempFiles([$dbBackupPath, $filesBackupPath]);

            // Obter informações do arquivo final
            $fullPath = Storage::disk($this->backupDisk)->path($zipPath);
            $fileSize = file_exists($fullPath) ? filesize($fullPath) : 0;

            $backupLog->update([
                'status' => BackupLog::STATUS_COMPLETED,
                'file_name' => basename($zipPath),
                'file_path' => $zipPath,
                'file_size' => $fileSize,
                'completed_at' => now(),
            ]);

            // Limpar backups antigos
            $this->cleanupOldBackups();

            Log::info('Backup completo criado com sucesso', [
                'backup_id' => $backupLog->id,
                'file_name' => basename($zipPath),
                'file_size' => $fileSize
            ]);

            return $backupLog;

        } catch (\Exception $e) {
            $backupLog->update([
                'status' => BackupLog::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);
            Log::error('Erro ao criar backup completo', [
                'backup_id' => $backupLog->id,
                'backup_name' => $backupName,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Criar backup apenas do banco de dados
     */
    public function createDatabaseBackup(string $backupName = null): string
    {
        $backupName = $backupName ?: 'db_backup_' . now()->format('Y-m-d_H-i-s');
        $connection = config('database.default');

        if ($connection === 'sqlite') {
            return $this->createSQLiteBackup($backupName);
        } else {
            return $this->createMySQLBackup($backupName);
        }
    }

    /**
     * Criar backup SQLite
     */
    protected function createSQLiteBackup(string $backupName): string
    {
        $filename = "{$backupName}.sqlite";
        $filepath = "{$this->backupPath}/database/{$filename}";

        $dbPath = database_path('database.sqlite');

        if (!file_exists($dbPath)) {
            throw new \Exception('Arquivo de banco SQLite não encontrado');
        }

        // Para SQLite, simplesmente copiar o arquivo
        $content = file_get_contents($dbPath);
        Storage::disk($this->backupDisk)->put($filepath, $content);

        return $filepath;
    }

    /**
     * Criar backup MySQL
     */
    protected function createMySQLBackup(string $backupName): string
    {
        $filename = "{$backupName}.sql";
        $filepath = "{$this->backupPath}/database/{$filename}";

        $connection = config('database.default');
        $database = config("database.connections.{$connection}.database");
        $username = config("database.connections.{$connection}.username");
        $password = config("database.connections.{$connection}.password");
        $host = config("database.connections.{$connection}.host");
        $port = config("database.connections.{$connection}.port", 3306);

        // Comando mysqldump
        $command = sprintf(
            'mysqldump --user=%s --password=%s --host=%s --port=%s --single-transaction --routines --triggers %s',
            escapeshellarg($username),
            escapeshellarg($password),
            escapeshellarg($host),
            escapeshellarg($port),
            escapeshellarg($database)
        );

        $output = [];
        $returnVar = 0;
        exec($command, $output, $returnVar);

        if ($returnVar !== 0) {
            throw new \Exception('Erro ao executar mysqldump: ' . implode("\n", $output));
        }

        $sqlContent = implode("\n", $output);
        Storage::disk($this->backupDisk)->put($filepath, $sqlContent);

        return $filepath;
    }

    /**
     * Criar backup dos arquivos
     */
    public function createFilesBackup(string $backupName = null): string
    {
        $backupName = $backupName ?: 'files_backup_' . now()->format('Y-m-d_H-i-s');
        $filename = "{$backupName}.zip";
        $filepath = "{$this->backupPath}/files/{$filename}";

        $zip = new ZipArchive();
        $tempPath = storage_path("app/temp_{$filename}");

        if ($zip->open($tempPath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('Não foi possível criar arquivo ZIP');
        }

        // Adicionar arquivos do storage/app/public
        $this->addDirectoryToZip($zip, storage_path('app/public'), 'public');
        
        // Adicionar arquivos do storage/app/private se existir
        if (is_dir(storage_path('app/private'))) {
            $this->addDirectoryToZip($zip, storage_path('app/private'), 'private');
        }

        $zip->close();

        // Mover para o storage disk
        $content = file_get_contents($tempPath);
        Storage::disk($this->backupDisk)->put($filepath, $content);
        unlink($tempPath);

        return $filepath;
    }

    /**
     * Criar arquivo ZIP com múltiplos arquivos
     */
    private function createZipBackup(string $backupName, array $files): string
    {
        $filename = "{$backupName}.zip";
        $filepath = "{$this->backupPath}/{$filename}";

        $zip = new ZipArchive();
        $tempPath = storage_path("app/temp_{$filename}");

        if ($zip->open($tempPath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('Não foi possível criar arquivo ZIP final');
        }

        foreach ($files as $file) {
            if (Storage::disk($this->backupDisk)->exists($file)) {
                $content = Storage::disk($this->backupDisk)->get($file);
                $zip->addFromString(basename($file), $content);
            }
        }

        $zip->close();

        // Mover para o storage disk
        $content = file_get_contents($tempPath);
        Storage::disk($this->backupDisk)->put($filepath, $content);
        unlink($tempPath);

        return $filepath;
    }

    /**
     * Adicionar diretório ao ZIP recursivamente
     */
    private function addDirectoryToZip(ZipArchive $zip, string $dir, string $zipDir = ''): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($files as $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                $relativePath = $zipDir . '/' . substr($filePath, strlen($dir) + 1);
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    /**
     * Limpar arquivos temporários
     */
    private function cleanupTempFiles(array $files): void
    {
        foreach ($files as $file) {
            if (Storage::disk($this->backupDisk)->exists($file)) {
                Storage::disk($this->backupDisk)->delete($file);
            }
        }
    }

    /**
     * Limpar backups antigos (manter apenas os últimos 7)
     */
    public function cleanupOldBackups(int $keepCount = 7): void
    {
        $backups = $this->listBackups();
        
        if (count($backups) > $keepCount) {
            $toDelete = array_slice($backups, $keepCount);
            
            foreach ($toDelete as $backup) {
                Storage::disk($this->backupDisk)->delete($backup['path']);
                Log::info('Backup antigo removido', ['path' => $backup['path']]);
            }
        }
    }

    /**
     * Listar backups existentes
     */
    public function listBackups(): array
    {
        $files = Storage::disk($this->backupDisk)->files($this->backupPath);
        $backups = [];

        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'zip') {
                $backups[] = [
                    'name' => basename($file),
                    'path' => $file,
                    'size' => Storage::disk($this->backupDisk)->size($file),
                    'created_at' => Storage::disk($this->backupDisk)->lastModified($file)
                ];
            }
        }

        // Ordenar por data de criação (mais recente primeiro)
        usort($backups, function ($a, $b) {
            return $b['created_at'] - $a['created_at'];
        });

        return $backups;
    }

    /**
     * Restaurar backup do banco de dados
     */
    public function restoreDatabase(string $backupPath): bool
    {
        try {
            if (!Storage::disk($this->backupDisk)->exists($backupPath)) {
                throw new \Exception('Arquivo de backup não encontrado');
            }

            $sqlContent = Storage::disk($this->backupDisk)->get($backupPath);
            
            // Executar SQL
            DB::unprepared($sqlContent);
            
            Log::info('Banco de dados restaurado com sucesso', ['backup_path' => $backupPath]);
            
            return true;

        } catch (\Exception $e) {
            Log::error('Erro ao restaurar banco de dados', [
                'backup_path' => $backupPath,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Criar backup apenas do banco de dados com logging
     */
    public function createDatabaseOnlyBackup(?int $userId = null): BackupLog
    {
        $backupLog = BackupLog::create([
            'type' => BackupLog::TYPE_BACKUP,
            'status' => BackupLog::STATUS_PENDING,
            'user_id' => $userId,
            'started_at' => now(),
            'metadata' => [
                'backup_type' => 'database_only',
                'includes' => ['database']
            ]
        ]);

        $timestamp = now()->format('Y-m-d_H-i-s');
        $backupName = "backup_db_{$timestamp}";

        try {
            $backupLog->update(['status' => BackupLog::STATUS_PROCESSING]);

            // Criar backup apenas do banco
            $dbBackupPath = $this->createDatabaseBackup($backupName);

            $fullPath = Storage::disk($this->backupDisk)->path($dbBackupPath);
            $fileSize = file_exists($fullPath) ? filesize($fullPath) : 0;

            $backupLog->update([
                'status' => BackupLog::STATUS_COMPLETED,
                'file_name' => basename($dbBackupPath),
                'file_path' => $dbBackupPath,
                'file_size' => $fileSize,
                'completed_at' => now(),
            ]);

            return $backupLog;

        } catch (\Exception $e) {
            $backupLog->update([
                'status' => BackupLog::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * Listar backups com logs
     */
    public function listBackupsWithLogs(): array
    {
        return BackupLog::byType(BackupLog::TYPE_BACKUP)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Baixar backup por ID
     */
    public function downloadBackupById(int $backupId): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $backupLog = BackupLog::findOrFail($backupId);

        if (!$backupLog->isCompleted()) {
            throw new \Exception('Backup não está completo');
        }

        $fullPath = Storage::disk($this->backupDisk)->path($backupLog->file_path);
        if (!file_exists($fullPath)) {
            throw new \Exception('Arquivo de backup não encontrado');
        }

        return Storage::disk($this->backupDisk)->download($backupLog->file_path, $backupLog->file_name);
    }

    /**
     * Deletar backup por ID
     */
    public function deleteBackupById(int $backupId): bool
    {
        $backupLog = BackupLog::findOrFail($backupId);

        try {
            if ($backupLog->file_path) {
                $fullPath = Storage::disk($this->backupDisk)->path($backupLog->file_path);
                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }
            }

            $backupLog->delete();

            return true;
        } catch (\Exception $e) {
            Log::error('Falha ao deletar backup', [
                'backup_id' => $backupId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Obter estatísticas de backup
     */
    public function getBackupStats(): array
    {
        $totalBackups = BackupLog::byType(BackupLog::TYPE_BACKUP)->count();
        $completedBackups = BackupLog::byType(BackupLog::TYPE_BACKUP)->completed()->count();
        $failedBackups = BackupLog::byType(BackupLog::TYPE_BACKUP)->failed()->count();

        $totalSize = BackupLog::byType(BackupLog::TYPE_BACKUP)
            ->completed()
            ->sum('file_size');

        $lastBackup = BackupLog::byType(BackupLog::TYPE_BACKUP)
            ->completed()
            ->latest()
            ->first();

        return [
            'total_backups' => $totalBackups,
            'completed_backups' => $completedBackups,
            'failed_backups' => $failedBackups,
            'success_rate' => $totalBackups > 0 ? round(($completedBackups / $totalBackups) * 100, 2) : 0,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'last_backup' => $lastBackup ? $lastBackup->created_at : null,
        ];
    }

    /**
     * Formatar bytes em formato legível
     */
    protected function formatBytes(int $bytes): string
    {
        if ($bytes === 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
