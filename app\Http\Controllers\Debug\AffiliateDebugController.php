<?php

namespace App\Http\Controllers\Debug;

use App\Http\Controllers\Controller;
use App\Services\AffiliateTrackingService;
use Illuminate\Http\Request;

class AffiliateDebugController extends Controller
{
    /**
     * Show affiliate tracking debug information
     */
    public function index(Request $request)
    {
        $affiliateService = new AffiliateTrackingService();
        
        $debugInfo = [
            'tracking_info' => $affiliateService->getTrackingInfo($request),
            'request_params' => [
                'ref' => $request->query('ref'),
                'cupom' => $request->query('cupom'),
                'full_url' => $request->fullUrl(),
            ],
            'headers' => [
                'user_agent' => $request->userAgent(),
                'referer' => $request->header('referer'),
                'ip' => $request->ip(),
            ],
            'cookies_raw' => $request->cookies->all(),
            'session_data' => [
                'affiliate_ref' => session('affiliate_ref'),
                'coupon_code' => session('coupon_code'),
                'all_session' => session()->all(),
            ],
        ];
        
        return response()->json($debugInfo, 200, [], JSON_PRETTY_PRINT);
    }
    
    /**
     * Clear affiliate tracking data
     */
    public function clear(Request $request)
    {
        $affiliateService = new AffiliateTrackingService();
        $affiliateService->clearAffiliateRef();
        
        return response()->json([
            'message' => 'Affiliate tracking data cleared',
            'timestamp' => now()->toISOString(),
        ]);
    }
    
    /**
     * Simulate affiliate sale
     */
    public function simulateSale(Request $request)
    {
        $user = auth()->user();
        
        if (!$user) {
            return response()->json([
                'error' => 'User must be authenticated to simulate sale',
            ], 401);
        }
        
        $affiliateService = new AffiliateTrackingService();
        
        $vendaAfiliado = $affiliateService->createAffiliateSale(
            $user,
            null, // assinatura_id
            'teste',
            100.00,
            $request
        );
        
        return response()->json([
            'message' => 'Sale simulation completed',
            'venda_afiliado' => $vendaAfiliado,
            'tracking_info' => $affiliateService->getTrackingInfo($request),
        ]);
    }
}
