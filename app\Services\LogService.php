<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Models\User;

class LogService
{
    /**
     * Log de ação do usuário
     */
    public function logUserAction(User $user, string $action, array $data = [], string $level = 'info'): void
    {
        $context = [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_role' => $user->role,
            'action' => $action,
            'timestamp' => now()->toISOString(),
            'data' => $data
        ];

        Log::channel('user_actions')->{$level}("User action: {$action}", $context);
    }

    /**
     * Log de agendamento
     */
    public function logAgendamento(string $action, int $agendamentoId, ?User $user = null, array $data = []): void
    {
        $context = [
            'agendamento_id' => $agendamentoId,
            'action' => $action,
            'timestamp' => now()->toISOString(),
            'data' => $data
        ];

        if ($user) {
            $context['user_id'] = $user->id;
            $context['user_role'] = $user->role;
        }

        Log::channel('agendamentos')->info("Agendamento {$action}", $context);
    }

    /**
     * Log de pagamento
     */
    public function logPagamento(string $action, array $paymentData, ?User $user = null): void
    {
        $context = [
            'action' => $action,
            'timestamp' => now()->toISOString(),
            'payment_data' => $paymentData
        ];

        if ($user) {
            $context['user_id'] = $user->id;
        }

        Log::channel('pagamentos')->info("Payment {$action}", $context);
    }

    /**
     * Log de erro de sistema
     */
    public function logSystemError(\Throwable $exception, Request $request = null, ?User $user = null): void
    {
        $context = [
            'exception' => [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ],
            'timestamp' => now()->toISOString()
        ];

        if ($request) {
            $context['request'] = [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'input' => $this->sanitizeInput($request->all())
            ];
        }

        if ($user) {
            $context['user_id'] = $user->id;
            $context['user_role'] = $user->role;
        }

        Log::channel('errors')->error('System error occurred', $context);
    }

    /**
     * Log de tentativa de login
     */
    public function logLoginAttempt(string $email, bool $successful, Request $request, string $reason = ''): void
    {
        $context = [
            'email' => $email,
            'successful' => $successful,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toISOString()
        ];

        if (!$successful && $reason) {
            $context['failure_reason'] = $reason;
        }

        $level = $successful ? 'info' : 'warning';
        $message = $successful ? 'Successful login' : 'Failed login attempt';

        Log::channel('auth')->{$level}($message, $context);
    }

    /**
     * Log de performance
     */
    public function logPerformance(string $operation, float $executionTime, array $data = []): void
    {
        $context = [
            'operation' => $operation,
            'execution_time_ms' => round($executionTime * 1000, 2),
            'timestamp' => now()->toISOString(),
            'data' => $data
        ];

        $level = $executionTime > 2.0 ? 'warning' : 'info';
        
        Log::channel('performance')->{$level}("Performance: {$operation}", $context);
    }

    /**
     * Log de email enviado
     */
    public function logEmailSent(string $to, string $subject, string $type, bool $successful = true, string $error = ''): void
    {
        $context = [
            'to' => $to,
            'subject' => $subject,
            'type' => $type,
            'successful' => $successful,
            'timestamp' => now()->toISOString()
        ];

        if (!$successful && $error) {
            $context['error'] = $error;
        }

        $level = $successful ? 'info' : 'error';
        $message = $successful ? 'Email sent successfully' : 'Email sending failed';

        Log::channel('emails')->{$level}($message, $context);
    }

    /**
     * Log de upload de arquivo
     */
    public function logFileUpload(string $filename, string $type, int $size, ?User $user = null, bool $successful = true): void
    {
        $context = [
            'filename' => $filename,
            'type' => $type,
            'size_bytes' => $size,
            'successful' => $successful,
            'timestamp' => now()->toISOString()
        ];

        if ($user) {
            $context['user_id'] = $user->id;
        }

        $level = $successful ? 'info' : 'error';
        $message = $successful ? 'File uploaded successfully' : 'File upload failed';

        Log::channel('files')->{$level}($message, $context);
    }

    /**
     * Sanitizar dados de entrada para log
     */
    private function sanitizeInput(array $input): array
    {
        $sensitiveFields = ['password', 'password_confirmation', 'token', 'api_key', 'secret'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($input[$field])) {
                $input[$field] = '[REDACTED]';
            }
        }

        return $input;
    }

    /**
     * Obter estatísticas de logs
     */
    public function getLogStats(string $channel = 'laravel', int $days = 7): array
    {
        // Esta é uma implementação básica
        // Em produção, você pode querer usar uma solução mais robusta como ELK Stack
        
        $logPath = storage_path("logs/{$channel}.log");
        
        if (!file_exists($logPath)) {
            return [
                'total_entries' => 0,
                'error_count' => 0,
                'warning_count' => 0,
                'info_count' => 0
            ];
        }

        $content = file_get_contents($logPath);
        $lines = explode("\n", $content);
        
        $stats = [
            'total_entries' => count($lines),
            'error_count' => substr_count($content, '.ERROR:'),
            'warning_count' => substr_count($content, '.WARNING:'),
            'info_count' => substr_count($content, '.INFO:')
        ];

        return $stats;
    }
}
