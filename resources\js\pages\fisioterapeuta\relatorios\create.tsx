import { FileUpload } from '@/components/file-upload';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, FileText, Plus, Save, User, X } from 'lucide-react';
import React from 'react';

interface Agendamento {
    id: number;
    data_hora: string;
    status: string;
    valor: number;
    endereco: string;
    observacoes?: string;
    paciente: {
        id: number;
        name: string;
        email: string;
        phone?: string;
    };
    formatted_data_hora: string;
    formatted_valor: string;
}

interface Props {
    agendamento: Agendamento;
}

interface FormData {
    observacoes: string;
    exercicios_realizados: string[];
    proximos_passos: string;
}

export default function RelatorioCreate({ agendamento }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        observacoes: '',
        exercicios_realizados: [] as string[],
        proximos_passos: '',
    });

    const [novoExercicio, setNovoExercicio] = React.useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('fisioterapeuta.relatorios.store', agendamento.id));
    };

    const adicionarExercicio = () => {
        if (novoExercicio.trim()) {
            setData('exercicios_realizados', [...data.exercicios_realizados, novoExercicio.trim()]);
            setNovoExercicio('');
        }
    };

    const removerExercicio = (index: number) => {
        const novosExercicios = data.exercicios_realizados.filter((_, i) => i !== index);
        setData('exercicios_realizados', novosExercicios);
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            adicionarExercicio();
        }
    };

    return (
        <AppLayout>
            <Head title={`Criar Relatório - Sessão #${agendamento.id}`} />

            <div className="py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center space-x-4">
                            <Link href={route('fisioterapeuta.agenda.show', agendamento.id)}>
                                <Button variant="ghost" size="sm">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Voltar
                                </Button>
                            </Link>
                            <div>
                                <h2 className="text-3xl font-bold tracking-tight">Criar Relatório da Sessão</h2>
                                <p className="text-muted-foreground">Registre os detalhes da sessão com {agendamento.paciente.name}</p>
                            </div>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Informações da Sessão */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Calendar className="mr-2 h-5 w-5" />
                                    Informações da Sessão
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <Calendar className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Data e Hora:</span>
                                            <span className="ml-2">{agendamento.formatted_data_hora}</span>
                                        </div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <User className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Paciente:</span>
                                            <span className="ml-2">{agendamento.paciente.name}</span>
                                        </div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <span className="font-medium">Valor:</span>
                                            <span className="ml-2 font-bold text-green-600">{agendamento.formatted_valor}</span>
                                        </div>
                                    </div>

                                    <div>
                                        <div className="mb-3 text-sm text-gray-600">
                                            <span className="font-medium">Endereço:</span>
                                            <span className="ml-2">{agendamento.endereco}</span>
                                        </div>
                                        <div className="mb-3 text-sm text-gray-600">
                                            <span className="font-medium">Status:</span>
                                            <Badge className="ml-2" variant="outline">
                                                {agendamento.status}
                                            </Badge>
                                        </div>
                                    </div>
                                </div>

                                {agendamento.observacoes && (
                                    <div className="mt-6 border-t pt-6">
                                        <h4 className="mb-2 font-medium text-gray-900">Observações do Agendamento:</h4>
                                        <p className="text-gray-600">{agendamento.observacoes}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Observações da Sessão */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <FileText className="mr-2 h-5 w-5" />
                                    Observações da Sessão
                                </CardTitle>
                                <CardDescription>Descreva detalhadamente o que foi observado durante a sessão</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <Label htmlFor="observacoes">Observações *</Label>
                                    <Textarea
                                        id="observacoes"
                                        placeholder="Descreva o estado do paciente, evolução, dificuldades encontradas, etc..."
                                        value={data.observacoes}
                                        onChange={(e) => setData('observacoes', e.target.value)}
                                        rows={6}
                                        className={errors.observacoes ? 'border-red-500' : ''}
                                    />
                                    {errors.observacoes && <p className="text-sm text-red-600">{errors.observacoes}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Exercícios Realizados */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Exercícios Realizados</CardTitle>
                                <CardDescription>Liste os exercícios que foram executados durante a sessão</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {/* Adicionar novo exercício */}
                                    <div className="flex space-x-2">
                                        <Input
                                            placeholder="Digite o exercício realizado..."
                                            value={novoExercicio}
                                            onChange={(e) => setNovoExercicio(e.target.value)}
                                            onKeyPress={handleKeyPress}
                                            className="flex-1"
                                        />
                                        <Button type="button" onClick={adicionarExercicio} disabled={!novoExercicio.trim()}>
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>

                                    {/* Lista de exercícios */}
                                    {data.exercicios_realizados.length > 0 && (
                                        <div className="space-y-2">
                                            <Label>Exercícios adicionados:</Label>
                                            <div className="space-y-2">
                                                {data.exercicios_realizados.map((exercicio, index) => (
                                                    <div key={index} className="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                                                        <span className="flex-1">{exercicio}</span>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removerExercicio(index)}
                                                            className="text-red-600 hover:text-red-700"
                                                        >
                                                            <X className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}

                                    {errors.exercicios_realizados && <p className="text-sm text-red-600">{errors.exercicios_realizados}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Próximos Passos */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Próximos Passos</CardTitle>
                                <CardDescription>Descreva as recomendações e o plano para as próximas sessões</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <Label htmlFor="proximos_passos">Próximos Passos *</Label>
                                    <Textarea
                                        id="proximos_passos"
                                        placeholder="Descreva as recomendações, exercícios para casa, objetivos para próximas sessões, etc..."
                                        value={data.proximos_passos}
                                        onChange={(e) => setData('proximos_passos', e.target.value)}
                                        rows={4}
                                        className={errors.proximos_passos ? 'border-red-500' : ''}
                                    />
                                    {errors.proximos_passos && <p className="text-sm text-red-600">{errors.proximos_passos}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Upload de Arquivos */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Anexos</CardTitle>
                                <CardDescription>Adicione fotos, vídeos ou documentos relacionados à sessão</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <FileUpload
                                    relatorioId={agendamento.id}
                                    uploadUrl={route('fisioterapeuta.relatorios.anexos.upload', agendamento.id)}
                                />
                            </CardContent>
                        </Card>

                        {/* Botões de Ação */}
                        <div className="flex justify-end space-x-4">
                            <Link href={route('fisioterapeuta.agenda.show', agendamento.id)}>
                                <Button type="button" variant="outline">
                                    Cancelar
                                </Button>
                            </Link>
                            <Button type="submit" disabled={processing}>
                                <Save className="mr-2 h-4 w-4" />
                                {processing ? 'Salvando...' : 'Salvar Relatório'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}
