import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useInitials } from '@/hooks/use-initials';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Calendar, MessageCircle, Star, ThumbsUp, TrendingUp } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/fisioterapeuta/dashboard',
    },
    {
        title: 'Avaliações Recebidas',
        href: '/fisioterapeuta/avaliacoes',
    },
];

interface Avaliacao {
    id: number;
    nota_geral: number;
    nota_pontualidade?: number;
    nota_profissionalismo?: number;
    nota_eficacia?: number;
    comentario?: string;
    recomendaria: boolean;
    anonima: boolean;
    created_at: string;
    paciente: {
        id: number;
        name: string;
        avatar?: string;
    };
    agendamento: {
        id: number;
        scheduled_at: string;
    };
}

interface Estatisticas {
    total: number;
    nota_media: number;
    nota_pontualidade: number;
    nota_profissionalismo: number;
    nota_eficacia: number;
    recomendacoes: number;
    percentual_recomendacao: number;
    distribuicao_notas: {
        5: number;
        4: number;
        3: number;
        2: number;
        1: number;
    };
}

interface Props {
    avaliacoes: {
        data: Avaliacao[];
        links: any[];
        meta: any;
    };
    estatisticas: Estatisticas;
}

export default function FisioterapeutaAvaliacoes({ avaliacoes, estatisticas }: Props) {
    const getInitials = useInitials();

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, index) => (
            <Star key={index} className={`h-4 w-4 ${index < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} />
        ));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Avaliações Recebidas" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                            <MessageCircle className="h-5 w-5" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold">Avaliações Recebidas</h1>
                            <p className="text-muted-foreground">Veja o feedback dos seus pacientes</p>
                        </div>
                    </div>
                </div>

                {/* Estatísticas */}
                {estatisticas.total > 0 && (
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total de Avaliações</CardTitle>
                                <MessageCircle className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{estatisticas.total}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Nota Média</CardTitle>
                                <Star className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{estatisticas.nota_media}</div>
                                <div className="mt-1 flex items-center gap-1">{renderStars(Math.round(estatisticas.nota_media))}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Recomendações</CardTitle>
                                <ThumbsUp className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{estatisticas.percentual_recomendacao}%</div>
                                <p className="text-xs text-muted-foreground">
                                    {estatisticas.recomendacoes} de {estatisticas.total}
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Tendência</CardTitle>
                                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-600">Positiva</div>
                                <p className="text-xs text-muted-foreground">Baseado nas últimas avaliações</p>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Detalhamento das Notas */}
                {estatisticas.total > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Detalhamento das Notas</CardTitle>
                            <CardDescription>Breakdown das suas avaliações por categoria</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 sm:grid-cols-3">
                                <div>
                                    <div className="mb-2 flex items-center justify-between">
                                        <span className="text-sm font-medium">Pontualidade</span>
                                        <span className="text-sm text-muted-foreground">{estatisticas.nota_pontualidade}</span>
                                    </div>
                                    <Progress value={(estatisticas.nota_pontualidade / 5) * 100} className="h-2" />
                                </div>
                                <div>
                                    <div className="mb-2 flex items-center justify-between">
                                        <span className="text-sm font-medium">Profissionalismo</span>
                                        <span className="text-sm text-muted-foreground">{estatisticas.nota_profissionalismo}</span>
                                    </div>
                                    <Progress value={(estatisticas.nota_profissionalismo / 5) * 100} className="h-2" />
                                </div>
                                <div>
                                    <div className="mb-2 flex items-center justify-between">
                                        <span className="text-sm font-medium">Eficácia</span>
                                        <span className="text-sm text-muted-foreground">{estatisticas.nota_eficacia}</span>
                                    </div>
                                    <Progress value={(estatisticas.nota_eficacia / 5) * 100} className="h-2" />
                                </div>
                            </div>

                            {/* Distribuição de Notas */}
                            <div>
                                <h4 className="mb-3 text-sm font-medium">Distribuição de Notas</h4>
                                <div className="space-y-2">
                                    {Object.entries(estatisticas.distribuicao_notas)
                                        .sort(([a], [b]) => Number(b) - Number(a))
                                        .map(([nota, quantidade]) => (
                                            <div key={nota} className="flex items-center gap-3">
                                                <div className="flex w-16 items-center gap-1">
                                                    <span className="text-sm">{nota}</span>
                                                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                                </div>
                                                <Progress
                                                    value={estatisticas.total > 0 ? (quantidade / estatisticas.total) * 100 : 0}
                                                    className="h-2 flex-1"
                                                />
                                                <span className="w-8 text-sm text-muted-foreground">{quantidade}</span>
                                            </div>
                                        ))}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Lista de Avaliações */}
                {avaliacoes.data.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <MessageCircle className="mb-4 h-12 w-12 text-muted-foreground" />
                            <h3 className="mb-2 text-lg font-semibold">Nenhuma avaliação encontrada</h3>
                            <p className="text-center text-muted-foreground">
                                Você ainda não recebeu avaliações dos seus pacientes. Continue oferecendo um excelente atendimento!
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="space-y-4">
                        {avaliacoes.data.map((avaliacao) => (
                            <Card key={avaliacao.id}>
                                <CardHeader>
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                            <Avatar className="h-12 w-12">
                                                <AvatarImage src={avaliacao.anonima ? undefined : avaliacao.paciente.avatar} />
                                                <AvatarFallback>{avaliacao.anonima ? '?' : getInitials(avaliacao.paciente.name)}</AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <CardTitle className="text-lg">
                                                    {avaliacao.anonima ? 'Paciente Anônimo' : avaliacao.paciente.name}
                                                </CardTitle>
                                                <CardDescription className="flex items-center gap-2">
                                                    <Calendar className="h-4 w-4" />
                                                    Consulta em {formatDate(avaliacao.agendamento.scheduled_at)}
                                                </CardDescription>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {avaliacao.anonima && <Badge variant="secondary">Anônima</Badge>}
                                            <div className="text-sm text-muted-foreground">{formatDate(avaliacao.created_at)}</div>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Nota Geral */}
                                    <div className="flex items-center gap-2">
                                        <span className="font-medium">Nota Geral:</span>
                                        <div className="flex items-center gap-1">
                                            {renderStars(avaliacao.nota_geral)}
                                            <span className="ml-2 text-sm text-muted-foreground">({avaliacao.nota_geral}/5)</span>
                                        </div>
                                    </div>

                                    {/* Notas Específicas */}
                                    {(avaliacao.nota_pontualidade || avaliacao.nota_profissionalismo || avaliacao.nota_eficacia) && (
                                        <div className="grid gap-2 sm:grid-cols-3">
                                            {avaliacao.nota_pontualidade && (
                                                <div className="text-sm">
                                                    <span className="font-medium">Pontualidade:</span>
                                                    <div className="mt-1 flex items-center gap-1">{renderStars(avaliacao.nota_pontualidade)}</div>
                                                </div>
                                            )}
                                            {avaliacao.nota_profissionalismo && (
                                                <div className="text-sm">
                                                    <span className="font-medium">Profissionalismo:</span>
                                                    <div className="mt-1 flex items-center gap-1">{renderStars(avaliacao.nota_profissionalismo)}</div>
                                                </div>
                                            )}
                                            {avaliacao.nota_eficacia && (
                                                <div className="text-sm">
                                                    <span className="font-medium">Eficácia:</span>
                                                    <div className="mt-1 flex items-center gap-1">{renderStars(avaliacao.nota_eficacia)}</div>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {/* Comentário */}
                                    {avaliacao.comentario && (
                                        <div className="rounded-lg bg-muted/50 p-3">
                                            <p className="text-sm">{avaliacao.comentario}</p>
                                        </div>
                                    )}

                                    {/* Recomendação */}
                                    <div className="flex items-center gap-2">
                                        <ThumbsUp className={`h-4 w-4 ${avaliacao.recomendaria ? 'text-green-600' : 'text-gray-400'}`} />
                                        <span className="text-sm">{avaliacao.recomendaria ? 'Recomendaria você' : 'Não recomendaria você'}</span>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}

                        {/* Paginação */}
                        {avaliacoes.meta.last_page > 1 && (
                            <div className="flex justify-center gap-2">
                                {avaliacoes.links.map((link, index) => (
                                    <Button
                                        key={index}
                                        variant={link.active ? 'default' : 'outline'}
                                        size="sm"
                                        asChild={!!link.url}
                                        disabled={!link.url}
                                    >
                                        {link.url ? (
                                            <Link href={link.url} dangerouslySetInnerHTML={{ __html: link.label }} />
                                        ) : (
                                            <span dangerouslySetInnerHTML={{ __html: link.label }} />
                                        )}
                                    </Button>
                                ))}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
