import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, Link } from '@inertiajs/react';
import {
    AlertTriangle,
    Building2,
    CheckCircle,
    Clock,
    CreditCard,
    ExternalLink,
    Eye,
    MessageCircle,
    Phone,
    Settings,
    Star,
    TrendingUp,
} from 'lucide-react';

interface Estabelecimento {
    id: number;
    nome: string;
    categoria: string;
    descricao?: string;
    telefone?: string;
    whatsapp: string;
    email?: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    site?: string;
    instagram?: string;
    facebook?: string;
    horario_funcionamento?: any;
    servicos_oferecidos?: string;
    slug?: string;
    ativo: boolean;
    plano_ativo: boolean;
    plano_vencimento?: string;
    avaliacao_media: number;
    total_avaliacoes: number;
}

interface Stats {
    plano_ativo: boolean;
    plano_vencimento?: string;
    dias_restantes?: number;
    avaliacao_media: number;
    total_avaliacoes: number;
    categoria: string;
    ativo: boolean;
    // Métricas de visualização
    total_views: number;
    views_mes_atual: number;
    views_semana_atual: number;
    // Métricas de contato
    total_contatos: number;
    contatos_mes_atual: number;
    contatos_whatsapp: number;
    contatos_telefone: number;
    // Informações de pagamento
    valor_plano: number;
    ultimo_pagamento?: string;
    dias_inadimplencia: number;
}

interface Props {
    estabelecimento: Estabelecimento;
    stats: Stats;
}

export default function EmpresaDashboard({ estabelecimento, stats }: Props) {
    const getStatusBadge = () => {
        if (!stats.plano_ativo) {
            return <Badge variant="destructive">Plano Inativo</Badge>;
        }

        if (stats.dias_restantes !== null && stats.dias_restantes !== undefined && stats.dias_restantes <= 7) {
            return (
                <Badge variant="outline" className="border-yellow-500 text-yellow-600">
                    Vence em {stats.dias_restantes} dias
                </Badge>
            );
        }

        return (
            <Badge variant="default" className="bg-green-600">
                Plano Ativo
            </Badge>
        );
    };

    const getCategoriaLabel = (categoria: string) => {
        const labels = {
            dentista: 'Dentista',
            farmacia: 'Farmácia',
            fisioterapia: 'Fisioterapia',
            outros: 'Outros',
        };
        return labels[categoria as keyof typeof labels] || categoria;
    };

    return (
        <AppLayout>
            <Head title="Dashboard - Empresa" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
                        <p className="text-muted-foreground">Gerencie seu estabelecimento e acompanhe suas métricas</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" asChild>
                            <Link href="/empresa/perfil">
                                <Settings className="mr-2 h-4 w-4" />
                                Configurações
                            </Link>
                        </Button>
                        {estabelecimento.slug && (
                            <Button variant="outline" asChild>
                                <a href={`/empresa/${estabelecimento.slug}`} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="mr-2 h-4 w-4" />
                                    Ver Página
                                </a>
                            </Button>
                        )}
                    </div>
                </div>

                {/* Alertas */}
                {!stats.plano_ativo && (
                    <Alert className="border-red-200 bg-red-50">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <AlertDescription className="text-red-800">
                            Seu plano está inativo. Ative seu plano para aparecer nas buscas e receber mais clientes.
                            <Button variant="link" className="ml-2 h-auto p-0 text-red-600">
                                Ativar Plano
                            </Button>
                        </AlertDescription>
                    </Alert>
                )}

                {stats.plano_ativo && stats.dias_restantes !== null && stats.dias_restantes !== undefined && stats.dias_restantes <= 7 && (
                    <Alert className="border-yellow-200 bg-yellow-50">
                        <Clock className="h-4 w-4 text-yellow-600" />
                        <AlertDescription className="text-yellow-800">
                            Seu plano vence em {stats.dias_restantes} dias ({stats.plano_vencimento}). Renove para continuar aparecendo nas buscas.
                            <Button variant="link" className="ml-2 h-auto p-0 text-yellow-600">
                                Renovar Plano
                            </Button>
                        </AlertDescription>
                    </Alert>
                )}

                {/* Cards de Estatísticas */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Status do Plano</CardTitle>
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-2">{getStatusBadge()}</div>
                            {stats.plano_vencimento && <p className="mt-1 text-xs text-muted-foreground">Vence em {stats.plano_vencimento}</p>}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Categoria</CardTitle>
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{getCategoriaLabel(stats.categoria)}</div>
                            <p className="text-xs text-muted-foreground">Tipo de estabelecimento</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Avaliação</CardTitle>
                            <Star className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-1 text-2xl font-bold">
                                {Number(stats.avaliacao_media).toFixed(1)}
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            </div>
                            <p className="text-xs text-muted-foreground">{stats.total_avaliacoes} avaliações</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Status</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {stats.ativo ? (
                                    <Badge variant="default" className="bg-green-600">
                                        Ativo
                                    </Badge>
                                ) : (
                                    <Badge variant="destructive">Inativo</Badge>
                                )}
                            </div>
                            <p className="text-xs text-muted-foreground">Visibilidade nas buscas</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Cards de Métricas */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Visualizações Totais</CardTitle>
                            <Eye className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_views}</div>
                            <p className="text-xs text-muted-foreground">Desde o início</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Views Este Mês</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.views_mes_atual}</div>
                            <p className="text-xs text-muted-foreground">Últimos 30 dias</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Contatos WhatsApp</CardTitle>
                            <MessageCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.contatos_whatsapp}</div>
                            <p className="text-xs text-muted-foreground">Cliques no WhatsApp</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Contatos Telefone</CardTitle>
                            <Phone className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.contatos_telefone}</div>
                            <p className="text-xs text-muted-foreground">Cliques no telefone</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Informações do Estabelecimento */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Informações do Estabelecimento</CardTitle>
                            <CardDescription>Dados básicos do seu estabelecimento</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h4 className="font-medium">{estabelecimento.nome}</h4>
                                <p className="text-sm text-muted-foreground">{getCategoriaLabel(estabelecimento.categoria)}</p>
                            </div>

                            {estabelecimento.descricao && (
                                <div>
                                    <h5 className="mb-1 text-sm font-medium">Descrição</h5>
                                    <p className="text-sm text-muted-foreground">{estabelecimento.descricao}</p>
                                </div>
                            )}

                            <div>
                                <h5 className="mb-1 text-sm font-medium">Endereço</h5>
                                <p className="text-sm text-muted-foreground">
                                    {estabelecimento.endereco}, {estabelecimento.cidade} - {estabelecimento.estado}
                                </p>
                            </div>

                            <div>
                                <h5 className="mb-1 text-sm font-medium">Contato</h5>
                                <p className="text-sm text-muted-foreground">WhatsApp: {estabelecimento.whatsapp}</p>
                                {estabelecimento.telefone && <p className="text-sm text-muted-foreground">Telefone: {estabelecimento.telefone}</p>}
                                {estabelecimento.email && <p className="text-sm text-muted-foreground">Email: {estabelecimento.email}</p>}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Ações Rápidas</CardTitle>
                            <CardDescription>Acesso rápido às principais funcionalidades</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-3">
                                <Button variant="outline" asChild className="justify-start">
                                    <Link href="/empresa/perfil">
                                        <Settings className="mr-2 h-4 w-4" />
                                        Editar Perfil
                                    </Link>
                                </Button>

                                {!stats.plano_ativo && (
                                    <Button className="justify-start">
                                        <CreditCard className="mr-2 h-4 w-4" />
                                        Ativar Plano
                                    </Button>
                                )}

                                {estabelecimento.slug && (
                                    <Button variant="outline" asChild className="justify-start">
                                        <a href={`/empresa/${estabelecimento.slug}`} target="_blank" rel="noopener noreferrer">
                                            <ExternalLink className="mr-2 h-4 w-4" />
                                            Ver Página Pública
                                        </a>
                                    </Button>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
