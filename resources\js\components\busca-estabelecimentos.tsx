import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Link } from '@inertiajs/react';
import { ExternalLink, MapPin, MessageCircle, Phone, Search, Star } from 'lucide-react';
import { useState } from 'react';

interface Estabelecimento {
    id: number;
    nome: string;
    slug: string;
    categoria: string;
    descricao?: string;
    telefone?: string;
    whatsapp: string;
    whatsapp_link: string;
    endereco_completo: string;
    distancia: number;
    avaliacao_media: number;
    total_avaliacoes: number;
    horario_funcionamento?: any;
}

interface BuscaResponse {
    success: boolean;
    estabelecimentos: Estabelecimento[];
    total: number;
    coordenadas: {
        lat: number;
        lng: number;
    };
    message?: string;
    mensagem?: {
        tipo: string;
        titulo: string;
        descricao: string;
        sugestoes: string[];
    };
    filtros?: {
        raio: number;
        categoria: string;
        total_na_regiao: number;
        avaliacao_minima?: number;
        aberto_agora?: boolean;
        aberto_24h?: boolean;
        ordenacao?: string;
    };
    pagination?: {
        current_page: number;
        per_page: number;
        total: number;
        total_pages: number;
        has_next_page: boolean;
        has_prev_page: boolean;
        from: number;
        to: number;
    };
}

const categoriaLabels = {
    dentista: 'Dentista',
    farmacia: 'Farmácia',
    fisioterapia: 'Fisioterapia',
    outros: 'Outros',
};

const categoriaEmojis = {
    dentista: '🦷',
    farmacia: '💊',
    fisioterapia: '💪',
    outros: '🏥',
};

export default function BuscaEstabelecimentos() {
    const [localizacao, setLocalizacao] = useState('');
    const [categoria, setCategoria] = useState('todos');
    const [raio, setRaio] = useState(10);
    const [estabelecimentos, setEstabelecimentos] = useState<Estabelecimento[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [buscaRealizada, setBuscaRealizada] = useState(false);
    const [mensagem, setMensagem] = useState<any>(null);
    const [filtros, setFiltros] = useState<any>(null);

    // Novos filtros avançados
    const [avaliacaoMinima, setAvaliacaoMinima] = useState<number | undefined>();
    const [abertoAgora, setAbertoAgora] = useState(false);
    const [aberto24h, setAberto24h] = useState(false);
    const [ordenacao, setOrdenacao] = useState('distancia');
    const [mostrarFiltrosAvancados, setMostrarFiltrosAvancados] = useState(false);

    // Paginação
    const [paginacao, setPaginacao] = useState<any>(null);
    const [paginaAtual, setPaginaAtual] = useState(1);

    const buscarEstabelecimentos = async (pagina = 1) => {
        if (!localizacao.trim()) {
            setError('Por favor, informe sua localização');
            return;
        }

        setLoading(true);
        setError('');

        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
            const response = await fetch('/api/estabelecimentos/buscar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    Accept: 'application/json',
                },
                body: JSON.stringify({
                    localizacao,
                    categoria: categoria === 'todos' ? null : categoria,
                    raio,
                    avaliacao_minima: avaliacaoMinima,
                    aberto_agora: abertoAgora,
                    aberto_24h: aberto24h,
                    ordenacao,
                    page: pagina,
                    per_page: 10,
                }),
            });

            const data: BuscaResponse = await response.json();

            if (data.success) {
                setEstabelecimentos(data.estabelecimentos);
                setMensagem(data.mensagem);
                setFiltros(data.filtros);
                setPaginacao(data.pagination);
                setPaginaAtual(pagina);
                setBuscaRealizada(true);
                setError('');
            } else {
                setError(data.message || 'Erro ao buscar estabelecimentos');
                setEstabelecimentos([]);
                setMensagem(null);
                setFiltros(null);
                setPaginacao(null);
            }
        } catch (err) {
            setError('Erro ao conectar com o servidor');
        } finally {
            setLoading(false);
        }
    };

    const obterLocalizacaoAtual = () => {
        if (!navigator.geolocation) {
            setError('Geolocalização não é suportada pelo seu navegador');
            return;
        }

        setLoading(true);
        navigator.geolocation.getCurrentPosition(
            async (position) => {
                const { latitude, longitude } = position.coords;

                try {
                    // Converter coordenadas em endereço usando Nominatim
                    const response = await fetch(
                        `https://nominatim.openstreetmap.org/reverse?lat=${latitude}&lon=${longitude}&format=json&countrycodes=br`,
                    );
                    const data = await response.json();

                    if (data.display_name) {
                        const endereco = `${data.address?.city || data.address?.town || data.address?.village || ''}, ${data.address?.state || ''}`;
                        setLocalizacao(endereco);
                    } else {
                        setLocalizacao(`${latitude}, ${longitude}`);
                    }
                } catch (err) {
                    setLocalizacao(`${latitude}, ${longitude}`);
                } finally {
                    setLoading(false);
                }
            },
            (error) => {
                setError('Não foi possível obter sua localização');
                setLoading(false);
            },
        );
    };

    return (
        <div className="mx-auto w-full max-w-6xl">
            {/* Formulário de Busca */}
            <Card className="mb-8 shadow-sm">
                <CardHeader className="text-center">
                    <CardTitle className="flex items-center justify-center gap-2 text-xl font-medium">
                        <Search className="h-5 w-5 text-primary" />
                        Encontre serviços de saúde perto de você
                    </CardTitle>
                    <CardDescription className="text-base">
                        Digite sua localização e encontre dentistas, farmácias e clínicas de fisioterapia próximas
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div className="space-y-2">
                            <Label htmlFor="localizacao" className="text-sm font-medium">
                                Localização
                            </Label>
                            <div className="flex gap-2">
                                <Input
                                    id="localizacao"
                                    placeholder="Digite seu CEP ou cidade"
                                    value={localizacao}
                                    onChange={(e) => setLocalizacao(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && buscarEstabelecimentos()}
                                    className="flex-1"
                                />
                                <Button type="button" variant="outline" size="sm" onClick={obterLocalizacaoAtual} disabled={loading} className="px-3">
                                    <MapPin className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="categoria" className="text-sm font-medium">
                                Categoria
                            </Label>
                            <select
                                id="categoria"
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                value={categoria}
                                onChange={(e) => setCategoria(e.target.value)}
                            >
                                <option value="todos">Todos os serviços</option>
                                <option value="dentista">Dentistas</option>
                                <option value="farmacia">Farmácias</option>
                                <option value="fisioterapia">Fisioterapia</option>
                            </select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="raio" className="text-sm font-medium">
                                Raio (km)
                            </Label>
                            <select
                                id="raio"
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                value={raio}
                                onChange={(e) => setRaio(Number(e.target.value))}
                            >
                                <option value={5}>5 km</option>
                                <option value={10}>10 km</option>
                                <option value={20}>20 km</option>
                                <option value={50}>50 km</option>
                            </select>
                        </div>

                        <div className="flex items-end">
                            <Button onClick={() => buscarEstabelecimentos(1)} disabled={loading} className="w-full">
                                {loading ? (
                                    <>
                                        <svg className="mr-2 h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                            ></path>
                                        </svg>
                                        Buscando...
                                    </>
                                ) : (
                                    <>
                                        <Search className="mr-2 h-4 w-4" />
                                        Buscar
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>

                    {/* Filtros Avançados */}
                    <div className="mt-6 border-t pt-6">
                        <Button
                            type="button"
                            variant="ghost"
                            onClick={() => setMostrarFiltrosAvancados(!mostrarFiltrosAvancados)}
                            className="mb-4 flex items-center gap-2"
                        >
                            <span>Filtros Avançados</span>
                            {mostrarFiltrosAvancados ? <span>▲</span> : <span>▼</span>}
                        </Button>

                        {mostrarFiltrosAvancados && (
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                {/* Avaliação Mínima */}
                                <div className="space-y-2">
                                    <Label className="text-sm font-medium">
                                        Avaliação Mínima: {avaliacaoMinima ? `${avaliacaoMinima} estrelas` : 'Qualquer'}
                                    </Label>
                                    <div className="px-2">
                                        <input
                                            type="range"
                                            min="0"
                                            max="5"
                                            step="0.5"
                                            value={avaliacaoMinima || 0}
                                            onChange={(e) => setAvaliacaoMinima(Number(e.target.value) || undefined)}
                                            className="w-full"
                                        />
                                        <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                                            <span>0</span>
                                            <span>2.5</span>
                                            <span>5</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Ordenação */}
                                <div className="space-y-2">
                                    <Label htmlFor="ordenacao" className="text-sm font-medium">
                                        Ordenar por
                                    </Label>
                                    <select
                                        id="ordenacao"
                                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                        value={ordenacao}
                                        onChange={(e) => setOrdenacao(e.target.value)}
                                    >
                                        <option value="distancia">Distância</option>
                                        <option value="avaliacao">Melhor Avaliação</option>
                                        <option value="nome">Nome (A-Z)</option>
                                        <option value="recente">Mais Recente</option>
                                    </select>
                                </div>

                                {/* Checkboxes */}
                                <div className="space-y-3">
                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="aberto-agora"
                                            checked={abertoAgora}
                                            onChange={(e) => setAbertoAgora(e.target.checked)}
                                            className="h-4 w-4"
                                        />
                                        <Label htmlFor="aberto-agora" className="text-sm">
                                            Aberto agora
                                        </Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="aberto-24h"
                                            checked={aberto24h}
                                            onChange={(e) => setAberto24h(e.target.checked)}
                                            className="h-4 w-4"
                                        />
                                        <Label htmlFor="aberto-24h" className="text-sm">
                                            Aberto 24 horas
                                        </Label>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {error && (
                        <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-600">
                            <div className="flex items-center gap-2">
                                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                </svg>
                                {error}
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Resultados */}
            {buscaRealizada && (
                <div className="space-y-6">
                    {/* Mensagem Descritiva */}
                    {mensagem && (
                        <Card
                            className={`shadow-sm ${
                                mensagem.tipo === 'sucesso'
                                    ? 'border-green-200 bg-green-50'
                                    : mensagem.tipo === 'fora_do_raio'
                                      ? 'border-yellow-200 bg-yellow-50'
                                      : 'border-blue-200 bg-blue-50'
                            }`}
                        >
                            <CardContent className="p-6">
                                <h3 className="mb-2 text-lg font-semibold">{mensagem.titulo}</h3>
                                <p className="mb-4 text-muted-foreground">{mensagem.descricao}</p>
                                {mensagem.sugestoes.length > 0 && (
                                    <div>
                                        <p className="mb-2 font-medium">Sugestões:</p>
                                        <ul className="list-inside list-disc space-y-1 text-sm text-muted-foreground">
                                            {mensagem.sugestoes.map((sugestao: string, index: number) => (
                                                <li key={index}>{sugestao}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    )}

                    <div className="text-center">
                        <h3 className="text-xl font-medium">
                            {estabelecimentos.length > 0
                                ? `${paginacao?.total || estabelecimentos.length} estabelecimento${(paginacao?.total || estabelecimentos.length) > 1 ? 's' : ''} encontrado${(paginacao?.total || estabelecimentos.length) > 1 ? 's' : ''}`
                                : 'Nenhum estabelecimento encontrado'}
                        </h3>
                        {estabelecimentos.length > 0 && (
                            <p className="mt-2 text-muted-foreground">Clique no card para ver mais detalhes ou no WhatsApp para contato direto</p>
                        )}
                    </div>

                    {estabelecimentos.length === 0 && (
                        <Card className="shadow-sm">
                            <CardContent className="py-12 text-center">
                                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                                    <Search className="h-6 w-6 text-muted-foreground" />
                                </div>
                                <p className="mb-2 text-base font-medium text-foreground">Nenhum estabelecimento encontrado</p>
                                <p className="text-sm text-muted-foreground">
                                    Tente aumentar o raio de busca ou verificar se a localização está correta.
                                </p>
                            </CardContent>
                        </Card>
                    )}

                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {estabelecimentos.map((estabelecimento) => (
                            <EstabelecimentoCard key={estabelecimento.id} estabelecimento={estabelecimento} />
                        ))}
                    </div>

                    {/* Paginação */}
                    {paginacao && paginacao.total_pages > 1 && (
                        <div className="mt-8 flex flex-col items-center gap-4">
                            <div className="text-sm text-muted-foreground">
                                Mostrando {paginacao.from} a {paginacao.to} de {paginacao.total} resultados
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => buscarEstabelecimentos(paginaAtual - 1)}
                                    disabled={!paginacao.has_prev_page || loading}
                                >
                                    Anterior
                                </Button>

                                {/* Páginas */}
                                {Array.from({ length: Math.min(5, paginacao.total_pages) }, (_, i) => {
                                    const pageNum = Math.max(1, Math.min(paginacao.total_pages - 4, paginaAtual - 2)) + i;
                                    if (pageNum > paginacao.total_pages) return null;

                                    return (
                                        <Button
                                            key={pageNum}
                                            variant={pageNum === paginaAtual ? 'default' : 'outline'}
                                            size="sm"
                                            onClick={() => buscarEstabelecimentos(pageNum)}
                                            disabled={loading}
                                        >
                                            {pageNum}
                                        </Button>
                                    );
                                })}

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => buscarEstabelecimentos(paginaAtual + 1)}
                                    disabled={!paginacao.has_next_page || loading}
                                >
                                    Próxima
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}

function EstabelecimentoCard({ estabelecimento }: { estabelecimento: Estabelecimento }) {
    return (
        <Card className="group h-full cursor-pointer shadow-sm transition-shadow hover:shadow-md">
            <Link href={`/estabelecimento/${estabelecimento.slug}`} className="block h-full">
                <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                        <div className="flex-1">
                            <CardTitle className="flex items-center gap-2 text-base font-medium transition-colors group-hover:text-primary">
                                <Badge variant="gradient" size="icon-lg" className="h-8 w-8">
                                    <span className="text-sm">{categoriaEmojis[estabelecimento.categoria as keyof typeof categoriaEmojis]}</span>
                                </Badge>
                                {estabelecimento.nome}
                            </CardTitle>
                            <div className="mt-2 flex items-center gap-2">
                                <Badge variant="secondary" className="text-xs">
                                    {categoriaLabels[estabelecimento.categoria as keyof typeof categoriaLabels]}
                                </Badge>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                    <MapPin className="h-3 w-3" />
                                    {estabelecimento.distancia} km
                                </div>
                            </div>
                        </div>
                        <ExternalLink className="h-4 w-4 text-muted-foreground opacity-0 transition-opacity group-hover:opacity-100" />
                    </div>
                </CardHeader>

                <CardContent className="space-y-4 pt-0">
                    {estabelecimento.descricao && (
                        <p className="line-clamp-2 text-sm leading-relaxed text-muted-foreground">{estabelecimento.descricao}</p>
                    )}

                    <div className="space-y-2">
                        <div className="flex items-start gap-2">
                            <MapPin className="mt-0.5 h-4 w-4 flex-shrink-0 text-muted-foreground" />
                            <span className="text-sm leading-relaxed text-muted-foreground">{estabelecimento.endereco_completo}</span>
                        </div>

                        {estabelecimento.avaliacao_media > 0 && (
                            <div className="flex items-center gap-2">
                                <Star className="h-4 w-4 fill-current text-yellow-500" />
                                <span className="text-sm font-medium">{estabelecimento.avaliacao_media}</span>
                                <span className="text-sm text-muted-foreground">
                                    ({estabelecimento.total_avaliacoes} avaliação{estabelecimento.total_avaliacoes !== 1 ? 'ões' : ''})
                                </span>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Link>

            {/* Botões de ação fora do Link para evitar conflitos */}
            <div className="px-6 pb-6">
                <div className="flex gap-2">
                    <Button asChild className="flex-1" size="sm" onClick={(e) => e.stopPropagation()}>
                        <a href={estabelecimento.whatsapp_link} target="_blank" rel="noopener noreferrer">
                            <MessageCircle className="mr-1.5 h-3.5 w-3.5" />
                            WhatsApp
                        </a>
                    </Button>

                    {estabelecimento.telefone && (
                        <Button asChild variant="outline" size="sm" className="px-3" onClick={(e) => e.stopPropagation()}>
                            <a href={`tel:${estabelecimento.telefone}`}>
                                <Phone className="h-3.5 w-3.5" />
                            </a>
                        </Button>
                    )}
                </div>
            </div>
        </Card>
    );
}
