<?php

namespace App\Http\Controllers;

use App\Models\Estabelecimento;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;

class EmpresaController extends Controller
{
    public function index()
    {
        return Inertia::render('empresa/cadastro');
    }

    public function store(Request $request)
    {
        $request->validate([
            'nome' => 'required|string|max:255',
            'categoria' => 'required|in:dentista,farmacia,fisioterapia,outros',
            'descricao' => 'nullable|string|max:1000',
            'telefone' => 'nullable|string|max:20',
            'whatsapp' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'endereco' => 'required|string|max:255',
            'cidade' => 'required|string|max:100',
            'estado' => 'required|string|size:2',
            'cep' => 'required|string|size:9',
            'horario_funcionamento' => 'nullable|array',
            'servicos_oferecidos' => 'nullable|string|max:1000',
            'site' => 'nullable|url|max:255',
            'instagram' => 'nullable|string|max:255',
            'facebook' => 'nullable|string|max:255',
        ]);

        // Obter coordenadas do endereço
        $coordenadas = $this->obterCoordenadas($request->cep, $request->endereco, $request->cidade, $request->estado);

        $estabelecimento = Estabelecimento::create([
            'nome' => $request->nome,
            'categoria' => $request->categoria,
            'descricao' => $request->descricao,
            'telefone' => $request->telefone,
            'whatsapp' => $request->whatsapp,
            'email' => $request->email,
            'endereco' => $request->endereco,
            'cidade' => $request->cidade,
            'estado' => $request->estado,
            'cep' => $request->cep,
            'latitude' => $coordenadas['lat'] ?? null,
            'longitude' => $coordenadas['lng'] ?? null,
            'horario_funcionamento' => $request->horario_funcionamento,
            'servicos_oferecidos' => $request->servicos_oferecidos,
            'site' => $request->site,
            'instagram' => $request->instagram,
            'facebook' => $request->facebook,
            'ativo' => true,
            'plano_ativo' => false, // Será ativado após pagamento
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Empresa cadastrada com sucesso! Agora você pode ativar seu plano.',
            'estabelecimento_id' => $estabelecimento->id
        ]);
    }

    public function ativarPlano(Request $request, Estabelecimento $estabelecimento)
    {
        $request->validate([
            'metodo_pagamento' => 'required|in:pix,boleto,cartao'
        ]);

        // Simular ativação do plano (aqui seria integrado com gateway de pagamento)
        $estabelecimento->update([
            'plano_ativo' => true,
            'plano_vencimento' => now()->addMonth()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Plano ativado com sucesso! Sua empresa já está visível nas buscas.',
            'vencimento' => $estabelecimento->plano_vencimento->format('d/m/Y')
        ]);
    }

    private function obterCoordenadas($cep, $endereco, $cidade, $estado)
    {
        try {
            // Primeiro tentar por CEP
            $cepLimpo = preg_replace('/[^0-9]/', '', $cep);
            $response = Http::get("https://viacep.com.br/ws/{$cepLimpo}/json/");

            if ($response->successful() && !isset($response->json()['erro'])) {
                $enderecoCompleto = "{$endereco}, {$cidade}, {$estado}, Brasil";
            } else {
                $enderecoCompleto = "{$endereco}, {$cidade}, {$estado}, Brasil";
            }

            // Obter coordenadas usando Nominatim
            $response = Http::get('https://nominatim.openstreetmap.org/search', [
                'q' => $enderecoCompleto,
                'format' => 'json',
                'limit' => 1,
                'countrycodes' => 'br'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (!empty($data)) {
                    return [
                        'lat' => (float) $data[0]['lat'],
                        'lng' => (float) $data[0]['lon']
                    ];
                }
            }
        } catch (\Exception $e) {
            // Falha silenciosa
        }

        return ['lat' => null, 'lng' => null];
    }
}
