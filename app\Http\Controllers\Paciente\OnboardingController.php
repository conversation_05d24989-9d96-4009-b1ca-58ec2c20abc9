<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class OnboardingController extends Controller
{
    /**
     * Display the paciente onboarding page.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Estados brasileiros
        $estados = [
            'AC' => 'Acre',
            'AL' => 'Alagoas',
            'AP' => 'Amapá',
            'AM' => 'Amazonas',
            'BA' => 'Bahia',
            'CE' => 'Ceará',
            'DF' => 'Distrito Federal',
            'ES' => 'Espírito Santo',
            'GO' => 'Goiás',
            'MA' => 'Maranhão',
            'MT' => 'Mato Grosso',
            'MS' => 'Mato Grosso do Sul',
            'MG' => 'Minas Gerais',
            'PA' => 'Pará',
            'PB' => 'Paraíba',
            'PR' => 'Paraná',
            'PE' => 'Pernambuco',
            'PI' => 'Piauí',
            'RJ' => 'Rio de Janeiro',
            'RN' => 'Rio Grande do Norte',
            'RS' => 'Rio Grande do Sul',
            'RO' => 'Rondônia',
            'RR' => 'Roraima',
            'SC' => 'Santa Catarina',
            'SP' => 'São Paulo',
            'SE' => 'Sergipe',
            'TO' => 'Tocantins',
        ];
        
        return Inertia::render('paciente/onboarding', [
            'user' => $user,
            'estados' => $estados,
        ]);
    }
    
    /**
     * Store the paciente onboarding data.
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        
        $validated = $request->validate([
            // Dados pessoais obrigatórios
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'birth_date' => 'required|date|before:today|after:' . now()->subYears(120)->toDateString(),
            'gender' => 'required|in:masculino,feminino,outro',
            
            // Endereço obrigatório
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:100',
            'state' => 'required|string|size:2',
            'zip_code' => 'required|string|max:9',
            
            // Informações médicas (pelo menos uma obrigatória)
            'medical_history' => 'nullable|string|min:20|max:2000',
            'emergency_contact' => 'nullable|string|min:20|max:500',

            // Objetivos do tratamento
            'main_objective' => 'required|string|in:alivio_dor,reabilitacao,fortalecimento,flexibilidade,postura,prevencao,condicionamento,outro',
            'pain_level' => 'required|string|in:0,1,2,3,4,5,6,7,8,9,10',
            'specific_areas' => 'nullable|array',
            'specific_areas.*' => 'string|max:50',
            'treatment_goals' => 'nullable|string|max:500',

            // Preferências
            'preferred_time' => 'required|string|in:manha,tarde,noite,flexivel',
            'preferred_days' => 'nullable|array',
            'preferred_days.*' => 'string|in:segunda,terca,quarta,quinta,sexta,sabado,domingo',
            'communication_preference' => 'required|string|in:whatsapp,email,sms,telefone',
            'reminder_frequency' => 'required|string|in:none,daily,weekly,before_session',
        ], [
            // Mensagens personalizadas
            'name.required' => 'O nome completo é obrigatório.',
            'phone.required' => 'O telefone é obrigatório.',
            'birth_date.required' => 'A data de nascimento é obrigatória.',
            'birth_date.before' => 'A data de nascimento deve ser anterior a hoje.',
            'birth_date.after' => 'Data de nascimento inválida.',
            'gender.required' => 'O gênero é obrigatório.',
            'address.required' => 'O endereço é obrigatório.',
            'city.required' => 'A cidade é obrigatória.',
            'state.required' => 'O estado é obrigatório.',
            'zip_code.required' => 'O CEP é obrigatório.',
            'medical_history.min' => 'O histórico médico deve ter pelo menos 20 caracteres.',
            'emergency_contact.min' => 'O contato de emergência deve ter pelo menos 20 caracteres.',
        ]);
        
        // Validar idade mínima
        $birthDate = \Carbon\Carbon::parse($validated['birth_date']);
        $age = $birthDate->diffInYears(now());
        
        if ($age < 16) {
            return back()->withErrors([
                'birth_date' => 'Você deve ter pelo menos 16 anos para usar a plataforma.'
            ]);
        }
        
        // Verificar se pelo menos um campo médico foi preenchido
        if (empty($validated['medical_history']) && empty($validated['emergency_contact'])) {
            return back()->withErrors([
                'medical_info' => 'Você deve preencher pelo menos o histórico médico ou o contato de emergência.'
            ]);
        }
        
        // Atualizar dados do usuário
        $user->update($validated);
        
        return redirect()->route('paciente.dashboard')
            ->with('success', 'Dados médicos configurados com sucesso! Agora você pode agendar consultas.');
    }
}
