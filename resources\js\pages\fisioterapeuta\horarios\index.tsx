import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { Calendar, Clock, Eye, Settings } from 'lucide-react';
import { useState } from 'react';
import { ExcecoesConfig } from './components/excecoes-config';
import { FeriadosConfig } from './components/feriados-config';
import { HorarioBaseConfig } from './components/horario-base-config';
import { PreviewCalendario } from './components/preview-calendario';

interface HorarioBase {
    id: number;
    dia_semana: number;
    hora_inicio: string;
    hora_fim: string;
    periodo_nome?: string;
    ativo: boolean;
}

interface HorarioExcecao {
    id: number;
    tipo: 'data_especifica' | 'semana' | 'mes' | 'periodo_personalizado';
    data_inicio: string;
    data_fim?: string;
    dia_semana?: number;
    hora_inicio?: string;
    hora_fim?: string;
    periodo_nome?: string;
    acao: 'disponivel' | 'indisponivel' | 'horario_customizado';
    motivo?: string;
    ativo: boolean;
    formatted_periodo: string;
    formatted_horario: string;
}

interface ConfigFeriados {
    id: number;
    fisioterapeuta_id: number;
    trabalha_feriados_nacionais: boolean;
    trabalha_feriados_estaduais: boolean;
    trabalha_feriados_municipais: boolean;
    feriados_excecoes?: number[];
    configuracoes_resumo: string;
}

interface Props {
    horariosBase: { [key: number]: HorarioBase[] };
    excecoes: {
        data: HorarioExcecao[];
        links: any[];
        meta: any;
    };
    configFeriados?: ConfigFeriados;
    diasSemana: { [key: number]: string };
}

export default function HorariosIndex({ horariosBase, excecoes, configFeriados, diasSemana }: Props) {
    const [activeTab, setActiveTab] = useState('base');

    const breadcrumbs = [
        { title: 'Dashboard', href: '/fisioterapeuta/dashboard' },
        { title: 'Configuração de Horários', href: '/fisioterapeuta/horarios' },
    ];

    const getHorariosSummary = () => {
        const diasConfigurados = Object.keys(horariosBase).length;
        const totalPeriodos = Object.values(horariosBase).reduce((total, horarios) => total + horarios.length, 0);
        const excecoesAtivas = excecoes.data.filter((e) => e.ativo).length;

        return {
            diasConfigurados,
            totalPeriodos,
            excecoesAtivas,
        };
    };

    const summary = getHorariosSummary();

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Configuração de Horários" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Configuração de Horários</h1>
                        <p className="text-muted-foreground">Configure seus horários de atendimento com flexibilidade total</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={() => setActiveTab('preview')} className="gap-2">
                            <Eye className="h-4 w-4" />
                            Preview
                        </Button>
                    </div>
                </div>

                {/* Cards de Resumo */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Dias Configurados</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{summary.diasConfigurados}/7</div>
                            <p className="text-xs text-muted-foreground">dias da semana com horários</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Períodos de Atendimento</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{summary.totalPeriodos}</div>
                            <p className="text-xs text-muted-foreground">períodos configurados</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Exceções Ativas</CardTitle>
                            <Settings className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{summary.excecoesAtivas}</div>
                            <p className="text-xs text-muted-foreground">exceções de horário</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Configuração de Feriados - Resumo */}
                {configFeriados && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Configuração de Feriados</CardTitle>
                            <CardDescription>{configFeriados.configuracoes_resumo}</CardDescription>
                        </CardHeader>
                    </Card>
                )}

                {/* Tabs de Configuração */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="base">Horários Base</TabsTrigger>
                        <TabsTrigger value="excecoes">Exceções</TabsTrigger>
                        <TabsTrigger value="feriados">Feriados</TabsTrigger>
                        <TabsTrigger value="preview">Preview</TabsTrigger>
                    </TabsList>

                    <TabsContent value="base" className="space-y-6">
                        <HorarioBaseConfig horariosBase={horariosBase} diasSemana={diasSemana} />
                    </TabsContent>

                    <TabsContent value="excecoes" className="space-y-6">
                        <ExcecoesConfig excecoes={excecoes} diasSemana={diasSemana} />
                    </TabsContent>

                    <TabsContent value="feriados" className="space-y-6">
                        <FeriadosConfig configFeriados={configFeriados} />
                    </TabsContent>

                    <TabsContent value="preview" className="space-y-6">
                        <PreviewCalendario />
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
