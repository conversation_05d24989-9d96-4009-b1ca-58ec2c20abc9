import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface EmailLayoutProps {
  preview: string;
  children: React.ReactNode;
}

export const EmailLayout = ({ preview, children }: EmailLayoutProps) => (
  <Html>
    <Head />
    <Preview>{preview}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={header}>
          <Img
            src="https://f4fisio.com.br/logo.png"
            width="120"
            height="40"
            alt="F4 Fisio"
            style={logo}
          />
        </Section>
        
        <Section style={content}>
          {children}
        </Section>
        
        <Section style={footer}>
          <Text style={footerText}>
            © 2025 F4 Fisio. Todos os direitos reservados.
          </Text>
          <Text style={footerText}>
            Fisioterapia, farmácias e dentistas reunidos em um só lugar.
          </Text>
          <Text style={footerLinks}>
            <Link href="https://f4fisio.com.br" style={link}>
              Site
            </Link>
            {' | '}
            <Link href="https://f4fisio.com.br/contato" style={link}>
              Contato
            </Link>
            {' | '}
            <Link href="https://f4fisio.com.br/politica-privacidade" style={link}>
              Política de Privacidade
            </Link>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const header = {
  padding: '20px 30px',
  borderBottom: '1px solid #e6ebf1',
};

const logo = {
  margin: '0 auto',
};

const content = {
  padding: '30px',
};

const footer = {
  padding: '20px 30px',
  borderTop: '1px solid #e6ebf1',
  textAlign: 'center' as const,
};

const footerText = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
  margin: '0 0 8px 0',
};

const footerLinks = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
  margin: '8px 0 0 0',
};

const link = {
  color: '#556cd6',
  textDecoration: 'none',
};
