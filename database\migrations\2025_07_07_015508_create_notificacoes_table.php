<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notificacoes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('agendamento_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('tipo', [
                'novo_agendamento',
                'agendamento_confirmado',
                'agendamento_cancelado',
                'sessao_iniciada',
                'sessao_finalizada',
                'lembrete_sessao'
            ]);
            $table->string('titulo');
            $table->text('mensagem');
            $table->boolean('lida')->default(false);
            $table->timestamp('data_envio');
            $table->timestamp('data_leitura')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'lida']);
            $table->index(['user_id', 'tipo']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notificacoes');
    }
};
