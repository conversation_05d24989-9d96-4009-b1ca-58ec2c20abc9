<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agendamentos', function (Blueprint $table) {
            // Tornar assinatura_id nullable para permitir agendamentos avulsos
            $table->foreignId('assinatura_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agendamentos', function (Blueprint $table) {
            // Reverter para NOT NULL (cuidado: pode falhar se houver registros com NULL)
            $table->foreignId('assinatura_id')->nullable(false)->change();
        });
    }
};
