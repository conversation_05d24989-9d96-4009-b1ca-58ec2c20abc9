import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { AlertCircle, BarChart3, CheckCircle, Clock, Database, Download, HardDrive, Settings, Trash2, XCircle } from 'lucide-react';
import { useState } from 'react';

interface BackupLog {
    id: number;
    type: string;
    status: string;
    file_name: string;
    file_size: number;
    created_at: string;
    completed_at: string;
    user: {
        name: string;
    };
    metadata: any;
}

interface BackupStats {
    total_backups: number;
    completed_backups: number;
    failed_backups: number;
    success_rate: number;
    total_size_formatted: string;
    last_backup: string;
}

interface Props {
    backups: BackupLog[];
    exports: BackupLog[];
    imports: BackupLog[];
    stats: BackupStats;
}

export default function BackupIndex({ backups, exports, imports, stats }: Props) {
    const [selectedTab, setSelectedTab] = useState('backups');
    const [showCreateDialog, setShowCreateDialog] = useState(false);
    const [showExportDialog, setShowExportDialog] = useState(false);
    const [showImportDialog, setShowImportDialog] = useState(false);
    const [showConfigDialog, setShowConfigDialog] = useState(false);

    const {
        data: backupData,
        setData: setBackupData,
        post: postBackup,
        processing: processingBackup,
    } = useForm({
        type: 'full',
    });

    const {
        data: exportData,
        setData: setExportData,
        post: postExport,
        processing: processingExport,
    } = useForm({
        type: 'users',
        format: 'excel',
        filters: {},
    });

    const {
        data: importData,
        setData: setImportData,
        post: postImport,
        processing: processingImport,
    } = useForm({
        file: null,
        type: 'users',
    });

    const {
        data: configData,
        setData: setConfigData,
        post: postConfig,
        processing: processingConfig,
    } = useForm({
        enabled: false,
        frequency: 'daily',
        time: '02:00',
        type: 'full',
        keep_days: 30,
    });

    const handleCreateBackup = () => {
        postBackup(route('admin.backup.create'), {
            onSuccess: () => setShowCreateDialog(false),
        });
    };

    const handleExportReport = () => {
        postExport(route('admin.export.report'), {
            onSuccess: () => setShowExportDialog(false),
        });
    };

    const handleImportFile = () => {
        postImport(route('admin.import.upload'), {
            onSuccess: () => setShowImportDialog(false),
        });
    };

    const handleConfigSave = () => {
        postConfig(route('admin.backup.auto-config'), {
            onSuccess: () => setShowConfigDialog(false),
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'completed':
                return (
                    <Badge variant="default" className="bg-green-500">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Concluído
                    </Badge>
                );
            case 'failed':
                return (
                    <Badge variant="destructive">
                        <XCircle className="mr-1 h-3 w-3" />
                        Falhou
                    </Badge>
                );
            case 'processing':
                return (
                    <Badge variant="secondary">
                        <Clock className="mr-1 h-3 w-3" />
                        Processando
                    </Badge>
                );
            default:
                return (
                    <Badge variant="outline">
                        <AlertCircle className="mr-1 h-3 w-3" />
                        Pendente
                    </Badge>
                );
        }
    };

    const formatFileSize = (bytes: number) => {
        if (!bytes) return 'N/A';
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(2)} ${units[unitIndex]}`;
    };

    return (
        <AppLayout>
            <Head title="Backup e Exportação" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Backup e Exportação</h1>
                        <p className="text-muted-foreground">Gerencie backups, exportações e importações do sistema</p>
                    </div>
                    <div className="flex gap-2">
                        <Dialog open={showConfigDialog} onOpenChange={setShowConfigDialog}>
                            <DialogTrigger asChild>
                                <Button variant="outline">
                                    <Settings className="mr-2 h-4 w-4" />
                                    Configurações
                                </Button>
                            </DialogTrigger>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>Configurações de Backup Automático</DialogTitle>
                                    <DialogDescription>Configure backups automáticos do sistema</DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4">
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            checked={configData.enabled}
                                            onCheckedChange={(checked) => setConfigData('enabled', checked as any)}
                                        />
                                        <Label>Ativar backup automático</Label>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label>Frequência</Label>
                                            <Select value={configData.frequency} onValueChange={(value) => setConfigData('frequency', value)}>
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="daily">Diário</SelectItem>
                                                    <SelectItem value="weekly">Semanal</SelectItem>
                                                    <SelectItem value="monthly">Mensal</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div>
                                            <Label>Horário</Label>
                                            <Input type="time" value={configData.time} onChange={(e) => setConfigData('time', e.target.value)} />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label>Tipo de Backup</Label>
                                            <Select value={configData.type} onValueChange={(value) => setConfigData('type', value)}>
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="full">Completo</SelectItem>
                                                    <SelectItem value="database">Apenas Banco</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div>
                                            <Label>Manter por (dias)</Label>
                                            <Input
                                                type="number"
                                                min="1"
                                                max="365"
                                                value={configData.keep_days}
                                                onChange={(e) => setConfigData('keep_days', parseInt(e.target.value))}
                                            />
                                        </div>
                                    </div>

                                    <Button onClick={handleConfigSave} disabled={processingConfig} className="w-full">
                                        Salvar Configurações
                                    </Button>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Backups</CardTitle>
                            <Database className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_backups}</div>
                            <p className="text-xs text-muted-foreground">{stats.completed_backups} concluídos</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Taxa de Sucesso</CardTitle>
                            <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.success_rate}%</div>
                            <p className="text-xs text-muted-foreground">{stats.failed_backups} falharam</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Espaço Usado</CardTitle>
                            <HardDrive className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_size_formatted}</div>
                            <p className="text-xs text-muted-foreground">Total armazenado</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Último Backup</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {stats.last_backup ? new Date(stats.last_backup).toLocaleDateString('pt-BR') : 'Nunca'}
                            </div>
                            <p className="text-xs text-muted-foreground">Data do último backup</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Main Content */}
                <Tabs value={selectedTab} onValueChange={setSelectedTab}>
                    <TabsList>
                        <TabsTrigger value="backups">Backups ({backups.length})</TabsTrigger>
                        <TabsTrigger value="exports">Exportações ({exports.length})</TabsTrigger>
                        <TabsTrigger value="imports">Importações ({imports.length})</TabsTrigger>
                    </TabsList>

                    <TabsContent value="backups" className="space-y-4">
                        <div className="flex items-center justify-between">
                            <h2 className="text-xl font-semibold">Backups do Sistema</h2>
                            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                                <DialogTrigger asChild>
                                    <Button>
                                        <Database className="mr-2 h-4 w-4" />
                                        Criar Backup
                                    </Button>
                                </DialogTrigger>
                                <DialogContent>
                                    <DialogHeader>
                                        <DialogTitle>Criar Novo Backup</DialogTitle>
                                        <DialogDescription>Selecione o tipo de backup que deseja criar</DialogDescription>
                                    </DialogHeader>
                                    <div className="space-y-4">
                                        <div>
                                            <Label>Tipo de Backup</Label>
                                            <Select value={backupData.type} onValueChange={(value) => setBackupData('type', value)}>
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="full">Backup Completo (Banco + Arquivos)</SelectItem>
                                                    <SelectItem value="database">Apenas Banco de Dados</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <Button onClick={handleCreateBackup} disabled={processingBackup} className="w-full">
                                            {processingBackup ? 'Criando...' : 'Criar Backup'}
                                        </Button>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>

                        <div className="grid gap-4">
                            {backups.map((backup) => (
                                <Card key={backup.id}>
                                    <CardContent className="p-4">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-1">
                                                <div className="flex items-center gap-2">
                                                    <h3 className="font-medium">{backup.file_name}</h3>
                                                    {getStatusBadge(backup.status)}
                                                </div>
                                                <p className="text-sm text-muted-foreground">
                                                    Criado em {new Date(backup.created_at).toLocaleString('pt-BR')} por {backup.user?.name}
                                                </p>
                                                <p className="text-sm text-muted-foreground">
                                                    Tamanho: {formatFileSize(backup.file_size)} | Tipo: {backup.metadata?.backup_type || backup.type}
                                                </p>
                                            </div>
                                            <div className="flex gap-2">
                                                {backup.status === 'completed' && (
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('admin.backup.download', backup.id)}>
                                                            <Download className="h-4 w-4" />
                                                        </Link>
                                                    </Button>
                                                )}
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => {
                                                        if (confirm('Tem certeza que deseja deletar este backup?')) {
                                                            // Delete backup logic
                                                        }
                                                    }}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </TabsContent>

                    {/* Similar structure for exports and imports tabs... */}
                </Tabs>
            </div>
        </AppLayout>
    );
}
