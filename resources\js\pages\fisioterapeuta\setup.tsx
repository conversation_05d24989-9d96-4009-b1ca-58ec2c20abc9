import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Head, useForm } from '@inertiajs/react';
import { CheckCircle, Stethoscope, User } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
}

interface Fisioterapeuta {
    id: number;
    crefito: string;
    specializations: string[];
    bio: string;
    hourly_rate: number;
    available_areas: string[];
}

interface Props {
    fisioterapeuta: Fisioterapeuta;
    user: User;
    especialidades: string[];
    areas: string[];
}

export default function FisioterapeutaSetup({ fisioterapeuta, user, especialidades, areas }: Props) {
    const [currentStep, setCurrentStep] = useState(1);
    const totalSteps = 4;

    const { data, setData, post, processing, errors } = useForm({
        // Dados do usuário
        name: user.name || '',
        phone: user.phone || '',
        
        // Dados do fisioterapeuta
        crefito: fisioterapeuta.crefito || '',
        specializations: fisioterapeuta.specializations || [],
        bio: fisioterapeuta.bio || '',
        hourly_rate: fisioterapeuta.hourly_rate || 0,
        available_areas: fisioterapeuta.available_areas || [],
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('fisioterapeuta.setup.store'));
    };

    const handleSpecializationChange = (especialidade: string, checked: boolean) => {
        if (checked) {
            setData('specializations', [...data.specializations, especialidade]);
        } else {
            setData('specializations', data.specializations.filter(s => s !== especialidade));
        }
    };

    const handleAreaChange = (area: string, checked: boolean) => {
        if (checked) {
            setData('available_areas', [...data.available_areas, area]);
        } else {
            setData('available_areas', data.available_areas.filter(a => a !== area));
        }
    };

    const getStepValidation = (step: number): boolean => {
        switch (step) {
            case 1:
                return data.name.length > 0 && data.phone.length > 0;
            case 2:
                return data.crefito.length > 0 && data.hourly_rate > 0;
            case 3:
                return data.specializations.length > 0;
            case 4:
                return data.available_areas.length > 0 && data.bio.length >= 50;
            default:
                return false;
        }
    };

    const canProceedToNext = getStepValidation(currentStep);
    const progress = (currentStep / totalSteps) * 100;

    const nextStep = () => {
        if (canProceedToNext && currentStep < totalSteps) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <Head title="Configuração do Perfil Profissional" />

            <div className="w-full max-w-2xl">
                {/* Header */}
                <div className="text-center mb-8">
                    <div className="flex justify-center mb-4">
                        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground">
                            <Stethoscope className="h-8 w-8" />
                        </div>
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900">Bem-vindo à F4 Fisio!</h1>
                    <p className="text-gray-600 mt-2">
                        Vamos configurar seu perfil profissional para que você possa começar a receber agendamentos
                    </p>
                </div>

                {/* Progress */}
                <div className="mb-8">
                    <div className="flex justify-between text-sm text-gray-600 mb-2">
                        <span>Passo {currentStep} de {totalSteps}</span>
                        <span>{Math.round(progress)}% concluído</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                </div>

                {/* Form */}
                <Card>
                    <form onSubmit={handleSubmit}>
                        {/* Step 1: Dados Pessoais */}
                        {currentStep === 1 && (
                            <>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Dados Pessoais
                                    </CardTitle>
                                    <CardDescription>
                                        Confirme suas informações básicas de contato
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Nome Completo *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Seu nome completo"
                                        />
                                        <InputError message={errors.name} />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="phone">Telefone *</Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="(11) 99999-9999"
                                        />
                                        <InputError message={errors.phone} />
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Step 2: Dados Profissionais */}
                        {currentStep === 2 && (
                            <>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <CheckCircle className="h-5 w-5" />
                                        Dados Profissionais
                                    </CardTitle>
                                    <CardDescription>
                                        Informações sobre sua formação e registro profissional
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="crefito">Número do CREFITO *</Label>
                                        <Input
                                            id="crefito"
                                            value={data.crefito}
                                            onChange={(e) => setData('crefito', e.target.value)}
                                            placeholder="CREFITO-3/123456-F"
                                        />
                                        <InputError message={errors.crefito} />
                                        <p className="text-sm text-muted-foreground">
                                            Exemplo: CREFITO-3/123456-F
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="hourly_rate">Valor da Hora (R$) *</Label>
                                        <Input
                                            id="hourly_rate"
                                            type="number"
                                            step="0.01"
                                            min="10"
                                            value={data.hourly_rate}
                                            onChange={(e) => setData('hourly_rate', parseFloat(e.target.value) || 0)}
                                            placeholder="80.00"
                                        />
                                        <InputError message={errors.hourly_rate} />
                                        <p className="text-sm text-muted-foreground">
                                            Valor mínimo: R$ 10,00
                                        </p>
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Step 3: Especialidades */}
                        {currentStep === 3 && (
                            <>
                                <CardHeader>
                                    <CardTitle>Especialidades</CardTitle>
                                    <CardDescription>
                                        Selecione suas áreas de especialização (pelo menos uma)
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid gap-3 sm:grid-cols-2">
                                        {especialidades.map((especialidade) => (
                                            <div key={especialidade} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`especialidade-${especialidade}`}
                                                    checked={data.specializations.includes(especialidade)}
                                                    onCheckedChange={(checked) => 
                                                        handleSpecializationChange(especialidade, checked as boolean)
                                                    }
                                                />
                                                <Label 
                                                    htmlFor={`especialidade-${especialidade}`}
                                                    className="text-sm font-normal"
                                                >
                                                    {especialidade}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                    <InputError message={errors.specializations} />
                                    {data.specializations.length > 0 && (
                                        <p className="mt-2 text-sm text-green-600">
                                            ✓ {data.specializations.length} especialidade(s) selecionada(s)
                                        </p>
                                    )}
                                </CardContent>
                            </>
                        )}

                        {/* Step 4: Áreas e Bio */}
                        {currentStep === 4 && (
                            <>
                                <CardHeader>
                                    <CardTitle>Finalização</CardTitle>
                                    <CardDescription>
                                        Áreas de atendimento e biografia profissional
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div>
                                        <Label className="text-base font-medium">Áreas de Atendimento *</Label>
                                        <p className="text-sm text-muted-foreground mb-3">
                                            Selecione as regiões onde você atende
                                        </p>
                                        <div className="grid gap-2 sm:grid-cols-2 max-h-48 overflow-y-auto border rounded-md p-3">
                                            {areas.map((area) => (
                                                <div key={area} className="flex items-center space-x-2">
                                                    <Checkbox
                                                        id={`area-${area}`}
                                                        checked={data.available_areas.includes(area)}
                                                        onCheckedChange={(checked) => 
                                                            handleAreaChange(area, checked as boolean)
                                                        }
                                                    />
                                                    <Label 
                                                        htmlFor={`area-${area}`}
                                                        className="text-sm font-normal"
                                                    >
                                                        {area}
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>
                                        <InputError message={errors.available_areas} />
                                        {data.available_areas.length > 0 && (
                                            <p className="mt-2 text-sm text-green-600">
                                                ✓ {data.available_areas.length} área(s) selecionada(s)
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="bio">Biografia Profissional *</Label>
                                        <Textarea
                                            id="bio"
                                            value={data.bio}
                                            onChange={(e) => setData('bio', e.target.value)}
                                            placeholder="Conte um pouco sobre sua experiência, formação e especialidades. Esta informação será exibida para os pacientes."
                                            rows={4}
                                        />
                                        <InputError message={errors.bio} />
                                        <p className={`text-sm ${data.bio.length >= 50 ? 'text-green-600' : 'text-muted-foreground'}`}>
                                            {data.bio.length >= 50 ? '✓' : ''} {data.bio.length}/1000 caracteres (mínimo 50)
                                        </p>
                                    </div>
                                </CardContent>
                            </>
                        )}

                        {/* Navigation */}
                        <div className="flex justify-between p-6 border-t">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={prevStep}
                                disabled={currentStep === 1}
                            >
                                Anterior
                            </Button>

                            {currentStep < totalSteps ? (
                                <Button
                                    type="button"
                                    onClick={nextStep}
                                    disabled={!canProceedToNext}
                                >
                                    Próximo
                                </Button>
                            ) : (
                                <Button
                                    type="submit"
                                    disabled={processing || !canProceedToNext}
                                    className="gap-2"
                                >
                                    {processing ? 'Finalizando...' : 'Finalizar Configuração'}
                                </Button>
                            )}
                        </div>
                    </form>
                </Card>
            </div>
        </div>
    );
}
