<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('relatorio_anexos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('relatorio_sessao_id')->constrained('relatorios_sessao')->onDelete('cascade');
            $table->string('nome_arquivo');
            $table->string('nome_original');
            $table->string('caminho_arquivo');
            $table->integer('tamanho_arquivo');
            $table->string('tipo_mime');
            $table->enum('tipo_anexo', ['imagem', 'documento', 'video', 'audio'])->default('documento');
            $table->text('descricao')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('relatorio_anexos');
    }
};
