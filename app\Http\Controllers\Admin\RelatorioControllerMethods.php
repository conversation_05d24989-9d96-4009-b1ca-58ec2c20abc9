<?php

namespace App\Http\Controllers\Admin;

use App\Models\Agendamento;
use App\Models\Assinatura;
use App\Models\Fisioterapeuta;
use App\Models\Pagamento;
use App\Models\RelatorioSessao;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

trait RelatorioControllerMethods
{
    // Métodos para relatório de pacientes
    private function getTotalPacientes()
    {
        return User::where('role', 'paciente')->count();
    }

    private function getNovosPacientes($dataInicio, $dataFim)
    {
        return User::where('role', 'paciente')
            ->whereBetween('created_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->count();
    }

    private function getPacientesAtivos($dataInicio, $dataFim)
    {
        return Agendamento::whereBetween('scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->distinct('paciente_id')
            ->count();
    }

    private function getPacientesInativos($dataInicio, $dataFim)
    {
        $totalPacientes = $this->getTotalPacientes();
        $pacientesAtivos = $this->getPacientesAtivos($dataInicio, $dataFim);
        
        return $totalPacientes - $pacientesAtivos;
    }

    private function getTaxaRetencao($dataInicio, $dataFim)
    {
        $inicioMes = Carbon::parse($dataInicio)->startOfMonth();
        $fimMes = Carbon::parse($dataFim)->endOfMonth();

        $pacientesInicioMes = User::where('role', 'paciente')
            ->where('created_at', '<', $inicioMes)
            ->count();

        $pacientesAtivos = Agendamento::whereBetween('scheduled_at', [$inicioMes, $fimMes])
            ->whereIn('paciente_id', function ($query) use ($inicioMes) {
                $query->select('id')
                    ->from('users')
                    ->where('role', 'paciente')
                    ->where('created_at', '<', $inicioMes);
            })
            ->distinct('paciente_id')
            ->count();

        return $pacientesInicioMes > 0 ? round(($pacientesAtivos / $pacientesInicioMes) * 100, 2) : 0;
    }

    private function getSessoesPorPaciente($dataInicio, $dataFim)
    {
        $totalSessoes = Agendamento::whereBetween('scheduled_at', [
            Carbon::parse($dataInicio)->startOfDay(),
            Carbon::parse($dataFim)->endOfDay()
        ])->count();

        $totalPacientesAtivos = $this->getPacientesAtivos($dataInicio, $dataFim);

        return $totalPacientesAtivos > 0 ? round($totalSessoes / $totalPacientesAtivos, 1) : 0;
    }

    private function getTempoMedioTratamento()
    {
        $tempoMedio = Assinatura::selectRaw('AVG(julianday(COALESCE(updated_at, datetime("now"))) - julianday(created_at)) as media_dias')
            ->where('status', '!=', 'ativa')
            ->first()
            ->media_dias ?? 0;

        return round($tempoMedio, 0);
    }

    private function getSatisfacaoGeral($dataInicio, $dataFim)
    {
        $satisfacao = RelatorioSessao::whereHas('agendamento', function ($query) use ($dataInicio, $dataFim) {
                $query->whereBetween('finished_at', [
                    Carbon::parse($dataInicio)->startOfDay(),
                    Carbon::parse($dataFim)->endOfDay()
                ]);
            })
            ->whereNotNull('patient_satisfaction')
            ->avg('patient_satisfaction');

        return round($satisfacao ?? 0, 1);
    }

    private function getEvolucaoNovosPacientes($dataInicio, $dataFim)
    {
        return User::selectRaw('DATE(created_at) as data, COUNT(*) as novos_pacientes')
            ->where('role', 'paciente')
            ->whereBetween('created_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->groupBy('data')
            ->orderBy('data')
            ->get()
            ->map(function ($item) {
                return [
                    'data' => Carbon::parse($item->data)->format('d/m'),
                    'novos_pacientes' => (int) $item->novos_pacientes,
                ];
            });
    }

    private function getDistribuicaoIdade()
    {
        return User::selectRaw('
                CASE 
                    WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) < 18 THEN "Menor de 18"
                    WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 18 AND 30 THEN "18-30 anos"
                    WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 31 AND 50 THEN "31-50 anos"
                    WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 51 AND 70 THEN "51-70 anos"
                    ELSE "Acima de 70"
                END as faixa_etaria,
                COUNT(*) as quantidade
            ')
            ->where('role', 'paciente')
            ->whereNotNull('birth_date')
            ->groupBy('faixa_etaria')
            ->get()
            ->map(function ($item) {
                return [
                    'faixa_etaria' => $item->faixa_etaria,
                    'quantidade' => (int) $item->quantidade,
                ];
            });
    }

    private function getTopPacientesPorSessoes($dataInicio, $dataFim)
    {
        return DB::table('agendamentos')
            ->join('users', 'agendamentos.paciente_id', '=', 'users.id')
            ->select(
                'users.name as paciente',
                'users.email',
                DB::raw('COUNT(agendamentos.id) as total_sessoes'),
                DB::raw('COUNT(CASE WHEN agendamentos.status = "concluido" THEN 1 END) as sessoes_concluidas'),
                DB::raw('SUM(agendamentos.price) as valor_total')
            )
            ->whereBetween('agendamentos.scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderBy('total_sessoes', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'paciente' => $item->paciente,
                    'email' => $item->email,
                    'total_sessoes' => (int) $item->total_sessoes,
                    'sessoes_concluidas' => (int) $item->sessoes_concluidas,
                    'valor_total' => (float) $item->valor_total,
                ];
            });
    }

    // Métodos para relatório de fisioterapeutas
    private function getTotalFisioterapeutas()
    {
        return User::where('role', 'fisioterapeuta')->count();
    }

    private function getFisioterapeutasAtivos($dataInicio, $dataFim)
    {
        return Agendamento::whereBetween('scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->distinct('fisioterapeuta_id')
            ->count();
    }

    private function getMediaSessoesFisio($dataInicio, $dataFim)
    {
        $totalSessoes = Agendamento::whereBetween('scheduled_at', [
            Carbon::parse($dataInicio)->startOfDay(),
            Carbon::parse($dataFim)->endOfDay()
        ])->count();

        $fisioterapeutasAtivos = $this->getFisioterapeutasAtivos($dataInicio, $dataFim);

        return $fisioterapeutasAtivos > 0 ? round($totalSessoes / $fisioterapeutasAtivos, 1) : 0;
    }

    private function getReceitaPorFisio($dataInicio, $dataFim)
    {
        $receita = Agendamento::whereBetween('scheduled_at', [
            Carbon::parse($dataInicio)->startOfDay(),
            Carbon::parse($dataFim)->endOfDay()
        ])->sum('price');

        $fisioterapeutasAtivos = $this->getFisioterapeutasAtivos($dataInicio, $dataFim);

        return $fisioterapeutasAtivos > 0 ? round($receita / $fisioterapeutasAtivos, 2) : 0;
    }

    private function getSatisfacaoFisios($dataInicio, $dataFim)
    {
        // Simulação baseada na satisfação dos pacientes com os fisioterapeutas
        $satisfacao = RelatorioSessao::whereHas('agendamento', function ($query) use ($dataInicio, $dataFim) {
                $query->whereBetween('finished_at', [
                    Carbon::parse($dataInicio)->startOfDay(),
                    Carbon::parse($dataFim)->endOfDay()
                ]);
            })
            ->whereNotNull('patient_satisfaction')
            ->avg('patient_satisfaction');

        return round($satisfacao ?? 0, 1);
    }

    private function getTempoRespostaMedio($dataInicio, $dataFim)
    {
        // Simulação - tempo entre agendamento e confirmação
        $agendamentos = Agendamento::whereNotNull('confirmed_at')
            ->whereBetween('created_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->get(['created_at', 'confirmed_at']);

        if ($agendamentos->isEmpty()) {
            return 0;
        }

        $totalHoras = $agendamentos->sum(function ($agendamento) {
            return Carbon::parse($agendamento->confirmed_at)->diffInHours(Carbon::parse($agendamento->created_at));
        });

        $tempoMedio = $totalHoras / $agendamentos->count();

        return round($tempoMedio, 1);
    }

    private function getTaxaCancelamentoFisio($dataInicio, $dataFim)
    {
        $totalAgendamentos = Agendamento::whereBetween('scheduled_at', [
            Carbon::parse($dataInicio)->startOfDay(),
            Carbon::parse($dataFim)->endOfDay()
        ])->count();

        $cancelamentos = Agendamento::where('status', 'cancelado')
            ->whereBetween('scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])->count();

        return $totalAgendamentos > 0 ? round(($cancelamentos / $totalAgendamentos) * 100, 2) : 0;
    }

    private function getEspecializacoesDemanda($dataInicio, $dataFim)
    {
        // Buscar agendamentos com fisioterapeutas
        $agendamentos = DB::table('agendamentos')
            ->join('fisioterapeutas', 'agendamentos.fisioterapeuta_id', '=', 'fisioterapeutas.user_id')
            ->select('fisioterapeutas.specializations')
            ->whereBetween('agendamentos.scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->whereNotNull('fisioterapeutas.specializations')
            ->get();

        // Processar especializações manualmente
        $especialidades = [];
        foreach ($agendamentos as $agendamento) {
            $specs = json_decode($agendamento->specializations, true);
            if (is_array($specs) && !empty($specs)) {
                $primeiraEspec = $specs[0];
                if (!isset($especialidades[$primeiraEspec])) {
                    $especialidades[$primeiraEspec] = 0;
                }
                $especialidades[$primeiraEspec]++;
            }
        }

        // Ordenar por demanda e limitar
        arsort($especialidades);
        $especialidades = array_slice($especialidades, 0, 10, true);

        // Converter para formato esperado
        $resultado = [];
        foreach ($especialidades as $especializacao => $demanda) {
            $resultado[] = [
                'especializacao' => $especializacao,
                'demanda' => $demanda,
            ];
        }

        return collect($resultado);
    }

    private function getRankingFisioterapeutas($dataInicio, $dataFim)
    {
        return DB::table('agendamentos')
            ->join('users', 'agendamentos.fisioterapeuta_id', '=', 'users.id')
            ->leftJoin('relatorios_sessao', 'agendamentos.id', '=', 'relatorios_sessao.agendamento_id')
            ->select(
                'users.name as fisioterapeuta',
                'users.email',
                DB::raw('COUNT(agendamentos.id) as total_sessoes'),
                DB::raw('COUNT(CASE WHEN agendamentos.status = "concluido" THEN 1 END) as sessoes_concluidas'),
                DB::raw('SUM(agendamentos.price) as receita_total'),
                DB::raw('AVG(relatorios_sessao.patient_satisfaction) as satisfacao_media'),
                DB::raw('COUNT(relatorios_sessao.id) as relatorios_criados')
            )
            ->whereBetween('agendamentos.scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderBy('total_sessoes', 'desc')
            ->limit(15)
            ->get()
            ->map(function ($item, $index) {
                return [
                    'posicao' => $index + 1,
                    'fisioterapeuta' => $item->fisioterapeuta,
                    'email' => $item->email,
                    'total_sessoes' => (int) $item->total_sessoes,
                    'sessoes_concluidas' => (int) $item->sessoes_concluidas,
                    'receita_total' => (float) $item->receita_total,
                    'satisfacao_media' => round($item->satisfacao_media ?? 0, 1),
                    'relatorios_criados' => (int) $item->relatorios_criados,
                    'taxa_conclusao' => $item->total_sessoes > 0 ? round(($item->sessoes_concluidas / $item->total_sessoes) * 100, 2) : 0,
                ];
            });
    }
}
