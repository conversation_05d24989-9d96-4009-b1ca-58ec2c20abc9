import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Link, router } from '@inertiajs/react';
import axios from 'axios';
import { AlertCircle, Bell, Calendar, Check, CheckCheck, CheckCircle, Clock, ExternalLink, Eye } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Notificacao {
    id: number;
    tipo: string;
    titulo: string;
    mensagem: string;
    data_envio: string;
    agendamento_id?: number;
}

interface NotificationsDropdownProps {
    className?: string;
}

export function NotificationsDropdown({ className }: NotificationsDropdownProps) {
    const [notificacoes, setNotificacoes] = useState<Notificacao[]>([]);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(false);
    const [open, setOpen] = useState(false);

    // Buscar notificações não lidas
    const fetchNotificacoes = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/notificacoes/nao-lidas');
            setNotificacoes(response.data.notificacoes);
            setTotal(response.data.total);
        } catch (error) {
            console.error('Erro ao buscar notificações:', error);
        } finally {
            setLoading(false);
        }
    };

    // Marcar notificação como lida
    const marcarComoLida = async (notificacaoId: number) => {
        try {
            await axios.post(`/notificacoes/${notificacaoId}/marcar-lida`);
            // Remover da lista local
            setNotificacoes((prev) => prev.filter((n) => n.id !== notificacaoId));
            setTotal((prev) => Math.max(0, prev - 1));
        } catch (error) {
            console.error('Erro ao marcar notificação como lida:', error);
        }
    };

    // Marcar todas como lidas
    const marcarTodasComoLidas = async () => {
        try {
            await axios.post('/notificacoes/marcar-todas-lidas');
            setNotificacoes([]);
            setTotal(0);
        } catch (error) {
            console.error('Erro ao marcar todas como lidas:', error);
        }
    };

    // Visualizar notificação específica
    const visualizarNotificacao = (notificacao: Notificacao) => {
        // Fechar o dropdown
        setOpen(false);
        // Navegar para a notificação específica (que irá redirecionar para o link apropriado)
        router.visit(`/notificacoes/${notificacao.id}`);
    };

    // Obter ícone de ação baseado no tipo
    const getActionIcon = (tipo: string) => {
        switch (tipo) {
            case 'novo_agendamento':
                return <Calendar className="h-3 w-3" />;
            case 'agendamento_confirmado':
                return <CheckCircle className="h-3 w-3" />;
            case 'agendamento_cancelado':
                return <AlertCircle className="h-3 w-3" />;
            case 'lembrete_sessao':
                return <Clock className="h-3 w-3" />;
            case 'sessao_iniciada':
            case 'sessao_finalizada':
                return <CheckCircle className="h-3 w-3" />;
            default:
                return <ExternalLink className="h-3 w-3" />;
        }
    };

    // Obter texto de ação baseado no tipo
    const getActionText = (tipo: string) => {
        switch (tipo) {
            case 'novo_agendamento':
                return 'Ver agendamento';
            case 'agendamento_confirmado':
                return 'Ver detalhes';
            case 'agendamento_cancelado':
                return 'Ver motivo';
            case 'lembrete_sessao':
                return 'Ver sessão';
            case 'sessao_iniciada':
                return 'Acompanhar';
            case 'sessao_finalizada':
                return 'Ver relatório';
            default:
                return 'Ver mais';
        }
    };

    // Buscar notificações quando o dropdown abrir
    useEffect(() => {
        if (open) {
            fetchNotificacoes();
        }
    }, [open]);

    // Buscar notificações a cada 30 segundos
    useEffect(() => {
        const interval = setInterval(fetchNotificacoes, 30000);
        return () => clearInterval(interval);
    }, []);

    // Buscar notificações na primeira carga
    useEffect(() => {
        fetchNotificacoes();
    }, []);

    const getTipoIcon = (tipo: string) => {
        switch (tipo) {
            case 'novo_agendamento':
                return '📅';
            case 'agendamento_confirmado':
                return '✅';
            case 'agendamento_cancelado':
                return '❌';
            case 'sessao_iniciada':
                return '▶️';
            case 'sessao_finalizada':
                return '🏁';
            case 'lembrete_sessao':
                return '⏰';
            default:
                return '📢';
        }
    };

    const getTipoColor = (tipo: string) => {
        switch (tipo) {
            case 'novo_agendamento':
                return 'bg-blue-100 text-blue-800';
            case 'agendamento_confirmado':
                return 'bg-green-100 text-green-800';
            case 'agendamento_cancelado':
                return 'bg-red-100 text-red-800';
            case 'sessao_iniciada':
                return 'bg-purple-100 text-purple-800';
            case 'sessao_finalizada':
                return 'bg-gray-100 text-gray-800';
            case 'lembrete_sessao':
                return 'bg-yellow-100 text-yellow-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className={`relative ${className}`}>
                    <Bell className="h-5 w-5" />
                    {total > 0 && (
                        <Badge
                            variant="destructive"
                            className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs"
                        >
                            {total > 99 ? '99+' : total}
                        </Badge>
                    )}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
                <div className="flex items-center justify-between p-2">
                    <DropdownMenuLabel className="p-0">Notificações {total > 0 && `(${total})`}</DropdownMenuLabel>
                    {total > 0 && (
                        <Button variant="ghost" size="sm" onClick={marcarTodasComoLidas} className="h-auto p-1 text-xs">
                            <CheckCheck className="mr-1 h-3 w-3" />
                            Marcar todas como lidas
                        </Button>
                    )}
                </div>
                <DropdownMenuSeparator />

                {loading ? (
                    <div className="p-4 text-center text-sm text-muted-foreground">Carregando...</div>
                ) : notificacoes.length === 0 ? (
                    <div className="p-4 text-center text-sm text-muted-foreground">Nenhuma notificação nova</div>
                ) : (
                    <ScrollArea className="max-h-96">
                        {notificacoes.map((notificacao) => (
                            <DropdownMenuItem
                                key={notificacao.id}
                                className="flex cursor-pointer flex-col items-start p-3 hover:bg-muted/50"
                                onClick={(e) => {
                                    e.preventDefault();
                                    visualizarNotificacao(notificacao);
                                }}
                            >
                                <div className="flex w-full items-start justify-between">
                                    <div className="flex flex-1 items-start space-x-2">
                                        <span className="text-lg">{getTipoIcon(notificacao.tipo)}</span>
                                        <div className="min-w-0 flex-1">
                                            <div className="flex items-center justify-between">
                                                <p className="truncate text-sm font-medium">{notificacao.titulo}</p>
                                                <div className="flex items-center gap-1">
                                                    <TooltipProvider>
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    className="h-auto p-1"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        visualizarNotificacao(notificacao);
                                                                    }}
                                                                >
                                                                    {getActionIcon(notificacao.tipo)}
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>{getActionText(notificacao.tipo)}</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TooltipProvider>
                                                    <TooltipProvider>
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    className="h-auto p-1"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        marcarComoLida(notificacao.id);
                                                                    }}
                                                                >
                                                                    <Check className="h-3 w-3" />
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>Marcar como lida</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TooltipProvider>
                                                </div>
                                            </div>
                                            <p className="mt-1 line-clamp-2 text-xs text-muted-foreground">{notificacao.mensagem}</p>
                                            <div className="mt-2 flex items-center justify-between">
                                                <Badge variant="secondary" className={`text-xs ${getTipoColor(notificacao.tipo)}`}>
                                                    {notificacao.tipo.replace('_', ' ')}
                                                </Badge>
                                                <span className="text-xs text-muted-foreground">{notificacao.data_envio}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </DropdownMenuItem>
                        ))}
                    </ScrollArea>
                )}

                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                    <Link href="/notificacoes" className="w-full text-center">
                        <Eye className="mr-2 h-4 w-4" />
                        Ver todas as notificações
                    </Link>
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
