<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orientacao_domiciliars', function (Blueprint $table) {
            $table->id();
            $table->foreignId('paciente_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('fisioterapeuta_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('agendamento_id')->nullable()->constrained('agendamentos')->onDelete('set null');
            $table->string('titulo');
            $table->text('descricao');
            $table->text('exercicios_recomendados')->nullable();
            $table->text('cuidados_posturais')->nullable();
            $table->text('atividades_evitar')->nullable();
            $table->text('dicas_gerais')->nullable();
            $table->integer('frequencia_dias')->nullable()->comment('Frequência em dias');
            $table->time('horario_recomendado')->nullable();
            $table->enum('prioridade', ['baixa', 'media', 'alta'])->default('media');
            $table->boolean('ativa')->default(true);
            $table->text('observacoes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orientacao_domiciliars');
    }
};
