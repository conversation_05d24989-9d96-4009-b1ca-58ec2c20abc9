<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\HorarioBase;
use App\Models\HorarioExcecao;
use App\Models\MedicoFeriadosConfig;
use Illuminate\Database\Seeder;

class HorariosExemploSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buscar um fisioterapeuta para exemplo
        $fisioterapeuta = User::where('role', 'fisioterapeuta')->first();
        
        if (!$fisioterapeuta) {
            $this->command->info('Nenhum fisioterapeuta encontrado. Criando um para exemplo...');
            
            $fisioterapeuta = User::create([
                'name' => 'Dr. <PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'fisioterapeuta',
                'phone' => '(11) 99999-9999',
                'active' => true,
            ]);
        }

        $this->command->info("Criando horários de exemplo para: {$fisioterapeuta->name}");

        // Limpar horários existentes
        HorarioBase::where('fisioterapeuta_id', $fisioterapeuta->id)->delete();
        HorarioExcecao::where('fisioterapeuta_id', $fisioterapeuta->id)->delete();
        MedicoFeriadosConfig::where('fisioterapeuta_id', $fisioterapeuta->id)->delete();

        // Criar horários base - Segunda a Sexta
        $horariosSegSex = [
            ['periodo' => 'Manhã', 'inicio' => '08:00', 'fim' => '12:00'],
            ['periodo' => 'Tarde', 'inicio' => '14:00', 'fim' => '18:00'],
        ];

        for ($dia = 1; $dia <= 5; $dia++) { // Segunda a Sexta
            foreach ($horariosSegSex as $horario) {
                HorarioBase::create([
                    'fisioterapeuta_id' => $fisioterapeuta->id,
                    'dia_semana' => $dia,
                    'hora_inicio' => $horario['inicio'],
                    'hora_fim' => $horario['fim'],
                    'periodo_nome' => $horario['periodo'],
                    'ativo' => true,
                ]);
            }
        }

        // Sábado - apenas manhã
        HorarioBase::create([
            'fisioterapeuta_id' => $fisioterapeuta->id,
            'dia_semana' => 6, // Sábado
            'hora_inicio' => '08:00',
            'hora_fim' => '12:00',
            'periodo_nome' => 'Manhã',
            'ativo' => true,
        ]);

        // Domingo - horário reduzido
        HorarioBase::create([
            'fisioterapeuta_id' => $fisioterapeuta->id,
            'dia_semana' => 0, // Domingo
            'hora_inicio' => '10:00',
            'hora_fim' => '14:00',
            'periodo_nome' => 'Plantão',
            'ativo' => true,
        ]);

        // Criar algumas exceções de exemplo
        $hoje = now();
        
        // Exceção 1: Indisponível em uma data específica
        HorarioExcecao::create([
            'fisioterapeuta_id' => $fisioterapeuta->id,
            'tipo' => 'data_especifica',
            'data_inicio' => $hoje->copy()->addDays(5)->format('Y-m-d'),
            'acao' => 'indisponivel',
            'motivo' => 'Consulta médica pessoal',
            'ativo' => true,
        ]);

        // Exceção 2: Horário customizado para uma semana
        HorarioExcecao::create([
            'fisioterapeuta_id' => $fisioterapeuta->id,
            'tipo' => 'semana',
            'data_inicio' => $hoje->copy()->addDays(10)->startOfWeek()->format('Y-m-d'),
            'data_fim' => $hoje->copy()->addDays(10)->endOfWeek()->format('Y-m-d'),
            'dia_semana' => 1, // Segunda-feira
            'hora_inicio' => '10:00',
            'hora_fim' => '16:00',
            'acao' => 'horario_customizado',
            'motivo' => 'Semana de curso de especialização',
            'ativo' => true,
        ]);

        // Exceção 3: Disponível em um domingo específico
        HorarioExcecao::create([
            'fisioterapeuta_id' => $fisioterapeuta->id,
            'tipo' => 'data_especifica',
            'data_inicio' => $hoje->copy()->addDays(14)->format('Y-m-d'),
            'hora_inicio' => '08:00',
            'hora_fim' => '17:00',
            'acao' => 'horario_customizado',
            'motivo' => 'Atendimento especial - evento esportivo',
            'ativo' => true,
        ]);

        // Configuração de feriados
        MedicoFeriadosConfig::create([
            'fisioterapeuta_id' => $fisioterapeuta->id,
            'trabalha_feriados_nacionais' => false,
            'trabalha_feriados_estaduais' => false,
            'trabalha_feriados_municipais' => true,
            'feriados_excecoes' => [], // Sem exceções específicas
        ]);

        $this->command->info('Horários de exemplo criados com sucesso!');
        $this->command->info('Configuração criada:');
        $this->command->info('- Segunda a Sexta: 08:00-12:00 e 14:00-18:00');
        $this->command->info('- Sábado: 08:00-12:00');
        $this->command->info('- Domingo: 10:00-14:00');
        $this->command->info('- Algumas exceções de exemplo foram adicionadas');
        $this->command->info('- Configurado para trabalhar apenas em feriados municipais');
    }
}
