import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Calendar, Clock, Download, Eye, FileText, Filter, Search, TrendingUp, User } from 'lucide-react';
import { useState } from 'react';

interface RelatorioSessao {
    id: number;
    observacoes: string;
    exercicios_realizados: string[];
    proximos_passos: string;
    created_at: string;
    agendamento: {
        id: number;
        data_hora: string;
        status: string;
        valor: number;
        paciente: {
            id: number;
            name: string;
            email: string;
        };
        formatted_data_hora: string;
        formatted_valor: string;
    };
}

interface Stats {
    total: number;
    mes_atual: number;
    semana_atual: number;
    pendentes: number;
}

interface Props {
    relatorios: {
        data: RelatorioSessao[];
        links: any[];
        meta: any;
    };
    stats: Stats;
    filtros: {
        search?: string;
        paciente_id?: string;
        data_inicio?: string;
        data_fim?: string;
    };
    pacientes: Array<{
        id: number;
        name: string;
    }>;
}

export default function RelatoriosIndex({ relatorios, stats, filtros, pacientes }: Props) {
    const [search, setSearch] = useState(filtros.search || '');
    const [pacienteId, setPacienteId] = useState(filtros.paciente_id || 'all');
    const [dataInicio, setDataInicio] = useState(filtros.data_inicio || '');
    const [dataFim, setDataFim] = useState(filtros.data_fim || '');

    const handleFilter = () => {
        router.get(
            route('fisioterapeuta.relatorios.index'),
            {
                search: search || undefined,
                paciente_id: pacienteId === 'all' ? undefined : pacienteId,
                data_inicio: dataInicio || undefined,
                data_fim: dataFim || undefined,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    const handleExport = () => {
        const params = new URLSearchParams({
            search: search || '',
            paciente_id: pacienteId === 'all' ? '' : pacienteId,
            data_inicio: dataInicio || '',
            data_fim: dataFim || '',
        });

        window.open(route('fisioterapeuta.relatorios.export') + '?' + params.toString());
    };

    return (
        <AppLayout>
            <Head title="Relatórios de Sessão" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div>
                                <h2 className="text-3xl font-bold tracking-tight">Relatórios de Sessão</h2>
                                <p className="text-muted-foreground">Gerencie e visualize os relatórios das suas sessões</p>
                            </div>
                            <div className="flex space-x-2">
                                <Button onClick={handleExport} variant="outline">
                                    <Download className="mr-2 h-4 w-4" />
                                    Exportar
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Stats */}
                    <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-4">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <FileText className="h-8 w-8 text-blue-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Total</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <Calendar className="h-8 w-8 text-green-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Este Mês</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.mes_atual}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <TrendingUp className="h-8 w-8 text-purple-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Esta Semana</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.semana_atual}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <Clock className="h-8 w-8 text-orange-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Pendentes</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.pendentes}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filtros */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Filter className="mr-2 h-5 w-5" />
                                Filtros
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-6">
                                <div className="md:col-span-2">
                                    <Input
                                        placeholder="Buscar por observações..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="w-full"
                                    />
                                </div>

                                <div>
                                    <Select value={pacienteId} onValueChange={setPacienteId}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Paciente" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">Todos</SelectItem>
                                            {pacientes.map((paciente) => (
                                                <SelectItem key={paciente.id} value={paciente.id.toString()}>
                                                    {paciente.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <Input
                                        type="date"
                                        placeholder="Data início"
                                        value={dataInicio}
                                        onChange={(e) => setDataInicio(e.target.value)}
                                        className="w-full"
                                    />
                                </div>

                                <div>
                                    <Input
                                        type="date"
                                        placeholder="Data fim"
                                        value={dataFim}
                                        onChange={(e) => setDataFim(e.target.value)}
                                        className="w-full"
                                    />
                                </div>

                                <div>
                                    <Button onClick={handleFilter} className="w-full">
                                        <Search className="mr-2 h-4 w-4" />
                                        Filtrar
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Lista de Relatórios */}
                    <div className="space-y-4">
                        {relatorios.data.length === 0 ? (
                            <Card>
                                <CardContent className="p-8 text-center">
                                    <FileText className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-4 text-lg font-medium text-gray-900">Nenhum relatório encontrado</h3>
                                    <p className="mt-2 text-gray-500">Não há relatórios que correspondam aos filtros selecionados.</p>
                                </CardContent>
                            </Card>
                        ) : (
                            relatorios.data.map((relatorio) => (
                                <Card key={relatorio.id} className="transition-shadow hover:shadow-md">
                                    <CardContent className="p-6">
                                        <div className="flex items-start justify-between">
                                            <div className="flex-1">
                                                <div className="mb-4 flex items-center space-x-4">
                                                    <h3 className="text-lg font-semibold text-gray-900">Sessão #{relatorio.agendamento.id}</h3>
                                                    <Badge variant="outline">{relatorio.agendamento.paciente.name}</Badge>
                                                </div>

                                                <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                                                    <div>
                                                        <div className="mb-2 flex items-center text-sm text-gray-600">
                                                            <Calendar className="mr-2 h-4 w-4" />
                                                            <span className="font-medium">Data da sessão:</span>
                                                            <span className="ml-2">{relatorio.agendamento.formatted_data_hora}</span>
                                                        </div>
                                                        <div className="mb-2 flex items-center text-sm text-gray-600">
                                                            <User className="mr-2 h-4 w-4" />
                                                            <span className="font-medium">Paciente:</span>
                                                            <span className="ml-2">{relatorio.agendamento.paciente.name}</span>
                                                        </div>
                                                        <div className="mb-2 flex items-center text-sm text-gray-600">
                                                            <Clock className="mr-2 h-4 w-4" />
                                                            <span className="font-medium">Relatório criado:</span>
                                                            <span className="ml-2">
                                                                {format(new Date(relatorio.created_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div>
                                                        <div className="mb-2 text-sm text-gray-600">
                                                            <span className="font-medium">Valor da sessão:</span>
                                                            <span className="ml-2 font-bold text-green-600">
                                                                {relatorio.agendamento.formatted_valor}
                                                            </span>
                                                        </div>
                                                        <div className="mb-2 text-sm text-gray-600">
                                                            <span className="font-medium">Exercícios realizados:</span>
                                                            <span className="ml-2">{relatorio.exercicios_realizados.length}</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="mb-4">
                                                    <h4 className="mb-2 font-medium text-gray-900">Observações:</h4>
                                                    <p className="text-sm text-gray-600">
                                                        {relatorio.observacoes.length > 200
                                                            ? relatorio.observacoes.substring(0, 200) + '...'
                                                            : relatorio.observacoes}
                                                    </p>
                                                </div>

                                                <div>
                                                    <h4 className="mb-2 font-medium text-gray-900">Próximos passos:</h4>
                                                    <p className="text-sm text-gray-600">
                                                        {relatorio.proximos_passos.length > 150
                                                            ? relatorio.proximos_passos.substring(0, 150) + '...'
                                                            : relatorio.proximos_passos}
                                                    </p>
                                                </div>
                                            </div>

                                            <div className="ml-6 flex flex-col space-y-2">
                                                <Link href={route('fisioterapeuta.relatorios.show', relatorio.id)}>
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="mr-2 h-4 w-4" />
                                                        Ver Completo
                                                    </Button>
                                                </Link>
                                                <Link href={route('fisioterapeuta.agenda.show', relatorio.agendamento.id)}>
                                                    <Button variant="ghost" size="sm">
                                                        <Calendar className="mr-2 h-4 w-4" />
                                                        Ver Sessão
                                                    </Button>
                                                </Link>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))
                        )}
                    </div>

                    {/* Paginação */}
                    {relatorios.links && relatorios.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <div className="flex space-x-1">
                                {relatorios.links.map((link: any, index: number) => (
                                    <Link
                                        key={index}
                                        href={link.url || '#'}
                                        className={`rounded-md px-3 py-2 text-sm ${
                                            link.active
                                                ? 'bg-blue-600 text-white'
                                                : link.url
                                                  ? 'border bg-white text-gray-700 hover:bg-gray-50'
                                                  : 'cursor-not-allowed bg-gray-100 text-gray-400'
                                        }`}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
