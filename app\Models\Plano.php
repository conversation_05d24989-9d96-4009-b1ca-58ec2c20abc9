<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Plano extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'sessions_per_month',
        'session_duration',
        'included_services',
        'benefits',
        'active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'included_services' => 'array',
        'benefits' => 'array',
        'active' => 'boolean',
    ];

    // Relacionamentos
    public function assinaturas()
    {
        return $this->hasMany(Assinatura::class);
    }

    // Scopes
    public function scopeAtivos($query)
    {
        return $query->where('active', true);
    }

    // Métodos auxiliares
    public function getFormattedPriceAttribute()
    {
        return 'R$ ' . number_format($this->price, 2, ',', '.');
    }
}
