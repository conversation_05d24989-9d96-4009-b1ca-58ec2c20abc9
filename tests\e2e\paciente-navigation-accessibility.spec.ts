import { expect, test } from '@playwright/test';

test.describe('Navegação e Acessibilidade do Paciente', () => {
    let patientEmail: string;
    let patientPassword = 'password123';

    test.beforeAll(async () => {
        const timestamp = Date.now();
        patientEmail = `paciente.nav.${timestamp}@example.com`;
    });

    test.beforeEach(async ({ page }) => {
        // Registrar e fazer login do paciente
        await page.goto('/register');
        await page.fill('input[name="name"]', 'Paciente Navegação');
        await page.fill('input[name="email"]', patientEmail);
        await page.fill('input[name="password"]', patientPassword);
        await page.fill('input[name="password_confirmation"]', patientPassword);
        await page.selectOption('select[name="role"]', 'paciente');
        await page.click('button[type="submit"]');
        await page.waitForURL(/.*paciente/);
    });

    test.describe('1. Navegação Principal', () => {
        test('deve ter sidebar com todos os links necessários', async ({ page }) => {
            await page.goto('/paciente/dashboard');
            
            // Verificar se a sidebar está presente
            const sidebar = page.locator('aside, nav[role="navigation"], [data-testid="sidebar"]');
            await expect(sidebar).toBeVisible();

            // Links esperados na navegação do paciente
            const expectedNavItems = [
                'Início',
                'Dashboard',
                'Perfil',
                'Agendamentos',
                'Histórico',
                'Pagamentos',
                'Planos',
                'Avaliações'
            ];

            for (const item of expectedNavItems) {
                const navLink = page.locator(`a:has-text("${item}"), button:has-text("${item}")`);
                if (await navLink.count() > 0) {
                    await expect(navLink.first()).toBeVisible();
                }
            }
        });

        test('deve permitir navegação entre todas as páginas principais', async ({ page }) => {
            const pages = [
                { name: 'Dashboard', url: '/paciente/dashboard', title: /dashboard|início/i },
                { name: 'Perfil', url: '/paciente/perfil', title: /perfil/i },
                { name: 'Agendamentos', url: '/paciente/agendamentos', title: /agendamento/i },
                { name: 'Histórico', url: '/paciente/historico', title: /histórico/i },
                { name: 'Pagamentos', url: '/paciente/pagamentos', title: /pagamento/i },
                { name: 'Planos', url: '/paciente/planos', title: /plano/i },
                { name: 'Avaliações', url: '/paciente/avaliacoes', title: /avaliação/i }
            ];

            for (const pageInfo of pages) {
                await page.goto(pageInfo.url);
                
                // Verificar se a página carrega corretamente
                await expect(page).toHaveURL(new RegExp(pageInfo.url.replace('/', '\\/')));
                await expect(page.locator('h1, h2')).toContainText(pageInfo.title);
                
                // Verificar se não há erros JavaScript
                const errors = await page.evaluate(() => window.console.error);
                // Note: Esta verificação pode precisar ser ajustada dependendo de como os erros são capturados
            }
        });

        test('deve manter estado ativo na navegação', async ({ page }) => {
            await page.goto('/paciente/dashboard');
            
            // Navegar para perfil
            const perfilLink = page.locator('a:has-text("Perfil"), button:has-text("Perfil")');
            if (await perfilLink.isVisible()) {
                await perfilLink.click();
                await expect(page).toHaveURL(/.*perfil/);
                
                // Verificar se o link do perfil está marcado como ativo
                const activeLink = page.locator('a[aria-current="page"], .active, [data-active="true"]');
                if (await activeLink.count() > 0) {
                    await expect(activeLink).toContainText(/perfil/i);
                }
            }
        });

        test('deve ter breadcrumbs funcionais', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Verificar se há breadcrumbs
            const breadcrumbs = page.locator('nav[aria-label="breadcrumb"], .breadcrumb, [data-testid="breadcrumb"]');
            if (await breadcrumbs.isVisible()) {
                // Verificar se contém links funcionais
                const breadcrumbLinks = breadcrumbs.locator('a');
                if (await breadcrumbLinks.count() > 0) {
                    const firstLink = breadcrumbLinks.first();
                    await expect(firstLink).toBeVisible();
                    
                    // Testar clique no breadcrumb
                    await firstLink.click();
                    await expect(page).toHaveURL(/.*paciente/);
                }
            }
        });
    });

    test.describe('2. Acessibilidade', () => {
        test('deve ter estrutura semântica adequada', async ({ page }) => {
            await page.goto('/paciente/dashboard');
            
            // Verificar se há elementos semânticos principais
            await expect(page.locator('main, [role="main"]')).toBeVisible();
            await expect(page.locator('nav, [role="navigation"]')).toBeVisible();
            
            // Verificar hierarquia de headings
            const h1 = page.locator('h1');
            if (await h1.count() > 0) {
                await expect(h1.first()).toBeVisible();
            }
        });

        test('deve ter labels adequados em formulários', async ({ page }) => {
            await page.goto('/paciente/perfil');
            
            // Verificar se inputs têm labels
            const inputs = page.locator('input:not([type="hidden"])');
            const inputCount = await inputs.count();
            
            for (let i = 0; i < Math.min(inputCount, 5); i++) {
                const input = inputs.nth(i);
                const inputId = await input.getAttribute('id');
                const inputName = await input.getAttribute('name');
                
                if (inputId) {
                    // Verificar se há label associado
                    const label = page.locator(`label[for="${inputId}"]`);
                    const hasLabel = await label.count() > 0;
                    
                    // Verificar se há aria-label
                    const ariaLabel = await input.getAttribute('aria-label');
                    
                    // Verificar se há placeholder descritivo
                    const placeholder = await input.getAttribute('placeholder');
                    
                    // Pelo menos uma forma de identificação deve estar presente
                    expect(hasLabel || ariaLabel || placeholder).toBeTruthy();
                }
            }
        });

        test('deve ter contraste adequado em botões', async ({ page }) => {
            await page.goto('/paciente/dashboard');
            
            // Verificar se botões são visíveis e clicáveis
            const buttons = page.locator('button:visible');
            const buttonCount = await buttons.count();
            
            if (buttonCount > 0) {
                const firstButton = buttons.first();
                await expect(firstButton).toBeVisible();
                
                // Verificar se o botão tem texto ou aria-label
                const buttonText = await firstButton.textContent();
                const ariaLabel = await firstButton.getAttribute('aria-label');
                
                expect(buttonText || ariaLabel).toBeTruthy();
            }
        });

        test('deve ser navegável por teclado', async ({ page }) => {
            await page.goto('/paciente/dashboard');
            
            // Testar navegação por Tab
            await page.keyboard.press('Tab');
            
            // Verificar se há elemento focado
            const focusedElement = page.locator(':focus');
            if (await focusedElement.count() > 0) {
                await expect(focusedElement).toBeVisible();
            }
            
            // Testar mais algumas navegações por Tab
            for (let i = 0; i < 3; i++) {
                await page.keyboard.press('Tab');
                await page.waitForTimeout(100);
            }
        });

        test('deve ter skip links para navegação', async ({ page }) => {
            await page.goto('/paciente/dashboard');
            
            // Verificar se há skip links (podem estar ocultos até receberem foco)
            const skipLinks = page.locator('a[href="#main"], a[href="#content"], .skip-link');
            if (await skipLinks.count() > 0) {
                // Focar no primeiro skip link
                await skipLinks.first().focus();
                await expect(skipLinks.first()).toBeVisible();
            }
        });
    });

    test.describe('3. Responsividade', () => {
        test('deve funcionar em tablet', async ({ page }) => {
            // Definir viewport de tablet
            await page.setViewportSize({ width: 768, height: 1024 });
            
            await page.goto('/paciente/dashboard');
            
            // Verificar se o layout se adapta
            await expect(page.locator('h1, h2')).toBeVisible();
            
            // Verificar se a navegação funciona em tablet
            const navItems = page.locator('nav a, nav button');
            if (await navItems.count() > 0) {
                await expect(navItems.first()).toBeVisible();
            }
        });

        test('deve ter menu mobile funcional', async ({ page }) => {
            // Definir viewport mobile
            await page.setViewportSize({ width: 375, height: 667 });
            
            await page.goto('/paciente/dashboard');
            
            // Procurar por botão de menu mobile
            const menuButton = page.locator(
                'button[aria-label*="menu"], button[aria-label*="Menu"], ' +
                'button:has-text("☰"), [data-testid="mobile-menu-button"], ' +
                '.hamburger, .menu-toggle'
            );
            
            if (await menuButton.isVisible()) {
                // Abrir menu
                await menuButton.click();
                
                // Verificar se o menu aparece
                const mobileMenu = page.locator('nav, .mobile-menu, [data-testid="mobile-menu"]');
                await expect(mobileMenu).toBeVisible();
                
                // Testar navegação no menu mobile
                const mobileNavItems = mobileMenu.locator('a, button');
                if (await mobileNavItems.count() > 0) {
                    await expect(mobileNavItems.first()).toBeVisible();
                }
                
                // Fechar menu (se houver botão de fechar)
                const closeButton = page.locator('button[aria-label*="close"], button[aria-label*="Close"], .close');
                if (await closeButton.isVisible()) {
                    await closeButton.click();
                }
            }
        });

        test('deve ter formulários responsivos', async ({ page }) => {
            // Testar em diferentes tamanhos de tela
            const viewports = [
                { width: 375, height: 667 }, // Mobile
                { width: 768, height: 1024 }, // Tablet
                { width: 1024, height: 768 }  // Desktop pequeno
            ];
            
            for (const viewport of viewports) {
                await page.setViewportSize(viewport);
                await page.goto('/paciente/perfil');
                
                // Verificar se o formulário é acessível
                const formInputs = page.locator('input, select, textarea');
                if (await formInputs.count() > 0) {
                    await expect(formInputs.first()).toBeVisible();
                    
                    // Verificar se os campos não estão cortados
                    const firstInput = formInputs.first();
                    const boundingBox = await firstInput.boundingBox();
                    if (boundingBox) {
                        expect(boundingBox.width).toBeGreaterThan(0);
                        expect(boundingBox.x).toBeGreaterThanOrEqual(0);
                    }
                }
            }
        });
    });

    test.describe('4. Performance e Carregamento', () => {
        test('deve carregar páginas rapidamente', async ({ page }) => {
            const pages = [
                '/paciente/dashboard',
                '/paciente/perfil',
                '/paciente/agendamentos',
                '/paciente/historico'
            ];
            
            for (const pageUrl of pages) {
                const startTime = Date.now();
                await page.goto(pageUrl);
                
                // Aguardar elemento principal carregar
                await expect(page.locator('h1, h2')).toBeVisible();
                
                const loadTime = Date.now() - startTime;
                
                // Verificar se carregou em menos de 5 segundos
                expect(loadTime).toBeLessThan(5000);
            }
        });

        test('deve lidar com estados de carregamento', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Verificar se há indicadores de carregamento
            const loadingIndicators = page.locator(
                '.loading, .spinner, [data-testid="loading"], ' +
                'text=Carregando, text=Loading'
            );
            
            // Se houver indicadores de carregamento, eles devem desaparecer
            if (await loadingIndicators.count() > 0) {
                await expect(loadingIndicators.first()).toBeHidden({ timeout: 10000 });
            }
        });
    });

    test.describe('5. Estados de Erro', () => {
        test('deve lidar com páginas não encontradas', async ({ page }) => {
            // Tentar acessar página inexistente
            await page.goto('/paciente/pagina-inexistente');
            
            // Verificar se há tratamento de erro 404
            const errorIndicators = page.locator(
                'text=404, text=Página não encontrada, text=Not found, ' +
                'h1:has-text("404"), h2:has-text("404")'
            );
            
            if (await errorIndicators.count() > 0) {
                await expect(errorIndicators.first()).toBeVisible();
            }
        });

        test('deve mostrar mensagens de erro em formulários', async ({ page }) => {
            await page.goto('/paciente/perfil');
            
            // Tentar submeter formulário com dados inválidos
            const nameField = page.locator('input[name="name"]');
            if (await nameField.isVisible()) {
                await nameField.clear();
                
                const submitButton = page.locator('button[type="submit"]');
                if (await submitButton.isVisible()) {
                    await submitButton.click();
                    
                    // Verificar se aparecem mensagens de erro
                    const errorMessages = page.locator(
                        '.error, .text-red-500, .text-danger, ' +
                        '[data-testid="error"], .invalid-feedback'
                    );
                    
                    if (await errorMessages.count() > 0) {
                        await expect(errorMessages.first()).toBeVisible();
                    }
                }
            }
        });
    });
});
