import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { DatePicker } from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { format } from 'date-fns';
import { AlertTriangle, ArrowLeft, CheckCircle, ChevronDown, Clock, Star, User } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Fisioterapeuta {
    id: number;
    user: {
        name: string;
        avatar?: string;
    };
    crefito: string;
    specializations: string[];
    hourly_rate: number;
    rating: number;
    total_reviews: number;
}

interface Props {
    fisioterapeutas: Fisioterapeuta[];
    fisioterapeutaSelecionado?: Fisioterapeuta;
    assinaturaAtual?: {
        sessoes_restantes: number;
        plano: {
            nome: string;
            sessions_per_month: number;
        };
    };
    isPlanoAvulso: boolean;
}

export default function PacienteAgendamentoCreate() {
    const pageProps = usePage().props as any;
    const { fisioterapeutas, fisioterapeutaSelecionado, assinaturaAtual, isPlanoAvulso } = pageProps;
    const [horariosDisponiveis, setHorariosDisponiveis] = useState<string[]>([]);
    const [loadingHorarios, setLoadingHorarios] = useState(false);
    const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
    const [fisioterapeutaOpen, setFisioterapeutaOpen] = useState(false);
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        fisioterapeuta_id: fisioterapeutaSelecionado?.id.toString() || '',
        data_agendamento: '',
        horario: '',
        tipo: 'sessao',
        observacoes: '',
        data_hora: '',
    });

    // Sincronizar data inicial se houver uma pré-selecionada
    useEffect(() => {
        if (data.data_agendamento && !selectedDate) {
            setSelectedDate(new Date(data.data_agendamento + 'T00:00:00'));
        }
    }, [data.data_agendamento, selectedDate]);

    // Buscar horários disponíveis quando fisioterapeuta e data forem selecionados
    useEffect(() => {
        if (data.fisioterapeuta_id && data.data_agendamento) {
            buscarHorariosDisponiveis();
        }
    }, [data.fisioterapeuta_id, data.data_agendamento]);

    // Função para lidar com mudança de data
    const handleDateChange = (date: Date | undefined) => {
        setSelectedDate(date);
        if (date) {
            const dateString = format(date, 'yyyy-MM-dd');
            setData('data_agendamento', dateString);
        } else {
            setData('data_agendamento', '');
        }
        // Reset horário quando data muda
        setData('horario', '');
        setHorariosDisponiveis([]);
    };

    const buscarHorariosDisponiveis = async () => {
        setLoadingHorarios(true);
        try {
            const response = await fetch(route('paciente.agendamentos.horarios-disponiveis'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    fisioterapeuta_id: data.fisioterapeuta_id,
                    data_agendamento: data.data_agendamento,
                }),
            });

            if (response.ok) {
                const result = await response.json();
                setHorariosDisponiveis(result.horarios || []);
            }
        } catch (error) {
            console.error('Erro ao buscar horários:', error);
        } finally {
            setLoadingHorarios(false);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (isSubmitting) return;

        // Limpar erros anteriores
        setValidationErrors({});

        // Validações do frontend
        const newValidationErrors: Record<string, string> = {};

        if (!data.fisioterapeuta_id) {
            newValidationErrors.fisioterapeuta_id = 'Selecione um fisioterapeuta';
        }

        if (!data.data_agendamento) {
            newValidationErrors.data_agendamento = 'Selecione uma data';
        } else {
            const selectedDate = new Date(data.data_agendamento + 'T00:00:00');
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate <= today) {
                newValidationErrors.data_agendamento = 'A data deve ser futura';
            }

            if (selectedDate.getDay() === 0) {
                newValidationErrors.data_agendamento = 'Não é possível agendar aos domingos';
            }

            // Verificar se a data não é muito distante (máximo 60 dias)
            const maxDate = new Date();
            maxDate.setDate(maxDate.getDate() + 60);
            if (selectedDate > maxDate) {
                newValidationErrors.data_agendamento = 'Não é possível agendar com mais de 60 dias de antecedência';
            }
        }

        if (!data.horario) {
            newValidationErrors.horario = 'Selecione um horário';
        }

        if (!data.tipo) {
            newValidationErrors.tipo = 'Selecione o tipo de agendamento';
        }

        // Verificar se há sessões restantes (apenas para planos mensais)
        if (!isPlanoAvulso && assinaturaAtual && assinaturaAtual.sessoes_restantes <= 0) {
            newValidationErrors.sessoes = 'Você não possui sessões restantes no seu plano';
        }

        // Verificar se o horário ainda está disponível (validação adicional)
        if (data.horario && !horariosDisponiveis.includes(data.horario)) {
            newValidationErrors.horario = 'Este horário não está mais disponível. Selecione outro horário.';
        }

        // Se há erros, mostrar e não submeter
        if (Object.keys(newValidationErrors).length > 0) {
            setValidationErrors(newValidationErrors);
            return;
        }

        // Combinar data e horário para enviar ao backend
        const dataHora = `${data.data_agendamento} ${data.horario}:00`;

        setIsSubmitting(true);

        // Atualizar os dados do formulário com a data_hora combinada
        setData('data_hora', dataHora);

        post(route('paciente.agendamentos.store'), {
            onFinish: () => {
                setIsSubmitting(false);
            },
            onError: () => {
                setIsSubmitting(false);
            },
        });
    };

    // Gerar datas disponíveis (próximos 30 dias, excluindo domingos)
    const getDatasDisponiveis = () => {
        const datas = [];
        const hoje = new Date();

        for (let i = 1; i <= 30; i++) {
            const data = new Date(hoje);
            data.setDate(hoje.getDate() + i);

            // Excluir domingos (0 = domingo)
            if (data.getDay() !== 0) {
                datas.push({
                    value: data.toISOString().split('T')[0],
                    label: data.toLocaleDateString('pt-BR', {
                        weekday: 'long',
                        day: '2-digit',
                        month: 'long',
                    }),
                });
            }
        }

        return datas;
    };

    const datasDisponiveis = getDatasDisponiveis();

    // Função para desabilitar datas passadas e domingos
    const isDateDisabled = (date: Date) => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const isPast = date < today;
        const isSunday = date.getDay() === 0;
        return isPast || isSunday;
    };

    return (
        <AppLayout>
            <Head title="Novo Agendamento" />

            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                <div className="flex items-center gap-4">
                    <Link href={route('paciente.agendamentos.index')}>
                        <Button variant="ghost" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Novo Agendamento</h1>
                        <p className="text-muted-foreground">Agende sua próxima sessão de fisioterapia</p>
                    </div>
                </div>

                {/* Informações do plano */}
                {isPlanoAvulso ? (
                    <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Plano Avulso:</strong> Você pagará R$ 120,00 por sessão no momento do agendamento.
                        </AlertDescription>
                    </Alert>
                ) : assinaturaAtual ? (
                    <>
                        {assinaturaAtual.sessoes_restantes === 0 ? (
                            <Alert>
                                <AlertTriangle className="h-4 w-4" />
                                <AlertDescription>
                                    Você não possui sessões restantes no seu plano atual.
                                    <Link href={route('paciente.planos')} className="ml-1 underline">
                                        Altere seu plano aqui
                                    </Link>
                                </AlertDescription>
                            </Alert>
                        ) : (
                            <Alert>
                                <CheckCircle className="h-4 w-4" />
                                <AlertDescription>
                                    <strong>Plano {assinaturaAtual.plano.nome}:</strong> Você possui {assinaturaAtual.sessoes_restantes} sessões
                                    restantes neste mês.
                                </AlertDescription>
                            </Alert>
                        )}
                    </>
                ) : (
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            Você precisa ter um plano ativo para agendar sessões.
                            <Link href={route('paciente.planos')} className="ml-1 underline">
                                Escolha um plano aqui
                            </Link>
                        </AlertDescription>
                    </Alert>
                )}

                {/* Formulário de agendamento - disponível para planos avulsos e mensais */}
                {(isPlanoAvulso || (assinaturaAtual && assinaturaAtual.sessoes_restantes > 0)) && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Dados do Agendamento</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Tipo de Agendamento */}
                                <div className="space-y-2">
                                    <Label htmlFor="tipo">Tipo de Agendamento</Label>
                                    <Select value={data.tipo} onValueChange={(value) => setData('tipo', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione o tipo" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="sessao">Sessão de Fisioterapia</SelectItem>
                                            <SelectItem value="avaliacao">Avaliação</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.tipo && <p className="text-sm text-red-600">{errors.tipo}</p>}
                                </div>

                                {/* Fisioterapeuta Selecionado */}
                                {fisioterapeutaSelecionado && (
                                    <Card className="border-green-200 bg-green-50">
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2 text-green-800">
                                                <CheckCircle className="h-5 w-5" />
                                                Fisioterapeuta Selecionado
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="flex items-start gap-4">
                                                <Avatar className="h-16 w-16">
                                                    <AvatarImage src={fisioterapeutaSelecionado.user.avatar} />
                                                    <AvatarFallback>
                                                        {fisioterapeutaSelecionado.user.name
                                                            .split(' ')
                                                            .map((n: string) => n[0])
                                                            .join('')
                                                            .toUpperCase()}
                                                    </AvatarFallback>
                                                </Avatar>

                                                <div className="flex-1">
                                                    <h3 className="text-lg font-semibold">{fisioterapeutaSelecionado.user.name}</h3>
                                                    <p className="text-sm text-muted-foreground">CREFITO: {fisioterapeutaSelecionado.crefito}</p>

                                                    <div className="mt-1 flex items-center gap-1">
                                                        {Array.from({ length: 5 }, (_, i) => (
                                                            <Star
                                                                key={i}
                                                                className={`h-4 w-4 ${
                                                                    i < Math.floor(fisioterapeutaSelecionado.rating)
                                                                        ? 'fill-yellow-400 text-yellow-400'
                                                                        : 'text-gray-300'
                                                                }`}
                                                            />
                                                        ))}
                                                        <span className="ml-1 text-sm text-muted-foreground">
                                                            ({fisioterapeutaSelecionado.total_reviews} avaliações)
                                                        </span>
                                                    </div>

                                                    <div className="mt-2 flex flex-wrap gap-1">
                                                        {Array.isArray(fisioterapeutaSelecionado.specializations) &&
                                                            fisioterapeutaSelecionado.specializations.slice(0, 3).map((spec: string) => (
                                                                <Badge key={spec} variant="secondary" className="text-xs">
                                                                    {spec}
                                                                </Badge>
                                                            ))}
                                                    </div>

                                                    <div className="mt-2 flex items-center gap-2 text-sm">
                                                        <Clock className="h-4 w-4 text-muted-foreground" />
                                                        <span className="font-medium">
                                                            R$ {Number(fisioterapeutaSelecionado.hourly_rate || 0).toFixed(2)}/sessão
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="mt-4 flex gap-2">
                                                <Link href={route('paciente.fisioterapeutas.show', fisioterapeutaSelecionado.id)}>
                                                    <Button variant="outline" size="sm">
                                                        Ver Perfil Completo
                                                    </Button>
                                                </Link>
                                                <Button variant="outline" size="sm" onClick={() => setData('fisioterapeuta_id', '')}>
                                                    Escolher Outro
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}

                                {/* Seleção de Fisioterapeuta */}
                                {!fisioterapeutaSelecionado && (
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <Label htmlFor="fisioterapeuta_id">Escolher Fisioterapeuta</Label>
                                            <Link href={route('paciente.fisioterapeutas.index')}>
                                                <Button variant="outline" size="sm">
                                                    <User className="mr-2 h-4 w-4" />
                                                    Buscar Fisioterapeutas
                                                </Button>
                                            </Link>
                                        </div>

                                        <Popover open={fisioterapeutaOpen} onOpenChange={setFisioterapeutaOpen}>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    role="combobox"
                                                    aria-expanded={fisioterapeutaOpen}
                                                    className="w-full justify-between border-input bg-background px-3 font-normal outline-offset-0 outline-none hover:bg-background focus-visible:outline-[3px]"
                                                >
                                                    <span className={cn('truncate', !data.fisioterapeuta_id && 'text-muted-foreground')}>
                                                        {data.fisioterapeuta_id
                                                            ? fisioterapeutas.find((fisio: any) => fisio.id.toString() === data.fisioterapeuta_id)
                                                                  ?.user.name
                                                            : 'Selecione um fisioterapeuta'}
                                                    </span>
                                                    <ChevronDown size={16} className="shrink-0 text-muted-foreground/80" aria-hidden="true" />
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent
                                                className="w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0"
                                                align="start"
                                            >
                                                <Command>
                                                    <CommandInput placeholder="Buscar fisioterapeuta..." />
                                                    <CommandList>
                                                        <CommandEmpty>Nenhum fisioterapeuta encontrado.</CommandEmpty>
                                                        <CommandGroup>
                                                            {fisioterapeutas.map((fisio: any) => (
                                                                <CommandItem
                                                                    key={fisio.id}
                                                                    value={`${fisio.user.name} ${fisio.crefito} ${Array.isArray(fisio.specializations) ? fisio.specializations.join(' ') : ''}`}
                                                                    onSelect={() => {
                                                                        setData('fisioterapeuta_id', fisio.id.toString());
                                                                        setFisioterapeutaOpen(false);
                                                                    }}
                                                                    className="flex items-center gap-3 py-3"
                                                                >
                                                                    <Avatar className="h-8 w-8">
                                                                        <AvatarImage src={fisio.user.avatar} />
                                                                        <AvatarFallback>
                                                                            {fisio.user.name
                                                                                .split(' ')
                                                                                .map((n: string) => n[0])
                                                                                .join('')
                                                                                .toUpperCase()}
                                                                        </AvatarFallback>
                                                                    </Avatar>
                                                                    <div className="flex-1">
                                                                        <div className="font-medium">{fisio.user.name}</div>
                                                                        <div className="text-xs text-muted-foreground">
                                                                            {Array.isArray(fisio.specializations)
                                                                                ? fisio.specializations.slice(0, 2).join(', ')
                                                                                : ''}
                                                                            {Array.isArray(fisio.specializations) &&
                                                                                fisio.specializations.length > 2 &&
                                                                                ` +${fisio.specializations.length - 2}`}
                                                                        </div>
                                                                        <div className="mt-1 flex items-center gap-1">
                                                                            {Array.from({ length: 5 }, (_, i) => (
                                                                                <Star
                                                                                    key={i}
                                                                                    className={`h-3 w-3 ${
                                                                                        i < Math.floor(fisio.rating)
                                                                                            ? 'fill-yellow-400 text-yellow-400'
                                                                                            : 'text-gray-300'
                                                                                    }`}
                                                                                />
                                                                            ))}
                                                                            <span className="ml-1 text-xs text-muted-foreground">
                                                                                R$ {Number(fisio.hourly_rate || 0).toFixed(2)}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    {data.fisioterapeuta_id === fisio.id.toString() && (
                                                                        <CheckCircle size={16} className="ml-auto text-primary" />
                                                                    )}
                                                                </CommandItem>
                                                            ))}
                                                        </CommandGroup>
                                                    </CommandList>
                                                </Command>
                                            </PopoverContent>
                                        </Popover>
                                        {errors.fisioterapeuta_id && <p className="text-sm text-red-600">{errors.fisioterapeuta_id}</p>}
                                    </div>
                                )}

                                {/* Data */}
                                <div className="space-y-2">
                                    <Label htmlFor="data_agendamento">Data do Agendamento</Label>
                                    <DatePicker
                                        value={selectedDate}
                                        onValueChange={handleDateChange}
                                        placeholder="Selecione uma data"
                                        disabledDates={isDateDisabled}
                                    />
                                    {(errors.data_agendamento || validationErrors.data_agendamento) && (
                                        <p className="text-sm text-red-600">{errors.data_agendamento || validationErrors.data_agendamento}</p>
                                    )}
                                </div>

                                {/* Horário */}
                                <div className="space-y-4">
                                    <Label htmlFor="horario">Horário Disponível</Label>

                                    {!data.fisioterapeuta_id || !data.data_agendamento ? (
                                        <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-8">
                                            <div className="text-center text-muted-foreground">
                                                <Clock className="mx-auto mb-2 h-8 w-8" />
                                                <p>Selecione fisioterapeuta e data primeiro</p>
                                            </div>
                                        </div>
                                    ) : loadingHorarios ? (
                                        <div className="flex items-center justify-center p-8">
                                            <div className="text-center">
                                                <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
                                                <p className="text-muted-foreground">Carregando horários disponíveis...</p>
                                            </div>
                                        </div>
                                    ) : horariosDisponiveis.length === 0 ? (
                                        <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-red-300 bg-red-50 p-8">
                                            <div className="text-center text-red-600">
                                                <AlertTriangle className="mx-auto mb-2 h-8 w-8" />
                                                <p className="font-medium">Nenhum horário disponível</p>
                                                <p className="text-sm">Tente selecionar outra data</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="space-y-3">
                                            <p className="text-sm text-muted-foreground">Selecione um dos horários disponíveis:</p>
                                            <div className="grid grid-cols-3 gap-2 sm:grid-cols-4 md:grid-cols-6">
                                                {horariosDisponiveis.map((horario) => (
                                                    <Button
                                                        key={horario}
                                                        type="button"
                                                        variant={data.horario === horario ? 'default' : 'outline'}
                                                        size="sm"
                                                        className="flex h-12 flex-col items-center justify-center"
                                                        onClick={() => setData('horario', horario)}
                                                    >
                                                        <Clock className="mb-1 h-3 w-3" />
                                                        <span className="text-xs font-medium">{horario}</span>
                                                    </Button>
                                                ))}
                                            </div>

                                            {data.horario && (
                                                <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-3">
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                    <span className="text-sm text-green-800">
                                                        Horário selecionado: <strong>{data.horario}</strong>
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {(errors.horario || validationErrors.horario) && (
                                        <p className="text-sm text-red-600">{errors.horario || validationErrors.horario}</p>
                                    )}
                                </div>

                                {/* Observações */}
                                <div className="space-y-2">
                                    <Label htmlFor="observacoes">Observações (opcional)</Label>
                                    <Textarea
                                        id="observacoes"
                                        placeholder="Descreva sintomas, dúvidas ou informações importantes..."
                                        value={data.observacoes}
                                        onChange={(e) => setData('observacoes', e.target.value)}
                                        rows={4}
                                    />
                                    {errors.observacoes && <p className="text-sm text-red-600">{errors.observacoes}</p>}
                                </div>

                                {/* Informações da assinatura */}
                                {assinaturaAtual && (
                                    <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                        <div className="mb-2 flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-blue-600" />
                                            <span className="font-medium text-blue-800">Plano Ativo</span>
                                        </div>
                                        <div className="text-sm text-blue-700">
                                            <p>
                                                <strong>Plano:</strong> {assinaturaAtual.plano?.nome}
                                            </p>
                                            <p>
                                                <strong>Sessões restantes este mês:</strong> {assinaturaAtual.sessoes_restantes}
                                            </p>
                                            {assinaturaAtual.sessoes_restantes <= 3 && assinaturaAtual.sessoes_restantes > 0 && (
                                                <p className="mt-1 font-medium text-amber-600">⚠️ Poucas sessões restantes</p>
                                            )}
                                            {assinaturaAtual.sessoes_restantes <= 0 && (
                                                <p className="mt-1 font-medium text-red-600">❌ Sem sessões restantes neste mês</p>
                                            )}
                                        </div>
                                    </div>
                                )}

                                {/* Erros gerais de validação */}
                                {validationErrors.sessoes && (
                                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                                        <div className="flex items-center gap-2">
                                            <AlertTriangle className="h-4 w-4 text-red-600" />
                                            <span className="text-sm text-red-800">{validationErrors.sessoes}</span>
                                        </div>
                                    </div>
                                )}

                                <div className="flex gap-4">
                                    <Button
                                        type="submit"
                                        disabled={
                                            processing ||
                                            isSubmitting ||
                                            (!isPlanoAvulso && (!assinaturaAtual || assinaturaAtual.sessoes_restantes === 0))
                                        }
                                        className="flex-1"
                                    >
                                        {processing || isSubmitting ? (
                                            <>
                                                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                                Agendando...
                                            </>
                                        ) : (
                                            'Confirmar Agendamento'
                                        )}
                                    </Button>
                                    <Button type="button" variant="outline" asChild>
                                        <Link href={route('paciente.agendamentos.index')}>
                                            <ArrowLeft className="mr-2 h-4 w-4" />
                                            Voltar
                                        </Link>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
