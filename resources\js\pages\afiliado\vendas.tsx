import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Calendar, DollarSign, Filter, Search, TrendingUp, Users } from 'lucide-react';
import { useState } from 'react';

interface VendaAfiliado {
    id: number;
    plano_tipo: string;
    valor_venda: number;
    comissao: number;
    status: 'pendente' | 'confirmada' | 'cancelada';
    created_at: string;
    cliente: {
        id: number;
        name: string;
        email: string;
    };
    assinatura?: {
        plano: {
            name: string;
            price: number;
        };
    };
}

interface Props {
    vendas: {
        data: VendaAfiliado[];
        links: any[];
        meta: any;
    };
    stats: {
        total: number;
        pendentes: number;
        confirmadas: number;
        canceladas: number;
    };
    filters: {
        status?: string;
        periodo?: string;
        search?: string;
    };
}

export default function AfiliadoVendas({ vendas, stats, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [statusFilter, setStatusFilter] = useState(filters?.status || 'all');
    const [periodoFilter, setPeriodoFilter] = useState(filters?.periodo || 'all');

    // Se não há dados, mostrar estado de carregamento
    if (!vendas || !stats || !filters) {
        return (
            <AppLayout
                breadcrumbs={[
                    { title: 'Dashboard', href: '/afiliado/dashboard' },
                    { title: 'Vendas', href: '/afiliado/vendas' },
                ]}
            >
                <Head title="Minhas Vendas" />
                <div className="flex min-h-screen items-center justify-center bg-background">
                    <div className="text-center">
                        <h1 className="mb-4 text-2xl font-bold">Carregando vendas...</h1>
                        <p className="text-muted-foreground">Se o problema persistir, entre em contato com o suporte.</p>
                    </div>
                </div>
            </AppLayout>
        );
    }

    const breadcrumbs = [
        { title: 'Dashboard', href: '/afiliado/dashboard' },
        { title: 'Vendas', href: '/afiliado/vendas' },
    ];

    const handleSearch = () => {
        router.get(
            '/afiliado/vendas',
            {
                search: searchTerm,
                status: statusFilter,
                periodo: periodoFilter,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    const handleFilterChange = (type: string, value: string) => {
        const newFilters = {
            search: searchTerm,
            status: statusFilter,
            periodo: periodoFilter,
            [type]: value,
        };

        if (type === 'status') setStatusFilter(value);
        if (type === 'periodo') setPeriodoFilter(value);

        router.get('/afiliado/vendas', newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: 'secondary',
            confirmada: 'default',
            cancelada: 'destructive',
        } as const;

        return <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>{status}</Badge>;
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Minhas Vendas" />

            <div className="min-h-screen bg-background">
                {/* Header */}
                <section className="bg-gradient-to-b from-background to-muted/30 py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 className="text-3xl font-bold">Minhas Vendas</h1>
                                <p className="text-muted-foreground">Acompanhe suas vendas e comissões em tempo real</p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Estatísticas */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Total de Vendas</CardTitle>
                                    <Users className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats?.total || 0}</div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Confirmadas</CardTitle>
                                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-green-600">{stats?.confirmadas || 0}</div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-yellow-600">{stats?.pendentes || 0}</div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Canceladas</CardTitle>
                                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-red-600">{stats?.canceladas || 0}</div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* Filtros */}
                <section className="py-4">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Filter className="h-5 w-5" />
                                    Filtros
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
                                    <div className="flex-1">
                                        <label className="text-sm font-medium">Buscar</label>
                                        <div className="flex gap-2">
                                            <Input
                                                placeholder="Nome do cliente ou email..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                            />
                                            <Button onClick={handleSearch}>
                                                <Search className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>

                                    <div className="min-w-[150px]">
                                        <label className="text-sm font-medium">Status</label>
                                        <Select value={statusFilter} onValueChange={(value) => handleFilterChange('status', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">Todos</SelectItem>
                                                <SelectItem value="pendente">Pendente</SelectItem>
                                                <SelectItem value="confirmada">Confirmada</SelectItem>
                                                <SelectItem value="cancelada">Cancelada</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="min-w-[150px]">
                                        <label className="text-sm font-medium">Período</label>
                                        <Select value={periodoFilter} onValueChange={(value) => handleFilterChange('periodo', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">Todos</SelectItem>
                                                <SelectItem value="hoje">Hoje</SelectItem>
                                                <SelectItem value="semana">Esta Semana</SelectItem>
                                                <SelectItem value="mes">Este Mês</SelectItem>
                                                <SelectItem value="ano">Este Ano</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Lista de Vendas */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle>Lista de Vendas</CardTitle>
                                <CardDescription>{vendas?.meta?.total || vendas?.data?.length || 0} vendas encontradas</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {vendas.data.length > 0 ? (
                                    <div className="overflow-x-auto">
                                        <table className="w-full">
                                            <thead>
                                                <tr className="border-b">
                                                    <th className="p-2 text-left text-sm font-medium">Data</th>
                                                    <th className="p-2 text-left text-sm font-medium">Cliente</th>
                                                    <th className="p-2 text-left text-sm font-medium">Plano</th>
                                                    <th className="p-2 text-left text-sm font-medium">Valor</th>
                                                    <th className="p-2 text-left text-sm font-medium">Comissão</th>
                                                    <th className="p-2 text-left text-sm font-medium">Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {vendas.data.map((venda) => (
                                                    <tr key={venda.id} className="border-b hover:bg-muted/50">
                                                        <td className="p-2 text-sm">{formatDate(venda.created_at)}</td>
                                                        <td className="p-2">
                                                            <div>
                                                                <div className="font-medium">{venda.cliente?.name || 'N/A'}</div>
                                                                <div className="text-sm text-muted-foreground">{venda.cliente?.email || 'N/A'}</div>
                                                            </div>
                                                        </td>
                                                        <td className="p-2 text-sm capitalize">{venda.plano_tipo}</td>
                                                        <td className="p-2 text-sm font-medium">{formatCurrency(venda.valor_venda)}</td>
                                                        <td className="p-2 text-sm font-medium text-green-600">{formatCurrency(venda.comissao)}</td>
                                                        <td className="p-2">{getStatusBadge(venda.status)}</td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                ) : (
                                    <div className="py-8 text-center">
                                        <Users className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                        <h3 className="mb-2 text-lg font-medium">Nenhuma venda encontrada</h3>
                                        <p className="mb-4 text-muted-foreground">
                                            {filters.search || filters.status !== 'all' || filters.periodo !== 'all'
                                                ? 'Tente ajustar os filtros para encontrar suas vendas.'
                                                : 'Comece a divulgar seu link para gerar suas primeiras vendas!'}
                                        </p>
                                        <Button asChild>
                                            <Link href="/afiliado/materiais">
                                                <TrendingUp className="mr-2 h-4 w-4" />
                                                Ver Materiais
                                            </Link>
                                        </Button>
                                    </div>
                                )}

                                {/* Paginação */}
                                {vendas.links && vendas.links.length > 3 && (
                                    <div className="mt-6 flex justify-center">
                                        <div className="flex gap-2">
                                            {vendas.links.map((link, index) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    size="sm"
                                                    disabled={!link.url}
                                                    onClick={() => link.url && router.get(link.url)}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </section>
            </div>
        </AppLayout>
    );
}
