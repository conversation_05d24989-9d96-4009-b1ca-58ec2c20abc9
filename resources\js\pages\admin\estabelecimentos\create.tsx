import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { safeRoute } from '@/utils/route-helper';
import { Head, router } from '@inertiajs/react';
import { ArrowLeft, Save } from 'lucide-react';
import React, { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface Props {
    usuariosDisponiveis: User[];
}

export default function EstabelecimentoCreate({ usuariosDisponiveis }: Props) {
    const [formData, setFormData] = useState({
        user_id: '',
        nome: '',
        categoria: '',
        descricao: '',
        telefone: '',
        whatsapp: '',
        email: '',
        endereco: '',
        cidade: '',
        estado: '',
        cep: '',
        site: '',
        instagram: '',
        facebook: '',
        ativo: true,
        plano_ativo: false,
    });

    const [loading, setLoading] = useState(false);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);

        router.post(safeRoute('admin.estabelecimentos.store'), formData, {
            onFinish: () => setLoading(false),
            onSuccess: () => {
                router.visit(safeRoute('admin.estabelecimentos.index'));
            },
        });
    };

    const handleInputChange = (field: string, value: string | boolean) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    return (
        <AppLayout>
            <Head title="Novo Estabelecimento - Admin" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Novo Estabelecimento</h1>
                        <p className="text-muted-foreground">Cadastre um novo estabelecimento na plataforma</p>
                    </div>
                    <Button variant="outline" onClick={() => router.visit(safeRoute('admin.estabelecimentos.index'))}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Voltar
                    </Button>
                </div>

                {/* Formulário */}
                <Card>
                    <CardHeader>
                        <CardTitle>Informações do Estabelecimento</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Usuário */}
                            <div className="space-y-2">
                                <Label htmlFor="user_id">Usuário Responsável</Label>
                                <Select value={formData.user_id} onValueChange={(value) => handleInputChange('user_id', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Selecione um usuário" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {usuariosDisponiveis.map((usuario) => (
                                            <SelectItem key={usuario.id} value={usuario.id.toString()}>
                                                {usuario.name} ({usuario.email})
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            {/* Informações Básicas */}
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="nome">Nome do Estabelecimento *</Label>
                                    <Input id="nome" value={formData.nome} onChange={(e) => handleInputChange('nome', e.target.value)} required />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="categoria">Categoria *</Label>
                                    <Select value={formData.categoria} onValueChange={(value) => handleInputChange('categoria', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione uma categoria" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="fisioterapia">Fisioterapia</SelectItem>
                                            <SelectItem value="farmacia">Farmácia</SelectItem>
                                            <SelectItem value="dentista">Dentista</SelectItem>
                                            <SelectItem value="clinica">Clínica</SelectItem>
                                            <SelectItem value="hospital">Hospital</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            {/* Descrição */}
                            <div className="space-y-2">
                                <Label htmlFor="descricao">Descrição</Label>
                                <Textarea
                                    id="descricao"
                                    value={formData.descricao}
                                    onChange={(e) => handleInputChange('descricao', e.target.value)}
                                    rows={3}
                                />
                            </div>

                            {/* Contato */}
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                <div className="space-y-2">
                                    <Label htmlFor="telefone">Telefone</Label>
                                    <Input id="telefone" value={formData.telefone} onChange={(e) => handleInputChange('telefone', e.target.value)} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="whatsapp">WhatsApp</Label>
                                    <Input id="whatsapp" value={formData.whatsapp} onChange={(e) => handleInputChange('whatsapp', e.target.value)} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="email">E-mail</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        value={formData.email}
                                        onChange={(e) => handleInputChange('email', e.target.value)}
                                    />
                                </div>
                            </div>

                            {/* Endereço */}
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="endereco">Endereço</Label>
                                    <Input id="endereco" value={formData.endereco} onChange={(e) => handleInputChange('endereco', e.target.value)} />
                                </div>

                                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                    <div className="space-y-2">
                                        <Label htmlFor="cidade">Cidade</Label>
                                        <Input id="cidade" value={formData.cidade} onChange={(e) => handleInputChange('cidade', e.target.value)} />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="estado">Estado</Label>
                                        <Input id="estado" value={formData.estado} onChange={(e) => handleInputChange('estado', e.target.value)} />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="cep">CEP</Label>
                                        <Input id="cep" value={formData.cep} onChange={(e) => handleInputChange('cep', e.target.value)} />
                                    </div>
                                </div>
                            </div>

                            {/* Redes Sociais */}
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                <div className="space-y-2">
                                    <Label htmlFor="site">Site</Label>
                                    <Input id="site" value={formData.site} onChange={(e) => handleInputChange('site', e.target.value)} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="instagram">Instagram</Label>
                                    <Input
                                        id="instagram"
                                        value={formData.instagram}
                                        onChange={(e) => handleInputChange('instagram', e.target.value)}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="facebook">Facebook</Label>
                                    <Input id="facebook" value={formData.facebook} onChange={(e) => handleInputChange('facebook', e.target.value)} />
                                </div>
                            </div>

                            {/* Botões */}
                            <div className="flex justify-end space-x-4">
                                <Button type="button" variant="outline" onClick={() => router.visit(safeRoute('admin.estabelecimentos.index'))}>
                                    Cancelar
                                </Button>
                                <Button type="submit" disabled={loading}>
                                    <Save className="mr-2 h-4 w-4" />
                                    {loading ? 'Salvando...' : 'Salvar'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
