import { Page, expect } from '@playwright/test';

export interface FisioterapeutaTestData {
    email: string;
    password: string;
    name: string;
    crefito: string;
    specializations: string[];
    bio: string;
    hourly_rate: number;
    available_areas: string[];
}

export const defaultFisioterapeutaData: FisioterapeutaTestData = {
    email: '<EMAIL>',
    password: 'fisio123',
    name: 'Dr. <PERSON>',
    crefito: 'CREFITO-3/123456-F',
    specializations: ['Ortopédica', 'Esportiva'],
    bio: 'Especialista em fisioterapia ortopédica e esportiva com mais de 10 anos de experiência.',
    hourly_rate: 80.0,
    available_areas: ['São Paulo', 'Zona Sul', 'Zona Oeste'],
};

/**
 * Realiza login como fisioterapeuta
 */
export async function loginAsFisioterapeuta(
    page: Page,
    email: string = defaultFisioterapeutaData.email,
    password: string = defaultFisioterapeutaData.password,
) {
    await page.goto('/login');

    // Aguardar a página carregar
    await page.waitForLoadState('networkidle');

    // Preencher formulário de login
    await page.fill('input[name="email"]', email);
    await page.fill('input[name="password"]', password);

    // Clicar no botão de login
    await page.click('button[type="submit"]');

    // Aguardar redirecionamento
    await page.waitForLoadState('networkidle');

    // Verificar se o login foi bem-sucedido
    // Pode ser redirecionado para setup se perfil não estiver completo
    const currentUrl = page.url();
    expect(currentUrl).toMatch(/\/(fisioterapeuta\/(dashboard|setup))/);
}

/**
 * Completa o setup inicial do fisioterapeuta
 */
export async function completeFisioterapeutaSetup(page: Page, data: Partial<FisioterapeutaTestData> = {}) {
    const setupData = { ...defaultFisioterapeutaData, ...data };

    // Navegar para setup se não estiver lá
    if (!page.url().includes('/setup')) {
        await page.goto('/fisioterapeuta/setup');
    }

    await page.waitForLoadState('networkidle');

    // Preencher CREFITO
    await page.fill('input[name="crefito"]', setupData.crefito);

    // Preencher especialidades (assumindo que há um campo de texto ou select)
    for (const specialization of setupData.specializations) {
        // Implementar baseado na interface real
        await page.fill('input[placeholder*="especialização"], input[placeholder*="Especialização"]', specialization);
        await page.keyboard.press('Enter');
    }

    // Preencher bio
    await page.fill('textarea[name="bio"]', setupData.bio);

    // Preencher valor por hora
    await page.fill('input[name="hourly_rate"]', setupData.hourly_rate.toString());

    // Selecionar áreas de atendimento
    for (const area of setupData.available_areas) {
        await page.check(`input[value="${area}"], input[type="checkbox"][name*="${area.toLowerCase()}"]`);
    }

    // Submeter formulário
    await page.click('button[type="submit"]');

    // Aguardar redirecionamento para dashboard
    await page.waitForURL('**/fisioterapeuta/dashboard');
    await page.waitForLoadState('networkidle');
}

/**
 * Verifica se o fisioterapeuta está autenticado e com perfil completo
 */
export async function ensureFisioterapeutaAuthenticated(page: Page, data: Partial<FisioterapeutaTestData> = {}) {
    await loginAsFisioterapeuta(page, data.email, data.password);

    // Se redirecionado para setup, completar
    if (page.url().includes('/setup')) {
        await completeFisioterapeutaSetup(page, data);
    }

    // Verificar se está no dashboard
    await expect(page).toHaveURL(/.*\/fisioterapeuta\/dashboard/);
}

/**
 * Navega para uma página específica do fisioterapeuta
 */
export async function navigateToFisioterapeutaPage(page: Page, pageName: string) {
    const routes = {
        dashboard: '/fisioterapeuta/dashboard',
        perfil: '/fisioterapeuta/perfil',
        agenda: '/fisioterapeuta/agenda',
        pacientes: '/fisioterapeuta/pacientes',
        disponibilidade: '/fisioterapeuta/disponibilidade',
        horarios: '/fisioterapeuta/horarios',
        relatorios: '/fisioterapeuta/relatorios',
        avaliacoes: '/fisioterapeuta/avaliacoes',
        afiliados: '/fisioterapeuta/afiliados',
    };

    const route = routes[pageName as keyof typeof routes];
    if (!route) {
        throw new Error(`Página '${pageName}' não encontrada`);
    }

    await page.goto(route);
    await page.waitForLoadState('networkidle');
}

/**
 * Verifica elementos comuns da interface do fisioterapeuta
 */
export async function verifyFisioterapeutaLayout(page: Page) {
    // Verificar se o menu lateral está presente
    await expect(page.locator('nav, aside, [role="navigation"]')).toBeVisible();

    // Verificar se o header está presente
    await expect(page.locator('header, [role="banner"]')).toBeVisible();

    // Verificar se há um indicador de usuário logado
    await expect(page.locator('text="Dr.", text="Fisioterapeuta", [data-testid="user-menu"]')).toBeVisible();
}

/**
 * Aguarda e verifica se uma notificação/toast apareceu
 */
export async function waitForNotification(page: Page, message?: string) {
    const notification = page.locator('[role="alert"], .toast, .notification, [data-testid="notification"]');
    await expect(notification).toBeVisible();

    if (message) {
        await expect(notification).toContainText(message);
    }

    return notification;
}

/**
 * Preenche um formulário genérico baseado em um objeto de dados
 */
export async function fillForm(page: Page, formData: Record<string, string | number | boolean>) {
    for (const [field, value] of Object.entries(formData)) {
        const input = page.locator(`input[name="${field}"], textarea[name="${field}"], select[name="${field}"]`);

        if ((await input.count()) > 0) {
            const inputType = await input.getAttribute('type');

            if (inputType === 'checkbox' || inputType === 'radio') {
                if (value) {
                    await input.check();
                } else {
                    await input.uncheck();
                }
            } else {
                await input.fill(value.toString());
            }
        }
    }
}

/**
 * Verifica se há erros de validação no formulário
 */
export async function checkFormValidation(page: Page) {
    const errorMessages = page.locator('.error, .invalid, [role="alert"], .text-red-500, .text-destructive');
    return await errorMessages.count();
}

/**
 * Simula upload de arquivo
 */
export async function uploadFile(page: Page, inputSelector: string, filePath: string) {
    const fileInput = page.locator(inputSelector);
    await fileInput.setInputFiles(filePath);
}

/**
 * Aguarda carregamento completo da página
 */
export async function waitForPageLoad(page: Page) {
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000); // Aguarda um pouco mais para animações
}

/**
 * Verifica se a página está responsiva
 */
export async function checkResponsiveness(page: Page) {
    // Testar em diferentes tamanhos de tela
    const viewports = [
        { width: 1920, height: 1080 }, // Desktop
        { width: 1024, height: 768 }, // Tablet
        { width: 375, height: 667 }, // Mobile
    ];

    for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await page.waitForTimeout(500);

        // Verificar se elementos principais estão visíveis
        await expect(page.locator('body')).toBeVisible();
    }

    // Voltar para desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
}
