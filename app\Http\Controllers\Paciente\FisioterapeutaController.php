<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Fisioterapeuta;
use App\Models\User;
use App\Models\Avaliacao;
use App\Models\Agendamento;
use App\Models\Disponibilidade;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class FisioterapeutaController extends Controller
{
    /**
     * Display a listing of physiotherapists.
     */
    public function index(Request $request)
    {
        $query = User::whereHas('fisioterapeuta')
            ->with(['fisioterapeuta'])
            ->where('active', true);

        // Filtro por busca de nome
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Filtro por especialização
        if ($request->filled('specialization')) {
            $query->whereHas('fisioterapeuta', function ($q) use ($request) {
                $q->whereJsonContains('specializations', $request->specialization);
            });
        }

        // Filtro por área de atendimento
        if ($request->filled('area')) {
            $query->whereHas('fisioterapeuta', function ($q) use ($request) {
                $q->whereJsonContains('available_areas', $request->area);
            });
        }

        // Filtro por avaliação mínima
        if ($request->filled('rating')) {
            $query->whereHas('fisioterapeuta', function ($q) use ($request) {
                $q->where('rating', '>=', $request->rating);
            });
        }

        // Filtro por disponibilidade
        if ($request->boolean('available_only')) {
            $query->whereHas('fisioterapeuta', function ($q) {
                $q->where('available', true);
            });
        }

        $fisioterapeutas = $query->paginate(12)->through(function ($user) {
            return [
                'id' => $user->id,
                'user' => [
                    'name' => $user->name,
                    'email' => $user->email,
                    'avatar' => $user->avatar,
                ],
                'crefito' => $user->fisioterapeuta->crefito,
                'specializations' => $user->fisioterapeuta->specializations ?? [],
                'bio' => $user->fisioterapeuta->bio,
                'hourly_rate' => (float) ($user->fisioterapeuta->hourly_rate ?? 0),
                'available_areas' => $user->fisioterapeuta->available_areas ?? [],
                'rating' => (float) ($user->fisioterapeuta->rating ?? 0),
                'total_reviews' => (int) ($user->fisioterapeuta->total_reviews ?? 0),
                'available' => $user->fisioterapeuta->available,
                'next_available_slot' => $this->getNextAvailableSlot($user->id),
            ];
        });

        // Buscar todas as especializações e áreas para os filtros
        $specializations = Fisioterapeuta::whereNotNull('specializations')
            ->get()
            ->pluck('specializations')
            ->flatten()
            ->unique()
            ->values()
            ->toArray();

        $areas = Fisioterapeuta::whereNotNull('available_areas')
            ->get()
            ->pluck('available_areas')
            ->flatten()
            ->unique()
            ->values()
            ->toArray();

        return Inertia::render('paciente/fisioterapeutas/index', [
            'fisioterapeutas' => $fisioterapeutas,
            'filters' => $request->only(['search', 'specialization', 'area', 'rating', 'available_only']),
            'specializations' => $specializations,
            'areas' => $areas,
        ]);
    }

    /**
     * Display the specified physiotherapist.
     */
    public function show(User $fisioterapeuta)
    {
        // Verificar se o usuário é realmente um fisioterapeuta
        if (!$fisioterapeuta->fisioterapeuta) {
            abort(404);
        }

        $fisioterapeuta->load(['fisioterapeuta']);

        // Buscar avaliações
        $avaliacoes = Avaliacao::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->with(['paciente'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($avaliacao) {
                return [
                    'id' => $avaliacao->id,
                    'rating' => $avaliacao->rating,
                    'comment' => $avaliacao->comment,
                    'created_at' => $avaliacao->created_at->toISOString(),
                    'paciente' => [
                        'name' => $avaliacao->paciente->name,
                        'avatar' => $avaliacao->paciente->avatar,
                    ],
                ];
            });

        // Calcular estatísticas
        $stats = [
            'total_sessions' => Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
                ->where('status', 'concluido')
                ->count(),
            'total_patients' => Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
                ->distinct('paciente_id')
                ->count(),
            'years_experience' => Carbon::parse($fisioterapeuta->created_at)->diffInYears(Carbon::now()),
        ];

        // Próximos horários disponíveis
        $nextAvailableSlots = $this->getNextAvailableSlots($fisioterapeuta->id, 10);

        return Inertia::render('paciente/fisioterapeutas/show', [
            'fisioterapeuta' => [
                'id' => $fisioterapeuta->id,
                'user' => [
                    'name' => $fisioterapeuta->name,
                    'email' => $fisioterapeuta->email,
                    'phone' => $fisioterapeuta->phone,
                    'avatar' => $fisioterapeuta->avatar,
                ],
                'crefito' => $fisioterapeuta->fisioterapeuta->crefito,
                'specializations' => $fisioterapeuta->fisioterapeuta->specializations ?? [],
                'bio' => $fisioterapeuta->fisioterapeuta->bio,
                'hourly_rate' => (float) ($fisioterapeuta->fisioterapeuta->hourly_rate ?? 0),
                'available_areas' => $fisioterapeuta->fisioterapeuta->available_areas ?? [],
                'rating' => (float) ($fisioterapeuta->fisioterapeuta->rating ?? 0),
                'total_reviews' => (int) ($fisioterapeuta->fisioterapeuta->total_reviews ?? 0),
                'available' => $fisioterapeuta->fisioterapeuta->available,
                'working_hours' => $fisioterapeuta->fisioterapeuta->working_hours ?? [],
                'next_available_slots' => $nextAvailableSlots,
            ],
            'avaliacoes' => $avaliacoes,
            'stats' => $stats,
        ]);
    }

    /**
     * Get the next available slot for a physiotherapist.
     */
    private function getNextAvailableSlot($fisioterapeutaId)
    {
        $hoje = Carbon::now();
        $proximosDias = 7;

        for ($i = 0; $i < $proximosDias; $i++) {
            $data = $hoje->copy()->addDays($i);
            
            // Pular finais de semana
            if ($data->isWeekend()) {
                continue;
            }

            $disponibilidades = Disponibilidade::where('fisioterapeuta_id', $fisioterapeutaId)
                ->where('tipo', 'disponivel')
                ->where('ativo', true)
                ->where(function ($query) use ($data) {
                    $query->where('data_inicio', '<=', $data)
                        ->where(function ($q) use ($data) {
                            $q->whereNull('data_fim')
                                ->orWhere('data_fim', '>=', $data);
                        });
                })
                ->get();

            foreach ($disponibilidades as $disponibilidade) {
                if ($disponibilidade->recorrente) {
                    $diaSemana = $data->dayOfWeek;
                    if (!in_array($diaSemana, $disponibilidade->dias_semana ?? [])) {
                        continue;
                    }
                }

                // Verificar se há horários livres
                $horaInicio = Carbon::parse($disponibilidade->hora_inicio);
                $horaFim = Carbon::parse($disponibilidade->hora_fim);
                
                while ($horaInicio->lt($horaFim)) {
                    $dataHora = $data->copy()->setTime($horaInicio->hour, $horaInicio->minute);
                    
                    // Verificar se não há agendamento neste horário
                    $agendamentoExistente = Agendamento::where('fisioterapeuta_id', $fisioterapeutaId)
                        ->where('scheduled_at', $dataHora)
                        ->whereNotIn('status', ['cancelado'])
                        ->exists();

                    if (!$agendamentoExistente && $dataHora->gt(Carbon::now())) {
                        return $dataHora->format('d/m/Y H:i');
                    }

                    $horaInicio->addHour();
                }
            }
        }

        return null;
    }

    /**
     * Get multiple next available slots for a physiotherapist.
     */
    private function getNextAvailableSlots($fisioterapeutaId, $limit = 10)
    {
        $slots = [];
        $hoje = Carbon::now();
        $proximosDias = 14;

        for ($i = 0; $i < $proximosDias && count($slots) < $limit; $i++) {
            $data = $hoje->copy()->addDays($i);
            
            // Pular finais de semana
            if ($data->isWeekend()) {
                continue;
            }

            $disponibilidades = Disponibilidade::where('fisioterapeuta_id', $fisioterapeutaId)
                ->where('tipo', 'disponivel')
                ->where('ativo', true)
                ->where(function ($query) use ($data) {
                    $query->where('data_inicio', '<=', $data)
                        ->where(function ($q) use ($data) {
                            $q->whereNull('data_fim')
                                ->orWhere('data_fim', '>=', $data);
                        });
                })
                ->get();

            foreach ($disponibilidades as $disponibilidade) {
                if (count($slots) >= $limit) {
                    break;
                }

                if ($disponibilidade->recorrente) {
                    $diaSemana = $data->dayOfWeek;
                    if (!in_array($diaSemana, $disponibilidade->dias_semana ?? [])) {
                        continue;
                    }
                }

                // Verificar se há horários livres
                $horaInicio = Carbon::parse($disponibilidade->hora_inicio);
                $horaFim = Carbon::parse($disponibilidade->hora_fim);
                
                while ($horaInicio->lt($horaFim) && count($slots) < $limit) {
                    $dataHora = $data->copy()->setTime($horaInicio->hour, $horaInicio->minute);
                    
                    // Verificar se não há agendamento neste horário
                    $agendamentoExistente = Agendamento::where('fisioterapeuta_id', $fisioterapeutaId)
                        ->where('scheduled_at', $dataHora)
                        ->whereNotIn('status', ['cancelado'])
                        ->exists();

                    if (!$agendamentoExistente && $dataHora->gt(Carbon::now())) {
                        $slots[] = $dataHora->format('d/m/Y H:i');
                    }

                    $horaInicio->addHour();
                }
            }
        }

        return $slots;
    }
}
