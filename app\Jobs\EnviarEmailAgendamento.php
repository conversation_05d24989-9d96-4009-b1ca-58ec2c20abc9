<?php

namespace App\Jobs;

use App\Models\Agendamento;
use App\Models\User;
use App\Notifications\AgendamentoNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class EnviarEmailAgendamento implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Agendamento $agendamento,
        public string $tipo,
        public ?int $userId = null,
        public string $motivo = ''
    ) {}

    public function handle(): void
    {
        try {
            // Determinar quem deve receber a notificação
            $user = $this->userId 
                ? User::find($this->userId)
                : $this->determinarDestinatario();

            if (!$user) {
                Log::warning('Usuário não encontrado para envio de email', [
                    'agendamento_id' => $this->agendamento->id,
                    'tipo' => $this->tipo,
                    'user_id' => $this->userId
                ]);
                return;
            }

            // Enviar notificação
            $user->notify(new AgendamentoNotification(
                $this->agendamento,
                $this->tipo,
                $this->motivo
            ));

            Log::info('Email de agendamento enviado com sucesso', [
                'agendamento_id' => $this->agendamento->id,
                'tipo' => $this->tipo,
                'user_id' => $user->id,
                'user_email' => $user->email
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao enviar email de agendamento', [
                'agendamento_id' => $this->agendamento->id,
                'tipo' => $this->tipo,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    private function determinarDestinatario(): ?User
    {
        return match($this->tipo) {
            'novo' => $this->agendamento->fisioterapeuta,
            'confirmado', 'cancelado', 'lembrete' => $this->agendamento->paciente,
            default => null
        };
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Job de envio de email falhou', [
            'agendamento_id' => $this->agendamento->id,
            'tipo' => $this->tipo,
            'error' => $exception->getMessage()
        ]);
    }
}
