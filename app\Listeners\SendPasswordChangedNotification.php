<?php

namespace App\Listeners;

use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendPasswordChangedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(PasswordReset $event): void
    {
        // Enviar email de confirmação de alteração de senha
        Mail::send('emails.password-changed', [], function ($message) use ($event) {
            $message->to($event->user->email)
                    ->subject('Senha Alterada - F4 Fisio')
                    ->from(config('mail.from.address', '<EMAIL>'), 'F4 Fisio');
        });
    }
}
