<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Mensagem extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'mensagens';

    protected $fillable = [
        'remetente_id',
        'destinatario_id',
        'agendamento_id',
        'conteudo',
        'tipo',
        'lida',
        'lida_em',
        'anexos',
    ];

    protected $casts = [
        'lida' => 'boolean',
        'lida_em' => 'datetime',
        'anexos' => 'array',
    ];

    // Relacionamentos
    public function remetente()
    {
        return $this->belongsTo(User::class, 'remetente_id');
    }

    public function destinatario()
    {
        return $this->belongsTo(User::class, 'destinatario_id');
    }

    public function agendamento()
    {
        return $this->belongsTo(Agendamento::class);
    }

    // Scopes
    public function scopeNaoLidas($query)
    {
        return $query->where('lida', false);
    }

    public function scopeLidas($query)
    {
        return $query->where('lida', true);
    }

    public function scopeEntre($query, $usuario1Id, $usuario2Id)
    {
        return $query->where(function ($q) use ($usuario1Id, $usuario2Id) {
            $q->where('remetente_id', $usuario1Id)
              ->where('destinatario_id', $usuario2Id);
        })->orWhere(function ($q) use ($usuario1Id, $usuario2Id) {
            $q->where('remetente_id', $usuario2Id)
              ->where('destinatario_id', $usuario1Id);
        });
    }

    public function scopePorTipo($query, $tipo)
    {
        return $query->where('tipo', $tipo);
    }

    // Métodos auxiliares
    public function marcarComoLida()
    {
        if (!$this->lida) {
            $this->update([
                'lida' => true,
                'lida_em' => now(),
            ]);
        }
    }

    public function isRemetente($userId)
    {
        return $this->remetente_id == $userId;
    }

    public function isDestinatario($userId)
    {
        return $this->destinatario_id == $userId;
    }

    public function podeVerMensagem($userId)
    {
        return $this->isRemetente($userId) || $this->isDestinatario($userId);
    }

    // Constantes para tipos de mensagem
    const TIPO_TEXTO = 'texto';
    const TIPO_IMAGEM = 'imagem';
    const TIPO_ARQUIVO = 'arquivo';
    const TIPO_SISTEMA = 'sistema';

    public static function getTipos()
    {
        return [
            self::TIPO_TEXTO => 'Texto',
            self::TIPO_IMAGEM => 'Imagem',
            self::TIPO_ARQUIVO => 'Arquivo',
            self::TIPO_SISTEMA => 'Sistema',
        ];
    }

    // Métodos estáticos para criação
    public static function enviarMensagem($remetenteId, $destinatarioId, $conteudo, $tipo = self::TIPO_TEXTO, $agendamentoId = null)
    {
        return self::create([
            'remetente_id' => $remetenteId,
            'destinatario_id' => $destinatarioId,
            'agendamento_id' => $agendamentoId,
            'conteudo' => $conteudo,
            'tipo' => $tipo,
            'lida' => false,
        ]);
    }

    public static function enviarMensagemSistema($destinatarioId, $conteudo, $agendamentoId = null)
    {
        return self::create([
            'remetente_id' => null, // Sistema
            'destinatario_id' => $destinatarioId,
            'agendamento_id' => $agendamentoId,
            'conteudo' => $conteudo,
            'tipo' => self::TIPO_SISTEMA,
            'lida' => false,
        ]);
    }

    // Buscar conversas de um usuário
    public static function getConversas($userId)
    {
        return self::select('mensagens.*')
            ->where(function ($query) use ($userId) {
                $query->where('remetente_id', $userId)
                      ->orWhere('destinatario_id', $userId);
            })
            ->with(['remetente', 'destinatario', 'agendamento'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->groupBy(function ($mensagem) use ($userId) {
                // Agrupar por conversa (outro usuário)
                return $mensagem->remetente_id == $userId 
                    ? $mensagem->destinatario_id 
                    : $mensagem->remetente_id;
            })
            ->map(function ($mensagens) {
                return $mensagens->first(); // Última mensagem de cada conversa
            });
    }

    // Contar mensagens não lidas
    public static function contarNaoLidas($userId)
    {
        return self::where('destinatario_id', $userId)
            ->naoLidas()
            ->count();
    }
}
