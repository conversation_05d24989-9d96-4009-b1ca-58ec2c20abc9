import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { AlertTriangle, CheckCircle, Clock, XCircle, Zap } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface Pagamento {
    id: number;
    transaction_id: string;
    amount: number;
    formatted_amount: string;
    status: string;
    created_at: string;
    user: {
        name: string;
        email: string;
    };
    plano: {
        name: string;
    };
}

interface Props {
    pagamentos: Pagamento[];
}

export default function MercadoPagoSimulator({ pagamentos }: Props) {
    const [processingId, setProcessingId] = useState<string | null>(null);

    const { post } = useForm();

    const handleWebhookSimulation = async (transactionId: string, action: 'approve' | 'reject' | 'cancel') => {
        setProcessingId(transactionId);

        try {
            const response = await fetch(route('admin.mercadopago.simulator.webhook'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    transaction_id: transactionId,
                    action: action,
                }),
            });

            const result = await response.json();

            if (result.success) {
                toast.success(`Webhook simulado com sucesso: ${result.message}`);
                // Recarregar a página para atualizar os dados
                window.location.reload();
            } else {
                toast.error(`Erro na simulação: ${result.message}`);
            }
        } catch (error) {
            toast.error('Erro ao simular webhook');
            console.error('Erro:', error);
        } finally {
            setProcessingId(null);
        }
    };

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            pendente: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pendente' },
            pago: { color: 'bg-green-100 text-green-800', icon: CheckCircle, text: 'Pago' },
            falhou: { color: 'bg-red-100 text-red-800', icon: XCircle, text: 'Falhou' },
            cancelado: { color: 'bg-gray-100 text-gray-800', icon: XCircle, text: 'Cancelado' },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pendente;
        const Icon = config.icon;

        return (
            <Badge className={config.color}>
                <Icon className="mr-1 h-3 w-3" />
                {config.text}
            </Badge>
        );
    };

    return (
        <AppLayout>
            <Head title="Simulador Mercado Pago - Admin" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Simulador Mercado Pago</h1>
                        <p className="mt-1 text-gray-600">Simule webhooks de pagamento para desenvolvimento</p>
                    </div>

                    <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5 text-orange-500" />
                        <span className="text-sm font-medium text-orange-600">Ambiente de Desenvolvimento</span>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Zap className="mr-2 h-5 w-5 text-blue-600" />
                            Pagamentos Pendentes
                        </CardTitle>
                        <CardDescription>Pagamentos simulados aguardando processamento via webhook</CardDescription>
                    </CardHeader>

                    <CardContent>
                        {pagamentos.length === 0 ? (
                            <div className="py-8 text-center">
                                <Clock className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                <h3 className="mb-2 text-lg font-medium text-gray-900">Nenhum pagamento pendente</h3>
                                <p className="text-gray-600">Não há pagamentos simulados aguardando processamento.</p>
                            </div>
                        ) : (
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Transaction ID</TableHead>
                                            <TableHead>Cliente</TableHead>
                                            <TableHead>Plano</TableHead>
                                            <TableHead>Valor</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>Data</TableHead>
                                            <TableHead>Ações</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {pagamentos.map((pagamento) => (
                                            <TableRow key={pagamento.id}>
                                                <TableCell className="font-mono text-sm">{pagamento.transaction_id}</TableCell>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{pagamento.user.name}</div>
                                                        <div className="text-sm text-gray-500">{pagamento.user.email}</div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>{pagamento.plano.name}</TableCell>
                                                <TableCell className="font-medium">{pagamento.formatted_amount}</TableCell>
                                                <TableCell>{getStatusBadge(pagamento.status)}</TableCell>
                                                <TableCell className="text-sm text-gray-500">{pagamento.created_at}</TableCell>
                                                <TableCell>
                                                    <div className="flex space-x-2">
                                                        <Button
                                                            size="sm"
                                                            onClick={() => handleWebhookSimulation(pagamento.transaction_id, 'approve')}
                                                            disabled={processingId === pagamento.transaction_id}
                                                            className="bg-green-600 text-white hover:bg-green-700"
                                                        >
                                                            <CheckCircle className="mr-1 h-3 w-3" />
                                                            {processingId === pagamento.transaction_id ? 'Processando...' : 'Aprovar'}
                                                        </Button>

                                                        <Button
                                                            size="sm"
                                                            variant="destructive"
                                                            onClick={() => handleWebhookSimulation(pagamento.transaction_id, 'reject')}
                                                            disabled={processingId === pagamento.transaction_id}
                                                        >
                                                            <XCircle className="mr-1 h-3 w-3" />
                                                            Rejeitar
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        )}
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Como Funciona</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4 text-sm text-gray-600">
                            <div className="flex items-start space-x-3">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-xs font-bold text-blue-600">
                                    1
                                </div>
                                <div>
                                    <p className="font-medium text-gray-900">Usuário inicia pagamento</p>
                                    <p>O sistema detecta afiliado via cookies e cria venda temporária</p>
                                </div>
                            </div>
                            <div className="flex items-start space-x-3">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-xs font-bold text-blue-600">
                                    2
                                </div>
                                <div>
                                    <p className="font-medium text-gray-900">Simulação do Mercado Pago</p>
                                    <p>Link de pagamento simulado é gerado com transaction_id único</p>
                                </div>
                            </div>
                            <div className="flex items-start space-x-3">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-xs font-bold text-blue-600">
                                    3
                                </div>
                                <div>
                                    <p className="font-medium text-gray-900">Webhook simulado</p>
                                    <p>Ao aprovar aqui, webhook é disparado e comissão é confirmada</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
