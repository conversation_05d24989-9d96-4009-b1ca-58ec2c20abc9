import { expect, test } from '@playwright/test';
import {
    checkElementExists,
    loginAsAdmin,
    navigateToAdminPage,
    testExportFunctionality,
    testPagination,
    testSearchFunctionality,
    verifyAdminPermissions,
    waitForPageLoad,
} from './helpers/admin-helpers';

test.describe('Sistema Admin - Testes Completos', () => {
    test.beforeEach(async ({ page }) => {
        // Login as admin before each test
        await loginAsAdmin(page);
    });

    test('1. Autenticação e Permissões Admin', async ({ page }) => {
        console.log('🔐 Testando autenticação e permissões administrativas...');

        // Verify admin permissions
        const hasPermissions = await verifyAdminPermissions(page);
        expect(hasPermissions).toBeTruthy();

        // Check admin sidebar navigation
        const adminNavItems = [
            'text="Painel"',
            'text="Usuários"',
            'text="Fisioterapeutas"',
            'text="Planos"',
            'text="Estabelecimentos"',
            'text="Pagamentos"',
            'text="Relatórios"',
        ];

        for (const navItem of adminNavItems) {
            await checkElementExists(page, navItem, `Item de navegação: ${navItem}`);
        }

        console.log('✅ Autenticação e permissões admin verificadas');
    });

    test('2. Dashboard Administrativo', async ({ page }) => {
        console.log('📊 Testando dashboard administrativo...');

        await navigateToAdminPage(page, 'dashboard');

        // Check dashboard title
        await checkElementExists(page, 'heading:has-text("Dashboard Administrativo")', 'Título do dashboard');

        // Check statistics cards - using more flexible selectors
        const statsSelectors = ['text="Total de Pacientes"', 'text="Fisioterapeutas"', 'text="Estabelecimentos"', 'text="Receita Mensal"'];

        for (const selector of statsSelectors) {
            await checkElementExists(page, selector, `Estatística: ${selector}`);
        }

        // Check quick actions - using link selectors instead of text
        const quickActionLinks = [
            'a[href="/admin/usuarios"]',
            'a[href="/admin/fisioterapeutas"]',
            'a[href="/admin/estabelecimentos"]',
            'a[href="/admin/pagamentos"]',
        ];

        for (const link of quickActionLinks) {
            await checkElementExists(page, link, `Link de ação rápida: ${link}`);
        }

        // Test navigation from quick actions - use first() to avoid strict mode violation
        const usersButton = page.locator('a[href="/admin/usuarios"]').first();
        if ((await usersButton.count()) > 0) {
            await usersButton.click();
            await waitForPageLoad(page);
            expect(page.url()).toContain('/admin/usuarios');
            console.log('✅ Navegação por ações rápidas funcionando');

            // Go back to dashboard
            await navigateToAdminPage(page, 'dashboard');
        }

        console.log('✅ Dashboard administrativo testado');
    });

    test('3. Gerenciamento de Usuários', async ({ page }) => {
        console.log('👥 Testando gerenciamento de usuários...');

        await navigateToAdminPage(page, 'usuarios');

        // Test basic page elements
        await checkElementExists(page, 'heading:has-text("Usuários")', 'Título da página de usuários');

        // Test user list
        const userListExists = await checkElementExists(page, 'table, .user-card, .user-row, [data-testid="users-list"]', 'Lista de usuários');

        // Test search functionality
        await testSearchFunctionality(page, 'usuários', 'admin');

        // Test pagination
        await testPagination(page, 'usuários');

        // Test export functionality
        await testExportFunctionality(page, 'usuários');

        // Test filters
        const filterSelectors = ['select[name="role"]', 'select[name="active"]', 'input[name="search"]'];

        for (const selector of filterSelectors) {
            await checkElementExists(page, selector, `Filtro: ${selector}`);
        }

        // Test create user button
        const createButton = page.locator('a[href*="create"], button:has-text("Novo"), button:has-text("Criar")');
        if ((await createButton.count()) > 0) {
            await createButton.first().click();
            await waitForPageLoad(page);

            await checkElementExists(page, 'form', 'Formulário de criação de usuário');
            await checkElementExists(page, 'input[name="name"]', 'Campo nome');
            await checkElementExists(page, 'input[name="email"]', 'Campo email');
            await checkElementExists(page, 'select[name="role"]', 'Campo role');

            console.log('✅ Formulário de criação de usuário acessível');
        }

        console.log('✅ Gerenciamento de usuários testado');
    });

    test('4. Gerenciamento de Fisioterapeutas', async ({ page }) => {
        console.log('🏥 Testando gerenciamento de fisioterapeutas...');

        await navigateToAdminPage(page, 'fisioterapeutas');

        // Test basic page elements
        await checkElementExists(page, 'heading:has-text("Fisioterapeutas")', 'Título da página de fisioterapeutas');

        // Test fisioterapeuta list
        await checkElementExists(
            page,
            'table, .fisioterapeuta-card, .fisioterapeuta-row, [data-testid="fisioterapeutas-list"]',
            'Lista de fisioterapeutas',
        );

        // Test search and filters
        await testSearchFunctionality(page, 'fisioterapeutas');

        // Test create fisioterapeuta
        const createButton = page.locator('a[href*="create"], button:has-text("Novo"), button:has-text("Criar")');
        if ((await createButton.count()) > 0) {
            await createButton.first().click();
            await waitForPageLoad(page);

            // Check form fields
            const formFields = ['select[name="user_id"]', 'input[name="crefito"]', 'textarea[name="bio"]', 'input[name="hourly_rate"]'];

            for (const field of formFields) {
                await checkElementExists(page, field, `Campo do formulário: ${field}`);
            }

            console.log('✅ Formulário de criação de fisioterapeuta acessível');
        }

        console.log('✅ Gerenciamento de fisioterapeutas testado');
    });

    test('5. Gerenciamento de Estabelecimentos', async ({ page }) => {
        console.log('🏢 Testando gerenciamento de estabelecimentos...');

        await navigateToAdminPage(page, 'estabelecimentos');

        // Test basic page elements
        await checkElementExists(page, 'h1:has-text("Estabelecimentos")', 'Título da página de estabelecimentos');

        // Test estabelecimento list
        await checkElementExists(page, '.estabelecimento-card, .estabelecimento-row, table tbody tr', 'Lista de estabelecimentos');

        // Test search functionality
        await testSearchFunctionality(page, 'estabelecimentos');

        // Test pagination
        await testPagination(page, 'estabelecimentos');

        // Test create estabelecimento
        const createButton = page.locator('a[href*="create"], button:has-text("Novo"), button:has-text("Criar")');
        if ((await createButton.count()) > 0) {
            await createButton.first().click();
            await waitForPageLoad(page);

            await checkElementExists(page, 'form', 'Formulário de criação de estabelecimento');
            console.log('✅ Formulário de criação de estabelecimento acessível');
        }

        console.log('✅ Gerenciamento de estabelecimentos testado');
    });

    test('6. Sistema de Pagamentos', async ({ page }) => {
        console.log('💳 Testando sistema de pagamentos...');

        await navigateToAdminPage(page, 'pagamentos');

        // Test basic page elements
        await checkElementExists(page, 'h1:has-text("Pagamentos")', 'Título da página de pagamentos');

        // Test payment list
        await checkElementExists(page, '.payment-card, .payment-row, table tbody tr', 'Lista de pagamentos');

        // Test filters
        const filterSelectors = ['select[name="status"]', 'input[name="date_from"]', 'input[name="date_to"]'];

        for (const selector of filterSelectors) {
            await checkElementExists(page, selector, `Filtro de pagamentos: ${selector}`);
        }

        // Test export functionality
        await testExportFunctionality(page, 'pagamentos');

        console.log('✅ Sistema de pagamentos testado');
    });

    test('7. Sistema de Afiliados', async ({ page }) => {
        console.log('🤝 Testando sistema de afiliados...');

        await navigateToAdminPage(page, 'afiliados');

        // Test basic page elements
        await checkElementExists(page, 'h1:has-text("Afiliados")', 'Título da página de afiliados');

        // Test affiliate list
        await checkElementExists(page, '.afiliado-card, .afiliado-row, table tbody tr', 'Lista de afiliados');

        // Test search functionality
        await testSearchFunctionality(page, 'afiliados');

        // Test status filters
        const statusButtons = [
            'button:has-text("Pendente")',
            'button:has-text("Aprovado")',
            'button:has-text("Rejeitado")',
            'button:has-text("Suspenso")',
        ];

        for (const button of statusButtons) {
            await checkElementExists(page, button, `Filtro de status: ${button}`);
        }

        console.log('✅ Sistema de afiliados testado');
    });

    test('8. Sistema de Relatórios', async ({ page }) => {
        console.log('📈 Testando sistema de relatórios...');

        await navigateToAdminPage(page, 'relatorios');

        // Test basic page elements
        await checkElementExists(page, 'h1:has-text("Relatórios")', 'Título da página de relatórios');

        // Test report types
        const reportTypes = ['text="Financeiro"', 'text="Operacional"', 'text="Pacientes"', 'text="Fisioterapeutas"'];

        for (const reportType of reportTypes) {
            await checkElementExists(page, reportType, `Tipo de relatório: ${reportType}`);
        }

        // Test date filters
        await checkElementExists(page, 'input[type="date"], input[name*="data"]', 'Filtros de data');

        console.log('✅ Sistema de relatórios testado');
    });

    test('9. Sistema de Backup', async ({ page }) => {
        console.log('💾 Testando sistema de backup...');

        await navigateToAdminPage(page, 'backup');

        // Test basic page elements
        await checkElementExists(page, 'h1:has-text("Backup")', 'Título da página de backup');

        // Test backup actions
        const backupActions = ['button:has-text("Criar Backup")', 'button:has-text("Configurar")', 'button:has-text("Limpar")'];

        for (const action of backupActions) {
            await checkElementExists(page, action, `Ação de backup: ${action}`);
        }

        console.log('✅ Sistema de backup testado');
    });

    test('10. Navegação e Responsividade', async ({ page }) => {
        console.log('📱 Testando navegação e responsividade...');

        // Test mobile navigation
        await page.setViewportSize({ width: 375, height: 667 });
        await navigateToAdminPage(page, 'dashboard');

        // Check if mobile menu exists
        await checkElementExists(page, 'button[aria-label*="menu"], .mobile-menu-button', 'Menu mobile');

        // Test desktop navigation
        await page.setViewportSize({ width: 1920, height: 1080 });
        await navigateToAdminPage(page, 'dashboard');

        // Test navigation between pages
        const adminPages = ['usuarios', 'fisioterapeutas', 'estabelecimentos', 'pagamentos', 'relatorios'];

        for (const adminPage of adminPages) {
            await navigateToAdminPage(page, adminPage);
            await waitForPageLoad(page);
            expect(page.url()).toContain(`/admin/${adminPage}`);
        }

        console.log('✅ Navegação e responsividade testadas');
    });
});
