<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EstabelecimentoImagem extends Model
{
    use HasFactory;

    protected $table = 'estabelecimento_imagens';

    protected $fillable = [
        'estabelecimento_id',
        'nome_arquivo',
        'nome_original',
        'tipo_mime',
        'tamanho',
        'descricao',
        'principal',
        'ordem',
    ];

    protected $casts = [
        'principal' => 'boolean',
        'tamanho' => 'integer',
        'ordem' => 'integer',
    ];

    public function estabelecimento(): BelongsTo
    {
        return $this->belongsTo(Estabelecimento::class);
    }

    public function getUrlAttribute(): string
    {
        // Para desenvolvimento, usar URLs de exemplo do Unsplash baseadas no nome do arquivo
        if (str_starts_with($this->nome_arquivo, 'exemplo_')) {
            $estabelecimento = $this->estabelecimento;
            $imagensExemplo = [
                'dentista' => [
                    'https://images.unsplash.com/photo-1629909613654-28e377c37b09?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1606811841689-23dfddce3e95?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=800&h=600&fit=crop'
                ],
                'farmacia' => [
                    'https://images.unsplash.com/photo-1576602976047-174e57a47881?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1585435557343-3b092031d4c1?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1631549916768-4119b2e5f926?w=800&h=600&fit=crop'
                ],
                'fisioterapia' => [
                    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=800&h=600&fit=crop'
                ]
            ];

            $categoria = $estabelecimento->categoria;
            if (isset($imagensExemplo[$categoria])) {
                return $imagensExemplo[$categoria][$this->ordem] ?? $imagensExemplo[$categoria][0];
            }
        }

        return asset('storage/estabelecimentos/' . $this->nome_arquivo);
    }
}
