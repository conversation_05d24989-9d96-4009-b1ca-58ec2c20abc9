<?php

namespace App\Http\Controllers\Fisioterapeuta;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Models\User;
use App\Models\Avaliacao;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        $fisioterapeuta = auth()->user()->load('fisioterapeuta');
        $hoje = Carbon::today();
        $inicioMes = Carbon::now()->startOfMonth();
        $fimMes = Carbon::now()->endOfMonth();

        // Estatísticas gerais
        $stats = [
            'agendamentosHoje' => Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
                ->whereDate('scheduled_at', $hoje)
                ->whereIn('status', ['agendado', 'confirmado', 'em_andamento'])
                ->count(),
            
            'sessoesMes' => Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
                ->whereBetween('scheduled_at', [$inicioMes, $fimMes])
                ->where('status', 'concluido')
                ->count(),
            
            'pacientesAtivos' => Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
                ->where('scheduled_at', '>=', Carbon::now()->subDays(30))
                ->distinct('paciente_id')
                ->count(),
            
            'avaliacaoMedia' => (float) ($fisioterapeuta->fisioterapeuta?->rating ?? 0.0),
            
            'receitaMes' => Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
                ->whereBetween('scheduled_at', [$inicioMes, $fimMes])
                ->where('status', 'concluido')
                ->sum('price') ?? 0,
        ];

        // Próximos agendamentos (hoje e próximos 7 dias)
        $proximosAgendamentos = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->with(['paciente', 'assinatura.plano'])
            ->whereBetween('scheduled_at', [
                Carbon::now(),
                Carbon::now()->addDays(7)
            ])
            ->whereIn('status', ['agendado', 'confirmado'])
            ->orderBy('scheduled_at')
            ->limit(10)
            ->get()
            ->map(function ($agendamento) {
                return [
                    'id' => $agendamento->id,
                    'data_hora' => $agendamento->scheduled_at,
                    'status' => $agendamento->status,
                    'tipo' => $agendamento->service_type,
                    'duracao' => $agendamento->duration,
                    'observacoes' => $agendamento->notes,
                    'paciente' => [
                        'id' => $agendamento->paciente->id,
                        'name' => $agendamento->paciente->name,
                        'phone' => $agendamento->paciente->phone,
                    ],
                    'endereco' => $agendamento->address,
                ];
            });

        // Agendamentos de hoje
        $agendamentosHoje = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->with(['paciente'])
            ->whereDate('scheduled_at', $hoje)
            ->orderBy('scheduled_at')
            ->get()
            ->map(function ($agendamento) {
                return [
                    'id' => $agendamento->id,
                    'horario' => $agendamento->scheduled_at->format('H:i'),
                    'status' => $agendamento->status,
                    'tipo' => $agendamento->service_type,
                    'paciente' => [
                        'name' => $agendamento->paciente->name,
                        'phone' => $agendamento->paciente->phone,
                    ],
                    'endereco' => $agendamento->address,
                ];
            });

        // Pacientes recentes (últimos atendimentos)
        $pacientesRecentes = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->with(['paciente'])
            ->where('status', 'concluido')
            ->orderBy('finished_at', 'desc')
            ->limit(5)
            ->get()
            ->unique('paciente_id')
            ->map(function ($agendamento) {
                return [
                    'id' => $agendamento->paciente->id,
                    'name' => $agendamento->paciente->name,
                    'email' => $agendamento->paciente->email,
                    'phone' => $agendamento->paciente->phone,
                    'ultima_sessao' => $agendamento->finished_at,
                    'total_sessoes' => Agendamento::where('fisioterapeuta_id', auth()->id())
                        ->where('paciente_id', $agendamento->paciente_id)
                        ->where('status', 'concluido')
                        ->count(),
                ];
            });

        // Avaliações recentes
        $avaliacoesRecentes = Avaliacao::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->with(['paciente', 'agendamento'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($avaliacao) {
                return [
                    'id' => $avaliacao->id,
                    'rating' => $avaliacao->rating,
                    'comentario' => $avaliacao->comment,
                    'recomenda' => $avaliacao->recommend,
                    'data' => $avaliacao->created_at,
                    'paciente' => [
                        'name' => $avaliacao->paciente->name,
                    ],
                ];
            });

        // Estatísticas da semana (para gráfico)
        $estatisticasSemana = [];
        for ($i = 6; $i >= 0; $i--) {
            $data = Carbon::now()->subDays($i);
            $sessoes = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
                ->whereDate('scheduled_at', $data)
                ->where('status', 'concluido')
                ->count();
            
            $estatisticasSemana[] = [
                'data' => $data->format('Y-m-d'),
                'dia' => $data->format('D'),
                'sessoes' => $sessoes,
            ];
        }

        // Alertas e notificações
        $alertas = [];
        
        // Verificar agendamentos sem confirmação
        $agendamentosPendentes = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->where('status', 'agendado')
            ->whereDate('scheduled_at', '<=', Carbon::tomorrow())
            ->count();
        
        if ($agendamentosPendentes > 0) {
            $alertas[] = [
                'tipo' => 'warning',
                'titulo' => 'Agendamentos Pendentes',
                'mensagem' => "Você tem {$agendamentosPendentes} agendamento(s) que precisam ser confirmados.",
                'acao' => route('fisioterapeuta.agenda.index'),
            ];
        }

        // Verificar relatórios pendentes
        $relatoriosPendentes = Agendamento::where('fisioterapeuta_id', $fisioterapeuta->id)
            ->where('status', 'concluido')
            ->whereDoesntHave('relatorioSessao')
            ->whereDate('finished_at', '>=', Carbon::now()->subDays(7))
            ->count();
        
        if ($relatoriosPendentes > 0) {
            $alertas[] = [
                'tipo' => 'info',
                'titulo' => 'Relatórios Pendentes',
                'mensagem' => "Você tem {$relatoriosPendentes} relatório(s) de sessão para preencher.",
                'acao' => route('fisioterapeuta.relatorios.pendentes'),
            ];
        }

        return Inertia::render('fisioterapeuta/dashboard', [
            'stats' => $stats,
            'proximosAgendamentos' => $proximosAgendamentos,
            'agendamentosHoje' => $agendamentosHoje,
            'pacientesRecentes' => $pacientesRecentes,
            'avaliacoesRecentes' => $avaliacoesRecentes,
            'estatisticasSemana' => $estatisticasSemana,
            'alertas' => $alertas,
        ]);
    }
}
