<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        // Só aplica para pacientes
        if ($user && $user->role === 'paciente' && !$user->has_subscription) {
            // Permite acesso às rotas de seleção de plano, logout e programa de afiliados
            $allowedRoutes = [
                'paciente.planos',
                'paciente.planos.store',
                'paciente.afiliados',
                'logout',
                'profile.edit',
                'profile.update',
                'profile.destroy'
            ];

            if (!in_array($request->route()->getName(), $allowedRoutes)) {
                return redirect()->route('paciente.planos')
                    ->with('warning', 'Você precisa escolher um plano para continuar.');
            }
        }

        return $next($request);
    }
}
