<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class Cupom extends Model
{
    use HasFactory;

    protected $table = 'cupons';

    protected $fillable = [
        'codigo',
        'tipo',
        'afiliado_id',
        'nome',
        'descricao',
        'tipo_desconto',
        'valor_desconto',
        'valor_minimo_pedido',
        'data_inicio',
        'data_fim',
        'limite_uso',
        'usos_realizados',
        'ativo',
        'created_by',
    ];

    protected $casts = [
        'valor_desconto' => 'decimal:2',
        'valor_minimo_pedido' => 'decimal:2',
        'data_inicio' => 'date',
        'data_fim' => 'date',
        'ativo' => 'boolean',
    ];

    // Relacionamentos
    public function afiliado()
    {
        return $this->belongsTo(Afiliado::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeAtivos($query)
    {
        return $query->where('ativo', true);
    }

    public function scopePublicos($query)
    {
        return $query->where('tipo', 'publico');
    }

    public function scopeAfiliadoExclusivo($query, $afiliadoId = null)
    {
        $query = $query->where('tipo', 'afiliado_exclusivo');

        if ($afiliadoId) {
            $query->where('afiliado_id', $afiliadoId);
        }

        return $query;
    }

    public function scopeValidos($query)
    {
        $hoje = Carbon::now()->toDateString();

        return $query->where('ativo', true)
                    ->where('data_inicio', '<=', $hoje)
                    ->where(function ($q) use ($hoje) {
                        $q->whereNull('data_fim')
                          ->orWhere('data_fim', '>=', $hoje);
                    })
                    ->where(function ($q) {
                        $q->whereNull('limite_uso')
                          ->orWhereRaw('usos_realizados < limite_uso');
                    });
    }

    // Métodos auxiliares
    public function isValido()
    {
        $hoje = Carbon::now();

        // Verificar se está ativo
        if (!$this->ativo) {
            return false;
        }

        // Verificar data de início
        if ($this->data_inicio && $hoje->lt(Carbon::parse($this->data_inicio))) {
            return false;
        }

        // Verificar data de fim
        if ($this->data_fim && $hoje->gt(Carbon::parse($this->data_fim))) {
            return false;
        }

        // Verificar limite de uso
        if ($this->limite_uso && $this->usos_realizados >= $this->limite_uso) {
            return false;
        }

        return true;
    }

    public function calcularDesconto($valorPedido)
    {
        if (!$this->isValido()) {
            return 0;
        }

        // Verificar valor mínimo do pedido
        if ($this->valor_minimo_pedido && $valorPedido < $this->valor_minimo_pedido) {
            return 0;
        }

        if ($this->tipo_desconto === 'percentual') {
            return ($valorPedido * $this->valor_desconto) / 100;
        } else {
            return min($this->valor_desconto, $valorPedido);
        }
    }

    public function incrementarUso()
    {
        $this->increment('usos_realizados');
    }

    public function getDescricaoDescontoAttribute()
    {
        if ($this->tipo_desconto === 'percentual') {
            return $this->valor_desconto . '% de desconto';
        } else {
            return 'R$ ' . number_format($this->valor_desconto, 2, ',', '.') . ' de desconto';
        }
    }

    public function getStatusAttribute()
    {
        if (!$this->ativo) {
            return 'inativo';
        }

        if (!$this->isValido()) {
            return 'expirado';
        }

        return 'ativo';
    }
}
