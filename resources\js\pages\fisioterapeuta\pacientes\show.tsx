import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Activity, ArrowLeft, Calendar, CreditCard, Eye, FileText, Mail, MapPin, Phone, User } from 'lucide-react';

interface Paciente {
    id: number;
    name: string;
    email: string;
    phone?: string;
    endereco?: string;
    data_nascimento?: string;
    created_at: string;
    stats: {
        total_sessoes: number;
        sessoes_mes: number;
        ultima_sessao?: string;
        proxima_sessao?: string;
        status_tratamento: 'ativo' | 'pausado' | 'concluido';
        valor_total_pago: number;
    };
    plano_atual?: {
        id: number;
        nome: string;
        status: string;
        valor: number;
        data_inicio: string;
        data_fim?: string;
    };
    agendamentos_recentes: Array<{
        id: number;
        data_hora: string;
        status: string;
        valor: number;
        formatted_data_hora: string;
        formatted_valor: string;
    }>;
    relatorios_recentes: Array<{
        id: number;
        agendamento_id: number;
        observacoes: string;
        created_at: string;
        agendamento: {
            data_hora: string;
        };
    }>;
}

interface Props {
    paciente: Paciente;
}

export default function PacienteShow({ paciente }: Props) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'ativo':
                return 'bg-green-100 text-green-800';
            case 'pausado':
                return 'bg-yellow-100 text-yellow-800';
            case 'concluido':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'ativo':
                return 'Ativo';
            case 'pausado':
                return 'Pausado';
            case 'concluido':
                return 'Concluído';
            default:
                return status;
        }
    };

    const getAgendamentoStatusColor = (status: string) => {
        switch (status) {
            case 'agendado':
                return 'bg-blue-100 text-blue-800';
            case 'confirmado':
                return 'bg-green-100 text-green-800';
            case 'em_andamento':
                return 'bg-yellow-100 text-yellow-800';
            case 'concluido':
                return 'bg-gray-100 text-gray-800';
            case 'cancelado':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <AppLayout>
            <Head title={`Paciente: ${paciente.name}`} />

            <div className="py-12">
                <div className="mx-auto max-w-6xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <Link href={route('fisioterapeuta.pacientes.index')}>
                                    <Button variant="ghost" size="sm">
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        Voltar
                                    </Button>
                                </Link>
                                <div>
                                    <h2 className="text-3xl font-bold tracking-tight">{paciente.name}</h2>
                                    <p className="text-muted-foreground">Detalhes do paciente e histórico de tratamento</p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Badge className={getStatusColor(paciente.stats.status_tratamento)}>
                                    {getStatusLabel(paciente.stats.status_tratamento)}
                                </Badge>
                                {paciente.plano_atual && <Badge variant="outline">{paciente.plano_atual.nome}</Badge>}
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                        {/* Coluna Principal */}
                        <div className="space-y-6 lg:col-span-2">
                            {/* Informações Pessoais */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <User className="mr-2 h-5 w-5" />
                                        Informações Pessoais
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <div className="mb-3 flex items-center text-sm text-gray-600">
                                                <Mail className="mr-2 h-4 w-4" />
                                                <span className="font-medium">Email:</span>
                                                <span className="ml-2">{paciente.email}</span>
                                            </div>
                                            {paciente.phone && (
                                                <div className="mb-3 flex items-center text-sm text-gray-600">
                                                    <Phone className="mr-2 h-4 w-4" />
                                                    <span className="font-medium">Telefone:</span>
                                                    <span className="ml-2">{paciente.phone}</span>
                                                </div>
                                            )}
                                            {paciente.endereco && (
                                                <div className="mb-3 flex items-center text-sm text-gray-600">
                                                    <MapPin className="mr-2 h-4 w-4" />
                                                    <span className="font-medium">Endereço:</span>
                                                    <span className="ml-2">{paciente.endereco}</span>
                                                </div>
                                            )}
                                        </div>

                                        <div>
                                            {paciente.data_nascimento && (
                                                <div className="mb-3 flex items-center text-sm text-gray-600">
                                                    <Calendar className="mr-2 h-4 w-4" />
                                                    <span className="font-medium">Data de Nascimento:</span>
                                                    <span className="ml-2">
                                                        {format(new Date(paciente.data_nascimento), 'dd/MM/yyyy', { locale: ptBR })}
                                                    </span>
                                                </div>
                                            )}
                                            <div className="mb-3 flex items-center text-sm text-gray-600">
                                                <User className="mr-2 h-4 w-4" />
                                                <span className="font-medium">Cliente desde:</span>
                                                <span className="ml-2">{format(new Date(paciente.created_at), 'dd/MM/yyyy', { locale: ptBR })}</span>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Agendamentos Recentes */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <Calendar className="mr-2 h-5 w-5" />
                                            Agendamentos Recentes
                                        </div>
                                        <Link href={route('fisioterapeuta.agenda.index', { search: paciente.name })}>
                                            <Button variant="outline" size="sm">
                                                Ver Todos
                                            </Button>
                                        </Link>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    {paciente.agendamentos_recentes.length === 0 ? (
                                        <div className="py-8 text-center">
                                            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                                            <h3 className="mt-4 text-lg font-medium text-gray-900">Nenhum agendamento</h3>
                                            <p className="mt-2 text-gray-500">Este paciente ainda não possui agendamentos.</p>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {paciente.agendamentos_recentes.map((agendamento) => (
                                                <div key={agendamento.id} className="flex items-center justify-between rounded-lg border p-4">
                                                    <div>
                                                        <div className="mb-1 flex items-center space-x-2">
                                                            <Badge className={getAgendamentoStatusColor(agendamento.status)}>
                                                                {agendamento.status}
                                                            </Badge>
                                                            <span className="text-sm text-gray-600">{agendamento.formatted_data_hora}</span>
                                                        </div>
                                                        <div className="text-sm font-medium text-green-600">{agendamento.formatted_valor}</div>
                                                    </div>
                                                    <Link href={route('fisioterapeuta.agenda.show', agendamento.id)}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="mr-2 h-4 w-4" />
                                                            Ver
                                                        </Button>
                                                    </Link>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Relatórios Recentes */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <FileText className="mr-2 h-5 w-5" />
                                            Relatórios Recentes
                                        </div>
                                        <Link href={route('fisioterapeuta.relatorios.index', { paciente_id: paciente.id })}>
                                            <Button variant="outline" size="sm">
                                                Ver Todos
                                            </Button>
                                        </Link>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    {paciente.relatorios_recentes.length === 0 ? (
                                        <div className="py-8 text-center">
                                            <FileText className="mx-auto h-12 w-12 text-gray-400" />
                                            <h3 className="mt-4 text-lg font-medium text-gray-900">Nenhum relatório</h3>
                                            <p className="mt-2 text-gray-500">Este paciente ainda não possui relatórios de sessão.</p>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {paciente.relatorios_recentes.map((relatorio) => (
                                                <div key={relatorio.id} className="rounded-lg border p-4">
                                                    <div className="mb-2 flex items-center justify-between">
                                                        <div className="text-sm text-gray-600">
                                                            Sessão de{' '}
                                                            {format(new Date(relatorio.agendamento.data_hora), 'dd/MM/yyyy', { locale: ptBR })}
                                                        </div>
                                                        <Link href={route('fisioterapeuta.relatorios.show', relatorio.id)}>
                                                            <Button variant="outline" size="sm">
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                Ver
                                                            </Button>
                                                        </Link>
                                                    </div>
                                                    <p className="text-sm text-gray-700">
                                                        {relatorio.observacoes.length > 100
                                                            ? relatorio.observacoes.substring(0, 100) + '...'
                                                            : relatorio.observacoes}
                                                    </p>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Estatísticas */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <Activity className="mr-2 h-5 w-5" />
                                        Estatísticas
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Total de sessões</span>
                                        <span className="font-medium">{paciente.stats.total_sessoes}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Sessões este mês</span>
                                        <span className="font-medium">{paciente.stats.sessoes_mes}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Valor total pago</span>
                                        <span className="font-medium text-green-600">
                                            R$ {paciente.stats.valor_total_pago.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                        </span>
                                    </div>
                                    {paciente.stats.ultima_sessao && (
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-gray-600">Última sessão</span>
                                            <span className="font-medium">
                                                {format(new Date(paciente.stats.ultima_sessao), 'dd/MM/yyyy', { locale: ptBR })}
                                            </span>
                                        </div>
                                    )}
                                    {paciente.stats.proxima_sessao && (
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-gray-600">Próxima sessão</span>
                                            <span className="font-medium">
                                                {format(new Date(paciente.stats.proxima_sessao), 'dd/MM/yyyy', { locale: ptBR })}
                                            </span>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Plano Atual */}
                            {paciente.plano_atual && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <CreditCard className="mr-2 h-5 w-5" />
                                            Plano Atual
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <h4 className="font-medium text-gray-900">{paciente.plano_atual.nome}</h4>
                                            <p className="text-sm text-gray-600">
                                                R$ {paciente.plano_atual.valor.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                            </p>
                                        </div>
                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between text-sm">
                                                <span className="text-gray-600">Início:</span>
                                                <span>{format(new Date(paciente.plano_atual.data_inicio), 'dd/MM/yyyy', { locale: ptBR })}</span>
                                            </div>
                                            {paciente.plano_atual.data_fim && (
                                                <div className="flex items-center justify-between text-sm">
                                                    <span className="text-gray-600">Fim:</span>
                                                    <span>{format(new Date(paciente.plano_atual.data_fim), 'dd/MM/yyyy', { locale: ptBR })}</span>
                                                </div>
                                            )}
                                            <div className="flex items-center justify-between text-sm">
                                                <span className="text-gray-600">Status:</span>
                                                <Badge variant="outline">{paciente.plano_atual.status}</Badge>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Ações Rápidas */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Ações Rápidas</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2">
                                    <Link href={route('fisioterapeuta.agenda.index', { search: paciente.name })} className="block">
                                        <Button variant="outline" className="w-full justify-start">
                                            <Calendar className="mr-2 h-4 w-4" />
                                            Ver Agendamentos
                                        </Button>
                                    </Link>
                                    <Link href={route('fisioterapeuta.relatorios.index', { paciente_id: paciente.id })} className="block">
                                        <Button variant="outline" className="w-full justify-start">
                                            <FileText className="mr-2 h-4 w-4" />
                                            Ver Relatórios
                                        </Button>
                                    </Link>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
