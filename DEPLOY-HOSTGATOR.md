# Deploy F4 Fisio na Hostgator

## Instruções Simples

### 1. Preparar arquivos para upload

1. <PERSON><PERSON>ça upload de todos os arquivos do projeto para o `public_html` (ou subpasta)
2. **IMPORTANTE**: Renomeie o arquivo `.env.hostgator` para `.env`
3. Edite o arquivo `.env` e altere:
    ```
    APP_URL=https://seudominio.com.br
    DB_DATABASE=/home/<USER>/public_html/database/database.sqlite
    ```
    **Substitua `SEUUSUARIO` pelo seu usuário real da Hostgator**

### 2. Configurar permissões

No File Manager do cPanel ou via terminal:

```bash
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### 3. Executar comandos (se tiver acesso SSH)

```bash
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 4. Se não tiver SSH

- Acesse `seudominio.com.br/artisan` no browser
- Ou use o File Manager para executar os comandos

## Como descobrir o caminho correto

No File Manager do cPanel, vá até a pasta onde você fez upload e veja o caminho completo na barra de endereços. Geralmente é algo como:

- `/home/<USER>/public_html/database/database.sqlite`
- `/home2/seuusuario/public_html/database/database.sqlite`

## Diferenças do .env.hostgator

- `APP_ENV=production` (em vez de local)
- `APP_DEBUG=false` (em vez de true)
- `DB_DATABASE=/home/<USER>/public_html/database/database.sqlite` (caminho absoluto)
- `LOG_LEVEL=error` (menos logs)

## Troubleshooting

Se der erro de banco:

1. Verifique se a pasta `database/` foi enviada
2. Verifique se o arquivo `database.sqlite` existe
3. Verifique permissões da pasta `database/`

## Importante

- Mantenha seu `.env` local para desenvolvimento
- Use `.env.hostgator` apenas para produção
- Nunca commite arquivos `.env` no Git
