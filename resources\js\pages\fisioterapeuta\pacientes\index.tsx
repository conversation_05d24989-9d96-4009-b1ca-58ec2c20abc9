import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Activity, Calendar, Clock, Eye, Filter, Mail, MapPin, Phone, Search, TrendingUp, UserPlus, Users } from 'lucide-react';
import { useState } from 'react';

interface Paciente {
    id: number;
    name: string;
    email: string;
    phone?: string;
    endereco?: string;
    data_nascimento?: string;
    created_at: string;
    stats: {
        total_sessoes: number;
        sessoes_mes: number;
        ultima_sessao?: string;
        proxima_sessao?: string;
        status_tratamento: 'ativo' | 'pausado' | 'concluido';
    };
    plano_atual?: {
        nome: string;
        status: string;
    };
}

interface Stats {
    total: number;
    ativos: number;
    novos_mes: number;
    sessoes_mes: number;
}

interface Props {
    pacientes: {
        data: Paciente[];
        links: any[];
        meta: any;
    };
    stats: Stats;
    filtros: {
        search?: string;
        status?: string;
    };
}

export default function PacientesIndex({ pacientes, stats, filtros }: Props) {
    const [search, setSearch] = useState(filtros.search || '');
    const [status, setStatus] = useState(filtros.status || 'all');

    const handleFilter = () => {
        router.get(
            route('fisioterapeuta.pacientes.index'),
            {
                search: search || undefined,
                status: status === 'all' ? undefined : status,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'ativo':
                return 'bg-green-100 text-green-800';
            case 'pausado':
                return 'bg-yellow-100 text-yellow-800';
            case 'concluido':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'ativo':
                return 'Ativo';
            case 'pausado':
                return 'Pausado';
            case 'concluido':
                return 'Concluído';
            default:
                return status;
        }
    };

    return (
        <AppLayout>
            <Head title="Meus Pacientes" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <h2 className="text-3xl font-bold tracking-tight">Meus Pacientes</h2>
                        <p className="text-muted-foreground">Gerencie seus pacientes e acompanhe o progresso dos tratamentos</p>
                    </div>

                    {/* Stats */}
                    <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-4">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <Users className="h-8 w-8 text-blue-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Total</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <Activity className="h-8 w-8 text-green-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Ativos</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.ativos}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <UserPlus className="h-8 w-8 text-purple-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Novos (Mês)</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.novos_mes}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center">
                                    <TrendingUp className="h-8 w-8 text-orange-600" />
                                    <div className="ml-4">
                                        <p className="text-sm font-medium text-gray-600">Sessões (Mês)</p>
                                        <p className="text-2xl font-bold text-gray-900">{stats.sessoes_mes}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filtros */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Filter className="mr-2 h-5 w-5" />
                                Filtros
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                <div>
                                    <Input
                                        placeholder="Buscar por nome ou email..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="w-full"
                                    />
                                </div>

                                <div>
                                    <Select value={status} onValueChange={setStatus}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Status do tratamento" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">Todos</SelectItem>
                                            <SelectItem value="ativo">Ativo</SelectItem>
                                            <SelectItem value="pausado">Pausado</SelectItem>
                                            <SelectItem value="concluido">Concluído</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <Button onClick={handleFilter} className="w-full">
                                        <Search className="mr-2 h-4 w-4" />
                                        Filtrar
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Lista de Pacientes */}
                    <div className="space-y-4">
                        {pacientes.data.length === 0 ? (
                            <Card>
                                <CardContent className="p-8 text-center">
                                    <Users className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-4 text-lg font-medium text-gray-900">Nenhum paciente encontrado</h3>
                                    <p className="mt-2 text-gray-500">Não há pacientes que correspondam aos filtros selecionados.</p>
                                </CardContent>
                            </Card>
                        ) : (
                            pacientes.data.map((paciente) => (
                                <Card key={paciente.id} className="transition-shadow hover:shadow-md">
                                    <CardContent className="p-6">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <div className="mb-4 flex items-center space-x-4">
                                                    <h3 className="text-lg font-semibold text-gray-900">{paciente.name}</h3>
                                                    <Badge className={getStatusColor(paciente.stats.status_tratamento)}>
                                                        {getStatusLabel(paciente.stats.status_tratamento)}
                                                    </Badge>
                                                    {paciente.plano_atual && <Badge variant="outline">{paciente.plano_atual.nome}</Badge>}
                                                </div>

                                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                    <div>
                                                        <div className="mb-2 flex items-center text-sm text-gray-600">
                                                            <Mail className="mr-2 h-4 w-4" />
                                                            <span>{paciente.email}</span>
                                                        </div>
                                                        {paciente.phone && (
                                                            <div className="mb-2 flex items-center text-sm text-gray-600">
                                                                <Phone className="mr-2 h-4 w-4" />
                                                                <span>{paciente.phone}</span>
                                                            </div>
                                                        )}
                                                        {paciente.endereco && (
                                                            <div className="mb-2 flex items-center text-sm text-gray-600">
                                                                <MapPin className="mr-2 h-4 w-4" />
                                                                <span>{paciente.endereco}</span>
                                                            </div>
                                                        )}
                                                    </div>

                                                    <div>
                                                        <div className="mb-2 flex items-center text-sm text-gray-600">
                                                            <Activity className="mr-2 h-4 w-4" />
                                                            <span>Total de sessões: </span>
                                                            <span className="ml-1 font-medium">{paciente.stats.total_sessoes}</span>
                                                        </div>
                                                        <div className="mb-2 flex items-center text-sm text-gray-600">
                                                            <TrendingUp className="mr-2 h-4 w-4" />
                                                            <span>Sessões este mês: </span>
                                                            <span className="ml-1 font-medium">{paciente.stats.sessoes_mes}</span>
                                                        </div>
                                                        {paciente.stats.ultima_sessao && (
                                                            <div className="mb-2 flex items-center text-sm text-gray-600">
                                                                <Clock className="mr-2 h-4 w-4" />
                                                                <span>Última sessão: </span>
                                                                <span className="ml-1">
                                                                    {format(new Date(paciente.stats.ultima_sessao), 'dd/MM/yyyy', { locale: ptBR })}
                                                                </span>
                                                            </div>
                                                        )}
                                                        {paciente.stats.proxima_sessao && (
                                                            <div className="mb-2 flex items-center text-sm text-gray-600">
                                                                <Calendar className="mr-2 h-4 w-4" />
                                                                <span>Próxima sessão: </span>
                                                                <span className="ml-1">
                                                                    {format(new Date(paciente.stats.proxima_sessao), 'dd/MM/yyyy', { locale: ptBR })}
                                                                </span>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="ml-6 flex flex-col space-y-2">
                                                <Link href={route('fisioterapeuta.pacientes.show', paciente.id)}>
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="mr-2 h-4 w-4" />
                                                        Ver Detalhes
                                                    </Button>
                                                </Link>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))
                        )}
                    </div>

                    {/* Paginação */}
                    {pacientes.links && pacientes.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <div className="flex space-x-1">
                                {pacientes.links.map((link: any, index: number) => (
                                    <Link
                                        key={index}
                                        href={link.url || '#'}
                                        className={`rounded-md px-3 py-2 text-sm ${
                                            link.active
                                                ? 'bg-blue-600 text-white'
                                                : link.url
                                                  ? 'border bg-white text-gray-700 hover:bg-gray-50'
                                                  : 'cursor-not-allowed bg-gray-100 text-gray-400'
                                        }`}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
