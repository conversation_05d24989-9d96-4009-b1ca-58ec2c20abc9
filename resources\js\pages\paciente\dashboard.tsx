import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Activity, ArrowRight, Calendar, CheckCircle, CreditCard, FileText, Heart, Plus, Star, TrendingUp, User } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Início',
        href: '/paciente/dashboard',
    },
];

interface Stats {
    proximoAgendamento: any;
    sessoesMes: number;
    sessoesRestantes: number;
    ultimaAvaliacao: any;
}

interface Props {
    stats: Stats;
    proximosAgendamentos: any[];
    historicoRecente: any[];
    planoAtual: any;
    pagamentosPendentes: any[];
}

export default function PacienteDashboard() {
    const pageProps = usePage().props as any;
    const { stats, proximosAgendamentos, historicoRecente, planoAtual, pagamentosPendentes } = pageProps;
    const { auth } = pageProps;

    const getGreeting = () => {
        const hour = new Date().getHours();
        if (hour < 12) return 'Bom dia';
        if (hour < 18) return 'Boa tarde';
        return 'Boa noite';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Início" />

            {/* Hero Section - Seguindo padrão da homepage */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-12 sm:py-16 md:py-24">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <Badge variant="gradient" size="icon-lg" className="mb-4 h-12 w-12 sm:mb-6 sm:h-16 sm:w-16">
                                <Heart className="h-6 w-6 sm:h-8 sm:w-8" />
                            </Badge>
                            <h1 className="mx-auto max-w-4xl text-3xl font-medium text-balance sm:text-4xl md:text-5xl lg:text-6xl">
                                {getGreeting()},<span className="block text-primary">{auth.user.name?.split(' ')[0]}!</span>
                            </h1>
                            <p className="mx-auto my-6 max-w-3xl text-lg text-balance text-muted-foreground sm:my-8 sm:text-xl">
                                Como está se sentindo hoje? Vamos cuidar da sua saúde juntos com carinho e dedicação.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Main Content */}
            <div className="mx-auto w-full max-w-7xl px-4 pb-20 sm:px-6 lg:px-8">
                <div className="space-y-8">
                    {/* Alertas importantes - Seguindo padrão da homepage */}
                    {pagamentosPendentes.length > 0 && (
                        <section className="-mx-4 bg-muted/30 py-8 sm:-mx-6 lg:-mx-8">
                            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                                <div className="space-y-6">
                                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                        <div className="flex items-start gap-4">
                                            <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                                                <CreditCard className="h-6 w-6 text-orange-600" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="text-lg font-medium text-foreground">Atenção aos Pagamentos</h3>
                                                <p className="mt-1 text-base text-muted-foreground">
                                                    Você tem {pagamentosPendentes.length} pagamento(s) pendente(s). Mantenha seu plano em dia para
                                                    continuar seu tratamento.
                                                </p>
                                                <Link href="/paciente/plano" className="mt-3 inline-block">
                                                    <Button variant="outline" size="sm" className="gap-2">
                                                        Ver detalhes <ArrowRight className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    )}

                    {/* Cards de estatísticas - Seguindo padrão da homepage */}
                    <section className="-mx-4 bg-muted/30 py-12 sm:-mx-6 lg:-mx-8">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mb-8 text-center">
                                <h2 className="text-3xl font-medium text-balance md:text-4xl">Seu Tratamento em Números</h2>
                                <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                                    Acompanhe seu progresso e mantenha-se informado sobre seu plano de fisioterapia.
                                </p>
                            </div>

                            <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
                                {/* Próximo Agendamento */}
                                <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                    <div className="mb-4 flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                            <Calendar className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">Próxima Consulta</p>
                                            {stats.proximoAgendamento && <p className="text-xs text-muted-foreground">Em breve</p>}
                                        </div>
                                    </div>
                                    {stats.proximoAgendamento ? (
                                        <div>
                                            <div className="mb-1 text-2xl font-semibold text-primary">
                                                {new Date(stats.proximoAgendamento.data_hora).toLocaleDateString('pt-BR', {
                                                    day: '2-digit',
                                                    month: 'short',
                                                })}
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                {new Date(stats.proximoAgendamento.data_hora).toLocaleTimeString('pt-BR', {
                                                    hour: '2-digit',
                                                    minute: '2-digit',
                                                })}{' '}
                                                • {stats.proximoAgendamento.fisioterapeuta.user.name}
                                            </p>
                                        </div>
                                    ) : (
                                        <div>
                                            <div className="mb-1 text-2xl font-semibold text-muted-foreground">--</div>
                                            <p className="text-sm text-muted-foreground">Nenhuma consulta agendada</p>
                                        </div>
                                    )}
                                </div>

                                {/* Sessões do Mês */}
                                <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                    <div className="mb-4 flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                            <Activity className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">Sessões Realizadas</p>
                                            <p className="text-xs text-muted-foreground">Este mês</p>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="mb-1 text-2xl font-semibold text-primary">{stats.sessoesMes}</div>
                                        <p className="text-sm text-muted-foreground">
                                            {planoAtual ? `de ${planoAtual.plano.sessions_per_month} disponíveis` : 'Sem plano ativo'}
                                        </p>
                                    </div>
                                </div>

                                {/* Sessões Restantes */}
                                <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                    <div className="mb-4 flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                            <TrendingUp className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">Sessões Restantes</p>
                                            <p className="text-xs text-muted-foreground">Disponível</p>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="mb-1 text-2xl font-semibold text-primary">{stats.sessoesRestantes}</div>
                                        <p className="text-sm text-muted-foreground">Disponíveis neste mês</p>
                                    </div>
                                </div>

                                {/* Plano Atual */}
                                <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                    <div className="mb-4 flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                            <Star className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <p className="text-sm font-medium text-foreground">Plano Atual</p>
                                            {planoAtual && <p className="text-xs text-muted-foreground">Ativo</p>}
                                        </div>
                                    </div>
                                    {planoAtual ? (
                                        <div>
                                            <div className="mb-1 text-2xl font-semibold text-primary">{planoAtual.plano.name}</div>
                                            <p className="text-sm text-muted-foreground">
                                                {planoAtual.type === 'avulsa' ? (
                                                    'Pagamento por sessão'
                                                ) : (
                                                    <>
                                                        {new Intl.NumberFormat('pt-BR', {
                                                            style: 'currency',
                                                            currency: 'BRL',
                                                        }).format(planoAtual.plano.price)}
                                                        /mês
                                                    </>
                                                )}
                                            </p>
                                        </div>
                                    ) : (
                                        <div>
                                            <div className="mb-1 text-2xl font-semibold text-muted-foreground">--</div>
                                            <p className="text-sm text-muted-foreground">Nenhum plano ativo</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* Seção de Agendamentos e Histórico - Seguindo padrão da homepage */}
                    <section className="py-12">
                        <div className="mb-8 text-center">
                            <h2 className="text-3xl font-medium text-balance md:text-4xl">Seus Agendamentos</h2>
                            <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                                Acompanhe suas consultas agendadas e o histórico do seu tratamento.
                            </p>
                        </div>

                        <div className="grid gap-8 lg:grid-cols-2">
                            {/* Próximos Agendamentos */}
                            <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                <div className="mb-6 flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                            <Calendar className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <h3 className="text-lg font-medium text-foreground">Próximas Consultas</h3>
                                            <p className="text-sm text-muted-foreground">Seus agendamentos para os próximos dias</p>
                                        </div>
                                    </div>
                                    <Link href="/paciente/agendamentos">
                                        <Button variant="outline" size="sm" className="gap-2">
                                            Ver todos <ArrowRight className="h-4 w-4" />
                                        </Button>
                                    </Link>
                                </div>
                                {proximosAgendamentos.length > 0 ? (
                                    <div className="space-y-4">
                                        {proximosAgendamentos.map((agendamento: any) => (
                                            <div
                                                key={agendamento.id}
                                                className="flex items-center gap-3 rounded-lg border border-transparent bg-muted/30 p-3"
                                            >
                                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                                    <User className="h-5 w-5 text-primary" />
                                                </div>
                                                <div className="flex-1">
                                                    <p className="font-medium text-foreground">{agendamento.fisioterapeuta.user.name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {new Date(agendamento.data_hora).toLocaleDateString('pt-BR', {
                                                            weekday: 'short',
                                                            day: '2-digit',
                                                            month: 'short',
                                                        })}{' '}
                                                        às{' '}
                                                        {new Date(agendamento.data_hora).toLocaleTimeString('pt-BR', {
                                                            hour: '2-digit',
                                                            minute: '2-digit',
                                                        })}
                                                    </p>
                                                </div>
                                                <span
                                                    className={`rounded-full px-2 py-1 text-xs font-medium ${
                                                        agendamento.status === 'confirmado'
                                                            ? 'bg-green-100 text-green-800'
                                                            : agendamento.status === 'agendado'
                                                              ? 'bg-blue-100 text-blue-800'
                                                              : 'bg-gray-100 text-gray-800'
                                                    }`}
                                                >
                                                    {agendamento.status === 'confirmado'
                                                        ? 'Confirmado'
                                                        : agendamento.status === 'agendado'
                                                          ? 'Agendado'
                                                          : agendamento.status}
                                                </span>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center py-8 text-center">
                                        <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted/50">
                                            <Calendar className="h-6 w-6 text-muted-foreground" />
                                        </div>
                                        <h3 className="mb-2 font-medium text-foreground">Nenhuma consulta agendada</h3>
                                        <p className="mb-4 text-sm text-muted-foreground">Que tal agendar sua próxima sessão?</p>
                                        {planoAtual && (
                                            <Link href="/paciente/agendamentos/create">
                                                <Button size="sm" className="gap-2">
                                                    <Plus className="h-4 w-4" />
                                                    Agendar Consulta
                                                </Button>
                                            </Link>
                                        )}
                                    </div>
                                )}
                            </div>

                            {/* Histórico Recente */}
                            <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                <div className="mb-6 flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                            <Activity className="h-5 w-5" />
                                        </Badge>
                                        <div>
                                            <h3 className="text-lg font-medium text-foreground">Histórico Recente</h3>
                                            <p className="text-sm text-muted-foreground">Suas últimas sessões realizadas</p>
                                        </div>
                                    </div>
                                    <Link href="/paciente/historico">
                                        <Button variant="outline" size="sm" className="gap-2">
                                            Ver histórico <ArrowRight className="h-4 w-4" />
                                        </Button>
                                    </Link>
                                </div>
                                {historicoRecente.length > 0 ? (
                                    <div className="space-y-4">
                                        {historicoRecente.map((agendamento: any) => (
                                            <div
                                                key={agendamento.id}
                                                className="flex items-center gap-3 rounded-lg border border-transparent bg-muted/30 p-3"
                                            >
                                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                                    <CheckCircle className="h-5 w-5 text-primary" />
                                                </div>
                                                <div className="flex-1">
                                                    <p className="font-medium text-foreground">{agendamento.fisioterapeuta.user.name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {new Date(agendamento.data_hora).toLocaleDateString('pt-BR', {
                                                            weekday: 'short',
                                                            day: '2-digit',
                                                            month: 'short',
                                                        })}{' '}
                                                        às{' '}
                                                        {new Date(agendamento.data_hora).toLocaleTimeString('pt-BR', {
                                                            hour: '2-digit',
                                                            minute: '2-digit',
                                                        })}
                                                    </p>
                                                </div>
                                                <Link href={`/paciente/agendamentos/${agendamento.id}`}>
                                                    <Button variant="ghost" size="sm">
                                                        <FileText className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center py-8 text-center">
                                        <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted/50">
                                            <Activity className="h-6 w-6 text-muted-foreground" />
                                        </div>
                                        <h3 className="mb-2 font-medium text-foreground">Nenhuma sessão realizada</h3>
                                        <p className="text-sm text-muted-foreground">Suas sessões concluídas aparecerão aqui.</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </section>

                    {/* Ações rápidas - Seguindo padrão da homepage */}
                    {planoAtual && (
                        <section className="-mx-4 bg-muted/30 py-12 sm:-mx-6 lg:-mx-8">
                            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                                <div className="mb-8 text-center">
                                    <h2 className="text-3xl font-medium text-balance md:text-4xl">Ações Rápidas</h2>
                                    <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                                        Acesse rapidamente as principais funcionalidades do seu tratamento.
                                    </p>
                                </div>

                                <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
                                    {/* Agendar Consulta */}
                                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                        <div className="mb-4 flex items-center gap-3">
                                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                                <Plus className="h-5 w-5" />
                                            </Badge>
                                            <div>
                                                <p className="text-sm font-medium text-foreground">Agendar Consulta</p>
                                                <p className="text-xs text-muted-foreground">Nova sessão</p>
                                            </div>
                                        </div>
                                        <p className="mb-4 text-sm text-muted-foreground">Marque sua próxima sessão de fisioterapia</p>
                                        <Link href="/paciente/agendamentos/create">
                                            <Button size="sm" className="w-full">
                                                Agendar Agora
                                            </Button>
                                        </Link>
                                    </div>

                                    {/* Meus Agendamentos */}
                                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                        <div className="mb-4 flex items-center gap-3">
                                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                                <Calendar className="h-5 w-5" />
                                            </Badge>
                                            <div>
                                                <p className="text-sm font-medium text-foreground">Meus Agendamentos</p>
                                                <p className="text-xs text-muted-foreground">Consultas</p>
                                            </div>
                                        </div>
                                        <p className="mb-4 text-sm text-muted-foreground">Visualize e gerencie suas consultas</p>
                                        <Link href="/paciente/agendamentos">
                                            <Button variant="outline" size="sm" className="w-full">
                                                Ver Agendamentos
                                            </Button>
                                        </Link>
                                    </div>

                                    {/* Histórico Médico */}
                                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                        <div className="mb-4 flex items-center gap-3">
                                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                                <FileText className="h-5 w-5" />
                                            </Badge>
                                            <div>
                                                <p className="text-sm font-medium text-foreground">Histórico Médico</p>
                                                <p className="text-xs text-muted-foreground">Progresso</p>
                                            </div>
                                        </div>
                                        <p className="mb-4 text-sm text-muted-foreground">Acompanhe seu progresso e evolução</p>
                                        <Link href="/paciente/historico">
                                            <Button variant="outline" size="sm" className="w-full">
                                                Ver Histórico
                                            </Button>
                                        </Link>
                                    </div>

                                    {/* Gerenciar Plano */}
                                    <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                        <div className="mb-4 flex items-center gap-3">
                                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                                <CreditCard className="h-5 w-5" />
                                            </Badge>
                                            <div>
                                                <p className="text-sm font-medium text-foreground">Gerenciar Plano</p>
                                                <p className="text-xs text-muted-foreground">Assinatura</p>
                                            </div>
                                        </div>
                                        <p className="mb-4 text-sm text-muted-foreground">Visualize e altere seu plano atual</p>
                                        <Link href="/paciente/plano">
                                            <Button variant="outline" size="sm" className="w-full">
                                                Gerenciar Plano
                                            </Button>
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </section>
                    )}
                </div>
            </div>

            {/* Footer motivacional - Seguindo padrão da homepage */}
            <section className="py-8 text-center">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="inline-flex items-center gap-2 text-muted-foreground">
                        <Heart className="h-5 w-5 text-primary" />
                        <span className="text-base">Cuidando da sua saúde com carinho e dedicação</span>
                        <Heart className="h-5 w-5 text-primary" />
                    </div>
                </div>
            </section>
        </AppLayout>
    );
}
