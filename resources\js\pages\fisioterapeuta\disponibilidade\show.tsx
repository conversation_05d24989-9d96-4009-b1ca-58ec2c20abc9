import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    ArrowLeft, 
    Calendar, 
    Clock, 
    Edit, 
    Trash2,
    ToggleLeft,
    ToggleRight,
    CalendarDays,
    Ban,
    CheckCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface Disponibilidade {
    id: number;
    tipo: 'disponivel' | 'indisponivel' | 'bloqueio';
    data_inicio: string;
    data_fim?: string;
    hora_inicio: string;
    hora_fim: string;
    dias_semana?: number[];
    recorrente: boolean;
    motivo?: string;
    ativo: boolean;
    formatted_horario: string;
    formatted_periodo: string;
    dias_semana_text: string;
    created_at: string;
    updated_at: string;
}

interface Props {
    disponibilidade: Disponibilidade;
}

const diasSemanaMap: { [key: number]: string } = {
    0: 'Domingo',
    1: 'Segunda-feira',
    2: 'Terça-feira',
    3: 'Quarta-feira',
    4: 'Quinta-feira',
    5: 'Sexta-feira',
    6: 'Sábado',
};

export default function Show({ disponibilidade }: Props) {
    const handleToggle = () => {
        router.post(route('fisioterapeuta.disponibilidade.toggle', disponibilidade.id), {}, {
            preserveScroll: true,
        });
    };

    const handleDelete = () => {
        if (confirm('Tem certeza que deseja excluir esta disponibilidade?')) {
            router.delete(route('fisioterapeuta.disponibilidade.destroy', disponibilidade.id));
        }
    };

    const getTipoColor = (tipo: string) => {
        switch (tipo) {
            case 'disponivel':
                return 'bg-green-100 text-green-800';
            case 'indisponivel':
                return 'bg-yellow-100 text-yellow-800';
            case 'bloqueio':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getTipoLabel = (tipo: string) => {
        switch (tipo) {
            case 'disponivel':
                return 'Disponível';
            case 'indisponivel':
                return 'Indisponível';
            case 'bloqueio':
                return 'Bloqueio';
            default:
                return tipo;
        }
    };

    const getTipoIcon = (tipo: string) => {
        switch (tipo) {
            case 'disponivel':
                return <CheckCircle className="h-5 w-5 text-green-600" />;
            case 'indisponivel':
                return <Clock className="h-5 w-5 text-yellow-600" />;
            case 'bloqueio':
                return <Ban className="h-5 w-5 text-red-600" />;
            default:
                return <Calendar className="h-5 w-5 text-gray-600" />;
        }
    };

    return (
        <AppLayout>
            <Head title="Detalhes da Disponibilidade" />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <Link href={route('fisioterapeuta.disponibilidade.index')}>
                                    <Button variant="ghost" size="sm">
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        Voltar
                                    </Button>
                                </Link>
                                <div>
                                    <h2 className="text-3xl font-bold tracking-tight">Detalhes da Disponibilidade</h2>
                                    <p className="text-muted-foreground">
                                        Visualize as informações desta disponibilidade
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleToggle}
                                >
                                    {disponibilidade.ativo ? (
                                        <ToggleRight className="h-4 w-4 text-green-600" />
                                    ) : (
                                        <ToggleLeft className="h-4 w-4 text-gray-400" />
                                    )}
                                    {disponibilidade.ativo ? 'Ativo' : 'Inativo'}
                                </Button>
                                
                                <Link href={route('fisioterapeuta.disponibilidade.edit', disponibilidade.id)}>
                                    <Button variant="outline" size="sm">
                                        <Edit className="mr-2 h-4 w-4" />
                                        Editar
                                    </Button>
                                </Link>
                                
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleDelete}
                                    className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                                >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Excluir
                                </Button>
                            </div>
                        </div>
                    </div>

                    <div className="space-y-6">
                        {/* Informações Principais */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-3">
                                    {getTipoIcon(disponibilidade.tipo)}
                                    <span>Informações Principais</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Tipo</label>
                                        <div className="mt-1">
                                            <Badge className={getTipoColor(disponibilidade.tipo)}>
                                                {getTipoLabel(disponibilidade.tipo)}
                                            </Badge>
                                        </div>
                                    </div>

                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Status</label>
                                        <div className="mt-1">
                                            <Badge variant={disponibilidade.ativo ? "default" : "secondary"}>
                                                {disponibilidade.ativo ? 'Ativo' : 'Inativo'}
                                            </Badge>
                                        </div>
                                    </div>

                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Período</label>
                                        <p className="mt-1 text-sm text-gray-900">{disponibilidade.formatted_periodo}</p>
                                    </div>

                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Horário</label>
                                        <p className="mt-1 text-sm text-gray-900">{disponibilidade.formatted_horario}</p>
                                    </div>

                                    {disponibilidade.recorrente && (
                                        <>
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Recorrência</label>
                                                <div className="mt-1">
                                                    <Badge variant="outline">
                                                        <Clock className="mr-1 h-3 w-3" />
                                                        Recorrente
                                                    </Badge>
                                                </div>
                                            </div>

                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Dias da Semana</label>
                                                <p className="mt-1 text-sm text-gray-900">{disponibilidade.dias_semana_text}</p>
                                            </div>
                                        </>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Motivo */}
                        {disponibilidade.motivo && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Motivo</CardTitle>
                                    <CardDescription>
                                        Descrição do motivo desta {disponibilidade.tipo === 'bloqueio' ? 'bloqueio' : 'indisponibilidade'}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-900">{disponibilidade.motivo}</p>
                                </CardContent>
                            </Card>
                        )}

                        {/* Detalhes Técnicos */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Detalhes Técnicos</CardTitle>
                                <CardDescription>
                                    Informações técnicas sobre esta disponibilidade
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Data de Criação</label>
                                        <p className="mt-1 text-sm text-gray-900">
                                            {format(new Date(disponibilidade.created_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                                        </p>
                                    </div>

                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Última Atualização</label>
                                        <p className="mt-1 text-sm text-gray-900">
                                            {format(new Date(disponibilidade.updated_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                                        </p>
                                    </div>

                                    <div>
                                        <label className="text-sm font-medium text-gray-500">ID</label>
                                        <p className="mt-1 text-sm text-gray-900 font-mono">#{disponibilidade.id}</p>
                                    </div>

                                    {disponibilidade.dias_semana && disponibilidade.dias_semana.length > 0 && (
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Dias da Semana (Códigos)</label>
                                            <p className="mt-1 text-sm text-gray-900 font-mono">
                                                [{disponibilidade.dias_semana.join(', ')}]
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Resumo Visual */}
                        {disponibilidade.dias_semana && disponibilidade.dias_semana.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <CalendarDays className="mr-2 h-5 w-5" />
                                        Resumo Semanal
                                    </CardTitle>
                                    <CardDescription>
                                        Visualização dos dias da semana configurados
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-7 gap-2">
                                        {[0, 1, 2, 3, 4, 5, 6].map((dia) => (
                                            <div
                                                key={dia}
                                                className={`p-3 text-center rounded-lg border ${
                                                    disponibilidade.dias_semana?.includes(dia)
                                                        ? 'bg-blue-50 border-blue-200 text-blue-800'
                                                        : 'bg-gray-50 border-gray-200 text-gray-400'
                                                }`}
                                            >
                                                <div className="text-xs font-medium">
                                                    {diasSemanaMap[dia].substring(0, 3)}
                                                </div>
                                                <div className="text-xs mt-1">
                                                    {disponibilidade.dias_semana?.includes(dia) ? (
                                                        <CheckCircle className="h-3 w-3 mx-auto" />
                                                    ) : (
                                                        <div className="h-3 w-3 mx-auto rounded-full bg-gray-300" />
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
