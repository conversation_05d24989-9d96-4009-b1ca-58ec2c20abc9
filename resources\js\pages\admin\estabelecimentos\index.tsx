import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Building2, CreditCard, Edit, ExternalLink, Eye, MoreHorizontal, Plus, Search, ToggleRight, Trash2 } from 'lucide-react';
import React, { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface Estabelecimento {
    id: number;
    nome: string;
    categoria: string;
    cidade: string;
    estado: string;
    whatsapp: string;
    slug?: string;
    ativo: boolean;
    plano_ativo: boolean;
    plano_vencimento?: string;
    user?: User;
    created_at: string;
}

interface Stats {
    total: number;
    ativos: number;
    com_plano: number;
    sem_plano: number;
}

interface Props {
    estabelecimentos: {
        data: Estabelecimento[];
        links: any[];
        meta: any;
    };
    filters: {
        ativo?: string;
        plano_ativo?: string;
        categoria?: string;
        search?: string;
    };
    stats: Stats;
}

export default function EstabelecimentosIndex({ estabelecimentos, filters, stats }: Props) {
    const [search, setSearch] = useState(filters.search || '');

    // Fallback para stats caso venha undefined
    const safeStats = React.useMemo(() => {
        if (!stats || typeof stats !== 'object') {
            return {
                total: 0,
                ativos: 0,
                com_plano: 0,
                sem_plano: 0,
            };
        }
        return stats;
    }, [stats]);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('admin.estabelecimentos.index'), { ...filters, search });
    };

    const handleFilter = (key: string, value: string) => {
        router.get(route('admin.estabelecimentos.index'), { ...filters, [key]: value });
    };

    const getCategoriaLabel = (categoria: string) => {
        const labels = {
            dentista: 'Dentista',
            farmacia: 'Farmácia',
            fisioterapia: 'Fisioterapia',
            outros: 'Outros',
        };
        return labels[categoria as keyof typeof labels] || categoria;
    };

    const toggleAtivo = (estabelecimento: Estabelecimento) => {
        router.post(route('admin.estabelecimentos.toggle-ativo', estabelecimento.id));
    };

    const togglePlano = (estabelecimento: Estabelecimento) => {
        router.post(route('admin.estabelecimentos.toggle-plano', estabelecimento.id));
    };

    const deleteEstabelecimento = (estabelecimento: Estabelecimento) => {
        if (confirm(`Tem certeza que deseja excluir o estabelecimento "${estabelecimento.nome}"?`)) {
            router.delete(route('admin.estabelecimentos.destroy', estabelecimento.id));
        }
    };

    return (
        <AppLayout>
            <Head title="Estabelecimentos - Admin" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Estabelecimentos</h1>
                        <p className="text-muted-foreground">Gerencie todos os estabelecimentos cadastrados na plataforma</p>
                    </div>
                    <Button asChild>
                        <Link href={route('admin.estabelecimentos.create')}>
                            <Plus className="mr-2 h-4 w-4" />
                            Novo Estabelecimento
                        </Link>
                    </Button>
                </div>

                {/* Estatísticas */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total</CardTitle>
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{safeStats.total}</div>
                            <p className="text-xs text-muted-foreground">Estabelecimentos cadastrados</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Ativos</CardTitle>
                            <ToggleRight className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{safeStats.ativos}</div>
                            <p className="text-xs text-muted-foreground">Visíveis nas buscas</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Com Plano</CardTitle>
                            <CreditCard className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{safeStats.com_plano}</div>
                            <p className="text-xs text-muted-foreground">Planos ativos</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Sem Plano</CardTitle>
                            <CreditCard className="h-4 w-4 text-red-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{safeStats.sem_plano}</div>
                            <p className="text-xs text-muted-foreground">Aguardando ativação</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filtros</CardTitle>
                        <CardDescription>Use os filtros abaixo para encontrar estabelecimentos específicos</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col gap-4 md:flex-row md:items-end">
                            <form onSubmit={handleSearch} className="flex-1">
                                <div className="relative">
                                    <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        placeholder="Buscar por nome, cidade ou email..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </form>

                            <Select value={filters.categoria || 'all'} onValueChange={(value) => handleFilter('categoria', value)}>
                                <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Categoria" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todas as categorias</SelectItem>
                                    <SelectItem value="dentista">Dentista</SelectItem>
                                    <SelectItem value="farmacia">Farmácia</SelectItem>
                                    <SelectItem value="fisioterapia">Fisioterapia</SelectItem>
                                    <SelectItem value="outros">Outros</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select value={filters.ativo || 'all'} onValueChange={(value) => handleFilter('ativo', value)}>
                                <SelectTrigger className="w-[150px]">
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todos</SelectItem>
                                    <SelectItem value="1">Ativos</SelectItem>
                                    <SelectItem value="0">Inativos</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select value={filters.plano_ativo || 'all'} onValueChange={(value) => handleFilter('plano_ativo', value)}>
                                <SelectTrigger className="w-[150px]">
                                    <SelectValue placeholder="Plano" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todos</SelectItem>
                                    <SelectItem value="1">Com Plano</SelectItem>
                                    <SelectItem value="0">Sem Plano</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {/* Tabela */}
                <Card>
                    <CardHeader>
                        <CardTitle>Estabelecimentos ({estabelecimentos?.meta?.total || 0})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Nome</TableHead>
                                    <TableHead>Categoria</TableHead>
                                    <TableHead>Localização</TableHead>
                                    <TableHead>Usuário</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Plano</TableHead>
                                    <TableHead className="text-right">Ações</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {estabelecimentos.data.map((estabelecimento) => (
                                    <TableRow key={estabelecimento.id}>
                                        <TableCell>
                                            <div>
                                                <div className="font-medium">{estabelecimento.nome}</div>
                                                {estabelecimento.slug && <div className="text-sm text-muted-foreground">/{estabelecimento.slug}</div>}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="outline">{getCategoriaLabel(estabelecimento.categoria)}</Badge>
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-sm">
                                                {estabelecimento.cidade} - {estabelecimento.estado}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            {estabelecimento.user ? (
                                                <div className="text-sm">
                                                    <div>{estabelecimento.user.name}</div>
                                                    <div className="text-muted-foreground">{estabelecimento.user.email}</div>
                                                </div>
                                            ) : (
                                                <Badge variant="secondary">Sem usuário</Badge>
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <Button variant="ghost" size="sm" onClick={() => toggleAtivo(estabelecimento)} className="h-auto p-0">
                                                {estabelecimento.ativo ? (
                                                    <Badge variant="default" className="bg-green-600">
                                                        Ativo
                                                    </Badge>
                                                ) : (
                                                    <Badge variant="destructive">Inativo</Badge>
                                                )}
                                            </Button>
                                        </TableCell>
                                        <TableCell>
                                            <Button variant="ghost" size="sm" onClick={() => togglePlano(estabelecimento)} className="h-auto p-0">
                                                {estabelecimento.plano_ativo ? (
                                                    <Badge variant="default" className="bg-blue-600">
                                                        Ativo
                                                    </Badge>
                                                ) : (
                                                    <Badge variant="outline">Inativo</Badge>
                                                )}
                                            </Button>
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuItem asChild>
                                                        <Link href={route('admin.estabelecimentos.show', estabelecimento.id)}>
                                                            <Eye className="mr-2 h-4 w-4" />
                                                            Visualizar
                                                        </Link>
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem asChild>
                                                        <Link href={route('admin.estabelecimentos.edit', estabelecimento.id)}>
                                                            <Edit className="mr-2 h-4 w-4" />
                                                            Editar
                                                        </Link>
                                                    </DropdownMenuItem>
                                                    {estabelecimento.slug && (
                                                        <DropdownMenuItem asChild>
                                                            <a href={`/empresa/${estabelecimento.slug}`} target="_blank" rel="noopener noreferrer">
                                                                <ExternalLink className="mr-2 h-4 w-4" />
                                                                Ver Página
                                                            </a>
                                                        </DropdownMenuItem>
                                                    )}
                                                    <DropdownMenuItem onClick={() => deleteEstabelecimento(estabelecimento)} className="text-red-600">
                                                        <Trash2 className="mr-2 h-4 w-4" />
                                                        Excluir
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {/* Paginação */}
                        {estabelecimentos.links.length > 3 && (
                            <div className="mt-4 flex items-center justify-center space-x-2">
                                {estabelecimentos.links.map((link, index) => (
                                    <Button
                                        key={index}
                                        variant={link.active ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => link.url && router.get(link.url)}
                                        disabled={!link.url}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
