import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { router } from '@inertiajs/react';
import { File, FileText, Image, Music, Upload, Video, X } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { toast } from 'sonner';

interface FileUploadProps {
    relatorioId: number;
    uploadUrl: string;
    onUploadSuccess?: (anexo: any) => void;
    className?: string;
}

interface Anexo {
    id: number;
    nome_original: string;
    tamanho_formatado: string;
    tipo_anexo: string;
    url: string;
    descricao?: string;
}

export function FileUpload({ relatorioId, uploadUrl, onUploadSuccess, className }: FileUploadProps) {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [descricao, setDescricao] = useState('');
    const [anexos, setAnexos] = useState<Anexo[]>([]);

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        // Validar tamanho (10MB)
        if (file.size > 10 * 1024 * 1024) {
            toast.error('O arquivo deve ter no máximo 10MB.');
            return;
        }

        uploadFile(file);
    };

    const uploadFile = async (file: File) => {
        setIsUploading(true);

        const formData = new FormData();
        formData.append('arquivo', file);
        formData.append('descricao', descricao);

        try {
            await new Promise((resolve, reject) => {
                router.post(uploadUrl, formData, {
                    onSuccess: (page) => {
                        const response = (page.props.flash as any)?.anexo || (page.props as any).anexo;
                        if (response) {
                            setAnexos((prev) => [...prev, response]);
                            setDescricao('');
                            toast.success('Arquivo enviado com sucesso!');
                            onUploadSuccess?.(response);
                        }
                        resolve(true);
                    },
                    onError: (errors) => {
                        const errorMessage = errors.arquivo || 'Erro ao fazer upload do arquivo.';
                        toast.error(errorMessage);
                        reject(new Error(errorMessage));
                    },
                    onFinish: () => setIsUploading(false),
                });
            });
        } catch (error) {
            console.error('Erro no upload:', error);
        }

        // Limpar input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const removeAnexo = async (anexoId: number) => {
        try {
            await new Promise((resolve, reject) => {
                router.delete(`/fisioterapeuta/relatorios/anexos/${anexoId}`, {
                    onSuccess: () => {
                        setAnexos((prev) => prev.filter((a) => a.id !== anexoId));
                        toast.success('Arquivo removido com sucesso!');
                        resolve(true);
                    },
                    onError: (errors) => {
                        const errorMessage = 'Erro ao remover arquivo.';
                        toast.error(errorMessage);
                        reject(new Error(errorMessage));
                    },
                });
            });
        } catch (error) {
            console.error('Erro ao remover anexo:', error);
        }
    };

    const getFileIcon = (tipoAnexo: string) => {
        switch (tipoAnexo) {
            case 'imagem':
                return <Image className="h-4 w-4" />;
            case 'video':
                return <Video className="h-4 w-4" />;
            case 'audio':
                return <Music className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    return (
        <div className={className}>
            <Card>
                <CardContent className="p-6">
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="descricao">Descrição do arquivo (opcional)</Label>
                            <Textarea
                                id="descricao"
                                placeholder="Descreva o conteúdo do arquivo..."
                                value={descricao}
                                onChange={(e) => setDescricao(e.target.value)}
                                className="mt-1"
                            />
                        </div>

                        <div>
                            <input
                                ref={fileInputRef}
                                type="file"
                                onChange={handleFileSelect}
                                className="hidden"
                                accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
                            />

                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => fileInputRef.current?.click()}
                                disabled={isUploading}
                                className="w-full"
                            >
                                <Upload className="mr-2 h-4 w-4" />
                                {isUploading ? 'Enviando...' : 'Selecionar Arquivo'}
                            </Button>
                        </div>

                        <div className="text-sm text-muted-foreground">
                            Formatos aceitos: Imagens, vídeos, áudios, PDF, DOC, DOCX, TXT
                            <br />
                            Tamanho máximo: 10MB
                        </div>
                    </div>

                    {anexos.length > 0 && (
                        <div className="mt-6 space-y-2">
                            <Label>Arquivos anexados</Label>
                            {anexos.map((anexo) => (
                                <div key={anexo.id} className="flex items-center justify-between rounded-lg border p-3">
                                    <div className="flex items-center space-x-3">
                                        {getFileIcon(anexo.tipo_anexo)}
                                        <div>
                                            <p className="text-sm font-medium">{anexo.nome_original}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {anexo.tamanho_formatado}
                                                {anexo.descricao && ` • ${anexo.descricao}`}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Button variant="ghost" size="sm" onClick={() => window.open(anexo.url, '_blank')}>
                                            Ver
                                        </Button>
                                        <Button variant="ghost" size="sm" onClick={() => removeAnexo(anexo.id)}>
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
