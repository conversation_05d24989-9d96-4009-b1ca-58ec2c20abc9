import { test, expect, type Page } from '@playwright/test';

// Configuração de teste
const BASE_URL = 'http://localhost:8000';

// Paciente com assinatura ativa
const PACIENTE_COM_ASSINATURA = {
    email: '<EMAIL>',
    password: 'paciente123',
    name: '<PERSON>'
};

test.describe('Teste de Funcionalidades Críticas - Verificação Completa', () => {
    test.beforeEach(async ({ page }) => {
        test.setTimeout(180000);
        page.setDefaultTimeout(30000);
        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');
    });

    test('1. Teste Completo do Formulário de Perfil', async ({ page }) => {
        console.log('👤 Testando formulário de perfil completo...');

        await fazerLogin(page);

        // Ir para perfil
        await page.goto('/paciente/perfil');
        await page.waitForLoadState('networkidle');

        // Verificar se chegou na página de perfil
        expect(page.url()).toContain('/paciente/perfil');
        console.log('✅ Página de perfil acessível');

        // Testar todos os campos do formulário
        const campos = [
            { name: 'name', valor: 'José da Silva - Teste Editado', tipo: 'input' },
            { name: 'email', valor: '<EMAIL>', tipo: 'input' },
            { name: 'phone', valor: '(11) 99999-8888', tipo: 'input' },
            { name: 'cpf', valor: '123.456.789-00', tipo: 'input' },
            { name: 'birth_date', valor: '1985-03-15', tipo: 'date' },
            { name: 'address', valor: 'Rua Teste, 123', tipo: 'input' },
            { name: 'city', valor: 'São Paulo', tipo: 'input' },
            { name: 'state', valor: 'SP', tipo: 'input' },
            { name: 'zip_code', valor: '01234-567', tipo: 'input' }
        ];

        for (const campo of campos) {
            const elemento = page.locator(`input[name="${campo.name}"]`);
            if (await elemento.isVisible()) {
                console.log(`✅ Campo ${campo.name} encontrado`);
                
                // Limpar e preencher
                await elemento.clear();
                await elemento.fill(campo.valor);
                
                // Verificar se o valor foi definido
                const valorAtual = await elemento.inputValue();
                if (valorAtual === campo.valor) {
                    console.log(`✅ Campo ${campo.name} preenchido corretamente`);
                } else {
                    console.log(`⚠️ Campo ${campo.name} não foi preenchido corretamente`);
                }
            } else {
                console.log(`❌ Campo ${campo.name} não encontrado`);
            }
        }

        // Testar salvamento
        const botaoSalvar = page.locator('button[type="submit"], button:has-text("Salvar"), button:has-text("Atualizar")').first();
        if (await botaoSalvar.isVisible()) {
            console.log('✅ Botão de salvar encontrado');
            await botaoSalvar.click();
            await page.waitForLoadState('networkidle');
            
            // Verificar se houve redirecionamento ou mensagem de sucesso
            const currentUrl = page.url();
            const temMensagemSucesso = await page.locator('text=/salvo/i, text=/atualizado/i, text=/sucesso/i').isVisible();
            
            if (temMensagemSucesso || currentUrl.includes('/paciente/perfil')) {
                console.log('✅ Formulário de perfil salvo com sucesso');
            } else {
                console.log('⚠️ Resultado do salvamento não claro');
            }
        } else {
            console.log('❌ Botão de salvar não encontrado');
        }
    });

    test('2. Teste do Sistema de Agendamentos', async ({ page }) => {
        console.log('📅 Testando sistema de agendamentos...');

        await fazerLogin(page);

        // Ir para agendamentos
        await page.goto('/paciente/agendamentos');
        await page.waitForLoadState('networkidle');

        if (page.url().includes('/paciente/agendamentos')) {
            console.log('✅ Página de agendamentos acessível');

            // Verificar se há agendamentos existentes
            const temAgendamentos = await page.locator('.agendamento, .appointment, .card').count() > 0;
            if (temAgendamentos) {
                console.log('✅ Agendamentos existentes encontrados');
            } else {
                console.log('ℹ️ Nenhum agendamento existente');
            }

            // Procurar botão de novo agendamento
            const botaoNovo = page.locator('button:has-text("Novo"), button:has-text("Agendar"), a:has-text("Novo"), a:has-text("Agendar")').first();
            
            if (await botaoNovo.isVisible()) {
                console.log('✅ Botão de novo agendamento encontrado');
                await botaoNovo.click();
                await page.waitForLoadState('networkidle');

                const currentUrl = page.url();
                if (currentUrl.includes('create') || currentUrl.includes('novo')) {
                    console.log('✅ Página de criação de agendamento acessível');

                    // Verificar elementos do formulário de agendamento
                    const elementosFormulario = [
                        'select', 'input[type="date"]', 'input[type="time"]', 
                        'textarea', 'input[name="data"]', 'input[name="hora"]'
                    ];

                    let elementosEncontrados = 0;
                    for (const seletor of elementosFormulario) {
                        const elemento = page.locator(seletor);
                        if (await elemento.count() > 0) {
                            elementosEncontrados++;
                            console.log(`✅ Elemento ${seletor} encontrado`);
                        }
                    }

                    if (elementosEncontrados > 0) {
                        console.log('✅ Formulário de agendamento presente');
                    } else {
                        console.log('❌ Formulário de agendamento não encontrado');
                    }
                } else {
                    console.log('⚠️ Não redirecionou para página de criação');
                }
            } else {
                console.log('⚠️ Botão de novo agendamento não encontrado');
            }
        } else {
            console.log('❌ Página de agendamentos não acessível');
        }
    });

    test('3. Teste do Sistema de Avaliações', async ({ page }) => {
        console.log('⭐ Testando sistema de avaliações...');

        await fazerLogin(page);

        // Ir para avaliações
        await page.goto('/paciente/avaliacoes');
        await page.waitForLoadState('networkidle');

        if (page.url().includes('/paciente/avaliacoes')) {
            console.log('✅ Página de avaliações acessível');

            // Verificar se há avaliações existentes
            const temAvaliacoes = await page.locator('.avaliacao, .review, .card').count() > 0;
            if (temAvaliacoes) {
                console.log('✅ Avaliações existentes encontradas');
            } else {
                console.log('ℹ️ Nenhuma avaliação existente');
            }

            // Verificar se há botão para nova avaliação
            const botaoNova = page.locator('button:has-text("Nova"), button:has-text("Avaliar"), a:has-text("Nova")').first();
            if (await botaoNova.isVisible()) {
                console.log('✅ Botão de nova avaliação encontrado');
            } else {
                console.log('ℹ️ Botão de nova avaliação não encontrado (pode ser normal)');
            }

            // Verificar elementos da página
            const temConteudo = await page.locator('h1, h2, .title, .content, main').count() > 0;
            if (temConteudo) {
                console.log('✅ Página de avaliações com conteúdo');
            } else {
                console.log('⚠️ Página de avaliações sem conteúdo visível');
            }
        } else {
            console.log('❌ Página de avaliações não acessível');
        }
    });

    test('4. Teste do Programa de Afiliados', async ({ page }) => {
        console.log('🤝 Testando programa de afiliados...');

        await fazerLogin(page);

        // Ir para afiliados
        await page.goto('/paciente/afiliados');
        await page.waitForLoadState('networkidle');

        if (page.url().includes('/paciente/afiliados')) {
            console.log('✅ Página de afiliados acessível');

            // Verificar elementos específicos do programa de afiliados
            const elementosAfiliados = [
                'text=/código/i', 'text=/link/i', 'text=/comissão/i', 
                'text=/indicação/i', 'text=/ganhos/i', 'text=/referral/i'
            ];

            let elementosEncontrados = 0;
            for (const seletor of elementosAfiliados) {
                if (await page.locator(seletor).count() > 0) {
                    elementosEncontrados++;
                    console.log(`✅ Elemento de afiliado encontrado: ${seletor}`);
                }
            }

            if (elementosEncontrados > 0) {
                console.log('✅ Conteúdo de afiliados presente');
            } else {
                console.log('⚠️ Conteúdo específico de afiliados não encontrado');
            }

            // Verificar se há formulários ou botões de ação
            const temFormulario = await page.locator('form, button, input').count() > 0;
            if (temFormulario) {
                console.log('✅ Elementos interativos encontrados na página de afiliados');
            } else {
                console.log('⚠️ Nenhum elemento interativo encontrado');
            }
        } else {
            console.log('❌ Página de afiliados não acessível');
        }
    });

    test('5. Teste de Integração WhatsApp', async ({ page }) => {
        console.log('📱 Testando integração WhatsApp...');

        // Ir para página de busca
        await page.goto('/buscar');
        await page.waitForLoadState('networkidle');

        // Fazer uma busca
        const campoBusca = page.locator('input[type="search"], input[placeholder*="buscar"], input[placeholder*="Buscar"]').first();
        if (await campoBusca.isVisible()) {
            await campoBusca.fill('São Paulo');
            await page.keyboard.press('Enter');
            await page.waitForLoadState('networkidle');

            // Verificar se há resultados com WhatsApp
            const linksWhatsApp = page.locator('a[href*="wa.me"], a[href*="whatsapp"], button:has-text("WhatsApp")');
            const countWhatsApp = await linksWhatsApp.count();

            if (countWhatsApp > 0) {
                console.log(`✅ ${countWhatsApp} links do WhatsApp encontrados`);
                
                // Verificar se os links estão corretos
                const primeiroLink = linksWhatsApp.first();
                const href = await primeiroLink.getAttribute('href');
                
                if (href && (href.includes('wa.me') || href.includes('whatsapp'))) {
                    console.log('✅ Links do WhatsApp estão corretos');
                } else {
                    console.log('⚠️ Links do WhatsApp podem estar incorretos');
                }
            } else {
                console.log('❌ Nenhum link do WhatsApp encontrado');
            }
        } else {
            console.log('❌ Campo de busca não encontrado');
        }
    });

    test('6. Teste de Validação de Formulários', async ({ page }) => {
        console.log('✅ Testando validação de formulários...');

        await fazerLogin(page);

        // Testar validação no formulário de perfil
        await page.goto('/paciente/perfil');
        await page.waitForLoadState('networkidle');

        if (page.url().includes('/paciente/perfil')) {
            // Limpar campo obrigatório e tentar salvar
            const campoEmail = page.locator('input[name="email"]');
            if (await campoEmail.isVisible()) {
                await campoEmail.clear();
                
                const botaoSalvar = page.locator('button[type="submit"]').first();
                if (await botaoSalvar.isVisible()) {
                    await botaoSalvar.click();
                    await page.waitForTimeout(2000);

                    // Verificar se há mensagem de erro
                    const temErro = await page.locator('.error, .text-red, .text-destructive, text=/obrigatório/i').isVisible();
                    if (temErro) {
                        console.log('✅ Validação de formulário funcionando');
                    } else {
                        console.log('⚠️ Validação de formulário não detectada');
                    }
                } else {
                    console.log('⚠️ Botão de salvar não encontrado para teste de validação');
                }
            } else {
                console.log('⚠️ Campo de email não encontrado para teste de validação');
            }
        }
    });

    test('7. Teste de Responsividade Avançada', async ({ page }) => {
        console.log('📱 Testando responsividade avançada...');

        const viewports = [
            { width: 320, height: 568, name: 'iPhone SE' },
            { width: 375, height: 667, name: 'iPhone 8' },
            { width: 768, height: 1024, name: 'iPad' },
            { width: 1024, height: 768, name: 'Desktop Small' }
        ];

        await fazerLogin(page);

        for (const viewport of viewports) {
            console.log(`📱 Testando viewport: ${viewport.name} (${viewport.width}x${viewport.height})`);
            
            await page.setViewportSize({ width: viewport.width, height: viewport.height });
            
            // Testar páginas principais
            const paginas = ['/paciente/dashboard', '/paciente/perfil', '/paciente/agendamentos'];
            
            for (const pagina of paginas) {
                await page.goto(pagina);
                await page.waitForLoadState('networkidle');

                // Verificar overflow horizontal
                const hasHorizontalScroll = await page.evaluate(() => {
                    return document.documentElement.scrollWidth > document.documentElement.clientWidth;
                });

                if (!hasHorizontalScroll) {
                    console.log(`✅ ${pagina} responsiva em ${viewport.name}`);
                } else {
                    console.log(`⚠️ ${pagina} tem overflow horizontal em ${viewport.name}`);
                }
            }
        }
    });

    // Função auxiliar para fazer login
    async function fazerLogin(page: Page) {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');

        await page.fill('input[type="email"], input[name="email"]', PACIENTE_COM_ASSINATURA.email);
        await page.fill('input[type="password"], input[name="password"]', PACIENTE_COM_ASSINATURA.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Verificar se o login foi bem-sucedido
        const currentUrl = page.url();
        if (!currentUrl.includes('/paciente/')) {
            throw new Error('Login falhou - não redirecionou para área do paciente');
        }
    }
});
