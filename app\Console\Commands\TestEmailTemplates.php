<?php

namespace App\Console\Commands;

use App\Mail\BoasVindasMail;
use App\Services\ReactEmailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmailTemplates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {template} {--email=} {--preview}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Testar templates de email React Email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $template = $this->argument('template');
        $email = $this->option('email');
        $preview = $this->option('preview');

        if ($preview) {
            return $this->previewTemplate($template);
        }

        if (!$email) {
            $this->error('Email é obrigatório quando não está em modo preview. Use --email=<EMAIL>');
            return 1;
        }

        return $this->sendTestEmail($template, $email);
    }

    private function previewTemplate(string $template): int
    {
        try {
            $reactEmailService = app(ReactEmailService::class);
            
            $html = match ($template) {
                'boas-vindas' => $reactEmailService->renderBoasVindas(
                    'João Silva',
                    '<EMAIL>',
                    'paciente',
                    'https://f4fisio.com.br/login'
                ),
                'agendamento-confirmado' => $reactEmailService->renderAgendamentoConfirmado([
                    'paciente_nome' => 'Maria Santos',
                    'fisioterapeuta_nome' => 'Dr. Carlos Oliveira',
                    'data_hora' => '15/01/2025 às 14:00',
                    'endereco' => 'Rua das Flores, 123 - São Paulo, SP',
                    'valor' => 'R$ 120,00',
                    'observacoes' => 'Trazer exames de raio-x da coluna',
                    'agendamento_url' => 'https://f4fisio.com.br/agendamentos/123',
                    'whatsapp_fisioterapeuta' => '5511999999999',
                ]),
                'lembrete-agendamento' => $reactEmailService->renderLembreteAgendamento([
                    'paciente_nome' => 'Ana Costa',
                    'fisioterapeuta_nome' => 'Dra. Fernanda Lima',
                    'data_hora' => 'amanhã às 10:00',
                    'endereco' => 'Clínica Saúde Total - Av. Paulista, 1000',
                    'horas_antecedencia' => 24,
                    'valor' => 'R$ 100,00',
                    'agendamento_url' => 'https://f4fisio.com.br/agendamentos/456',
                    'whatsapp_fisioterapeuta' => '5511888888888',
                ]),
                'agendamento-cancelado' => $reactEmailService->renderAgendamentoCancelado([
                    'paciente_nome' => 'Pedro Almeida',
                    'fisioterapeuta_nome' => 'Dr. Roberto Silva',
                    'data_hora' => '20/01/2025 às 16:00',
                    'motivo' => 'Fisioterapeuta teve um imprevisto familiar',
                    'reagendar_url' => 'https://f4fisio.com.br/reagendar/789',
                    'buscar_url' => 'https://f4fisio.com.br/buscar',
                ]),
                default => throw new \InvalidArgumentException("Template '{$template}' não encontrado")
            };

            // Salvar preview em arquivo temporário
            $filename = storage_path("app/email-preview-{$template}-" . date('Y-m-d-H-i-s') . '.html');
            file_put_contents($filename, $html);

            $this->info("Preview do template '{$template}' salvo em: {$filename}");
            $this->info("Abra o arquivo no navegador para visualizar o email.");

            return 0;

        } catch (\Exception $e) {
            $this->error("Erro ao gerar preview: {$e->getMessage()}");
            return 1;
        }
    }

    private function sendTestEmail(string $template, string $email): int
    {
        try {
            switch ($template) {
                case 'boas-vindas':
                    Mail::to($email)->send(new BoasVindasMail(
                        'Usuário Teste',
                        $email,
                        'paciente'
                    ));
                    break;

                default:
                    $this->error("Envio de teste não implementado para o template '{$template}'");
                    return 1;
            }

            $this->info("Email de teste '{$template}' enviado para: {$email}");
            return 0;

        } catch (\Exception $e) {
            $this->error("Erro ao enviar email: {$e->getMessage()}");
            return 1;
        }
    }
}
