<?php

namespace App\Http\Controllers\Afiliado;

use App\Http\Controllers\Controller;
use App\Models\Cupom;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CupomController extends Controller
{
    /**
     * Display a listing of cupons for affiliate
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;

        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }

        // Buscar cupons públicos e exclusivos do afiliado
        $query = Cupom::with(['afiliado', 'createdBy'])
                      ->where(function ($q) use ($afiliado) {
                          $q->where('tipo', 'publico')
                            ->orWhere(function ($subQ) use ($afiliado) {
                                $subQ->where('tipo', 'afiliado_exclusivo')
                                     ->where('afiliado_id', $afiliado->id);
                            });
                      })
                      ->validos();

        // Filtros
        if ($request->filled('tipo') && $request->tipo !== 'all') {
            if ($request->tipo === 'meus_exclusivos') {
                $query->where('tipo', 'afiliado_exclusivo')
                      ->where('afiliado_id', $afiliado->id);
            } elseif ($request->tipo === 'publicos') {
                $query->where('tipo', 'publico');
            }
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('codigo', 'like', '%' . $request->search . '%')
                  ->orWhere('nome', 'like', '%' . $request->search . '%');
            });
        }

        $cupons = $query->orderBy('created_at', 'desc')
                       ->paginate(10)
                       ->withQueryString();

        // Estatísticas
        $stats = [
            'total_cupons' => Cupom::where(function ($q) use ($afiliado) {
                $q->where('tipo', 'publico')
                  ->orWhere(function ($subQ) use ($afiliado) {
                      $subQ->where('tipo', 'afiliado_exclusivo')
                           ->where('afiliado_id', $afiliado->id);
                  });
            })->validos()->count(),

            'meus_exclusivos' => Cupom::where('tipo', 'afiliado_exclusivo')
                                     ->where('afiliado_id', $afiliado->id)
                                     ->validos()
                                     ->count(),

            'publicos' => Cupom::where('tipo', 'publico')
                              ->validos()
                              ->count(),
        ];

        return Inertia::render('afiliado/cupons', [
            'cupons' => $cupons,
            'stats' => $stats,
            'filters' => $request->only(['tipo', 'search']),
            'afiliado' => $afiliado,
        ]);
    }

    /**
     * Show cupom details
     */
    public function show(Cupom $cupom)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;

        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }

        // Verificar se o afiliado tem acesso a este cupom
        if ($cupom->tipo === 'afiliado_exclusivo' && $cupom->afiliado_id !== $afiliado->id) {
            abort(403, 'Você não tem acesso a este cupom.');
        }

        $cupom->load(['afiliado', 'createdBy']);

        return Inertia::render('afiliado/cupons/show', [
            'cupom' => $cupom,
            'afiliado' => $afiliado,
        ]);
    }

    /**
     * Get cupom link for sharing
     */
    public function getLink(Cupom $cupom)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;

        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return response()->json(['error' => 'Acesso negado'], 403);
        }

        // Verificar se o afiliado tem acesso a este cupom
        if ($cupom->tipo === 'afiliado_exclusivo' && $cupom->afiliado_id !== $afiliado->id) {
            return response()->json(['error' => 'Você não tem acesso a este cupom'], 403);
        }

        if (!$cupom->isValido()) {
            return response()->json(['error' => 'Este cupom não está mais válido'], 400);
        }

        // Gerar link com código do cupom e referência do afiliado
        $baseUrl = config('app.url');
        $link = $baseUrl . '/planos?cupom=' . $cupom->codigo . '&ref=' . $afiliado->codigo_afiliado;

        return response()->json([
            'link' => $link,
            'codigo' => $cupom->codigo,
            'descricao' => $cupom->descricao_desconto,
        ]);
    }
}
