import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { BarChart3, Building2, Calendar, DollarSign, Eye, FileText, UserCheck, Users } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard Administrativo',
        href: '/admin/dashboard',
    },
];

interface Stats {
    totalPacientes: number;
    totalFisioterapeutas: number;
    totalEstabelecimentos: number;
    agendamentosHoje: number;
    receitaMensal: number;
}

interface Agendamento {
    id: number;
    paciente: string;
    fisioterapeuta: string;
    data_hora: string;
    status: string;
    endereco: string;
}

interface EstatisticaMensal {
    mes: string;
    agendamentos: number;
    receita: number;
}

interface Props {
    stats?: Stats;
    agendamentosRecentes?: Agendamento[];
    estatisticasMensais?: EstatisticaMensal[];
}

const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
    }).format(value);
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const getStatusBadge = (status: string) => {
    const statusConfig = {
        agendado: { label: 'Agendado', variant: 'default' as const },
        confirmado: { label: 'Confirmado', variant: 'default' as const },
        em_andamento: { label: 'Em Andamento', variant: 'secondary' as const },
        concluido: { label: 'Concluído', variant: 'default' as const },
        cancelado: { label: 'Cancelado', variant: 'destructive' as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'default' as const };
    return <Badge variant={config.variant}>{config.label}</Badge>;
};

export default function AdminDashboard({ stats, agendamentosRecentes, estatisticasMensais }: Props) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard Administrativo" />
            <div className="flex h-full flex-1 flex-col gap-6 overflow-x-auto p-4 md:p-6">
                {/* Header Section */}
                <div className="space-y-2">
                    <h1 className="text-2xl font-bold tracking-tight text-foreground md:text-3xl">Dashboard Administrativo</h1>
                    <p className="text-base text-muted-foreground md:text-lg">Gerencie todo o sistema F4 Fisio</p>
                </div>

                {/* Cards de estatísticas */}
                <div className="grid gap-4 sm:grid-cols-2 md:gap-6 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Pacientes</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.totalPacientes ?? 0}</div>
                            <p className="text-xs text-muted-foreground">Pacientes cadastrados</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Fisioterapeutas</CardTitle>
                            <UserCheck className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.totalFisioterapeutas ?? 0}</div>
                            <p className="text-xs text-muted-foreground">Profissionais ativos</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Estabelecimentos</CardTitle>
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.totalEstabelecimentos ?? 0}</div>
                            <p className="text-xs text-muted-foreground">Empresas ativas</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Receita Mensal</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats?.receitaMensal ?? 0)}</div>
                            <p className="text-xs text-muted-foreground">Faturamento do mês</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Seções de conteúdo */}
                <div className="grid gap-4 md:gap-6 lg:grid-cols-2">
                    {/* Agendamentos Recentes */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Agendamentos Recentes
                            </CardTitle>
                            <CardDescription>Últimos agendamentos realizados no sistema</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {(agendamentosRecentes?.length ?? 0) > 0 ? (
                                <div className="space-y-4">
                                    {agendamentosRecentes?.slice(0, 5).map((agendamento) => (
                                        <div key={agendamento.id} className="flex items-center justify-between rounded-lg border p-3">
                                            <div className="space-y-1">
                                                <p className="text-sm font-medium">{agendamento.paciente}</p>
                                                <p className="text-xs text-muted-foreground">com {agendamento.fisioterapeuta}</p>
                                                <p className="text-xs text-muted-foreground">{formatDate(agendamento.data_hora)}</p>
                                            </div>
                                            <div className="text-right">{getStatusBadge(agendamento.status)}</div>
                                        </div>
                                    ))}
                                    <div className="pt-2">
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href="/admin/agendamentos">
                                                <Eye className="mr-2 h-4 w-4" />
                                                Ver Todos
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center py-8 text-center">
                                    <Calendar className="mb-2 h-8 w-8 text-muted-foreground" />
                                    <p className="text-sm font-medium text-muted-foreground">Nenhum agendamento encontrado</p>
                                    <p className="text-xs text-muted-foreground">Os agendamentos aparecerão aqui quando forem criados</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Estatísticas Mensais */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <BarChart3 className="h-5 w-5" />
                                Evolução Mensal
                            </CardTitle>
                            <CardDescription>Agendamentos e receita dos últimos 6 meses</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {(estatisticasMensais?.length ?? 0) > 0 ? (
                                <div className="space-y-4">
                                    {estatisticasMensais?.map((estatistica, index) => (
                                        <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                                            <div className="space-y-1">
                                                <p className="text-sm font-medium">{estatistica.mes}</p>
                                                <p className="text-xs text-muted-foreground">{estatistica.agendamentos} agendamentos</p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium">{formatCurrency(estatistica.receita)}</p>
                                            </div>
                                        </div>
                                    ))}
                                    <div className="pt-2">
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href="/admin/relatorios">
                                                <FileText className="mr-2 h-4 w-4" />
                                                Ver Relatórios
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center py-8 text-center">
                                    <BarChart3 className="mb-2 h-8 w-8 text-muted-foreground" />
                                    <p className="text-sm font-medium text-muted-foreground">Dados insuficientes</p>
                                    <p className="text-xs text-muted-foreground">Estatísticas aparecerão conforme o uso do sistema</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Ações Rápidas */}
                <Card>
                    <CardHeader>
                        <CardTitle>Ações Rápidas</CardTitle>
                        <CardDescription>Acesso rápido às principais funcionalidades administrativas</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
                            <Button variant="outline" asChild>
                                <Link href="/admin/usuarios">
                                    <Users className="mr-2 h-4 w-4" />
                                    Gerenciar Usuários
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/admin/fisioterapeutas">
                                    <UserCheck className="mr-2 h-4 w-4" />
                                    Fisioterapeutas
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/admin/estabelecimentos">
                                    <Building2 className="mr-2 h-4 w-4" />
                                    Estabelecimentos
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/admin/pagamentos">
                                    <DollarSign className="mr-2 h-4 w-4" />
                                    Pagamentos
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/admin/relatorios">
                                    <FileText className="mr-2 h-4 w-4" />
                                    Relatórios
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
