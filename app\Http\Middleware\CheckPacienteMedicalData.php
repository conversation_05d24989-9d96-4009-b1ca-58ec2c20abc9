<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPacienteMedicalData
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Só aplica para pacientes com assinatura ativa
        if ($user->role !== 'paciente' || !$user->has_subscription) {
            return $next($request);
        }

        // Verificar se o paciente tem dados médicos essenciais
        if (!$this->hasMedicalDataComplete($user)) {
            // Permitir acesso apenas às rotas de onboarding, perfil, planos, logout e configurações
            $allowedRoutes = [
                'paciente.onboarding',
                'paciente.onboarding.store',
                'paciente.perfil',
                'paciente.perfil.update',
                'paciente.perfil.avatar.upload',
                'paciente.perfil.avatar.remove',
                'paciente.planos',
                'paciente.plano.index',
                'paciente.plano.subscribe',
                'paciente.plano.cancel',
                'paciente.plano.change',
                'paciente.afiliados',
                'logout',
                'profile.edit',
                'profile.update',
                'profile.destroy',
                'password.edit',
                'password.update',
                'appearance'
            ];

            if (!in_array($request->route()->getName(), $allowedRoutes)) {
                return redirect()->route('paciente.onboarding')
                    ->with('warning', 'Você precisa completar seus dados médicos para continuar.');
            }
        }

        return $next($request);
    }

    /**
     * Check if paciente has complete medical data
     */
    private function hasMedicalDataComplete($user): bool
    {
        // Verificar campos obrigatórios básicos
        $requiredFields = [
            'name',
            'phone',
            'birth_date',
            'gender',
            'address',
            'city',
            'state',
            'zip_code'
        ];

        foreach ($requiredFields as $field) {
            $value = $user->$field;
            
            // Verificar se o campo está vazio
            if (empty($value)) {
                return false;
            }
        }

        // Verificar se tem pelo menos informações médicas básicas
        // Pelo menos um dos dois deve estar preenchido
        if (empty($user->medical_history) && empty($user->emergency_contact)) {
            return false;
        }

        // Se tem histórico médico, deve ter pelo menos 20 caracteres
        if (!empty($user->medical_history) && strlen($user->medical_history) < 20) {
            return false;
        }

        // Se tem contato de emergência, deve ter pelo menos 20 caracteres
        if (!empty($user->emergency_contact) && strlen($user->emergency_contact) < 20) {
            return false;
        }

        // Verificar se a data de nascimento é válida (não pode ser no futuro e deve ter pelo menos 16 anos)
        if ($user->birth_date) {
            $birthDate = \Carbon\Carbon::parse($user->birth_date);
            $age = $birthDate->diffInYears(now());
            
            if ($age < 16) {
                return false;
            }
        }

        return true;
    }
}
