<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class HorarioExcecao extends Model
{
    use HasFactory;

    protected $table = 'horarios_excecoes';

    protected $fillable = [
        'fisioterapeuta_id',
        'tipo',
        'data_inicio',
        'data_fim',
        'dia_semana',
        'hora_inicio',
        'hora_fim',
        'periodo_nome',
        'acao',
        'motivo',
        'ativo',
    ];

    protected $casts = [
        'data_inicio' => 'date',
        'data_fim' => 'date',
        'hora_inicio' => 'datetime:H:i',
        'hora_fim' => 'datetime:H:i',
        'ativo' => 'boolean',
    ];

    // Relacionamentos
    public function fisioterapeuta()
    {
        return $this->belongsTo(User::class, 'fisioterapeuta_id');
    }

    // Scopes
    public function scopeAtivas($query)
    {
        return $query->where('ativo', true);
    }

    public function scopePorFisioterapeuta($query, $fisioterapeutaId)
    {
        return $query->where('fisioterapeuta_id', $fisioterapeutaId);
    }

    public function scopePorPeriodo($query, $dataInicio, $dataFim = null)
    {
        $dataFim = $dataFim ?? $dataInicio;
        
        return $query->where(function ($q) use ($dataInicio, $dataFim) {
            $q->where(function ($subQ) use ($dataInicio, $dataFim) {
                // Exceções que começam no período
                $subQ->whereBetween('data_inicio', [$dataInicio, $dataFim]);
            })->orWhere(function ($subQ) use ($dataInicio, $dataFim) {
                // Exceções que terminam no período
                $subQ->whereBetween('data_fim', [$dataInicio, $dataFim]);
            })->orWhere(function ($subQ) use ($dataInicio, $dataFim) {
                // Exceções que englobam o período
                $subQ->where('data_inicio', '<=', $dataInicio)
                     ->where('data_fim', '>=', $dataFim);
            });
        });
    }

    public function scopePorTipo($query, $tipo)
    {
        return $query->where('tipo', $tipo);
    }

    // Métodos auxiliares
    public function aplicaParaData($data)
    {
        $dataCarbon = Carbon::parse($data);
        
        // Verifica se a data está no período da exceção
        if ($dataCarbon->lt($this->data_inicio)) {
            return false;
        }

        if ($this->data_fim && $dataCarbon->gt($this->data_fim)) {
            return false;
        }

        // Se especifica dia da semana, verifica se coincide
        if ($this->dia_semana !== null && $dataCarbon->dayOfWeek !== $this->dia_semana) {
            return false;
        }

        return true;
    }

    public function isDisponivel($data, $hora = null)
    {
        if (!$this->ativo || !$this->aplicaParaData($data)) {
            return false;
        }

        // Se a ação é indisponível, retorna false
        if ($this->acao === 'indisponivel') {
            return false;
        }

        // Se não especifica horário, considera disponível o dia todo
        if (!$this->hora_inicio || !$this->hora_fim) {
            return $this->acao === 'disponivel';
        }

        // Se especifica horário, verifica se a hora está no período
        if ($hora) {
            $horaCarbon = Carbon::parse($hora);
            $horaInicio = Carbon::parse($this->hora_inicio);
            $horaFim = Carbon::parse($this->hora_fim);

            return $horaCarbon->between($horaInicio, $horaFim);
        }

        return true;
    }

    public function getFormattedPeriodoAttribute()
    {
        $inicio = $this->data_inicio->format('d/m/Y');
        $fim = $this->data_fim ? $this->data_fim->format('d/m/Y') : $inicio;
        
        return $inicio === $fim ? $inicio : $inicio . ' - ' . $fim;
    }

    public function getFormattedHorarioAttribute()
    {
        if (!$this->hora_inicio || !$this->hora_fim) {
            return 'Dia todo';
        }

        return Carbon::parse($this->hora_inicio)->format('H:i') . ' - ' . 
               Carbon::parse($this->hora_fim)->format('H:i');
    }
}
