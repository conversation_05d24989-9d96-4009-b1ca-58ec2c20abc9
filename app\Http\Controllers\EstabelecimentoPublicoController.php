<?php

namespace App\Http\Controllers;

use App\Models\Estabelecimento;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EstabelecimentoPublicoController extends Controller
{
    public function show($slug)
    {
        $estabelecimento = Estabelecimento::where('slug', $slug)
            ->where('ativo', true)
            ->where('plano_ativo', true)
            ->with(['user', 'imagens'])
            ->firstOrFail();

        // Buscar estabelecimentos relacionados (mesma categoria e cidade)
        $relacionados = Estabelecimento::where('categoria', $estabelecimento->categoria)
            ->where('cidade', $estabelecimento->cidade)
            ->where('id', '!=', $estabelecimento->id)
            ->where('ativo', true)
            ->where('plano_ativo', true)
            ->limit(3)
            ->get();

        // Dados estruturados para SEO
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $estabelecimento->nome,
            'description' => $estabelecimento->descricao,
            'url' => url("/estabelecimento/{$estabelecimento->slug}"),
            'telephone' => $estabelecimento->telefone,
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => $estabelecimento->endereco,
                'addressLocality' => $estabelecimento->cidade,
                'addressRegion' => $estabelecimento->estado,
                'postalCode' => $estabelecimento->cep,
                'addressCountry' => 'BR'
            ],
            'geo' => [
                '@type' => 'GeoCoordinates',
                'latitude' => $estabelecimento->latitude,
                'longitude' => $estabelecimento->longitude
            ],
            'aggregateRating' => [
                '@type' => 'AggregateRating',
                'ratingValue' => $estabelecimento->avaliacao_media,
                'reviewCount' => $estabelecimento->total_avaliacoes
            ],
            'openingHours' => $this->formatOpeningHours($estabelecimento->horario_funcionamento),
            'priceRange' => '$$',
            'image' => asset('images/estabelecimento-default.jpg'),
            'sameAs' => array_filter([
                $estabelecimento->site,
                $estabelecimento->instagram ? "https://instagram.com/{$estabelecimento->instagram}" : null,
                $estabelecimento->facebook ? "https://facebook.com/{$estabelecimento->facebook}" : null,
            ])
        ];

        // Meta tags dinâmicas
        $metaTitle = "{$estabelecimento->nome} - {$estabelecimento->cidade}, {$estabelecimento->estado} | F4 Fisio";
        $metaDescription = "{$estabelecimento->descricao} Localizado em {$estabelecimento->cidade}, {$estabelecimento->estado}. Contato direto via WhatsApp. Avaliação: {$estabelecimento->avaliacao_media}/5 ({$estabelecimento->total_avaliacoes} avaliações).";
        
        return Inertia::render('estabelecimento/show', [
            'estabelecimento' => [
                'id' => $estabelecimento->id,
                'nome' => $estabelecimento->nome,
                'slug' => $estabelecimento->slug,
                'categoria' => $estabelecimento->categoria,
                'descricao' => $estabelecimento->descricao,
                'telefone' => $estabelecimento->telefone,
                'whatsapp' => $estabelecimento->whatsapp,
                'whatsapp_link' => $estabelecimento->whatsapp_link,
                'email' => $estabelecimento->email,
                'endereco_completo' => $estabelecimento->endereco_completo,
                'endereco' => $estabelecimento->endereco,
                'cidade' => $estabelecimento->cidade,
                'estado' => $estabelecimento->estado,
                'cep' => $estabelecimento->cep,
                'horario_funcionamento' => $estabelecimento->horario_funcionamento,
                'servicos_oferecidos' => $estabelecimento->servicos_oferecidos,
                'site' => $estabelecimento->site,
                'instagram' => $estabelecimento->instagram,
                'facebook' => $estabelecimento->facebook,
                'avaliacao_media' => $estabelecimento->avaliacao_media,
                'total_avaliacoes' => $estabelecimento->total_avaliacoes,
                'latitude' => $estabelecimento->latitude,
                'longitude' => $estabelecimento->longitude,
                'imagens' => $estabelecimento->imagens->map(function ($imagem) {
                    return [
                        'id' => $imagem->id,
                        'nome_arquivo' => $imagem->nome_arquivo,
                        'nome_original' => $imagem->nome_original,
                        'descricao' => $imagem->descricao,
                        'principal' => $imagem->principal,
                        'url' => $imagem->url,
                    ];
                }),
            ],
            'relacionados' => $relacionados->map(function ($item) {
                return [
                    'id' => $item->id,
                    'nome' => $item->nome,
                    'slug' => $item->slug,
                    'categoria' => $item->categoria,
                    'descricao' => $item->descricao,
                    'endereco_completo' => $item->endereco_completo,
                    'avaliacao_media' => $item->avaliacao_media,
                    'total_avaliacoes' => $item->total_avaliacoes,
                ];
            }),
            'structuredData' => $structuredData,
            'meta' => [
                'title' => $metaTitle,
                'description' => $metaDescription,
                'canonical' => url("/estabelecimento/{$estabelecimento->slug}"),
                'og_title' => $estabelecimento->nome,
                'og_description' => $metaDescription,
                'og_image' => asset('images/estabelecimento-default.jpg'),
                'og_url' => url("/estabelecimento/{$estabelecimento->slug}"),
            ]
        ]);
    }

    private function formatOpeningHours($horarioFuncionamento)
    {
        if (!$horarioFuncionamento || !is_array($horarioFuncionamento)) {
            return [];
        }

        $diasSemana = [
            'segunda' => 'Mo',
            'terca' => 'Tu', 
            'quarta' => 'We',
            'quinta' => 'Th',
            'sexta' => 'Fr',
            'sabado' => 'Sa',
            'domingo' => 'Su'
        ];

        $openingHours = [];
        foreach ($horarioFuncionamento as $dia => $horario) {
            if (isset($diasSemana[$dia]) && isset($horario['abertura']) && isset($horario['fechamento'])) {
                $openingHours[] = $diasSemana[$dia] . ' ' . $horario['abertura'] . '-' . $horario['fechamento'];
            }
        }

        return $openingHours;
    }
}
