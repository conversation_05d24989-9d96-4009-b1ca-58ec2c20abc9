import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { formatCEP, formatCNPJ, formatCPF, formatPhone } from '@/lib/validations';
import { AlertCircle, CheckCircle, HelpCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';

export interface ValidatedInputProps extends Omit<React.ComponentProps<typeof Input>, 'onChange' | 'onBlur'> {
    label?: string;
    error?: string;
    success?: boolean;
    tooltip?: string;
    required?: boolean;
    autoFormat?: 'cpf' | 'cnpj' | 'phone' | 'cep';
    onChange?: (value: string) => void;
    onBlur?: (value: string) => void;
    showValidationIcon?: boolean;
}

export function ValidatedInput({
    label,
    error,
    success,
    tooltip,
    required,
    autoFormat,
    onChange,
    onBlur,
    showValidationIcon = true,
    className,
    ...props
}: ValidatedInputProps) {
    const [internalValue, setInternalValue] = useState(String(props.value || ''));
    const [isFocused, setIsFocused] = useState(false);

    useEffect(() => {
        if (props.value !== undefined) {
            setInternalValue(String(props.value));
        }
    }, [props.value]);

    const formatValue = (value: string): string => {
        if (!autoFormat) return value;

        switch (autoFormat) {
            case 'cpf':
                return formatCPF(value);
            case 'cnpj':
                return formatCNPJ(value);
            case 'phone':
                return formatPhone(value);
            case 'cep':
                return formatCEP(value);
            default:
                return value;
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let value = e.target.value;

        // Aplica formatação se necessário
        if (autoFormat) {
            value = formatValue(value);
        }

        setInternalValue(value);
        onChange?.(value);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        onBlur?.(e.target.value);
    };

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(true);
        props.onFocus?.(e);
    };

    const hasError = Boolean(error);
    const hasSuccess = success && !hasError && !isFocused && internalValue && internalValue.trim().length > 0;

    return (
        <div className="space-y-2">
            {label && (
                <div className="flex items-center gap-2">
                    <Label htmlFor={props.id} className={cn(required && "after:ml-0.5 after:text-red-500 after:content-['*']")}>
                        {label}
                    </Label>
                    {tooltip && (
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p className="max-w-xs">{tooltip}</p>
                            </TooltipContent>
                        </Tooltip>
                    )}
                </div>
            )}

            <div className="relative">
                <Input
                    {...props}
                    value={internalValue}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    onFocus={handleFocus}
                    className={cn(
                        hasError && 'border-red-500 focus-visible:ring-red-500/20',
                        hasSuccess && 'border-green-500 focus-visible:ring-green-500/20',
                        showValidationIcon && (hasError || hasSuccess) && 'pr-10',
                        className,
                    )}
                    aria-invalid={hasError}
                    aria-describedby={error ? `${props.id}-error` : undefined}
                />

                {showValidationIcon && (hasError || hasSuccess) && (
                    <div className="absolute top-1/2 right-3 -translate-y-1/2">
                        {hasError && <AlertCircle className="h-4 w-4 text-red-500" />}
                        {hasSuccess && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                )}
            </div>

            {error && (
                <p id={`${props.id}-error`} className="flex items-center gap-1 text-sm text-red-600">
                    <AlertCircle className="h-3 w-3" />
                    {error}
                </p>
            )}
        </div>
    );
}
