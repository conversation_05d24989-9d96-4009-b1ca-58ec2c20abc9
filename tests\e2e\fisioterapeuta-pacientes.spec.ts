import { test, expect } from '@playwright/test';
import { 
  ensureFisioterapeutaAuthenticated,
  navigateToFisioterapeutaPage,
  waitForPageLoad,
  waitForNotification,
  checkResponsiveness
} from './helpers/fisioterapeuta-helpers';

test.describe('Fisioterapeuta - Gerenciamento de Pacientes', () => {
  
  test.beforeEach(async ({ page }) => {
    await ensureFisioterapeutaAuthenticated(page);
    await navigateToFisioterapeutaPage(page, 'pacientes');
  });

  test('deve carregar página de pacientes corretamente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Verificar se o título da página está correto
    await expect(page.locator('h1, h2')).toContainText(/pacientes/i);
    
    // Verificar se há uma lista de pacientes ou mensagem de "nenhum paciente"
    const pacientesList = page.locator('.pacientes-list, [data-testid="pacientes-list"]');
    const noPacientesMessage = page.locator('text="Nenhum paciente", text="Sem pacientes"');
    
    const hasPacientesList = await pacientesList.count() > 0;
    const hasNoPacientesMessage = await noPacientesMessage.count() > 0;
    
    expect(hasPacientesList || hasNoPacientesMessage).toBeTruthy();
  });

  test('deve exibir estatísticas de pacientes', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar estatísticas como total de pacientes, novos no mês, etc.
    const statsCards = page.locator('.stats-card, [data-testid="stats"], .card');
    
    if (await statsCards.count() > 0) {
      await expect(statsCards.first()).toBeVisible();
      
      // Verificar se há números nas estatísticas
      const numbers = page.locator('text=/\\d+/');
      await expect(numbers).toHaveCountGreaterThan(0);
    }
  });

  test('deve permitir buscar pacientes por nome', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar campo de busca
    const searchInput = page.locator('input[name="search"], input[placeholder*="buscar"], input[placeholder*="paciente"]');
    
    if (await searchInput.count() > 0) {
      await searchInput.fill('João');
      await page.keyboard.press('Enter');
      
      // Aguardar resultados da busca
      await page.waitForTimeout(1000);
      
      // Verificar se a busca foi executada
      await expect(page.locator('body')).toBeVisible();
    }
  });

  test('deve permitir filtrar pacientes por status', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar filtro de status
    const statusFilter = page.locator('select[name*="status"], [data-testid="status-filter"]');
    
    if (await statusFilter.count() > 0) {
      // Testar diferentes status
      const statusOptions = ['ativo', 'inativo', 'todos'];
      
      for (const status of statusOptions) {
        await statusFilter.selectOption(status);
        await page.waitForTimeout(1000);
        
        // Verificar se a página foi atualizada
        await expect(page.locator('body')).toBeVisible();
      }
    }
  });

  test('deve exibir lista de pacientes com informações básicas', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar itens de pacientes na lista
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      const firstPaciente = pacienteItems.first();
      
      // Verificar se há informações básicas do paciente
      await expect(firstPaciente).toBeVisible();
      
      // Verificar se contém nome, data do último agendamento, etc.
      const pacienteText = await firstPaciente.textContent();
      
      // Verificar se contém pelo menos algumas informações esperadas
      expect(pacienteText).toBeTruthy();
      expect(pacienteText!.length).toBeGreaterThan(0);
    }
  });

  test('deve permitir visualizar detalhes do paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar pacientes clicáveis
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      // Clicar no primeiro paciente
      await pacienteItems.first().click();
      
      // Verificar se navegou para página de detalhes ou abriu modal
      const detailsPage = page.locator('h1:has-text("Detalhes"), h2:has-text("Paciente")');
      const modal = page.locator('.modal, .dialog, [role="dialog"]');
      
      const hasDetailsPage = await detailsPage.count() > 0;
      const hasModal = await modal.count() > 0;
      
      expect(hasDetailsPage || hasModal).toBeTruthy();
    }
  });

  test('deve exibir histórico de agendamentos do paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Navegar para detalhes de um paciente
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      await pacienteItems.first().click();
      await waitForPageLoad(page);
      
      // Procurar seção de histórico de agendamentos
      const historicoSection = page.locator('text="Histórico", text="Agendamentos", [data-testid="historico"]');
      
      if (await historicoSection.count() > 0) {
        await expect(historicoSection).toBeVisible();
        
        // Verificar se há lista de agendamentos
        const agendamentosList = page.locator('.agendamento-item, .appointment-item, [data-testid="agendamento"]');
        const noAgendamentosMessage = page.locator('text="Nenhum agendamento", text="Sem histórico"');
        
        const hasAgendamentos = await agendamentosList.count() > 0;
        const hasNoAgendamentosMessage = await noAgendamentosMessage.count() > 0;
        
        expect(hasAgendamentos || hasNoAgendamentosMessage).toBeTruthy();
      }
    }
  });

  test('deve permitir adicionar notas sobre o paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Navegar para detalhes de um paciente
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      await pacienteItems.first().click();
      await waitForPageLoad(page);
      
      // Procurar campo de notas
      const notasField = page.locator('textarea[name="notas"], textarea[placeholder*="nota"]');
      
      if (await notasField.count() > 0) {
        await notasField.fill('Paciente apresenta boa evolução no tratamento. Recomenda-se continuar com exercícios de fortalecimento.');
        
        // Procurar botão de salvar notas
        const salvarButton = page.locator('button:has-text("Salvar"), button:has-text("Adicionar Nota")');
        
        if (await salvarButton.count() > 0) {
          await salvarButton.click();
          
          // Aguardar notificação de sucesso
          await waitForNotification(page);
        }
      }
    }
  });

  test('deve exibir informações de contato do paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Navegar para detalhes de um paciente
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      await pacienteItems.first().click();
      await waitForPageLoad(page);
      
      // Procurar informações de contato
      const contatoInfo = page.locator('text=/\(\d{2}\)\s*\d{4,5}-\d{4}/, text=/@/, [data-testid="contato"]');
      
      if (await contatoInfo.count() > 0) {
        await expect(contatoInfo.first()).toBeVisible();
      }
    }
  });

  test('deve exibir dados médicos relevantes do paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Navegar para detalhes de um paciente
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      await pacienteItems.first().click();
      await waitForPageLoad(page);
      
      // Procurar seção de dados médicos
      const dadosMedicosSection = page.locator('text="Dados Médicos", text="Informações Médicas", [data-testid="dados-medicos"]');
      
      if (await dadosMedicosSection.count() > 0) {
        await expect(dadosMedicosSection).toBeVisible();
      }
    }
  });

  test('deve permitir visualizar avaliações do paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Navegar para detalhes de um paciente
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      await pacienteItems.first().click();
      await waitForPageLoad(page);
      
      // Procurar seção de avaliações
      const avaliacoesSection = page.locator('text="Avaliações", text="Feedback", [data-testid="avaliacoes"]');
      
      if (await avaliacoesSection.count() > 0) {
        await expect(avaliacoesSection).toBeVisible();
      }
    }
  });

  test('deve permitir agendar nova sessão para o paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Navegar para detalhes de um paciente
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      await pacienteItems.first().click();
      await waitForPageLoad(page);
      
      // Procurar botão de agendar nova sessão
      const agendarButton = page.locator('button:has-text("Agendar"), button:has-text("Nova Sessão")');
      
      if (await agendarButton.count() > 0) {
        await agendarButton.click();
        
        // Verificar se abriu modal ou navegou para página de agendamento
        const agendamentoModal = page.locator('.modal, .dialog, [role="dialog"]');
        const agendamentoPage = page.locator('h1:has-text("Agendar"), h2:has-text("Novo Agendamento")');
        
        const hasModal = await agendamentoModal.count() > 0;
        const hasPage = await agendamentoPage.count() > 0;
        
        expect(hasModal || hasPage).toBeTruthy();
      }
    }
  });

  test('deve permitir filtrar por período de último atendimento', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar filtros de data
    const dateFilters = page.locator('input[type="date"], [data-testid="date-filter"]');
    
    if (await dateFilters.count() > 0) {
      const today = new Date().toISOString().split('T')[0];
      const lastWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      // Definir período
      await dateFilters.first().fill(lastWeek);
      if (await dateFilters.count() > 1) {
        await dateFilters.nth(1).fill(today);
      }
      
      // Aguardar atualização da lista
      await page.waitForTimeout(1000);
      
      // Verificar se a página foi atualizada
      await expect(page.locator('body')).toBeVisible();
    }
  });

  test('deve exibir estatísticas de evolução do paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Navegar para detalhes de um paciente
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      await pacienteItems.first().click();
      await waitForPageLoad(page);
      
      // Procurar gráficos ou estatísticas de evolução
      const evolucaoSection = page.locator('text="Evolução", text="Progresso", [data-testid="evolucao"]');
      
      if (await evolucaoSection.count() > 0) {
        await expect(evolucaoSection).toBeVisible();
      }
    }
  });

  test('deve permitir exportar dados do paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Navegar para detalhes de um paciente
    const pacienteItems = page.locator('.paciente-item, .patient-item, [data-testid="paciente"]');
    
    if (await pacienteItems.count() > 0) {
      await pacienteItems.first().click();
      await waitForPageLoad(page);
      
      // Procurar botão de exportar
      const exportButton = page.locator('button:has-text("Exportar"), button:has-text("Download")');
      
      if (await exportButton.count() > 0) {
        await exportButton.click();
        
        // Aguardar download ou modal de exportação
        await page.waitForTimeout(2000);
      }
    }
  });

  test('deve ser responsivo em diferentes tamanhos de tela', async ({ page }) => {
    await checkResponsiveness(page);
    
    // Verificar se elementos principais ainda estão visíveis em mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await waitForPageLoad(page);
    
    await expect(page.locator('h1, h2')).toBeVisible();
  });

  test('deve permitir paginação da lista de pacientes', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar controles de paginação
    const paginationControls = page.locator('.pagination, [data-testid="pagination"]');
    
    if (await paginationControls.count() > 0) {
      await expect(paginationControls).toBeVisible();
      
      // Procurar botão de próxima página
      const nextButton = page.locator('button:has-text("Próxima"), button:has-text("Next"), a:has-text("›")');
      
      if (await nextButton.count() > 0) {
        await nextButton.click();
        await waitForPageLoad(page);
        
        // Verificar se a página mudou
        await expect(page.locator('body')).toBeVisible();
      }
    }
  });

  test('deve exibir indicadores visuais de status do paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar indicadores de status (ativo, inativo, etc.)
    const statusIndicators = page.locator('.status, .badge, .chip, [data-testid="status"]');
    
    if (await statusIndicators.count() > 0) {
      await expect(statusIndicators.first()).toBeVisible();
    }
  });
});
