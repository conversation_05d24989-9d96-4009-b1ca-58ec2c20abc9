<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('avaliacoes', function (Blueprint $table) {
            // Novas colunas de avaliação detalhada
            $table->tinyInteger('nota_geral')->nullable()->after('fisioterapeuta_id');
            $table->tinyInteger('nota_pontualidade')->nullable()->after('nota_geral');
            $table->tinyInteger('nota_profissionalismo')->nullable()->after('nota_pontualidade');
            $table->tinyInteger('nota_eficacia')->nullable()->after('nota_profissionalismo');
            $table->text('comentario')->nullable()->after('nota_eficacia');
            $table->boolean('recomendaria')->default(true)->after('comentario');

            // Pontos positivos e negativos (JSON)
            $table->json('pontos_positivos')->nullable()->after('recomendaria');
            $table->json('pontos_melhorar')->nullable()->after('pontos_positivos');

            // Sistema de moderação
            $table->boolean('anonima')->default(false)->after('pontos_melhorar');
            $table->boolean('aprovada')->default(false)->after('anonima');
            $table->timestamp('aprovada_em')->nullable()->after('aprovada');
            $table->foreignId('aprovada_por')->nullable()->constrained('users')->onDelete('set null')->after('aprovada_em');

            // Soft deletes
            $table->softDeletes();

            // Índices para performance
            $table->index(['fisioterapeuta_id', 'aprovada']);
            $table->index(['paciente_id', 'created_at']);
            $table->index('nota_geral');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('avaliacoes', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropForeign(['aprovada_por']);
            $table->dropColumn([
                'nota_geral',
                'nota_pontualidade',
                'nota_profissionalismo',
                'nota_eficacia',
                'comentario',
                'recomendaria',
                'pontos_positivos',
                'pontos_melhorar',
                'anonima',
                'aprovada',
                'aprovada_em',
                'aprovada_por'
            ]);
        });
    }
};
