<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Estabelecimento;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Rota para rastrear cliques de contato
Route::post('/estabelecimento/{slug}/contato', function (Request $request, $slug) {
    $estabelecimento = Estabelecimento::where('slug', $slug)
        ->where('ativo', true)
        ->where('plano_ativo', true)
        ->first();
    
    if (!$estabelecimento) {
        return response()->json(['error' => 'Estabelecimento não encontrado'], 404);
    }
    
    $tipo = $request->input('tipo', 'whatsapp'); // whatsapp ou telefone
    $estabelecimento->incrementarContato($tipo);
    
    return response()->json(['success' => true]);
});
