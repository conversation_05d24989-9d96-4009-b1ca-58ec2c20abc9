import { test, expect } from '@playwright/test';
import { 
  loginAsFisioterapeuta, 
  completeFisioterapeutaSetup, 
  defaultFisioterapeutaData,
  verifyFisioterapeutaLayout,
  waitForPageLoad
} from './helpers/fisioterapeuta-helpers';

test.describe('Fisioterapeuta - Autenticação e Setup', () => {
  
  test.beforeEach(async ({ page }) => {
    // Limpar cookies e storage antes de cada teste
    await page.context().clearCookies();
    await page.context().clearPermissions();
  });

  test('deve fazer login como fisioterapeuta com sucesso', async ({ page }) => {
    await loginAsFisioterapeuta(page);
    
    // Verificar se foi redirecionado para dashboard ou setup
    const currentUrl = page.url();
    expect(currentUrl).toMatch(/\/(fisioterapeuta\/(dashboard|setup))/);
    
    // Verificar layout básico
    await verifyFisioterapeutaLayout(page);
  });

  test('deve exibir erro com credenciais inválidas', async ({ page }) => {
    await page.goto('/login');
    await waitForPageLoad(page);
    
    // Tentar login com credenciais inválidas
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'senhaerrada');
    await page.click('button[type="submit"]');
    
    // Verificar se há mensagem de erro
    await expect(page.locator('.error, .invalid, [role="alert"], .text-red-500')).toBeVisible();
  });

  test('deve redirecionar para setup se perfil não estiver completo', async ({ page }) => {
    // Assumindo que existe um fisioterapeuta sem perfil completo
    await loginAsFisioterapeuta(page);
    
    // Se redirecionado para setup, verificar elementos da página
    if (page.url().includes('/setup')) {
      await expect(page.locator('h1, h2')).toContainText(/setup|configuração|perfil/i);
      await expect(page.locator('input[name="crefito"]')).toBeVisible();
      await expect(page.locator('textarea[name="bio"]')).toBeVisible();
      await expect(page.locator('input[name="hourly_rate"]')).toBeVisible();
    }
  });

  test('deve completar setup inicial com sucesso', async ({ page }) => {
    await loginAsFisioterapeuta(page);
    
    // Se não estiver no setup, navegar para lá
    if (!page.url().includes('/setup')) {
      await page.goto('/fisioterapeuta/setup');
    }
    
    await waitForPageLoad(page);
    
    // Completar setup
    await completeFisioterapeutaSetup(page);
    
    // Verificar se foi redirecionado para dashboard
    await expect(page).toHaveURL(/.*\/fisioterapeuta\/dashboard/);
    
    // Verificar se dashboard carregou corretamente
    await expect(page.locator('h1, h2')).toContainText(/dashboard|painel/i);
  });

  test('deve validar campos obrigatórios no setup', async ({ page }) => {
    await loginAsFisioterapeuta(page);
    
    if (!page.url().includes('/setup')) {
      await page.goto('/fisioterapeuta/setup');
    }
    
    await waitForPageLoad(page);
    
    // Tentar submeter formulário vazio
    await page.click('button[type="submit"]');
    
    // Verificar se há mensagens de validação
    const errorCount = await page.locator('.error, .invalid, [role="alert"], .text-red-500').count();
    expect(errorCount).toBeGreaterThan(0);
  });

  test('deve validar formato do CREFITO', async ({ page }) => {
    await loginAsFisioterapeuta(page);
    
    if (!page.url().includes('/setup')) {
      await page.goto('/fisioterapeuta/setup');
    }
    
    await waitForPageLoad(page);
    
    // Testar CREFITO inválido
    await page.fill('input[name="crefito"]', 'CREFITO_INVALIDO');
    await page.click('button[type="submit"]');
    
    // Verificar se há erro de validação para CREFITO
    const crefitoError = page.locator('input[name="crefito"] ~ .error, input[name="crefito"] + .error');
    if (await crefitoError.count() > 0) {
      await expect(crefitoError).toBeVisible();
    }
  });

  test('deve permitir logout', async ({ page }) => {
    await loginAsFisioterapeuta(page);
    await waitForPageLoad(page);
    
    // Procurar botão de logout
    const logoutButton = page.locator('button:has-text("Sair"), button:has-text("Logout"), a:has-text("Sair"), a:has-text("Logout")');
    
    if (await logoutButton.count() > 0) {
      await logoutButton.first().click();
      
      // Verificar se foi redirecionado para página de login
      await expect(page).toHaveURL(/.*\/(login|welcome|home)/);
    }
  });

  test('deve verificar middleware de verificação de perfil', async ({ page }) => {
    await loginAsFisioterapeuta(page);
    
    // Tentar acessar páginas que requerem perfil completo
    const protectedPages = [
      '/fisioterapeuta/agenda',
      '/fisioterapeuta/pacientes',
      '/fisioterapeuta/relatorios'
    ];
    
    for (const pagePath of protectedPages) {
      await page.goto(pagePath);
      await waitForPageLoad(page);
      
      const currentUrl = page.url();
      
      // Se perfil não estiver completo, deve redirecionar para setup
      if (currentUrl.includes('/setup')) {
        await expect(page.locator('h1, h2')).toContainText(/setup|configuração/i);
      } else {
        // Se perfil estiver completo, deve carregar a página
        expect(currentUrl).toContain(pagePath);
      }
    }
  });

  test('deve manter sessão após refresh da página', async ({ page }) => {
    await loginAsFisioterapeuta(page);
    await waitForPageLoad(page);
    
    const urlBeforeRefresh = page.url();
    
    // Fazer refresh da página
    await page.reload();
    await waitForPageLoad(page);
    
    // Verificar se ainda está logado
    const urlAfterRefresh = page.url();
    expect(urlAfterRefresh).toBe(urlBeforeRefresh);
    
    // Verificar se layout ainda está presente
    await verifyFisioterapeutaLayout(page);
  });

  test('deve verificar redirecionamento baseado em role', async ({ page }) => {
    // Fazer login e verificar se usuário com role fisioterapeuta é redirecionado corretamente
    await loginAsFisioterapeuta(page);
    
    // Verificar se está na área do fisioterapeuta
    const currentUrl = page.url();
    expect(currentUrl).toMatch(/\/fisioterapeuta\//);
    
    // Tentar acessar área de outro role (deve ser bloqueado ou redirecionado)
    await page.goto('/paciente/dashboard');
    await waitForPageLoad(page);
    
    // Deve ser redirecionado de volta ou mostrar erro 403
    const finalUrl = page.url();
    expect(finalUrl).not.toContain('/paciente/dashboard');
  });

  test('deve verificar elementos de segurança básica', async ({ page }) => {
    await loginAsFisioterapeuta(page);
    await waitForPageLoad(page);
    
    // Verificar se há token CSRF nos formulários
    const csrfTokens = page.locator('input[name="_token"], meta[name="csrf-token"]');
    if (await csrfTokens.count() > 0) {
      await expect(csrfTokens.first()).toBeVisible();
    }
    
    // Verificar se não há informações sensíveis expostas no HTML
    const pageContent = await page.content();
    expect(pageContent).not.toContain('password');
    expect(pageContent).not.toContain('secret');
  });
});
