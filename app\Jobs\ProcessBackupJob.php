<?php

namespace App\Jobs;

use App\Services\BackupService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessBackupJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    protected $backupType;
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $backupType = 'full', ?int $userId = null)
    {
        $this->backupType = $backupType;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(BackupService $backupService): void
    {
        try {
            Log::info('Iniciando job de backup', [
                'type' => $this->backupType,
                'user_id' => $this->userId
            ]);

            if ($this->backupType === 'database') {
                $backupLog = $backupService->createDatabaseOnlyBackup($this->userId);
            } else {
                $backupLog = $backupService->createFullBackup($this->userId);
            }

            Log::info('Backup concluído com sucesso', [
                'backup_id' => $backupLog->id,
                'type' => $this->backupType
            ]);

        } catch (\Exception $e) {
            Log::error('Falha no job de backup', [
                'type' => $this->backupType,
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Job de backup falhou', [
            'type' => $this->backupType,
            'user_id' => $this->userId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
