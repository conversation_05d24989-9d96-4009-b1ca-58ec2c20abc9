<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('avaliacoes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('agendamento_id')->constrained()->onDelete('cascade');
            $table->foreignId('paciente_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('fisioterapeuta_id')->constrained('users')->onDelete('cascade');
            $table->integer('rating'); // Nota de 1 a 5
            $table->text('comment')->nullable(); // Comentário
            $table->boolean('recommend')->default(true); // Recomendaria o fisioterapeuta
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('avaliacoes');
    }
};
