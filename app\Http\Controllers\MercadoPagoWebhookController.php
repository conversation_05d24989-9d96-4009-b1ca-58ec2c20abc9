<?php

namespace App\Http\Controllers;

use App\Models\Pagamento;
use App\Models\VendaAfiliado;
use App\Services\MercadoPagoService;
use App\Services\AffiliateTrackingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MercadoPagoWebhookController extends Controller
{
    protected $mercadoPagoService;

    public function __construct(MercadoPagoService $mercadoPagoService)
    {
        $this->mercadoPagoService = $mercadoPagoService;
    }

    /**
     * Handle Mercado Pago webhook notifications
     */
    public function handle(Request $request)
    {
        try {
            Log::info('Webhook Mercado Pago recebido', [
                'headers' => $request->headers->all(),
                'body' => $request->all()
            ]);

            // Verificar se é uma notificação de pagamento
            if ($request->input('type') === 'payment') {
                $paymentId = $request->input('data.id');
                
                if ($paymentId) {
                    $this->processPaymentNotification($paymentId);
                }
            }

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            Log::error('Erro ao processar webhook Mercado Pago', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Process payment notification from Mercado Pago
     */
    private function processPaymentNotification($paymentId)
    {
        try {
            // Buscar informações do pagamento no Mercado Pago
            $paymentInfo = $this->mercadoPagoService->getPayment($paymentId);
            
            if (!$paymentInfo) {
                Log::warning('Pagamento não encontrado no Mercado Pago', ['payment_id' => $paymentId]);
                return;
            }

            $externalReference = $paymentInfo['external_reference'] ?? null;
            $status = $paymentInfo['status'] ?? null;

            if (!$externalReference) {
                Log::warning('External reference não encontrado no pagamento', [
                    'payment_id' => $paymentId,
                    'payment_info' => $paymentInfo
                ]);
                return;
            }

            // Buscar o pagamento no banco de dados
            $pagamento = Pagamento::where('id', $externalReference)->first();

            if (!$pagamento) {
                Log::warning('Pagamento não encontrado no banco de dados', [
                    'external_reference' => $externalReference,
                    'payment_id' => $paymentId
                ]);
                return;
            }

            // Mapear status do Mercado Pago para status interno
            $newStatus = $this->mapMercadoPagoStatus($status);

            // Atualizar o pagamento se o status mudou
            if ($pagamento->status !== $newStatus) {
                $updateData = [
                    'status' => $newStatus,
                    'transaction_id' => $paymentId,
                    'gateway_response' => $paymentInfo,
                ];

                // Se foi aprovado, definir data de pagamento
                if ($newStatus === 'pago') {
                    $updateData['paid_at'] = Carbon::now();
                    $updateData['method'] = $this->mapPaymentMethod($paymentInfo['payment_method_id'] ?? null);
                }

                $pagamento->update($updateData);

                Log::info('Pagamento atualizado via webhook', [
                    'pagamento_id' => $pagamento->id,
                    'old_status' => $pagamento->status,
                    'new_status' => $newStatus,
                    'payment_id' => $paymentId
                ]);

                // Aqui você pode adicionar lógica adicional como:
                // - Enviar email de confirmação
                // - Ativar assinatura
                // - Notificar o usuário
                $this->handlePaymentStatusChange($pagamento, $newStatus);
            }

        } catch (\Exception $e) {
            Log::error('Erro ao processar notificação de pagamento', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Map Mercado Pago status to internal status
     */
    private function mapMercadoPagoStatus($mpStatus)
    {
        switch ($mpStatus) {
            case 'approved':
                return 'pago';
            case 'pending':
            case 'in_process':
                return 'pendente';
            case 'rejected':
            case 'cancelled':
                return 'falhou';
            default:
                return 'pendente';
        }
    }

    /**
     * Map Mercado Pago payment method to internal method
     */
    private function mapPaymentMethod($mpMethod)
    {
        if (!$mpMethod) return null;

        // Mapear métodos do MP para métodos internos
        $methodMap = [
            'pix' => 'pix',
            'bolbradesco' => 'boleto',
            'visa' => 'cartao_credito',
            'master' => 'cartao_credito',
            'amex' => 'cartao_credito',
            'elo' => 'cartao_credito',
            'hipercard' => 'cartao_credito',
            'debvisa' => 'cartao_debito',
            'debmaster' => 'cartao_debito',
        ];

        return $methodMap[$mpMethod] ?? 'cartao_credito';
    }

    /**
     * Handle additional actions when payment status changes
     */
    private function handlePaymentStatusChange(Pagamento $pagamento, $newStatus)
    {
        if ($newStatus === 'pago') {
            // Pagamento aprovado - ações adicionais
            Log::info('Pagamento aprovado - executando ações adicionais', [
                'pagamento_id' => $pagamento->id,
                'assinatura_id' => $pagamento->assinatura_id
            ]);

            // Confirmar venda de afiliado se existir
            $this->confirmAffiliateCommission($pagamento);

            // Aqui você pode adicionar:
            // - Ativar/renovar assinatura
            // - Enviar email de confirmação
            // - Criar notificação para o usuário
            // - Atualizar status da assinatura se necessário
        }
    }

    /**
     * Confirm affiliate commission when payment is approved
     */
    private function confirmAffiliateCommission(Pagamento $pagamento)
    {
        try {
            // Buscar venda de afiliado relacionada a este transaction_id
            $vendaAfiliado = null;

            // Primeiro, tentar buscar por transaction_id (mais preciso)
            if ($pagamento->transaction_id) {
                $vendaAfiliado = VendaAfiliado::where('transaction_id', $pagamento->transaction_id)
                    ->where('status', 'pendente')
                    ->first();
            }

            // Se não encontrou por transaction_id, buscar por assinatura_id (fallback)
            if (!$vendaAfiliado) {
                $vendaAfiliado = VendaAfiliado::where('assinatura_id', $pagamento->assinatura_id)
                    ->where('status', 'pendente')
                    ->whereNull('transaction_id') // Apenas vendas sem transaction_id específico
                    ->first();
            }

            if ($vendaAfiliado) {
                // Confirmar a venda de afiliado
                $vendaAfiliado->confirmar('Pagamento confirmado via webhook - Transaction ID: ' . $pagamento->transaction_id);

                Log::info('Comissão de afiliado confirmada', [
                    'venda_afiliado_id' => $vendaAfiliado->id,
                    'afiliado_id' => $vendaAfiliado->afiliado_id,
                    'pagamento_id' => $pagamento->id,
                    'transaction_id' => $pagamento->transaction_id,
                    'comissao' => $vendaAfiliado->comissao,
                    'valor_venda' => $vendaAfiliado->valor_venda,
                ]);

                // Atualizar estatísticas do afiliado
                $affiliateService = new AffiliateTrackingService();
                $affiliateService->updateAffiliateStats($vendaAfiliado->afiliado);

            } else {
                Log::info('Nenhuma venda de afiliado encontrada para confirmação', [
                    'pagamento_id' => $pagamento->id,
                    'assinatura_id' => $pagamento->assinatura_id,
                    'transaction_id' => $pagamento->transaction_id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Erro ao confirmar comissão de afiliado', [
                'pagamento_id' => $pagamento->id,
                'transaction_id' => $pagamento->transaction_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
