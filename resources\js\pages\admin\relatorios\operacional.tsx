import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { 
    ArrowLeft,
    BarChart3, 
    Clock, 
    CheckCircle,
    AlertTriangle,
    Users,
    Calendar,
    Download,
    Filter,
    RefreshCw,
    Star
} from 'lucide-react';
import { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

interface Stats {
    total_sessoes: number;
    sessoes_concluidas: number;
    sessoes_canceladas: number;
    taxa_conclusao: number;
    tempo_medio_sessao: number;
    satisfacao_media: number;
    relatorios_pendentes: number;
    utilizacao_fisioterapeutas: number;
}

interface EvolucaoSessoes {
    data: string;
    sessoes: number;
    concluidas: number;
    canceladas: number;
}

interface SatisfacaoFisio {
    fisioterapeuta: string;
    satisfacao_media: number;
    total_avaliacoes: number;
}

interface StatusDistribution {
    status: string;
    quantidade: number;
    percentual: number;
}

interface Props {
    stats: Stats;
    evolucaoSessoes: EvolucaoSessoes[];
    satisfacaoFisioterapeutas: SatisfacaoFisio[];
    statusDistribution: StatusDistribution[];
    filtros: {
        data_inicio?: string;
        data_fim?: string;
    };
}

const COLORS = ['#22c55e', '#ef4444', '#f59e0b', '#3b82f6'];

export default function RelatorioOperacional({ 
    stats, 
    evolucaoSessoes, 
    satisfacaoFisioterapeutas, 
    statusDistribution, 
    filtros 
}: Props) {
    const [dataInicio, setDataInicio] = useState(filtros.data_inicio || '');
    const [dataFim, setDataFim] = useState(filtros.data_fim || '');
    const [isLoading, setIsLoading] = useState(false);

    const handleFilter = () => {
        setIsLoading(true);
        router.get(route('admin.relatorios.operacional'), {
            data_inicio: dataInicio,
            data_fim: dataFim,
        }, {
            preserveState: true,
            onFinish: () => setIsLoading(false),
        });
    };

    const handleExport = (formato: string) => {
        const params = new URLSearchParams({
            tipo: 'operacional',
            formato,
            data_inicio: dataInicio,
            data_fim: dataFim,
        });

        window.open(`${route('admin.relatorios.export')}?${params.toString()}`, '_blank');
    };

    const formatNumber = (value: number) => {
        return new Intl.NumberFormat('pt-BR').format(value);
    };

    const formatPercentage = (value: number) => {
        return `${value.toFixed(1)}%`;
    };

    const formatTime = (minutes: number) => {
        const hours = Math.floor(minutes / 60);
        const mins = Math.round(minutes % 60);
        if (hours > 0) {
            return `${hours}h ${mins}min`;
        }
        return `${mins}min`;
    };

    const getStatusColor = (status: string) => {
        const colors = {
            'concluido': 'bg-green-500',
            'cancelado': 'bg-red-500',
            'agendado': 'bg-blue-500',
            'em_andamento': 'bg-yellow-500',
        };
        return colors[status as keyof typeof colors] || 'bg-gray-500';
    };

    return (
        <AppLayout>
            <Head title="Relatório Operacional" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href={route('admin.relatorios.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Relatório Operacional</h1>
                            <p className="text-muted-foreground">
                                Análise detalhada das métricas operacionais
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            onClick={() => handleExport('csv')}
                            className="gap-2"
                        >
                            <Download className="h-4 w-4" />
                            Exportar CSV
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => handleExport('excel')}
                            className="gap-2"
                        >
                            <Download className="h-4 w-4" />
                            Exportar Excel
                        </Button>
                    </div>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="data_inicio">Data Início</Label>
                                <Input
                                    id="data_inicio"
                                    type="date"
                                    value={dataInicio}
                                    onChange={(e) => setDataInicio(e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="data_fim">Data Fim</Label>
                                <Input
                                    id="data_fim"
                                    type="date"
                                    value={dataFim}
                                    onChange={(e) => setDataFim(e.target.value)}
                                />
                            </div>
                            <div className="flex items-end">
                                <Button 
                                    onClick={handleFilter} 
                                    disabled={isLoading}
                                    className="w-full gap-2"
                                >
                                    {isLoading ? (
                                        <RefreshCw className="h-4 w-4 animate-spin" />
                                    ) : (
                                        <Filter className="h-4 w-4" />
                                    )}
                                    Aplicar Filtros
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Métricas Principais */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Sessões</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(stats.total_sessoes)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Taxa de Conclusão</CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">
                                {formatPercentage(stats.taxa_conclusao)}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Tempo Médio de Sessão</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatTime(stats.tempo_medio_sessao)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Satisfação Média</CardTitle>
                            <Star className="h-4 w-4 text-yellow-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600">
                                {stats.satisfacao_media.toFixed(1)}/5
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Sessões Concluídas</CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(stats.sessoes_concluidas)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Sessões Canceladas</CardTitle>
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{formatNumber(stats.sessoes_canceladas)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Relatórios Pendentes</CardTitle>
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600">{formatNumber(stats.relatorios_pendentes)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Utilização de Fisioterapeutas</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatPercentage(stats.utilizacao_fisioterapeutas)}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Gráficos */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Evolução das Sessões</CardTitle>
                            <CardDescription>Sessões por dia no período selecionado</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <LineChart data={evolucaoSessoes}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="data" />
                                    <YAxis />
                                    <Tooltip />
                                    <Line 
                                        type="monotone" 
                                        dataKey="sessoes" 
                                        stroke="#3b82f6" 
                                        strokeWidth={2}
                                        name="Total"
                                    />
                                    <Line 
                                        type="monotone" 
                                        dataKey="concluidas" 
                                        stroke="#22c55e" 
                                        strokeWidth={2}
                                        name="Concluídas"
                                    />
                                    <Line 
                                        type="monotone" 
                                        dataKey="canceladas" 
                                        stroke="#ef4444" 
                                        strokeWidth={2}
                                        name="Canceladas"
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Distribuição por Status</CardTitle>
                            <CardDescription>Proporção de sessões por status</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <PieChart>
                                    <Pie
                                        data={statusDistribution}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ status, percentual }) => `${status} ${percentual.toFixed(1)}%`}
                                        outerRadius={80}
                                        fill="#8884d8"
                                        dataKey="quantidade"
                                    >
                                        {statusDistribution.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                        ))}
                                    </Pie>
                                    <Tooltip />
                                </PieChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Satisfação dos Fisioterapeutas */}
                <Card>
                    <CardHeader>
                        <CardTitle>Satisfação por Fisioterapeuta</CardTitle>
                        <CardDescription>Avaliação média dos fisioterapeutas</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ResponsiveContainer width="100%" height={400}>
                            <BarChart data={satisfacaoFisioterapeutas} layout="horizontal">
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis type="number" domain={[0, 5]} />
                                <YAxis dataKey="fisioterapeuta" type="category" width={150} />
                                <Tooltip 
                                    formatter={(value, name) => [
                                        `${Number(value).toFixed(1)}/5`, 
                                        'Satisfação Média'
                                    ]}
                                />
                                <Bar dataKey="satisfacao_media" fill="#f59e0b" />
                            </BarChart>
                        </ResponsiveContainer>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
