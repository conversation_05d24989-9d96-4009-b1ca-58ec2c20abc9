import { useState } from 'react';
import { useForm, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Plus, Edit, Trash2, Calendar, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface HorarioExcecao {
    id: number;
    tipo: 'data_especifica' | 'semana' | 'mes' | 'periodo_personalizado';
    data_inicio: string;
    data_fim?: string;
    dia_semana?: number;
    hora_inicio?: string;
    hora_fim?: string;
    periodo_nome?: string;
    acao: 'disponivel' | 'indisponivel' | 'horario_customizado';
    motivo?: string;
    ativo: boolean;
    formatted_periodo: string;
    formatted_horario: string;
}

interface Props {
    excecoes: {
        data: HorarioExcecao[];
        links: any[];
        meta: any;
    };
    diasSemana: { [key: number]: string };
}

export function ExcecoesConfig({ excecoes, diasSemana }: Props) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingExcecao, setEditingExcecao] = useState<HorarioExcecao | null>(null);

    const { data, setData, post, put, processing, errors, reset } = useForm({
        tipo: '',
        data_inicio: '',
        data_fim: '',
        dia_semana: '',
        hora_inicio: '',
        hora_fim: '',
        periodo_nome: '',
        acao: '',
        motivo: '',
    });

    const tiposExcecao = {
        'data_especifica': 'Data Específica',
        'semana': 'Semana Específica',
        'mes': 'Mês Específico',
        'periodo_personalizado': 'Período Personalizado',
    };

    const acoes = {
        'disponivel': 'Disponível',
        'indisponivel': 'Indisponível',
        'horario_customizado': 'Horário Customizado',
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        const url = editingExcecao 
            ? route('fisioterapeuta.horarios.excecoes.update', editingExcecao.id)
            : route('fisioterapeuta.horarios.excecoes.store');

        const method = editingExcecao ? put : post;

        method(url, {
            onSuccess: () => {
                setIsDialogOpen(false);
                setEditingExcecao(null);
                reset();
                toast.success(editingExcecao ? 'Exceção atualizada!' : 'Exceção criada!');
            },
            onError: (errors) => {
                if (errors.horario) {
                    toast.error(errors.horario);
                }
            },
        });
    };

    const handleEdit = (excecao: HorarioExcecao) => {
        setEditingExcecao(excecao);
        setData({
            tipo: excecao.tipo,
            data_inicio: excecao.data_inicio,
            data_fim: excecao.data_fim || '',
            dia_semana: excecao.dia_semana?.toString() || '',
            hora_inicio: excecao.hora_inicio || '',
            hora_fim: excecao.hora_fim || '',
            periodo_nome: excecao.periodo_nome || '',
            acao: excecao.acao,
            motivo: excecao.motivo || '',
        });
        setIsDialogOpen(true);
    };

    const handleDelete = (excecao: HorarioExcecao) => {
        if (confirm('Tem certeza que deseja remover esta exceção?')) {
            router.delete(route('fisioterapeuta.horarios.excecoes.destroy', excecao.id), {
                onSuccess: () => {
                    toast.success('Exceção removida!');
                },
            });
        }
    };

    const openNewDialog = () => {
        setEditingExcecao(null);
        reset();
        setIsDialogOpen(true);
    };

    const getAcaoColor = (acao: string) => {
        switch (acao) {
            case 'disponivel':
                return 'bg-green-100 text-green-800';
            case 'indisponivel':
                return 'bg-red-100 text-red-800';
            case 'horario_customizado':
                return 'bg-blue-100 text-blue-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getTipoColor = (tipo: string) => {
        switch (tipo) {
            case 'data_especifica':
                return 'bg-purple-100 text-purple-800';
            case 'semana':
                return 'bg-orange-100 text-orange-800';
            case 'mes':
                return 'bg-yellow-100 text-yellow-800';
            case 'periodo_personalizado':
                return 'bg-indigo-100 text-indigo-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Exceções de Horário</h2>
                    <p className="text-muted-foreground">
                        Configure exceções para datas ou períodos específicos
                    </p>
                </div>
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                    <DialogTrigger asChild>
                        <Button onClick={openNewDialog} className="gap-2">
                            <Plus className="h-4 w-4" />
                            Nova Exceção
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>
                                {editingExcecao ? 'Editar Exceção' : 'Nova Exceção de Horário'}
                            </DialogTitle>
                            <DialogDescription>
                                Configure uma exceção para sobrescrever os horários base
                            </DialogDescription>
                        </DialogHeader>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="tipo">Tipo de Exceção</Label>
                                    <Select
                                        value={data.tipo}
                                        onValueChange={(value) => setData('tipo', value)}
                                        disabled={!!editingExcecao}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione o tipo" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(tiposExcecao).map(([value, label]) => (
                                                <SelectItem key={value} value={value}>
                                                    {label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.tipo && (
                                        <p className="text-sm text-destructive">{errors.tipo}</p>
                                    )}
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="acao">Ação</Label>
                                    <Select
                                        value={data.acao}
                                        onValueChange={(value) => setData('acao', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione a ação" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(acoes).map(([value, label]) => (
                                                <SelectItem key={value} value={value}>
                                                    {label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.acao && (
                                        <p className="text-sm text-destructive">{errors.acao}</p>
                                    )}
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="data_inicio">Data Início</Label>
                                    <Input
                                        id="data_inicio"
                                        type="date"
                                        value={data.data_inicio}
                                        onChange={(e) => setData('data_inicio', e.target.value)}
                                        required
                                    />
                                    {errors.data_inicio && (
                                        <p className="text-sm text-destructive">{errors.data_inicio}</p>
                                    )}
                                </div>

                                {data.tipo !== 'data_especifica' && (
                                    <div className="grid gap-2">
                                        <Label htmlFor="data_fim">Data Fim</Label>
                                        <Input
                                            id="data_fim"
                                            type="date"
                                            value={data.data_fim}
                                            onChange={(e) => setData('data_fim', e.target.value)}
                                        />
                                        {errors.data_fim && (
                                            <p className="text-sm text-destructive">{errors.data_fim}</p>
                                        )}
                                    </div>
                                )}
                            </div>

                            {data.acao === 'horario_customizado' && (
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="grid gap-2">
                                        <Label htmlFor="hora_inicio">Hora Início</Label>
                                        <Input
                                            id="hora_inicio"
                                            type="time"
                                            value={data.hora_inicio}
                                            onChange={(e) => setData('hora_inicio', e.target.value)}
                                        />
                                        {errors.hora_inicio && (
                                            <p className="text-sm text-destructive">{errors.hora_inicio}</p>
                                        )}
                                    </div>

                                    <div className="grid gap-2">
                                        <Label htmlFor="hora_fim">Hora Fim</Label>
                                        <Input
                                            id="hora_fim"
                                            type="time"
                                            value={data.hora_fim}
                                            onChange={(e) => setData('hora_fim', e.target.value)}
                                        />
                                        {errors.hora_fim && (
                                            <p className="text-sm text-destructive">{errors.hora_fim}</p>
                                        )}
                                    </div>
                                </div>
                            )}

                            <div className="grid gap-2">
                                <Label htmlFor="motivo">Motivo (opcional)</Label>
                                <Textarea
                                    id="motivo"
                                    value={data.motivo}
                                    onChange={(e) => setData('motivo', e.target.value)}
                                    placeholder="Descreva o motivo da exceção..."
                                    rows={3}
                                />
                                {errors.motivo && (
                                    <p className="text-sm text-destructive">{errors.motivo}</p>
                                )}
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setIsDialogOpen(false)}
                                >
                                    Cancelar
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {editingExcecao ? 'Atualizar' : 'Criar'}
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Lista de Exceções */}
            <div className="space-y-4">
                {excecoes.data.length === 0 ? (
                    <Card>
                        <CardContent className="text-center py-8">
                            <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                            <h3 className="text-lg font-medium mb-2">Nenhuma exceção configurada</h3>
                            <p className="text-muted-foreground mb-4">
                                Crie exceções para sobrescrever seus horários base em datas específicas
                            </p>
                            <Button onClick={openNewDialog} className="gap-2">
                                <Plus className="h-4 w-4" />
                                Criar primeira exceção
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    excecoes.data.map((excecao) => (
                        <Card key={excecao.id}>
                            <CardContent className="p-4">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-2">
                                            <Badge className={getTipoColor(excecao.tipo)}>
                                                {tiposExcecao[excecao.tipo]}
                                            </Badge>
                                            <Badge className={getAcaoColor(excecao.acao)}>
                                                {acoes[excecao.acao]}
                                            </Badge>
                                            {!excecao.ativo && (
                                                <Badge variant="secondary">Inativo</Badge>
                                            )}
                                        </div>
                                        <div className="space-y-1">
                                            <p className="font-medium">{excecao.formatted_periodo}</p>
                                            {excecao.formatted_horario !== 'Dia todo' && (
                                                <p className="text-sm text-muted-foreground">
                                                    Horário: {excecao.formatted_horario}
                                                </p>
                                            )}
                                            {excecao.motivo && (
                                                <p className="text-sm text-muted-foreground">
                                                    {excecao.motivo}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                    <div className="flex gap-1">
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleEdit(excecao)}
                                        >
                                            <Edit className="h-3 w-3" />
                                        </Button>
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleDelete(excecao)}
                                        >
                                            <Trash2 className="h-3 w-3" />
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))
                )}
            </div>
        </div>
    );
}
