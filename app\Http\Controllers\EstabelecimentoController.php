<?php

namespace App\Http\Controllers;

use App\Models\Estabelecimento;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class EstabelecimentoController extends Controller
{
    public function buscar(Request $request)
    {
        $request->validate([
            'localizacao' => 'required|string',
            'categoria' => 'nullable|in:dentista,farmacia,fisioterapia,todos',
            'raio' => 'nullable|integer|min:1|max:50',
            'avaliacao_minima' => 'nullable|numeric|min:0|max:5',
            'aberto_agora' => 'nullable|boolean',
            'aberto_24h' => 'nullable|boolean',
            'ordenacao' => 'nullable|in:distancia,avaliacao,nome,recente',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:5|max:50'
        ]);

        $localizacao = $request->localizacao;
        $categoria = $request->categoria ?? 'todos';
        $raio = $request->raio ?? 10;
        $avaliacaoMinima = $request->avaliacao_minima;
        $abertoAgora = $request->aberto_agora;
        $aberto24h = $request->aberto_24h;
        $ordenacao = $request->ordenacao ?? 'distancia';
        $page = $request->page ?? 1;
        $perPage = $request->per_page ?? 20;

        // Criar chave de cache baseada nos parâmetros de busca
        $cacheKey = 'busca_estabelecimentos_' . md5(serialize([
            'localizacao' => $localizacao,
            'categoria' => $categoria,
            'raio' => $raio,
            'avaliacao_minima' => $avaliacaoMinima,
            'aberto_agora' => $abertoAgora,
            'aberto_24h' => $aberto24h,
            'ordenacao' => $ordenacao,
            'page' => $page,
            'per_page' => $perPage
        ]));

        // Tentar obter resultado do cache (cache por 15 minutos)
        $resultadoCache = Cache::get($cacheKey);
        if ($resultadoCache) {
            return response()->json($resultadoCache);
        }

        // Tentar obter coordenadas da localização
        $coordenadas = $this->obterCoordenadas($localizacao);

        if (!$coordenadas) {
            return response()->json([
                'success' => false,
                'message' => 'Não foi possível encontrar a localização informada.'
            ], 400);
        }

        // Buscar estabelecimentos próximos
        $query = Estabelecimento::ativos()
            ->comPlanoAtivo()
            ->proximosDe($coordenadas['lat'], $coordenadas['lng'], $raio);

        if ($categoria !== 'todos') {
            $query->porCategoria($categoria);
        }

        // Aplicar filtros avançados
        if ($avaliacaoMinima) {
            $query->where('avaliacao_media', '>=', $avaliacaoMinima);
        }

        if ($aberto24h) {
            $query->where(function($q) {
                $q->whereJsonContains('horario_funcionamento->segunda', ['inicio' => '00:00', 'fim' => '23:59'])
                  ->orWhereJsonContains('horario_funcionamento', ['24h' => true]);
            });
        }

        if ($abertoAgora) {
            $agora = now();
            $diaSemana = strtolower($agora->locale('pt_BR')->dayName);
            $horaAtual = $agora->format('H:i');

            $query->where(function($q) use ($diaSemana, $horaAtual) {
                $q->whereJsonContains("horario_funcionamento->{$diaSemana}", function($horario) use ($horaAtual) {
                    return $horario['inicio'] <= $horaAtual && $horario['fim'] >= $horaAtual;
                });
            });
        }

        $todosEstabelecimentos = $query->get();

        // Filtrar por distância real em km e mapear dados
        $estabelecimentos = $todosEstabelecimentos
            ->map(function ($estabelecimento) {
                $distanciaKm = $estabelecimento->distancia / 111.045;
                $estabelecimento->distancia_km = round($distanciaKm, 2);
                return $estabelecimento;
            })
            ->filter(function ($estabelecimento) use ($raio) {
                return $estabelecimento->distancia_km <= $raio;
            });

        // Aplicar ordenação
        switch ($ordenacao) {
            case 'avaliacao':
                $estabelecimentos = $estabelecimentos->sortByDesc('avaliacao_media');
                break;
            case 'nome':
                $estabelecimentos = $estabelecimentos->sortBy('nome');
                break;
            case 'recente':
                $estabelecimentos = $estabelecimentos->sortByDesc('created_at');
                break;
            case 'distancia':
            default:
                $estabelecimentos = $estabelecimentos->sortBy('distancia_km');
                break;
        }

        // Aplicar paginação
        $total = $estabelecimentos->count();
        $offset = ($page - 1) * $perPage;
        $estabelecimentosPaginados = $estabelecimentos->slice($offset, $perPage);

        // Mapear dados finais
        $estabelecimentosPaginados = $estabelecimentosPaginados->map(function ($estabelecimento) {
            return [
                'id' => $estabelecimento->id,
                'nome' => $estabelecimento->nome,
                'categoria' => $estabelecimento->categoria,
                'descricao' => $estabelecimento->descricao,
                'telefone' => $estabelecimento->telefone,
                'whatsapp' => $estabelecimento->whatsapp,
                'whatsapp_link' => $estabelecimento->whatsapp_link,
                'endereco_completo' => $estabelecimento->endereco_completo,
                'distancia' => $estabelecimento->distancia_km,
                'avaliacao_media' => $estabelecimento->avaliacao_media,
                'total_avaliacoes' => $estabelecimento->total_avaliacoes,
                'horario_funcionamento' => $estabelecimento->horario_funcionamento,
                'slug' => $estabelecimento->slug,
            ];
        })->values();

        // Informações de paginação
        $totalPages = ceil($total / $perPage);
        $hasNextPage = $page < $totalPages;
        $hasPrevPage = $page > 1;

        // Análise para mensagens descritivas
        $totalEstabelecimentos = $todosEstabelecimentos->count();
        $estabelecimentosNoRaio = $total;
        $categorias = $estabelecimentos->groupBy('categoria');

        // Gerar mensagem descritiva
        $mensagem = $this->gerarMensagemDescritiva(
            $estabelecimentosNoRaio,
            $totalEstabelecimentos,
            $categorias,
            $raio,
            $categoria,
            $coordenadas
        );

        $resultado = [
            'success' => true,
            'estabelecimentos' => $estabelecimentosPaginados,
            'total' => $total,
            'coordenadas' => $coordenadas,
            'mensagem' => $mensagem,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_next_page' => $hasNextPage,
                'has_prev_page' => $hasPrevPage,
                'from' => $offset + 1,
                'to' => min($offset + $perPage, $total)
            ],
            'filtros' => [
                'raio' => $raio,
                'categoria' => $categoria,
                'avaliacao_minima' => $avaliacaoMinima,
                'aberto_agora' => $abertoAgora,
                'aberto_24h' => $aberto24h,
                'ordenacao' => $ordenacao,
                'total_na_regiao' => $totalEstabelecimentos
            ]
        ];

        // Armazenar resultado no cache por 15 minutos
        Cache::put($cacheKey, $resultado, now()->addMinutes(15));

        return response()->json($resultado);
    }

    private function obterCoordenadas($localizacao)
    {
        // Primeiro, tentar como CEP
        if (preg_match('/^\d{5}-?\d{3}$/', $localizacao)) {
            $coords = $this->obterCoordenadasPorCep($localizacao);
            if ($coords) {
                return $coords;
            }
        }

        // Senão, tentar como endereço/cidade
        return $this->obterCoordenadasPorEndereco($localizacao);
    }

    private function obterCoordenadasPorCep($cep)
    {
        try {
            $cep = preg_replace('/[^0-9]/', '', $cep);
            $response = Http::get("https://viacep.com.br/ws/{$cep}/json/");

            if ($response->successful() && !isset($response->json()['erro'])) {
                $data = $response->json();

                // Para CEPs válidos, usar coordenadas da cidade diretamente se disponível
                $coordenadasCidades = [
                    'São Paulo' => ['lat' => -23.5505, 'lng' => -46.6333],
                    'Rio de Janeiro' => ['lat' => -22.9068, 'lng' => -43.1729],
                    'Belo Horizonte' => ['lat' => -19.9191, 'lng' => -43.9378],
                    'Brasília' => ['lat' => -15.7801, 'lng' => -47.9292],
                    'Salvador' => ['lat' => -12.9714, 'lng' => -38.5014],
                    'Fortaleza' => ['lat' => -3.7319, 'lng' => -38.5267],
                    'Recife' => ['lat' => -8.0476, 'lng' => -34.8770],
                    'Porto Alegre' => ['lat' => -30.0346, 'lng' => -51.2177],
                    'Curitiba' => ['lat' => -25.4284, 'lng' => -49.2733],
                    'Goiânia' => ['lat' => -16.6869, 'lng' => -49.2648],
                ];

                $cidade = $data['localidade'];
                if (isset($coordenadasCidades[$cidade])) {
                    return $coordenadasCidades[$cidade];
                }

                // Se não tiver coordenadas fixas, tentar com o endereço
                $endereco = "{$data['localidade']}, {$data['uf']}";
                return $this->obterCoordenadasPorEndereco($endereco);
            }
        } catch (\Exception $e) {
            // Falha silenciosa, tentar outros métodos
        }

        return null;
    }

    private function obterCoordenadasPorEndereco($endereco)
    {
        // Coordenadas fixas para cidades principais (fallback)
        $coordenadasFixas = [
            'São Paulo' => ['lat' => -23.5505, 'lng' => -46.6333],
            'Rio de Janeiro' => ['lat' => -22.9068, 'lng' => -43.1729],
            'Belo Horizonte' => ['lat' => -19.9191, 'lng' => -43.9378],
            'Brasília' => ['lat' => -15.7801, 'lng' => -47.9292],
            'Salvador' => ['lat' => -12.9714, 'lng' => -38.5014],
            'Fortaleza' => ['lat' => -3.7319, 'lng' => -38.5267],
            'Recife' => ['lat' => -8.0476, 'lng' => -34.8770],
            'Porto Alegre' => ['lat' => -30.0346, 'lng' => -51.2177],
            'Curitiba' => ['lat' => -25.4284, 'lng' => -49.2733],
            'Goiânia' => ['lat' => -16.6869, 'lng' => -49.2648],
        ];

        // Verificar se é uma cidade conhecida
        foreach ($coordenadasFixas as $cidade => $coords) {
            if (stripos($endereco, $cidade) !== false) {
                return $coords;
            }
        }

        try {
            // Limpar e codificar o endereço para URL
            $enderecoLimpo = $this->limparEndereco($endereco);

            // Usando Nominatim (OpenStreetMap) - gratuito
            $response = Http::timeout(10)->get('https://nominatim.openstreetmap.org/search', [
                'q' => $enderecoLimpo . ', Brasil',
                'format' => 'json',
                'limit' => 1,
                'countrycodes' => 'br'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (!empty($data)) {
                    return [
                        'lat' => (float) $data[0]['lat'],
                        'lng' => (float) $data[0]['lon']
                    ];
                }
            }
        } catch (\Exception $e) {
            // Falha silenciosa, usar fallback
        }

        // Se tudo falhar, retornar null para indicar localização não encontrada
        return null;
    }

    private function limparEndereco($endereco)
    {
        // Remover caracteres especiais e normalizar
        $endereco = trim($endereco);

        // Substituir caracteres acentuados
        $acentos = [
            'á' => 'a', 'à' => 'a', 'ã' => 'a', 'â' => 'a', 'ä' => 'a',
            'é' => 'e', 'è' => 'e', 'ê' => 'e', 'ë' => 'e',
            'í' => 'i', 'ì' => 'i', 'î' => 'i', 'ï' => 'i',
            'ó' => 'o', 'ò' => 'o', 'õ' => 'o', 'ô' => 'o', 'ö' => 'o',
            'ú' => 'u', 'ù' => 'u', 'û' => 'u', 'ü' => 'u',
            'ç' => 'c', 'ñ' => 'n',
            'Á' => 'A', 'À' => 'A', 'Ã' => 'A', 'Â' => 'A', 'Ä' => 'A',
            'É' => 'E', 'È' => 'E', 'Ê' => 'E', 'Ë' => 'E',
            'Í' => 'I', 'Ì' => 'I', 'Î' => 'I', 'Ï' => 'I',
            'Ó' => 'O', 'Ò' => 'O', 'Õ' => 'O', 'Ô' => 'O', 'Ö' => 'O',
            'Ú' => 'U', 'Ù' => 'U', 'Û' => 'U', 'Ü' => 'U',
            'Ç' => 'C', 'Ñ' => 'N'
        ];

        return strtr($endereco, $acentos);
    }

    public function categorias()
    {
        return response()->json([
            'categorias' => [
                ['value' => 'todos', 'label' => 'Todos os serviços'],
                ['value' => 'dentista', 'label' => 'Dentistas'],
                ['value' => 'farmacia', 'label' => 'Farmácias'],
                ['value' => 'fisioterapia', 'label' => 'Fisioterapia'],
            ]
        ]);
    }

    private function gerarMensagemDescritiva($estabelecimentosNoRaio, $totalEstabelecimentos, $categorias, $raio, $categoria, $coordenadas)
    {
        if ($estabelecimentosNoRaio === 0) {
            if ($totalEstabelecimentos === 0) {
                return [
                    'tipo' => 'sem_estabelecimentos_regiao',
                    'titulo' => 'Nenhum estabelecimento encontrado nesta região',
                    'descricao' => 'Não encontramos estabelecimentos de saúde cadastrados nesta localização. Tente buscar em uma cidade próxima ou entre em contato conosco para cadastrar estabelecimentos da sua região.',
                    'sugestoes' => [
                        'Tente buscar em uma cidade próxima',
                        'Entre em contato para cadastrar estabelecimentos da região'
                    ]
                ];
            } else {
                return [
                    'tipo' => 'fora_do_raio',
                    'titulo' => "Nenhum estabelecimento encontrado em um raio de {$raio}km",
                    'descricao' => "Encontramos {$totalEstabelecimentos} estabelecimento(s) na região, mas nenhum dentro do raio de {$raio}km especificado.",
                    'sugestoes' => [
                        'Tente aumentar o raio de busca',
                        'Verifique se a localização está correta'
                    ]
                ];
            }
        }

        if ($categoria !== 'todos' && $categorias->count() === 1) {
            $categoriaNome = [
                'dentista' => 'dentistas',
                'farmacia' => 'farmácias',
                'fisioterapia' => 'clínicas de fisioterapia'
            ][$categoria] ?? $categoria;

            return [
                'tipo' => 'categoria_especifica',
                'titulo' => "Encontramos {$estabelecimentosNoRaio} {$categoriaNome} na região",
                'descricao' => "Todos os estabelecimentos encontrados são da categoria selecionada.",
                'sugestoes' => []
            ];
        }

        if ($categorias->count() === 1) {
            $categoriaNome = [
                'dentista' => 'dentistas',
                'farmacia' => 'farmácias',
                'fisioterapia' => 'clínicas de fisioterapia'
            ][$categorias->keys()->first()] ?? $categorias->keys()->first();

            return [
                'tipo' => 'apenas_um_tipo',
                'titulo' => "Encontramos apenas {$categoriaNome} nesta região",
                'descricao' => "Todos os {$estabelecimentosNoRaio} estabelecimentos encontrados são {$categoriaNome}. Para encontrar outros tipos de serviços, tente aumentar o raio de busca.",
                'sugestoes' => [
                    'Aumente o raio de busca para encontrar outros tipos de serviços',
                    'Tente buscar em uma localização próxima'
                ]
            ];
        }

        return [
            'tipo' => 'sucesso',
            'titulo' => "Encontramos {$estabelecimentosNoRaio} estabelecimentos na região",
            'descricao' => "Resultados encontrados em um raio de {$raio}km da localização informada.",
            'sugestoes' => []
        ];
    }
}
