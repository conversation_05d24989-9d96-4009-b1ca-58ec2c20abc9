<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class Disponibilidade extends Model
{
    use HasFactory;

    protected $table = 'disponibilidades';

    protected $fillable = [
        'fisioterapeuta_id',
        'tipo',
        'data_inicio',
        'data_fim',
        'hora_inicio',
        'hora_fim',
        'dias_semana',
        'recorrente',
        'motivo',
        'ativo',
    ];

    protected $casts = [
        'data_inicio' => 'date',
        'data_fim' => 'date',
        'hora_inicio' => 'datetime:H:i',
        'hora_fim' => 'datetime:H:i',
        'dias_semana' => 'array',
        'recorrente' => 'boolean',
        'ativo' => 'boolean',
    ];

    // Relacionamentos
    public function fisioterapeuta()
    {
        return $this->belongsTo(User::class, 'fisioterapeuta_id');
    }

    // Scopes
    public function scopeAtivas($query)
    {
        return $query->where('ativo', true);
    }

    public function scopeDisponiveis($query)
    {
        return $query->where('tipo', 'disponivel');
    }

    public function scopeBloqueios($query)
    {
        return $query->whereIn('tipo', ['indisponivel', 'bloqueio']);
    }

    public function scopePorFisioterapeuta($query, $fisioterapeutaId)
    {
        return $query->where('fisioterapeuta_id', $fisioterapeutaId);
    }

    public function scopePorPeriodo($query, $dataInicio, $dataFim)
    {
        return $query->where(function ($q) use ($dataInicio, $dataFim) {
            $q->where(function ($subQ) use ($dataInicio, $dataFim) {
                // Disponibilidades que começam no período
                $subQ->whereBetween('data_inicio', [$dataInicio, $dataFim]);
            })->orWhere(function ($subQ) use ($dataInicio, $dataFim) {
                // Disponibilidades que terminam no período
                $subQ->whereBetween('data_fim', [$dataInicio, $dataFim]);
            })->orWhere(function ($subQ) use ($dataInicio, $dataFim) {
                // Disponibilidades que englobam o período
                $subQ->where('data_inicio', '<=', $dataInicio)
                     ->where('data_fim', '>=', $dataFim);
            })->orWhere(function ($subQ) use ($dataInicio) {
                // Disponibilidades recorrentes ativas
                $subQ->where('recorrente', true)
                     ->where('data_inicio', '<=', $dataInicio)
                     ->where(function ($recQ) use ($dataInicio) {
                         $recQ->whereNull('data_fim')
                              ->orWhere('data_fim', '>=', $dataInicio);
                     });
            });
        });
    }

    // Métodos auxiliares
    public function isDisponivel($data, $hora)
    {
        if (!$this->ativo || $this->tipo !== 'disponivel') {
            return false;
        }

        $dataCarbon = Carbon::parse($data);
        $horaCarbon = Carbon::parse($hora);

        // Verificar se a data está no período
        if ($dataCarbon->lt($this->data_inicio)) {
            return false;
        }

        if ($this->data_fim && $dataCarbon->gt($this->data_fim)) {
            return false;
        }

        // Verificar dia da semana (se especificado)
        if ($this->dias_semana && !in_array($dataCarbon->dayOfWeek, $this->dias_semana)) {
            return false;
        }

        // Verificar horário
        $horaInicio = Carbon::parse($this->hora_inicio);
        $horaFim = Carbon::parse($this->hora_fim);

        return $horaCarbon->between($horaInicio, $horaFim);
    }

    public function isBloqueado($data, $hora)
    {
        if (!$this->ativo || !in_array($this->tipo, ['indisponivel', 'bloqueio'])) {
            return false;
        }

        return $this->isDisponivel($data, $hora); // Usa a mesma lógica de verificação
    }

    public function getFormattedHorarioAttribute()
    {
        return Carbon::parse($this->hora_inicio)->format('H:i') . ' - ' . 
               Carbon::parse($this->hora_fim)->format('H:i');
    }

    public function getFormattedPeriodoAttribute()
    {
        $inicio = $this->data_inicio->format('d/m/Y');
        $fim = $this->data_fim ? $this->data_fim->format('d/m/Y') : 'Indefinido';
        
        return $inicio . ($this->data_fim ? ' - ' . $fim : ' (em diante)');
    }

    public function getDiasSemanaTextAttribute()
    {
        if (!$this->dias_semana) {
            return 'Todos os dias';
        }

        $dias = [
            0 => 'Dom',
            1 => 'Seg',
            2 => 'Ter',
            3 => 'Qua',
            4 => 'Qui',
            5 => 'Sex',
            6 => 'Sáb',
        ];

        return collect($this->dias_semana)
            ->map(fn($dia) => $dias[$dia] ?? '')
            ->filter()
            ->implode(', ');
    }
}
