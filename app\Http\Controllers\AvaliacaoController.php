<?php

namespace App\Http\Controllers;

use App\Models\Avaliacao;
use App\Models\Agendamento;
use App\Models\User;
use App\Http\Requests\AvaliacaoRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class AvaliacaoController extends Controller
{
    /**
     * Listar avaliações (para admins)
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Avaliacao::class);

        $query = Avaliacao::with(['paciente', 'fisioterapeuta', 'agendamento'])
            ->orderBy('created_at', 'desc');

        // Filtros
        if ($request->filled('status')) {
            if ($request->status === 'aprovadas') {
                $query->aprovadas();
            } elseif ($request->status === 'pendentes') {
                $query->pendentes();
            }
        }

        if ($request->filled('fisioterapeuta_id')) {
            $query->porFisioterapeuta($request->fisioterapeuta_id);
        }

        if ($request->filled('nota_minima')) {
            $query->comNotaMinima($request->nota_minima);
        }

        $avaliacoes = $query->paginate(20);

        // Estatísticas gerais
        $estatisticas = [
            'total' => Avaliacao::count(),
            'aprovadas' => Avaliacao::aprovadas()->count(),
            'pendentes' => Avaliacao::pendentes()->count(),
            'nota_media_geral' => round(Avaliacao::aprovadas()->avg('nota_geral'), 1),
        ];

        return Inertia::render('admin/avaliacoes/index', [
            'avaliacoes' => $avaliacoes,
            'estatisticas' => $estatisticas,
            'filtros' => $request->only(['status', 'fisioterapeuta_id', 'nota_minima']),
        ]);
    }

    /**
     * Exibir formulário de avaliação
     */
    public function create(Request $request, Agendamento $agendamento)
    {
        $user = Auth::user();

        // Verificar se pode avaliar
        if (!Avaliacao::verificarPodeAvaliar($agendamento->id, $user->id)) {
            return redirect()->back()
                ->withErrors(['error' => 'Você não pode avaliar este agendamento.']);
        }

        return Inertia::render('avaliacoes/create', [
            'agendamento' => [
                'id' => $agendamento->id,
                'data' => $agendamento->scheduled_at,
                'fisioterapeuta' => [
                    'id' => $agendamento->fisioterapeuta->id,
                    'nome' => $agendamento->fisioterapeuta->name,
                    'avatar' => $agendamento->fisioterapeuta->avatar,
                ],
            ],
            'pontos_positivos' => Avaliacao::getPontosPositivos(),
            'pontos_melhorar' => Avaliacao::getPontosMelhorar(),
        ]);
    }

    /**
     * Salvar avaliação
     */
    public function store(AvaliacaoRequest $request)
    {
        $user = Auth::user();
        $dados = $request->validated();

        $agendamento = Agendamento::findOrFail($dados['agendamento_id']);

        // Verificar se pode avaliar
        if (!Avaliacao::verificarPodeAvaliar($agendamento->id, $user->id)) {
            return response()->json(['error' => 'Você não pode avaliar este agendamento.'], 403);
        }

        try {
            $avaliacao = Avaliacao::create([
                'agendamento_id' => $agendamento->id,
                'paciente_id' => $user->id,
                'fisioterapeuta_id' => $agendamento->fisioterapeuta_id,
                'nota_geral' => $dados['nota_geral'],
                'nota_pontualidade' => $dados['nota_pontualidade'] ?? null,
                'nota_profissionalismo' => $dados['nota_profissionalismo'] ?? null,
                'nota_eficacia' => $dados['nota_eficacia'] ?? null,
                'comentario' => $dados['comentario'] ?? null,
                'recomendaria' => $dados['recomendaria'] ?? true,
                'pontos_positivos' => $dados['pontos_positivos'] ?? [],
                'pontos_melhorar' => $dados['pontos_melhorar'] ?? [],
                'anonima' => $dados['anonima'] ?? false,
                'aprovada' => false, // Sempre pendente inicialmente
            ]);

            // Log para auditoria
            Log::info('Avaliação criada', [
                'avaliacao_id' => $avaliacao->id,
                'paciente_id' => $user->id,
                'fisioterapeuta_id' => $agendamento->fisioterapeuta_id,
                'nota_geral' => $dados['nota_geral'],
            ]);

            return redirect()->route('paciente.agendamentos.show', $agendamento)
                ->with('success', 'Avaliação enviada com sucesso! Ela será analisada antes de ser publicada.');

        } catch (\Exception $e) {
            Log::error('Erro ao criar avaliação', [
                'error' => $e->getMessage(),
                'paciente_id' => $user->id,
                'agendamento_id' => $agendamento->id,
            ]);

            return back()
                ->withErrors(['error' => 'Erro ao salvar avaliação. Tente novamente.'])
                ->withInput();
        }
    }

    /**
     * Exibir avaliação específica
     */
    public function show(Avaliacao $avaliacao)
    {
        $this->authorize('view', $avaliacao);

        $avaliacao->load(['paciente', 'fisioterapeuta', 'agendamento', 'aprovadaPor']);

        return Inertia::render('avaliacoes/show', [
            'avaliacao' => $avaliacao,
        ]);
    }

    /**
     * Aprovar avaliação (admin)
     */
    public function aprovar(Avaliacao $avaliacao)
    {
        $this->authorize('update', $avaliacao);

        $avaliacao->aprovar(Auth::id());

        Log::info('Avaliação aprovada', [
            'avaliacao_id' => $avaliacao->id,
            'aprovada_por' => Auth::id(),
        ]);

        return back()->with('success', 'Avaliação aprovada com sucesso!');
    }

    /**
     * Reprovar avaliação (admin)
     */
    public function reprovar(Avaliacao $avaliacao)
    {
        $this->authorize('update', $avaliacao);

        $avaliacao->reprovar();

        Log::info('Avaliação reprovada', [
            'avaliacao_id' => $avaliacao->id,
            'reprovada_por' => Auth::id(),
        ]);

        return back()->with('success', 'Avaliação reprovada.');
    }

    /**
     * Listar avaliações de um fisioterapeuta (público)
     */
    public function fisioterapeuta(User $fisioterapeuta, Request $request)
    {
        if ($fisioterapeuta->role !== 'fisioterapeuta') {
            abort(404);
        }

        $avaliacoes = Avaliacao::porFisioterapeuta($fisioterapeuta->id)
            ->aprovadas()
            ->with(['paciente', 'agendamento'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $estatisticas = Avaliacao::getEstatisticasFisioterapeuta($fisioterapeuta->id);

        return Inertia::render('avaliacoes/fisioterapeuta', [
            'fisioterapeuta' => [
                'id' => $fisioterapeuta->id,
                'nome' => $fisioterapeuta->name,
                'avatar' => $fisioterapeuta->avatar,
                'especialidades' => $fisioterapeuta->especialidades ?? [],
            ],
            'avaliacoes' => $avaliacoes,
            'estatisticas' => $estatisticas,
        ]);
    }

    /**
     * Minhas avaliações (paciente)
     */
    public function minhas(Request $request)
    {
        $user = Auth::user();

        $avaliacoes = Avaliacao::porPaciente($user->id)
            ->with(['fisioterapeuta', 'agendamento'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('paciente/avaliacoes/index', [
            'avaliacoes' => $avaliacoes,
        ]);
    }

    /**
     * Avaliações recebidas (fisioterapeuta)
     */
    public function recebidas(Request $request)
    {
        $user = Auth::user();

        $avaliacoes = Avaliacao::porFisioterapeuta($user->id)
            ->aprovadas()
            ->with(['paciente', 'agendamento'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $estatisticas = Avaliacao::getEstatisticasFisioterapeuta($user->id);

        return Inertia::render('fisioterapeuta/avaliacoes/index', [
            'avaliacoes' => $avaliacoes,
            'estatisticas' => $estatisticas,
        ]);
    }

    /**
     * Verificar se pode avaliar agendamento
     */
    public function podeAvaliar(Agendamento $agendamento)
    {
        $user = Auth::user();
        $pode = Avaliacao::verificarPodeAvaliar($agendamento->id, $user->id);

        return response()->json(['pode_avaliar' => $pode]);
    }



}
