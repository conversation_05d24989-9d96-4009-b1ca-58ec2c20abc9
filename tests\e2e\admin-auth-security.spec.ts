import { expect, test } from '@playwright/test';
import { checkElementExists, DEFAULT_ADMIN_CREDENTIALS, loginAsAdmin, navigateToAdminPage, waitForPageLoad } from './helpers/admin-helpers';

test.describe('Admin - Autenticação e Segurança', () => {
    test('1. Login Admin Válido', async ({ page }) => {
        console.log('🔐 Testando login admin válido...');

        await page.goto('/login');
        await page.waitForLoadState('networkidle');

        // Fill login form with admin credentials
        await page.getByRole('textbox', { name: 'Endereço de email' }).fill(DEFAULT_ADMIN_CREDENTIALS.email);
        await page.getByRole('textbox', { name: '<PERSON>ha' }).fill(DEFAULT_ADMIN_CREDENTIALS.password);

        // Submit form
        await page.getByRole('button', { name: 'En<PERSON>' }).click();

        // Should redirect to admin dashboard
        await page.waitForURL('**/admin/dashboard', { timeout: 15000 });
        expect(page.url()).toContain('/admin/dashboard');

        // Verify admin elements are present
        await checkElementExists(page, 'heading:has-text("Dashboard Administrativo")', 'Título do dashboard admin');

        console.log('✅ Login admin válido funcionando');
    });

    test('2. Login Admin Inválido', async ({ page }) => {
        console.log('🚫 Testando login admin inválido...');

        await page.goto('/login');
        await page.waitForLoadState('networkidle');

        // Try invalid credentials
        await page.fill('input[name="email"], input[type="email"]', '<EMAIL>');
        await page.fill('input[name="password"], input[type="password"]', 'wrongpassword');

        await page.click('button[type="submit"], button:has-text("Entrar"), button:has-text("Login")');

        // Should stay on login page or show error
        await page.waitForTimeout(2000);

        const isStillOnLogin = page.url().includes('/login') || (await page.locator('text="credenciais", text="inválido", text="erro"').count()) > 0;

        expect(isStillOnLogin).toBeTruthy();
        console.log('✅ Login inválido rejeitado corretamente');
    });

    test('3. Proteção de Rotas Admin sem Autenticação', async ({ page }) => {
        console.log('🛡️ Testando proteção de rotas admin...');

        // Try to access admin routes without authentication
        const adminRoutes = [
            '/admin/dashboard',
            '/admin/usuarios',
            '/admin/fisioterapeutas',
            '/admin/estabelecimentos',
            '/admin/pagamentos',
            '/admin/relatorios',
        ];

        for (const route of adminRoutes) {
            await page.goto(route);
            await page.waitForLoadState('networkidle');

            // Should redirect to login
            const isRedirectedToLogin = page.url().includes('/login');
            expect(isRedirectedToLogin).toBeTruthy();
            console.log(`✅ Rota ${route} protegida - redirecionou para login`);
        }
    });

    test('4. Proteção de Rotas Admin com Usuário Não-Admin', async ({ page }) => {
        console.log('👤 Testando acesso com usuário não-admin...');

        // First, try to login with a non-admin user (if exists)
        await page.goto('/login');
        await page.waitForLoadState('networkidle');

        // Try with a potential paciente/fisioterapeuta account
        const nonAdminCredentials = [
            { email: '<EMAIL>', password: 'password' },
            { email: '<EMAIL>', password: 'password' },
            { email: '<EMAIL>', password: 'password' },
        ];

        for (const creds of nonAdminCredentials) {
            await page.fill('input[name="email"], input[type="email"]', creds.email);
            await page.fill('input[name="password"], input[type="password"]', creds.password);

            await page.click('button[type="submit"], button:has-text("Entrar"), button:has-text("Login")');
            await page.waitForTimeout(2000);

            // If login successful, try to access admin routes
            if (!page.url().includes('/login')) {
                console.log(`🔍 Testando acesso admin com usuário: ${creds.email}`);

                await page.goto('/admin/dashboard');
                await page.waitForLoadState('networkidle');

                // Should not be able to access admin dashboard
                const hasAdminAccess = page.url().includes('/admin/dashboard') && (await page.locator('text="Dashboard Administrativo"').count()) > 0;

                expect(hasAdminAccess).toBeFalsy();
                console.log(`✅ Usuário ${creds.email} não consegue acessar área admin`);

                // Logout
                const logoutButton = page.locator('button:has-text("Sair"), a:has-text("Logout"), button:has-text("Logout")');
                if ((await logoutButton.count()) > 0) {
                    await logoutButton.first().click();
                    await page.waitForLoadState('networkidle');
                }
                break;
            }
        }
    });

    test('5. Verificação de Permissões Admin', async ({ page }) => {
        console.log('🔑 Verificando permissões específicas do admin...');

        await loginAsAdmin(page);

        // Check admin-specific functionalities
        const adminOnlyFeatures = [
            { url: '/admin/usuarios', feature: 'Gerenciamento de usuários' },
            { url: '/admin/fisioterapeutas', feature: 'Gerenciamento de fisioterapeutas' },
            { url: '/admin/estabelecimentos', feature: 'Gerenciamento de estabelecimentos' },
            { url: '/admin/pagamentos', feature: 'Sistema de pagamentos' },
            { url: '/admin/relatorios', feature: 'Relatórios administrativos' },
            { url: '/admin/backup', feature: 'Sistema de backup' },
        ];

        for (const { url, feature } of adminOnlyFeatures) {
            await page.goto(url);
            await waitForPageLoad(page);

            const hasAccess = page.url().includes(url);
            expect(hasAccess).toBeTruthy();
            console.log(`✅ Admin tem acesso a: ${feature}`);
        }
    });

    test('6. Teste de Sessão Admin', async ({ page }) => {
        console.log('⏰ Testando persistência de sessão admin...');

        await loginAsAdmin(page);

        // Navigate to different admin pages
        const adminPages = ['usuarios', 'fisioterapeutas', 'estabelecimentos'];

        for (const adminPage of adminPages) {
            await navigateToAdminPage(page, adminPage);
            await waitForPageLoad(page);

            // Verify still authenticated
            expect(page.url()).toContain(`/admin/${adminPage}`);
            console.log(`✅ Sessão mantida ao navegar para ${adminPage}`);
        }

        // Test page refresh
        await page.reload();
        await waitForPageLoad(page);

        // Should still be authenticated
        const stillAuthenticated = page.url().includes('/admin/');
        expect(stillAuthenticated).toBeTruthy();
        console.log('✅ Sessão mantida após refresh da página');
    });

    test('7. Logout Admin', async ({ page }) => {
        console.log('🚪 Testando logout admin...');

        await loginAsAdmin(page);

        // Find and click logout button
        const logoutSelectors = [
            'button:has-text("Sair")',
            'a:has-text("Logout")',
            'button:has-text("Logout")',
            '[data-testid="logout"]',
            '.logout-button',
        ];

        let loggedOut = false;
        for (const selector of logoutSelectors) {
            const element = page.locator(selector);
            if ((await element.count()) > 0) {
                await element.first().click();
                await page.waitForLoadState('networkidle');

                // Should redirect to login or home page
                const isLoggedOut = page.url().includes('/login') || page.url() === new URL(page.url()).origin + '/';
                if (isLoggedOut) {
                    loggedOut = true;
                    console.log('✅ Logout realizado com sucesso');
                    break;
                }
            }
        }

        if (!loggedOut) {
            console.log('⚠️ Botão de logout não encontrado ou não funcionou');
        }

        // Try to access admin area after logout
        await page.goto('/admin/dashboard');
        await page.waitForLoadState('networkidle');

        // Should redirect to login
        const redirectedToLogin = page.url().includes('/login');
        expect(redirectedToLogin).toBeTruthy();
        console.log('✅ Acesso negado após logout');
    });

    test('8. Teste de Segurança CSRF', async ({ page }) => {
        console.log('🛡️ Testando proteção CSRF...');

        await loginAsAdmin(page);
        await navigateToAdminPage(page, 'usuarios');

        // Check if forms have CSRF tokens
        const forms = page.locator('form');
        const formCount = await forms.count();

        if (formCount > 0) {
            for (let i = 0; i < formCount; i++) {
                const form = forms.nth(i);
                const hasCSRFToken = (await form.locator('input[name="_token"], input[name="csrf_token"]').count()) > 0;

                if (hasCSRFToken) {
                    console.log('✅ Token CSRF encontrado no formulário');
                } else {
                    console.log('⚠️ Token CSRF não encontrado no formulário');
                }
            }
        } else {
            console.log('ℹ️ Nenhum formulário encontrado para verificar CSRF');
        }
    });

    test('9. Teste de Headers de Segurança', async ({ page }) => {
        console.log('🔒 Verificando headers de segurança...');

        await loginAsAdmin(page);

        // Check response headers
        const response = await page.goto('/admin/dashboard');
        const headers = response?.headers();

        if (headers) {
            // Check for security headers
            const securityHeaders = ['x-frame-options', 'x-content-type-options', 'x-xss-protection', 'strict-transport-security'];

            for (const header of securityHeaders) {
                if (headers[header]) {
                    console.log(`✅ Header de segurança encontrado: ${header}`);
                } else {
                    console.log(`⚠️ Header de segurança ausente: ${header}`);
                }
            }
        }
    });

    test('10. Teste de Rate Limiting', async ({ page }) => {
        console.log('⚡ Testando rate limiting...');

        // Test multiple rapid login attempts
        const maxAttempts = 5;
        let blockedAttempts = 0;

        for (let i = 0; i < maxAttempts; i++) {
            await page.goto('/login');
            await page.waitForLoadState('networkidle');

            await page.fill('input[name="email"], input[type="email"]', '<EMAIL>');
            await page.fill('input[name="password"], input[type="password"]', 'wrongpassword');

            await page.click('button[type="submit"], button:has-text("Entrar"), button:has-text("Login")');
            await page.waitForTimeout(1000);

            // Check if rate limited
            const isRateLimited = (await page.locator('text="muitas tentativas", text="rate limit", text="bloqueado"').count()) > 0;

            if (isRateLimited) {
                blockedAttempts++;
                console.log(`✅ Rate limiting ativo após ${i + 1} tentativas`);
                break;
            }
        }

        if (blockedAttempts === 0) {
            console.log('ℹ️ Rate limiting não detectado (pode estar configurado diferentemente)');
        }
    });
});
