<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;

class UserModeController extends Controller
{
    /**
     * Switch user mode between normal and afiliado
     */
    public function switch(Request $request): RedirectResponse
    {
        $user = auth()->user();
        $targetMode = $request->input('mode');

        // Validar modo solicitado
        if (!in_array($targetMode, ['normal', 'afiliado'])) {
            return redirect()->back()->with('error', 'Modo inválido.');
        }

        // Se tentando alternar para modo afiliado, verificar se pode
        if ($targetMode === 'afiliado' && !$user->canSwitchToAfiliadoMode()) {
            return redirect()->back()->with('error', 'Você não tem permissão para acessar o modo afiliado.');
        }

        // Definir o modo na sessão
        session(['user_mode' => $targetMode]);

        // Redirecionar para a dashboard apropriada
        if ($targetMode === 'afiliado') {
            return redirect()->route('afiliado.dashboard')
                ->with('success', 'Modo alterado para Afiliado.');
        } else {
            return redirect()->route('dashboard')
                ->with('success', 'Modo alterado para Normal.');
        }
    }

    /**
     * Get current user mode
     */
    public function getCurrentMode(): string
    {
        return session('user_mode', 'normal');
    }

    /**
     * Check if user can switch to afiliado mode
     */
    public function canSwitchToAfiliado(): bool
    {
        return auth()->check() && auth()->user()->canSwitchToAfiliadoMode();
    }
}
