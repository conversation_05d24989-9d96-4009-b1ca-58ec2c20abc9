import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Clock, User } from 'lucide-react';

interface UserActivityLog {
    id: number;
    action: string;
    description: string;
    old_values?: any;
    new_values?: any;
    ip_address?: string;
    user_agent?: string;
    created_at: string;
    admin_user?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Usuario {
    id: number;
    name: string;
    email: string;
    role: string;
}

interface Props {
    usuario: Usuario;
    logs: {
        data: UserActivityLog[];
        links: any[];
        meta: any;
    };
}

const breadcrumbItems: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/admin/dashboard' },
    { title: '<PERSON>u<PERSON><PERSON><PERSON>', href: '/admin/usuarios' },
    { title: 'Histórico', href: '#' },
];

const getActionColor = (action: string) => {
    switch (action) {
        case 'created':
            return 'text-green-600 bg-green-50';
        case 'updated':
            return 'text-blue-600 bg-blue-50';
        case 'deleted':
            return 'text-red-600 bg-red-50';
        default:
            return 'text-gray-600 bg-gray-50';
    }
};

const getActionIcon = (action: string) => {
    switch (action) {
        case 'created':
            return '✓';
        case 'updated':
            return '✏️';
        case 'deleted':
            return '🗑️';
        default:
            return '📝';
    }
};

export default function HistoryPage({ usuario, logs }: Props) {
    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <Head title={`Histórico - ${usuario.name}`} />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Histórico de Ações</h1>
                        <p className="text-gray-600">Histórico de ações do usuário: {usuario.name}</p>
                    </div>
                    <Link href="/admin/usuarios">
                        <Button variant="outline">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar
                        </Button>
                    </Link>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <User className="h-5 w-5" />
                            Informações do Usuário
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div>
                                <label className="text-sm font-medium text-gray-500">Nome</label>
                                <p className="text-gray-900">{usuario.name}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-gray-500">Email</label>
                                <p className="text-gray-900">{usuario.email}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-gray-500">Tipo</label>
                                <p className="text-gray-900 capitalize">{usuario.role}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Clock className="h-5 w-5" />
                            Histórico de Atividades
                        </CardTitle>
                        <CardDescription>Total de {logs.meta?.total || logs.data.length} atividades registradas</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {logs.data.length === 0 ? (
                            <div className="py-8 text-center">
                                <Clock className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                <p className="text-gray-500">Nenhuma atividade registrada</p>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {logs.data.map((log) => (
                                    <div key={log.id} className="rounded-lg border p-4 transition-colors hover:bg-gray-50">
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start gap-3">
                                                <div className={`rounded-full p-2 text-sm font-medium ${getActionColor(log.action)}`}>
                                                    {getActionIcon(log.action)}
                                                </div>
                                                <div className="flex-1">
                                                    <p className="font-medium text-gray-900">{log.description}</p>
                                                    <div className="mt-1 text-sm text-gray-500">
                                                        <p>
                                                            Por: {log.admin_user?.name || 'Sistema'}
                                                            {log.admin_user?.email && ` (${log.admin_user.email})`}
                                                        </p>
                                                        <p>{new Date(log.created_at).toLocaleString('pt-BR')}</p>
                                                        {log.ip_address && <p>IP: {log.ip_address}</p>}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
