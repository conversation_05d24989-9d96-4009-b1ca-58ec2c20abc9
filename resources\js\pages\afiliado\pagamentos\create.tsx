import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AfiliadoLayout from '@/layouts/AfiliadoLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, DollarSign, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Props {
    afiliado: any;
    saldo_disponivel: number;
    valor_minimo: number;
}

export default function CreatePagamento({ afiliado, saldo_disponivel, valor_minimo }: Props) {
    const [metodoPagamento, setMetodoPagamento] = useState('');
    
    const { data, setData, post, processing, errors } = useForm({
        valor_solicitado: '',
        metodo_pagamento: '',
        dados_pagamento: {
            chave_pix: '',
            tipo_chave: '',
            banco: '',
            agencia: '',
            conta: '',
            tipo_conta: '',
            titular: '',
            cpf_titular: '',
        },
        observacoes_afiliado: '',
    });

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(value);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('afiliado.pagamentos.store'));
    };

    const handleMetodoPagamentoChange = (value: string) => {
        setMetodoPagamento(value);
        setData('metodo_pagamento', value);
    };

    const updateDadosPagamento = (key: string, value: string) => {
        setData('dados_pagamento', {
            ...data.dados_pagamento,
            [key]: value,
        });
    };

    return (
        <AfiliadoLayout>
            <Head title="Solicitar Pagamento de Comissão" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" asChild>
                        <Link href={route('afiliado.pagamentos.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Voltar
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Solicitar Pagamento</h1>
                        <p className="text-muted-foreground">
                            Solicite o pagamento das suas comissões acumuladas
                        </p>
                    </div>
                </div>

                {/* Saldo Info */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <DollarSign className="h-5 w-5" />
                            Saldo Disponível
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-3xl font-bold text-green-600 mb-2">
                            {formatCurrency(saldo_disponivel)}
                        </div>
                        <p className="text-sm text-muted-foreground">
                            Valor mínimo para saque: {formatCurrency(valor_minimo)}
                        </p>
                    </CardContent>
                </Card>

                {/* Alert se saldo insuficiente */}
                {saldo_disponivel < valor_minimo && (
                    <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                            Você precisa ter pelo menos {formatCurrency(valor_minimo)} em comissões para solicitar um pagamento.
                        </AlertDescription>
                    </Alert>
                )}

                {/* Form */}
                {saldo_disponivel >= valor_minimo && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Dados da Solicitação</CardTitle>
                            <CardDescription>
                                Preencha os dados para solicitar o pagamento das suas comissões
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Valor */}
                                <div className="space-y-2">
                                    <Label htmlFor="valor_solicitado">Valor a Solicitar</Label>
                                    <Input
                                        id="valor_solicitado"
                                        type="number"
                                        step="0.01"
                                        min={valor_minimo}
                                        max={saldo_disponivel}
                                        value={data.valor_solicitado}
                                        onChange={(e) => setData('valor_solicitado', e.target.value)}
                                        placeholder={`Mínimo: ${formatCurrency(valor_minimo)}`}
                                        required
                                    />
                                    {errors.valor_solicitado && (
                                        <p className="text-sm text-red-600">{errors.valor_solicitado}</p>
                                    )}
                                </div>

                                {/* Método de Pagamento */}
                                <div className="space-y-2">
                                    <Label htmlFor="metodo_pagamento">Método de Pagamento</Label>
                                    <Select value={metodoPagamento} onValueChange={handleMetodoPagamentoChange}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione o método de pagamento" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="pix">PIX</SelectItem>
                                            <SelectItem value="transferencia_bancaria">Transferência Bancária</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.metodo_pagamento && (
                                        <p className="text-sm text-red-600">{errors.metodo_pagamento}</p>
                                    )}
                                </div>

                                {/* Dados PIX */}
                                {metodoPagamento === 'pix' && (
                                    <div className="space-y-4 p-4 border rounded-lg">
                                        <h3 className="font-medium">Dados PIX</h3>
                                        
                                        <div className="space-y-2">
                                            <Label htmlFor="tipo_chave">Tipo de Chave PIX</Label>
                                            <Select 
                                                value={data.dados_pagamento.tipo_chave} 
                                                onValueChange={(value) => updateDadosPagamento('tipo_chave', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Selecione o tipo de chave" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="cpf">CPF</SelectItem>
                                                    <SelectItem value="email">E-mail</SelectItem>
                                                    <SelectItem value="telefone">Telefone</SelectItem>
                                                    <SelectItem value="aleatoria">Chave Aleatória</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="chave_pix">Chave PIX</Label>
                                            <Input
                                                id="chave_pix"
                                                value={data.dados_pagamento.chave_pix}
                                                onChange={(e) => updateDadosPagamento('chave_pix', e.target.value)}
                                                placeholder="Digite sua chave PIX"
                                                required
                                            />
                                        </div>
                                    </div>
                                )}

                                {/* Dados Bancários */}
                                {metodoPagamento === 'transferencia_bancaria' && (
                                    <div className="space-y-4 p-4 border rounded-lg">
                                        <h3 className="font-medium">Dados Bancários</h3>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="banco">Banco</Label>
                                                <Input
                                                    id="banco"
                                                    value={data.dados_pagamento.banco}
                                                    onChange={(e) => updateDadosPagamento('banco', e.target.value)}
                                                    placeholder="Nome do banco"
                                                    required
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="agencia">Agência</Label>
                                                <Input
                                                    id="agencia"
                                                    value={data.dados_pagamento.agencia}
                                                    onChange={(e) => updateDadosPagamento('agencia', e.target.value)}
                                                    placeholder="Número da agência"
                                                    required
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="conta">Conta</Label>
                                                <Input
                                                    id="conta"
                                                    value={data.dados_pagamento.conta}
                                                    onChange={(e) => updateDadosPagamento('conta', e.target.value)}
                                                    placeholder="Número da conta"
                                                    required
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="tipo_conta">Tipo de Conta</Label>
                                                <Select 
                                                    value={data.dados_pagamento.tipo_conta} 
                                                    onValueChange={(value) => updateDadosPagamento('tipo_conta', value)}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Tipo de conta" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="corrente">Conta Corrente</SelectItem>
                                                        <SelectItem value="poupanca">Conta Poupança</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="titular">Titular da Conta</Label>
                                                <Input
                                                    id="titular"
                                                    value={data.dados_pagamento.titular}
                                                    onChange={(e) => updateDadosPagamento('titular', e.target.value)}
                                                    placeholder="Nome completo do titular"
                                                    required
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="cpf_titular">CPF do Titular</Label>
                                                <Input
                                                    id="cpf_titular"
                                                    value={data.dados_pagamento.cpf_titular}
                                                    onChange={(e) => updateDadosPagamento('cpf_titular', e.target.value)}
                                                    placeholder="000.000.000-00"
                                                    required
                                                />
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Observações */}
                                <div className="space-y-2">
                                    <Label htmlFor="observacoes_afiliado">Observações (Opcional)</Label>
                                    <Textarea
                                        id="observacoes_afiliado"
                                        value={data.observacoes_afiliado}
                                        onChange={(e) => setData('observacoes_afiliado', e.target.value)}
                                        placeholder="Adicione observações sobre esta solicitação..."
                                        rows={3}
                                    />
                                </div>

                                {/* Buttons */}
                                <div className="flex gap-4">
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Enviando...' : 'Solicitar Pagamento'}
                                    </Button>
                                    <Button type="button" variant="outline" asChild>
                                        <Link href={route('afiliado.pagamentos.index')}>
                                            Cancelar
                                        </Link>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AfiliadoLayout>
    );
}
