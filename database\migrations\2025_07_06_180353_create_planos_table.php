<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('planos', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nome do plano
            $table->text('description');
            $table->decimal('price', 8, 2); // Preço mensal
            $table->integer('sessions_per_month'); // Sessões por mês
            $table->integer('session_duration'); // Duração da sessão em minutos
            $table->json('included_services'); // Serviços inclusos
            $table->json('benefits')->nullable(); // Benefícios adicionais
            $table->boolean('active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('planos');
    }
};
