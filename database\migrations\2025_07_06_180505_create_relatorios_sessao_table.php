<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('relatorios_sessao', function (Blueprint $table) {
            $table->id();
            $table->foreignId('agendamento_id')->constrained()->onDelete('cascade');
            $table->text('objective'); // Objetivo da sessão
            $table->text('activities_performed'); // Atividades realizadas
            $table->text('patient_response'); // Resposta do paciente
            $table->text('observations')->nullable(); // Observações gerais
            $table->text('next_session_plan')->nullable(); // Plano para próxima sessão
            $table->json('vital_signs')->nullable(); // Sinais vitais
            $table->integer('pain_scale')->nullable(); // Escala de dor (0-10)
            $table->text('exercises_prescribed')->nullable(); // Exercícios prescritos
            $table->text('recommendations')->nullable(); // Recomendações
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('relatorios_sessao');
    }
};
