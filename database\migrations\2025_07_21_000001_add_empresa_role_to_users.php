<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (DB::getDriverName() === 'pgsql') {
            // Para PostgreSQL, trabalhar com check constraints

            // Remover a constraint existente
            DB::statement("ALTER TABLE users DROP CONSTRAINT IF EXISTS users_role_check");

            // Adicionar nova constraint com 'empresa'
            DB::statement("
                ALTER TABLE users
                ADD CONSTRAINT users_role_check
                CHECK (role IN ('admin', 'fisioterapeuta', 'paciente', 'afiliado', 'empresa'))
            ");

        } else {
            // Para outros bancos (MySQL), usar o método tradicional
            Schema::table('users', function (Blueprint $table) {
                $table->enum('role', ['admin', 'fisioterapeuta', 'paciente', 'afiliado', 'empresa'])->default('paciente')->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (DB::getDriverName() === 'pgsql') {
            // Verificar se existem usuários com role 'empresa'
            $empresaUsers = DB::table('users')->where('role', 'empresa')->count();

            if ($empresaUsers > 0) {
                throw new \Exception("Não é possível reverter: existem {$empresaUsers} usuários com role 'empresa'. Remova-os primeiro.");
            }

            // Remover a constraint atual
            DB::statement("ALTER TABLE users DROP CONSTRAINT IF EXISTS users_role_check");

            // Adicionar constraint sem 'empresa'
            DB::statement("
                ALTER TABLE users
                ADD CONSTRAINT users_role_check
                CHECK (role IN ('admin', 'fisioterapeuta', 'paciente', 'afiliado'))
            ");

        } else {
            // Para outros bancos (MySQL)
            Schema::table('users', function (Blueprint $table) {
                $table->enum('role', ['admin', 'fisioterapeuta', 'paciente', 'afiliado'])->default('paciente')->change();
            });
        }
    }
};
