import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { router, usePage } from '@inertiajs/react';
import { ChevronDown, User, UserCheck } from 'lucide-react';

interface ModeSwitcherProps {
    className?: string;
}

export function ModeSwitcher({ className }: ModeSwitcherProps) {
    const { auth, currentUserMode } = usePage().props as any;
    const user = auth?.user;

    // Verificar se o usuário pode alternar para modo afiliado
    const canSwitchToAfiliado = user?.afiliado && user.afiliado.status === 'aprovado' && user.afiliado.ativo;

    // Se não pode alternar, não mostrar o componente
    if (!canSwitchToAfiliado) {
        return null;
    }

    const currentMode = currentUserMode || 'normal';
    const isAfiliadoMode = currentMode === 'afiliado';

    const handleModeSwitch = (mode: string) => {
        router.post(
            '/switch-mode',
            { mode },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className={className}>
                    {isAfiliadoMode ? (
                        <>
                            <UserCheck className="mr-2 h-4 w-4" />
                            Modo Afiliado
                        </>
                    ) : (
                        <>
                            <User className="mr-2 h-4 w-4" />
                            Modo Normal
                        </>
                    )}
                    <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handleModeSwitch('normal')} className={currentMode === 'normal' ? 'bg-muted' : ''}>
                    <User className="mr-2 h-4 w-4" />
                    <div className="flex flex-col">
                        <span>Modo Normal</span>
                        <span className="text-xs text-muted-foreground">
                            {user?.role === 'paciente'
                                ? 'Paciente'
                                : user?.role === 'fisioterapeuta'
                                  ? 'Fisioterapeuta'
                                  : user?.role === 'admin'
                                    ? 'Administrador'
                                    : user?.role === 'empresa'
                                      ? 'Empresa'
                                      : 'Usuário'}
                        </span>
                    </div>
                    {currentMode === 'normal' && (
                        <Badge variant="secondary" className="ml-auto">
                            Ativo
                        </Badge>
                    )}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleModeSwitch('afiliado')} className={currentMode === 'afiliado' ? 'bg-muted' : ''}>
                    <UserCheck className="mr-2 h-4 w-4" />
                    <div className="flex flex-col">
                        <span>Modo Afiliado</span>
                        <span className="text-xs text-muted-foreground">Código: {user?.afiliado?.codigo_afiliado}</span>
                    </div>
                    {currentMode === 'afiliado' && (
                        <Badge variant="secondary" className="ml-auto">
                            Ativo
                        </Badge>
                    )}
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
