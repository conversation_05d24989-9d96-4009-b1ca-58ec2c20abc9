import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Award, Calendar, Clock, Mail, MapPin, MessageCircle, Phone, Star } from 'lucide-react';

interface Fisioterapeuta {
    id: number;
    user: {
        name: string;
        email: string;
        phone?: string;
        avatar?: string;
    };
    crefito: string;
    specializations: string[];
    bio: string;
    hourly_rate: number;
    available_areas: string[];
    rating: number;
    total_reviews: number;
    available: boolean;
    working_hours: {
        [key: string]: {
            start: string;
            end: string;
        };
    };
    next_available_slots: string[];
}

interface Avaliacao {
    id: number;
    rating: number;
    comment: string;
    created_at: string;
    paciente: {
        name: string;
        avatar?: string;
    };
}

interface Props {
    fisioterapeuta: Fisioterapeuta;
    avaliacoes: Avaliacao[];
    stats: {
        total_sessions: number;
        total_patients: number;
        years_experience: number;
    };
}

export default function PacienteFisioterapeutaShow({ fisioterapeuta, avaliacoes, stats }: Props) {
    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, i) => (
            <Star key={i} className={`h-4 w-4 ${i < Math.floor(rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} />
        ));
    };

    const diasSemana = {
        monday: 'Segunda-feira',
        tuesday: 'Terça-feira',
        wednesday: 'Quarta-feira',
        thursday: 'Quinta-feira',
        friday: 'Sexta-feira',
        saturday: 'Sábado',
        sunday: 'Domingo',
    };

    return (
        <AppLayout>
            <Head title={`${fisioterapeuta.user.name} - Perfil`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                <div className="flex items-center gap-4">
                    <Link href={route('paciente.fisioterapeutas.index')}>
                        <Button variant="ghost" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Perfil do Fisioterapeuta</h1>
                        <p className="text-muted-foreground">Informações detalhadas e disponibilidade</p>
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Coluna Principal */}
                    <div className="space-y-6 lg:col-span-2">
                        {/* Informações Básicas */}
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-start gap-6">
                                    <Avatar className="h-24 w-24">
                                        <AvatarImage src={fisioterapeuta.user.avatar} />
                                        <AvatarFallback className="text-xl">
                                            {fisioterapeuta.user.name
                                                .split(' ')
                                                .map((n) => n[0])
                                                .join('')
                                                .toUpperCase()}
                                        </AvatarFallback>
                                    </Avatar>

                                    <div className="flex-1">
                                        <div className="flex items-start justify-between">
                                            <div>
                                                <h2 className="text-2xl font-bold">{fisioterapeuta.user.name}</h2>
                                                <p className="text-muted-foreground">CREFITO: {fisioterapeuta.crefito}</p>

                                                <div className="mt-2 flex items-center gap-2">
                                                    {renderStars(fisioterapeuta.rating)}
                                                    <span className="font-medium">
                                                        {fisioterapeuta.rating ? fisioterapeuta.rating.toFixed(1) : '0.0'}
                                                    </span>
                                                    <span className="text-muted-foreground">({fisioterapeuta.total_reviews} avaliações)</span>
                                                </div>
                                            </div>

                                            {fisioterapeuta.available && <Badge className="bg-green-100 text-green-800">Disponível</Badge>}
                                        </div>

                                        <div className="mt-4 grid grid-cols-3 gap-4 rounded-lg bg-gray-50 p-4">
                                            <div className="text-center">
                                                <div className="text-2xl font-bold text-blue-600">{stats.total_sessions}</div>
                                                <div className="text-sm text-muted-foreground">Sessões realizadas</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="text-2xl font-bold text-green-600">{stats.total_patients}</div>
                                                <div className="text-sm text-muted-foreground">Pacientes atendidos</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="text-2xl font-bold text-purple-600">{stats.years_experience}</div>
                                                <div className="text-sm text-muted-foreground">Anos de experiência</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Sobre */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Sobre o Profissional</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="leading-relaxed text-muted-foreground">{fisioterapeuta.bio}</p>
                            </CardContent>
                        </Card>

                        {/* Especializações */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Award className="h-5 w-5" />
                                    Especializações
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-wrap gap-2">
                                    {fisioterapeuta.specializations.map((spec) => (
                                        <Badge key={spec} variant="secondary">
                                            {spec}
                                        </Badge>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Áreas de Atendimento */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MapPin className="h-5 w-5" />
                                    Áreas de Atendimento
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-wrap gap-2">
                                    {fisioterapeuta.available_areas.map((area) => (
                                        <Badge key={area} variant="outline">
                                            {area}
                                        </Badge>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Avaliações */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MessageCircle className="h-5 w-5" />
                                    Avaliações dos Pacientes
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {avaliacoes.slice(0, 3).map((avaliacao) => (
                                        <div key={avaliacao.id} className="border-b pb-4 last:border-b-0">
                                            <div className="flex items-start gap-3">
                                                <Avatar className="h-8 w-8">
                                                    <AvatarImage src={avaliacao.paciente.avatar} />
                                                    <AvatarFallback>
                                                        {avaliacao.paciente.name
                                                            .split(' ')
                                                            .map((n) => n[0])
                                                            .join('')
                                                            .toUpperCase()}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <div className="flex-1">
                                                    <div className="mb-1 flex items-center gap-2">
                                                        <span className="font-medium">{avaliacao.paciente.name}</span>
                                                        <div className="flex">{renderStars(avaliacao.rating)}</div>
                                                    </div>
                                                    <p className="text-sm text-muted-foreground">{avaliacao.comment}</p>
                                                    <p className="mt-1 text-xs text-muted-foreground">
                                                        {new Date(avaliacao.created_at).toLocaleDateString('pt-BR')}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))}

                                    {avaliacoes.length > 3 && (
                                        <div className="text-center">
                                            <Button variant="outline" size="sm">
                                                Ver todas as avaliações ({avaliacoes.length})
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Ações Rápidas */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Agendar Consulta</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-green-600">
                                        R$ {fisioterapeuta.hourly_rate ? fisioterapeuta.hourly_rate.toFixed(2) : '0,00'}
                                    </div>
                                    <div className="text-sm text-muted-foreground">por sessão</div>
                                </div>

                                <Separator />

                                <Link href={route('paciente.agendamentos.create', { fisioterapeuta: fisioterapeuta.id })}>
                                    <Button className="w-full">
                                        <Calendar className="mr-2 h-4 w-4" />
                                        Agendar Sessão
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>

                        {/* Próximos Horários */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Clock className="h-5 w-5" />
                                    Próximos Horários
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {fisioterapeuta.next_available_slots.slice(0, 5).map((slot, index) => (
                                        <div key={index} className="flex items-center justify-between rounded bg-gray-50 p-2">
                                            <span className="text-sm">{slot}</span>
                                            <Badge variant="outline" className="text-xs">
                                                Disponível
                                            </Badge>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Horários de Funcionamento */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Horários de Atendimento</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {Object.entries(fisioterapeuta.working_hours).map(([day, hours]) => (
                                        <div key={day} className="flex justify-between text-sm">
                                            <span>{diasSemana[day as keyof typeof diasSemana]}</span>
                                            <span className="text-muted-foreground">
                                                {hours.start} - {hours.end}
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Contato */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Contato</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex items-center gap-2 text-sm">
                                    <Mail className="h-4 w-4 text-muted-foreground" />
                                    <span>{fisioterapeuta.user.email}</span>
                                </div>
                                {fisioterapeuta.user.phone && (
                                    <div className="flex items-center gap-2 text-sm">
                                        <Phone className="h-4 w-4 text-muted-foreground" />
                                        <span>{fisioterapeuta.user.phone}</span>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
