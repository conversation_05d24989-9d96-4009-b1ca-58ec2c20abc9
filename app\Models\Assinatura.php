<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Assinatura extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plano_id',
        'status',
        'start_date',
        'end_date',
        'sessions_used',
        'current_period_start',
        'current_period_end',
        'monthly_price',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'current_period_start' => 'date',
        'current_period_end' => 'date',
        'monthly_price' => 'decimal:2',
    ];

    // Relacionamentos
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function plano()
    {
        return $this->belongsTo(Plano::class);
    }

    public function agendamentos()
    {
        return $this->hasMany(Agendamento::class);
    }

    public function pagamentos()
    {
        return $this->hasMany(Pagamento::class);
    }

    // Scopes
    public function scopeAtivas($query)
    {
        return $query->where('status', 'ativa');
    }

    // Métodos auxiliares
    public function canScheduleSession()
    {
        return $this->status === 'ativa' &&
               $this->sessions_used < $this->plano->sessions_per_month;
    }

    public function getRemainingSessionsAttribute()
    {
        return $this->plano->sessions_per_month - $this->sessions_used;
    }
}
