# Configuração do Resend para Envio de Emails

## Visão Geral

O sistema F4 Fisio utiliza o **Resend** como provedor de email para envio de notificações automáticas quando agendamentos são confirmados, cancelados ou reagendados.

## Configuração

### 1. Variáveis de Ambiente

Adicione as seguintes variáveis no arquivo `.env`:

```env
# Configurações de Email
MAIL_MAILER=resend
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="F4 Fisio"

# Configurações do Resend
RESEND_API_KEY=your_resend_api_key_here
RESEND_DOMAIN=f4fisio.com.br
```

### 2. Obter API Key do Resend

1. Acesse [resend.com](https://resend.com)
2. Crie uma conta ou faça login
3. Vá para **API Keys** no dashboard
4. Clique em **Create API Key**
5. Dê um nome para a chave (ex: "F4 Fisio Production")
6. Copie a chave gerada e adicione no `.env`

### 3. Configurar Domínio (Opcional)

Para usar um domínio personalizado:

1. No dashboard do Resend, vá para **Domains**
2. Clique em **Add Domain**
3. Digite seu domínio (ex: `f4fisio.com.br`)
4. Configure os registros DNS conforme instruções
5. Aguarde a verificação

## Funcionalidades Implementadas

### Emails Automáticos

O sistema envia emails automaticamente nos seguintes casos:

#### 1. Agendamento Confirmado
- **Trigger**: Quando fisioterapeuta confirma um agendamento
- **Destinatário**: Paciente
- **Template**: `emails.agendamento-confirmado`
- **Conteúdo**: Detalhes da consulta, preparação, instruções

#### 2. Agendamento Cancelado
- **Trigger**: Quando agendamento é cancelado
- **Destinatário**: Paciente ou Fisioterapeuta
- **Template**: `emails.agendamento-cancelado`
- **Conteúdo**: Motivo do cancelamento, próximos passos

#### 3. Novo Agendamento
- **Trigger**: Quando paciente cria novo agendamento
- **Destinatário**: Fisioterapeuta
- **Template**: `emails.novo-agendamento`
- **Conteúdo**: Dados do paciente, horário, ações necessárias

#### 4. Lembrete de Consulta
- **Trigger**: 24h antes da consulta (via cron job)
- **Destinatário**: Paciente e Fisioterapeuta
- **Template**: `emails.lembrete-agendamento`
- **Conteúdo**: Lembrete com detalhes da consulta

## Estrutura do Código

### Job de Envio
```php
// app/Jobs/EnviarEmailAgendamento.php
EnviarEmailAgendamento::dispatch($agendamento, 'confirmado', $paciente_id);
```

### Service de Notificação
```php
// app/Services/NotificacaoService.php
public function notificarAgendamentoConfirmado(Agendamento $agendamento)
{
    // Criar notificação no banco
    $notificacao = Notificacao::criarNotificacao(...);
    
    // Enviar email via Resend
    EnviarEmailAgendamento::dispatch($agendamento, 'confirmado', $agendamento->paciente_id);
    
    return $notificacao;
}
```

### Templates de Email

Os templates estão localizados em `resources/views/emails/`:

- `agendamento-confirmado.blade.php`
- `agendamento-cancelado.blade.php`
- `novo-agendamento.blade.php`
- `lembrete-agendamento.blade.php`
- `layout.blade.php` (layout base)

## Monitoramento e Logs

### Logs de Email
Os emails são logados em `storage/logs/laravel.log`:

```
[INFO] Email de agendamento enviado com sucesso
[ERROR] Erro ao enviar email de agendamento
```

### Dashboard do Resend
No dashboard do Resend você pode monitorar:
- Emails enviados
- Taxa de entrega
- Bounces e reclamações
- Estatísticas de abertura

## Fallback e Tratamento de Erros

### Configuração de Fallback
Se o Resend falhar, o sistema:
1. Loga o erro
2. Mantém a notificação no banco de dados
3. Permite reenvio manual via admin

### Retry de Jobs
Jobs falhados são automaticamente reprocessados:
```php
// config/queue.php
'failed' => [
    'driver' => 'database',
    'table' => 'failed_jobs',
],
```

## Comandos Úteis

### Testar Configuração
```bash
php artisan tinker
Mail::raw('Teste', function($msg) { $msg->to('<EMAIL>')->subject('Teste Resend'); });
```

### Processar Fila de Emails
```bash
php artisan queue:work
```

### Ver Jobs Falhados
```bash
php artisan queue:failed
```

### Reprocessar Job Falhado
```bash
php artisan queue:retry {job_id}
```

## Custos e Limites

### Plano Gratuito Resend
- 3.000 emails/mês
- 100 emails/dia
- Ideal para desenvolvimento e testes

### Planos Pagos
- Pro: $20/mês - 50.000 emails
- Business: $80/mês - 200.000 emails
- Consulte [resend.com/pricing](https://resend.com/pricing)

## Troubleshooting

### Email não está sendo enviado
1. Verifique se `RESEND_API_KEY` está configurada
2. Confirme se `MAIL_MAILER=resend`
3. Verifique logs em `storage/logs/laravel.log`
4. Teste a API key no dashboard do Resend

### Emails indo para spam
1. Configure SPF, DKIM e DMARC
2. Use domínio verificado no Resend
3. Evite palavras que ativam filtros de spam
4. Mantenha boa reputação de envio

### Rate Limit Exceeded
1. Verifique limites do seu plano
2. Implemente throttling nos jobs
3. Considere upgrade do plano

## Próximos Passos

1. **Configurar domínio personalizado** para melhor deliverability
2. **Implementar webhooks** para tracking de abertura/clique
3. **Criar templates responsivos** para melhor experiência mobile
4. **Adicionar analytics** de engajamento de email
