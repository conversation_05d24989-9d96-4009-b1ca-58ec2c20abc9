<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Plano;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PlanoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        \Log::info('PlanoController@index called', [
            'user' => auth()->user()?->toArray(),
            'request_data' => $request->all()
        ]);

        $query = Plano::withCount('assinaturas');

        // Filtro por status
        if ($request->filled('active') && $request->active !== 'all') {
            $query->where('active', $request->boolean('active'));
        }

        // Busca por nome
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $planos = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('admin/planos', [
            'planos' => $planos,
            'filters' => $request->only(['active', 'search']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('admin/planos/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'sessions_per_month' => 'required|integer|min:1',
            'session_duration' => 'required|integer|min:30',
            'included_services' => 'required|array|min:1',
            'benefits' => 'required|array|min:1',
            'active' => 'boolean',
        ]);

        Plano::create($validated);

        return redirect()->route('admin.planos.index')
            ->with('success', 'Plano criado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Plano $plano)
    {
        $plano->load(['assinaturas.user']);

        return Inertia::render('admin/planos/show', [
            'plano' => $plano,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Plano $plano)
    {
        return Inertia::render('admin/planos/edit', [
            'plano' => $plano,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Plano $plano)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'sessions_per_month' => 'required|integer|min:1',
            'session_duration' => 'required|integer|min:30',
            'included_services' => 'required|array|min:1',
            'benefits' => 'required|array|min:1',
            'active' => 'boolean',
        ]);

        $plano->update($validated);

        return redirect()->route('admin.planos.index')
            ->with('success', 'Plano atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Plano $plano)
    {
        // Verificar se há assinaturas ativas
        if ($plano->assinaturas()->where('status', 'ativa')->exists()) {
            return back()->with('error', 'Não é possível remover um plano com assinaturas ativas.');
        }

        $plano->delete();

        return redirect()->route('admin.planos.index')
            ->with('success', 'Plano removido com sucesso!');
    }
}
