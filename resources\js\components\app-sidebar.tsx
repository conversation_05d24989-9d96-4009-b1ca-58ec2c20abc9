import { AffiliateProgramLink } from '@/components/affiliate-program-link';
import { ModeSwitcher } from '@/components/mode-switcher';
import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import {
    BarChart3,
    Bell,
    BookOpen,
    Calendar,
    Clock,
    CreditCard,
    DollarSign,
    Download,
    FileText,
    Folder,
    LayoutGrid,
    MessageCircle,
    Search,
    User,
    UserCheck,
    Users,
} from 'lucide-react';
import AppLogo from './app-logo';

// Navegação para Admin
const adminNavItems: NavItem[] = [
    {
        title: 'Painel',
        href: '/admin/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Usuários',
        href: '/admin/usuarios',
        icon: Users,
    },
    {
        title: 'Fisioterapeutas',
        href: '/admin/fisioterapeutas',
        icon: UserCheck,
    },
    {
        title: 'Planos',
        href: '/admin/planos',
        icon: CreditCard,
    },
    {
        title: 'Pagamentos',
        href: '/admin/pagamentos',
        icon: DollarSign,
    },
    {
        title: 'Relatórios',
        href: '/admin/relatorios',
        icon: BarChart3,
    },
];

// Navegação para Fisioterapeuta
const fisioterapeutaNavItems: NavItem[] = [
    {
        title: 'Painel',
        href: '/fisioterapeuta/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Perfil',
        href: '/fisioterapeuta/perfil',
        icon: User,
    },
    {
        title: 'Agenda',
        href: '/fisioterapeuta/agenda',
        icon: Calendar,
    },
    {
        title: 'Disponibilidade',
        href: '/fisioterapeuta/disponibilidade',
        icon: Clock,
    },
    {
        title: 'Pacientes',
        href: '/fisioterapeuta/pacientes',
        icon: Users,
    },
    {
        title: 'Relatórios',
        href: '/fisioterapeuta/relatorios',
        icon: FileText,
    },
    {
        title: 'Avaliações',
        href: '/fisioterapeuta/avaliacoes',
        icon: MessageCircle,
    },
];

// Navegação para Paciente
const pacienteNavItems: NavItem[] = [
    {
        title: 'Início',
        href: '/paciente/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Buscar Fisioterapeutas',
        href: '/paciente/fisioterapeutas',
        icon: Search,
    },
    {
        title: 'Buscar Serviços de Saúde',
        href: '/buscar',
        icon: Search,
    },
    {
        title: 'Notificações',
        href: '/notificacoes',
        icon: Bell,
    },
    {
        title: 'Perfil',
        href: '/paciente/perfil',
        icon: User,
    },
    {
        title: 'Agendamentos',
        href: '/paciente/agendamentos',
        icon: Calendar,
    },
    {
        title: 'Histórico',
        href: '/paciente/historico',
        icon: Clock,
    },
    {
        title: 'Pagamentos',
        href: '/paciente/pagamentos',
        icon: DollarSign,
    },
    {
        title: 'Planos',
        href: '/paciente/planos',
        icon: CreditCard,
    },
    {
        title: 'Avaliações',
        href: '/paciente/avaliacoes',
        icon: MessageCircle,
    },
];

// Navegação para Afiliados
const afiliadoNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/afiliado/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Vendas',
        href: '/afiliado/vendas',
        icon: DollarSign,
    },
    {
        title: 'Cupons',
        href: '/afiliado/cupons',
        icon: FileText,
    },
    {
        title: 'Pagamentos',
        href: '/afiliado/pagamentos',
        icon: CreditCard,
    },
    {
        title: 'Materiais',
        href: '/afiliado/materiais',
        icon: Download,
    },
    {
        title: 'Perfil',
        href: '/afiliado/perfil',
        icon: UserCheck,
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Site F4 Fisio',
        href: '/',
        icon: Folder,
    },
    {
        title: 'Suporte',
        href: '/contato',
        icon: BookOpen,
    },
];

export function AppSidebar() {
    const { auth, currentUserMode } = usePage().props as any;
    const user = auth?.user;
    const userMode = currentUserMode || 'normal';

    // Determinar itens de navegação baseado no role e modo
    let mainNavItems: NavItem[] = [];
    let dashboardHref = '/dashboard';

    // Se estiver em modo afiliado
    if (userMode === 'afiliado' && user?.canSwitchToAfiliadoMode) {
        mainNavItems = afiliadoNavItems;
        dashboardHref = '/afiliado/dashboard';
    } else if (user?.role === 'admin') {
        mainNavItems = adminNavItems;
        dashboardHref = '/admin/dashboard';
    } else if (user?.role === 'fisioterapeuta') {
        mainNavItems = fisioterapeutaNavItems;
        dashboardHref = '/fisioterapeuta/dashboard';
    } else if (user?.role === 'paciente') {
        mainNavItems = pacienteNavItems;
        dashboardHref = '/paciente/dashboard';
    } else if (user?.role === 'afiliado') {
        mainNavItems = afiliadoNavItems;
        dashboardHref = '/afiliado/dashboard';
    }

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={dashboardHref} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                    {user?.hasAfiliadoProfile && (
                        <SidebarMenuItem>
                            <ModeSwitcher className="w-full" />
                        </SidebarMenuItem>
                    )}
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
                {user && <AffiliateProgramLink user={user} currentUserMode={userMode} />}
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
