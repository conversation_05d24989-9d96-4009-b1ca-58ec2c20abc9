<?php

namespace App\Services;

use App\Models\BackupLog;
use App\Models\User;
use App\Models\Estabelecimento;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use League\Csv\Reader;
use Carbon\Carbon;

class ImportService
{
    protected $importDisk;

    public function __construct()
    {
        $this->importDisk = Storage::disk('local');
    }

    /**
     * Importar usuários de arquivo CSV/Excel
     */
    public function importUsers(string $filePath, array $options = [], ?int $userId = null): BackupLog
    {
        $importLog = BackupLog::create([
            'type' => BackupLog::TYPE_IMPORT,
            'status' => BackupLog::STATUS_PENDING,
            'user_id' => $userId,
            'started_at' => now(),
            'metadata' => [
                'import_type' => 'users',
                'file_path' => $filePath,
                'options' => $options
            ]
        ]);

        try {
            $importLog->update(['status' => BackupLog::STATUS_PROCESSING]);

            if (!$this->importDisk->exists($filePath)) {
                throw new \Exception('Arquivo não encontrado');
            }

            $fileExtension = pathinfo($filePath, PATHINFO_EXTENSION);
            
            if (in_array($fileExtension, ['xlsx', 'xls'])) {
                $data = $this->readExcelFile($filePath);
            } elseif ($fileExtension === 'csv') {
                $data = $this->readCsvFile($filePath);
            } else {
                throw new \Exception('Formato de arquivo não suportado');
            }

            $results = $this->processUsersData($data, $options);
            
            $importLog->update([
                'status' => BackupLog::STATUS_COMPLETED,
                'completed_at' => now(),
                'metadata' => array_merge($importLog->metadata, [
                    'results' => $results
                ])
            ]);

            return $importLog;

        } catch (\Exception $e) {
            $importLog->update([
                'status' => BackupLog::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * Importar estabelecimentos de arquivo CSV/Excel
     */
    public function importEstabelecimentos(string $filePath, array $options = [], ?int $userId = null): BackupLog
    {
        $importLog = BackupLog::create([
            'type' => BackupLog::TYPE_IMPORT,
            'status' => BackupLog::STATUS_PENDING,
            'user_id' => $userId,
            'started_at' => now(),
            'metadata' => [
                'import_type' => 'estabelecimentos',
                'file_path' => $filePath,
                'options' => $options
            ]
        ]);

        try {
            $importLog->update(['status' => BackupLog::STATUS_PROCESSING]);

            if (!$this->importDisk->exists($filePath)) {
                throw new \Exception('Arquivo não encontrado');
            }

            $fileExtension = pathinfo($filePath, PATHINFO_EXTENSION);
            
            if (in_array($fileExtension, ['xlsx', 'xls'])) {
                $data = $this->readExcelFile($filePath);
            } elseif ($fileExtension === 'csv') {
                $data = $this->readCsvFile($filePath);
            } else {
                throw new \Exception('Formato de arquivo não suportado');
            }

            $results = $this->processEstabelecimentosData($data, $options);
            
            $importLog->update([
                'status' => BackupLog::STATUS_COMPLETED,
                'completed_at' => now(),
                'metadata' => array_merge($importLog->metadata, [
                    'results' => $results
                ])
            ]);

            return $importLog;

        } catch (\Exception $e) {
            $importLog->update([
                'status' => BackupLog::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * Ler arquivo Excel
     */
    protected function readExcelFile(string $filePath): array
    {
        $fullPath = $this->importDisk->path($filePath);
        
        $data = Excel::toArray(new class implements \Maatwebsite\Excel\Concerns\ToArray {
            public function array(array $array): array {
                return $array;
            }
        }, $fullPath);

        return $data[0] ?? []; // Primeira planilha
    }

    /**
     * Ler arquivo CSV
     */
    protected function readCsvFile(string $filePath): array
    {
        $fullPath = $this->importDisk->path($filePath);
        
        $csv = Reader::createFromPath($fullPath, 'r');
        $csv->setHeaderOffset(0); // Primeira linha como cabeçalho
        
        $data = [];
        foreach ($csv as $record) {
            $data[] = $record;
        }
        
        return $data;
    }

    /**
     * Processar dados de usuários
     */
    protected function processUsersData(array $data, array $options): array
    {
        $results = [
            'total' => count($data),
            'imported' => 0,
            'skipped' => 0,
            'errors' => []
        ];

        // Remover cabeçalho se existir
        if (isset($options['has_header']) && $options['has_header']) {
            array_shift($data);
            $results['total']--;
        }

        DB::beginTransaction();
        
        try {
            foreach ($data as $index => $row) {
                try {
                    $userData = $this->mapUserData($row, $options);
                    
                    // Validar dados
                    $validator = Validator::make($userData, [
                        'name' => 'required|string|max:255',
                        'email' => 'required|email|unique:users,email',
                        'phone' => 'nullable|string|max:20',
                        'role' => 'required|in:admin,fisioterapeuta,paciente,empresa',
                    ]);

                    if ($validator->fails()) {
                        $results['errors'][] = [
                            'row' => $index + 1,
                            'errors' => $validator->errors()->all()
                        ];
                        $results['skipped']++;
                        continue;
                    }

                    // Verificar se usuário já existe
                    if (User::where('email', $userData['email'])->exists()) {
                        if (!isset($options['update_existing']) || !$options['update_existing']) {
                            $results['skipped']++;
                            continue;
                        }
                        
                        // Atualizar usuário existente
                        $user = User::where('email', $userData['email'])->first();
                        $user->update($userData);
                    } else {
                        // Criar novo usuário
                        $userData['password'] = Hash::make($userData['password'] ?? 'password123');
                        $userData['email_verified_at'] = now();
                        User::create($userData);
                    }

                    $results['imported']++;

                } catch (\Exception $e) {
                    $results['errors'][] = [
                        'row' => $index + 1,
                        'error' => $e->getMessage()
                    ];
                    $results['skipped']++;
                }
            }

            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        return $results;
    }

    /**
     * Processar dados de estabelecimentos
     */
    protected function processEstabelecimentosData(array $data, array $options): array
    {
        $results = [
            'total' => count($data),
            'imported' => 0,
            'skipped' => 0,
            'errors' => []
        ];

        // Remover cabeçalho se existir
        if (isset($options['has_header']) && $options['has_header']) {
            array_shift($data);
            $results['total']--;
        }

        DB::beginTransaction();
        
        try {
            foreach ($data as $index => $row) {
                try {
                    $estabelecimentoData = $this->mapEstabelecimentoData($row, $options);
                    
                    // Validar dados
                    $validator = Validator::make($estabelecimentoData, [
                        'nome' => 'required|string|max:255',
                        'categoria' => 'required|string|max:100',
                        'telefone' => 'nullable|string|max:20',
                        'email' => 'nullable|email',
                        'endereco' => 'required|string|max:500',
                        'cidade' => 'required|string|max:100',
                        'estado' => 'required|string|max:2',
                        'cep' => 'required|string|max:10',
                    ]);

                    if ($validator->fails()) {
                        $results['errors'][] = [
                            'row' => $index + 1,
                            'errors' => $validator->errors()->all()
                        ];
                        $results['skipped']++;
                        continue;
                    }

                    // Gerar slug único
                    $estabelecimentoData['slug'] = $this->generateUniqueSlug($estabelecimentoData['nome']);
                    
                    // Criar estabelecimento
                    Estabelecimento::create($estabelecimentoData);
                    $results['imported']++;

                } catch (\Exception $e) {
                    $results['errors'][] = [
                        'row' => $index + 1,
                        'error' => $e->getMessage()
                    ];
                    $results['skipped']++;
                }
            }

            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        return $results;
    }

    /**
     * Mapear dados do usuário
     */
    protected function mapUserData(array $row, array $options): array
    {
        $mapping = $options['column_mapping'] ?? [
            'name' => 0,
            'email' => 1,
            'phone' => 2,
            'role' => 3
        ];

        return [
            'name' => $row[$mapping['name']] ?? '',
            'email' => $row[$mapping['email']] ?? '',
            'phone' => $row[$mapping['phone']] ?? null,
            'role' => $row[$mapping['role']] ?? 'paciente',
            'active' => true
        ];
    }

    /**
     * Mapear dados do estabelecimento
     */
    protected function mapEstabelecimentoData(array $row, array $options): array
    {
        $mapping = $options['column_mapping'] ?? [
            'nome' => 0,
            'categoria' => 1,
            'telefone' => 2,
            'email' => 3,
            'endereco' => 4,
            'cidade' => 5,
            'estado' => 6,
            'cep' => 7
        ];

        return [
            'nome' => $row[$mapping['nome']] ?? '',
            'categoria' => $row[$mapping['categoria']] ?? '',
            'telefone' => $row[$mapping['telefone']] ?? null,
            'email' => $row[$mapping['email']] ?? null,
            'endereco' => $row[$mapping['endereco']] ?? '',
            'cidade' => $row[$mapping['cidade']] ?? '',
            'estado' => $row[$mapping['estado']] ?? '',
            'cep' => $row[$mapping['cep']] ?? '',
            'ativo' => true
        ];
    }

    /**
     * Gerar slug único
     */
    protected function generateUniqueSlug(string $name): string
    {
        $slug = \Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        while (Estabelecimento::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Listar importações
     */
    public function listImports(): array
    {
        return BackupLog::byType(BackupLog::TYPE_IMPORT)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Obter detalhes da importação
     */
    public function getImportDetails(int $importId): BackupLog
    {
        return BackupLog::with('user')->findOrFail($importId);
    }

    /**
     * Deletar importação
     */
    public function deleteImport(int $importId): bool
    {
        $importLog = BackupLog::findOrFail($importId);

        try {
            $importLog->delete();
            return true;
        } catch (\Exception $e) {
            Log::error('Falha ao deletar importação', [
                'import_id' => $importId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Validar arquivo de importação
     */
    public function validateImportFile(string $filePath, string $type): array
    {
        if (!$this->importDisk->exists($filePath)) {
            return [
                'valid' => false,
                'error' => 'Arquivo não encontrado'
            ];
        }

        $fileExtension = pathinfo($filePath, PATHINFO_EXTENSION);

        if (!in_array($fileExtension, ['xlsx', 'xls', 'csv'])) {
            return [
                'valid' => false,
                'error' => 'Formato de arquivo não suportado. Use Excel (.xlsx, .xls) ou CSV (.csv)'
            ];
        }

        try {
            if (in_array($fileExtension, ['xlsx', 'xls'])) {
                $data = $this->readExcelFile($filePath);
            } else {
                $data = $this->readCsvFile($filePath);
            }

            if (empty($data)) {
                return [
                    'valid' => false,
                    'error' => 'Arquivo está vazio'
                ];
            }

            // Validar estrutura baseada no tipo
            $requiredColumns = $this->getRequiredColumns($type);
            $firstRow = $data[0];

            if (count($firstRow) < count($requiredColumns)) {
                return [
                    'valid' => false,
                    'error' => 'Arquivo não possui colunas suficientes. Esperado: ' . implode(', ', $requiredColumns)
                ];
            }

            return [
                'valid' => true,
                'rows_count' => count($data),
                'columns_count' => count($firstRow),
                'preview' => array_slice($data, 0, 5) // Primeiras 5 linhas para preview
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'error' => 'Erro ao ler arquivo: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Obter colunas obrigatórias por tipo
     */
    protected function getRequiredColumns(string $type): array
    {
        switch ($type) {
            case 'users':
                return ['Nome', 'Email', 'Telefone', 'Tipo'];
            case 'estabelecimentos':
                return ['Nome', 'Categoria', 'Telefone', 'Email', 'Endereço', 'Cidade', 'Estado', 'CEP'];
            default:
                return [];
        }
    }

    /**
     * Gerar template de importação
     */
    public function generateImportTemplate(string $type): string
    {
        $templates = [
            'users' => [
                ['Nome', 'Email', 'Telefone', 'Tipo'],
                ['João Silva', '<EMAIL>', '(11) 99999-9999', 'paciente'],
                ['Maria Santos', '<EMAIL>', '(11) 88888-8888', 'fisioterapeuta'],
            ],
            'estabelecimentos' => [
                ['Nome', 'Categoria', 'Telefone', 'Email', 'Endereço', 'Cidade', 'Estado', 'CEP'],
                ['Clínica Exemplo', 'clinica', '(11) 3333-3333', '<EMAIL>', 'Rua das Flores, 123', 'São Paulo', 'SP', '01234-567'],
                ['Farmácia Central', 'farmacia', '(11) 4444-4444', '<EMAIL>', 'Av. Principal, 456', 'São Paulo', 'SP', '01234-890'],
            ]
        ];

        if (!isset($templates[$type])) {
            throw new \Exception('Tipo de template não encontrado');
        }

        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $fileName = "template_{$type}_{$timestamp}.xlsx";
        $filePath = "templates/{$fileName}";

        Excel::store(new class($templates[$type]) implements \Maatwebsite\Excel\Concerns\FromArray {
            private $data;

            public function __construct($data) {
                $this->data = $data;
            }

            public function array(): array {
                return $this->data;
            }
        }, $filePath, 'local');

        return $filePath;
    }
}
