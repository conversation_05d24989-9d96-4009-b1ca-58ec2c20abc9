<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Pagamento;
use App\Models\Assinatura;
use Carbon\Carbon;

class PagamentoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $assinaturas = Assinatura::with('plano')->get();

        if ($assinaturas->isEmpty()) {
            return;
        }

        $formasPagamento = ['cartao_credito', 'cartao_debito', 'pix', 'boleto'];
        $statuses = ['pago', 'pendente', 'falhou', 'cancelado'];
        $pagamentos = [];

        foreach ($assinaturas as $assinatura) {
            // Criar histórico de pagamentos (últimos 3 meses + mês atual + próximos 2 meses)
            for ($i = 3; $i >= -2; $i--) {
                $dataVencimento = Carbon::now()->subMonths($i)->day(10); // Todo dia 10
                $status = $statuses[array_rand($statuses)];

                // Ajustar probabilidades para dados mais realistas
                if ($i > 0) {
                    // Pagamentos passados têm maior chance de estar pagos
                    $status = rand(1, 10) <= 8 ? 'pago' : $status;
                } elseif ($i === 0) {
                    // Pagamento do mês atual - mix de pagos e pendentes
                    $status = rand(1, 10) <= 6 ? 'pago' : 'pendente';
                } else {
                    // Pagamentos futuros são sempre pendentes
                    $status = 'pendente';
                }

                $formaPagamento = $formasPagamento[array_rand($formasPagamento)];
                $dataPagamento = null;
                $transactionId = null;

                if ($status === 'pago') {
                    // Para pagamentos pagos, definir data de pagamento realista
                    if ($i >= 0) {
                        // Pagamentos do passado e presente
                        $dataPagamento = $dataVencimento->copy()->addDays(rand(-2, 5));
                        // Garantir que não seja futuro
                        if ($dataPagamento->isFuture()) {
                            $dataPagamento = Carbon::now()->subDays(rand(1, 10));
                        }
                    }
                    $transactionId = 'TXN_' . strtoupper(uniqid());
                }

                $pagamentos[] = [
                    'assinatura_id' => $assinatura->id,
                    'amount' => $assinatura->monthly_price,
                    'status' => $status,
                    'method' => $formaPagamento,
                    'transaction_id' => $transactionId,
                    'due_date' => $dataVencimento,
                    'paid_at' => $dataPagamento,
                    'notes' => $this->getObservacaoByStatus($status),
                    'created_at' => $dataVencimento->copy()->subDays(rand(1, 5)),
                    'updated_at' => $dataPagamento ?? $dataVencimento->copy()->subDays(rand(1, 5)),
                ];
            }

            // Criar alguns pagamentos futuros para assinaturas ativas
            if ($assinatura->status === 'ativa') {
                for ($i = 1; $i <= 2; $i++) {
                    $dataVencimento = Carbon::now()->addMonths($i)->day(10);
                    
                    $pagamentos[] = [
                        'assinatura_id' => $assinatura->id,
                        'amount' => $assinatura->monthly_price,
                        'status' => 'pendente',
                        'method' => $formasPagamento[array_rand($formasPagamento)],
                        'transaction_id' => null,
                        'due_date' => $dataVencimento,
                        'paid_at' => null,
                        'notes' => 'Pagamento programado',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
        }

        // Inserir pagamentos em lotes para melhor performance
        $chunks = array_chunk($pagamentos, 50);
        foreach ($chunks as $chunk) {
            Pagamento::insert($chunk);
        }
    }

    private function getObservacaoByStatus($status)
    {
        $observacoes = [
            'pago' => [
                'Pagamento processado com sucesso',
                'Transação aprovada automaticamente',
                'Pagamento confirmado pelo gateway',
                'Cobrança processada via cartão',
                'PIX recebido e confirmado',
            ],
            'pendente' => [
                'Aguardando processamento',
                'Boleto gerado, aguardando pagamento',
                'Cobrança enviada ao cliente',
                'Aguardando confirmação do pagamento',
                null,
            ],
            'falhou' => [
                'Cartão recusado - saldo insuficiente',
                'Falha na comunicação com o gateway',
                'Dados do cartão inválidos',
                'Transação negada pelo banco',
                'Limite de cartão excedido',
            ],
            'cancelado' => [
                'Cancelado pelo cliente',
                'Assinatura cancelada',
                'Pagamento cancelado por solicitação',
                'Cancelado por inadimplência',
                null,
            ],
        ];

        $opcoes = $observacoes[$status] ?? [null];
        return $opcoes[array_rand($opcoes)];
    }
}
