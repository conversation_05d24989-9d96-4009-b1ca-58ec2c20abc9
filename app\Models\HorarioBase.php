<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class HorarioBase extends Model
{
    use HasFactory;

    protected $table = 'horarios_base';

    protected $fillable = [
        'fisioterapeuta_id',
        'dia_semana',
        'hora_inicio',
        'hora_fim',
        'periodo_nome',
        'ativo',
    ];

    protected $casts = [
        'hora_inicio' => 'datetime:H:i',
        'hora_fim' => 'datetime:H:i',
        'ativo' => 'boolean',
    ];

    // Relacionamentos
    public function fisioterapeuta()
    {
        return $this->belongsTo(User::class, 'fisioterapeuta_id');
    }

    // Scopes
    public function scopeAtivos($query)
    {
        return $query->where('ativo', true);
    }

    public function scopePorFisioterapeuta($query, $fisioterapeutaId)
    {
        return $query->where('fisioterapeuta_id', $fisioterapeutaId);
    }

    public function scopePorDiaSemana($query, $diaSemana)
    {
        return $query->where('dia_semana', $diaSemana);
    }

    // Métodos auxiliares
    public function getDiaSemanaTextoAttribute()
    {
        $dias = [
            0 => 'Domingo',
            1 => 'Segunda-feira',
            2 => 'Terça-feira',
            3 => 'Quarta-feira',
            4 => 'Quinta-feira',
            5 => 'Sexta-feira',
            6 => 'Sábado',
        ];

        return $dias[$this->dia_semana] ?? 'Desconhecido';
    }

    public function getFormattedHorarioAttribute()
    {
        return Carbon::parse($this->hora_inicio)->format('H:i') . ' - ' . 
               Carbon::parse($this->hora_fim)->format('H:i');
    }

    public function isDisponivel($hora)
    {
        if (!$this->ativo) {
            return false;
        }

        $horaCarbon = Carbon::parse($hora);
        $horaInicio = Carbon::parse($this->hora_inicio);
        $horaFim = Carbon::parse($this->hora_fim);

        return $horaCarbon->between($horaInicio, $horaFim);
    }

    public function conflitaCom($horaInicio, $horaFim)
    {
        $inicioExistente = Carbon::parse($this->hora_inicio);
        $fimExistente = Carbon::parse($this->hora_fim);
        $novoInicio = Carbon::parse($horaInicio);
        $novoFim = Carbon::parse($horaFim);

        // Verifica se há sobreposição
        return $novoInicio->lt($fimExistente) && $novoFim->gt($inicioExistente);
    }
}
