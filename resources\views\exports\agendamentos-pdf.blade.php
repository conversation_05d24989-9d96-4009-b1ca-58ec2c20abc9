<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Relatório de Agendamentos - F4 Fisio</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #0066cc;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .filters {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .filters h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .filters p {
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #0066cc;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .summary {
            background-color: #e7f3ff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .summary h3 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
        .status-confirmado {
            color: #28a745;
            font-weight: bold;
        }
        .status-pendente {
            color: #ffc107;
            font-weight: bold;
        }
        .status-cancelado {
            color: #dc3545;
            font-weight: bold;
        }
        .status-concluido {
            color: #17a2b8;
            font-weight: bold;
        }
        .value {
            text-align: right;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>F4 Fisio - Relatório de Agendamentos</h1>
        <p>Gerado em: {{ $generated_at->format('d/m/Y H:i:s') }}</p>
    </div>

    @if(!empty($filters))
    <div class="filters">
        <h3>Filtros Aplicados:</h3>
        @if(isset($filters['status']))
            <p><strong>Status:</strong> {{ ucfirst($filters['status']) }}</p>
        @endif
        @if(isset($filters['fisioterapeuta_id']))
            <p><strong>Fisioterapeuta ID:</strong> {{ $filters['fisioterapeuta_id'] }}</p>
        @endif
        @if(isset($filters['date_from']))
            <p><strong>Data Inicial:</strong> {{ \Carbon\Carbon::parse($filters['date_from'])->format('d/m/Y') }}</p>
        @endif
        @if(isset($filters['date_to']))
            <p><strong>Data Final:</strong> {{ \Carbon\Carbon::parse($filters['date_to'])->format('d/m/Y') }}</p>
        @endif
    </div>
    @endif

    <div class="summary">
        <h3>Resumo</h3>
        <p><strong>Total de Agendamentos:</strong> {{ count($agendamentos) }}</p>
        <p><strong>Por Status:</strong></p>
        <ul>
            @foreach($agendamentos->groupBy('status') as $status => $statusAgendamentos)
                <li>{{ ucfirst($status) }}: {{ $statusAgendamentos->count() }}</li>
            @endforeach
        </ul>
        <p><strong>Valor Total:</strong> R$ {{ number_format($agendamentos->sum('price'), 2, ',', '.') }}</p>
        <p><strong>Valor Médio por Sessão:</strong> R$ {{ number_format($agendamentos->avg('price'), 2, ',', '.') }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Paciente</th>
                <th>Fisioterapeuta</th>
                <th>Data/Hora</th>
                <th>Status</th>
                <th>Valor</th>
                <th>Observações</th>
            </tr>
        </thead>
        <tbody>
            @foreach($agendamentos as $agendamento)
            <tr>
                <td>{{ $agendamento->id }}</td>
                <td>{{ $agendamento->paciente->name ?? 'N/A' }}</td>
                <td>{{ $agendamento->fisioterapeuta->name ?? 'N/A' }}</td>
                <td>{{ $agendamento->scheduled_at->format('d/m/Y H:i') }}</td>
                <td class="status-{{ $agendamento->status }}">
                    {{ ucfirst($agendamento->status) }}
                </td>
                <td class="value">R$ {{ number_format($agendamento->price, 2, ',', '.') }}</td>
                <td>{{ \Str::limit($agendamento->notes ?? 'N/A', 50) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>F4 Fisio - Sistema de Gestão de Fisioterapia</p>
        <p>Este relatório foi gerado automaticamente pelo sistema</p>
    </div>
</body>
</html>
