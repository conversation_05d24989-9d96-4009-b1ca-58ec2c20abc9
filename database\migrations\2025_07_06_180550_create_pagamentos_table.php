<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pagamentos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('assinatura_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 8, 2); // Valor pago
            $table->enum('status', ['pendente', 'pago', 'falhou', 'cancelado'])->default('pendente');
            $table->enum('method', ['cartao_credito', 'cartao_debito', 'pix', 'boleto'])->nullable();
            $table->string('transaction_id')->nullable(); // ID da transação no gateway
            $table->date('due_date'); // Data de vencimento
            $table->datetime('paid_at')->nullable(); // Data do pagamento
            $table->text('notes')->nullable(); // Observações
            $table->json('gateway_response')->nullable(); // Resposta do gateway de pagamento
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pagamentos');
    }
};
