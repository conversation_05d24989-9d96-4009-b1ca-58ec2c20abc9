import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, Mail, Phone, User, Calendar, DollarSign } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import { safeRoute } from '@/utils/route-helper';

interface Fisioterapeuta {
    id: number;
    crefito: string;
    specializations: string[];
    bio?: string;
    experience_years?: number;
    hourly_rate: number;
    active: boolean;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
        phone?: string;
        active: boolean;
    };
    agendamentos?: Array<{
        id: number;
        data_hora: string;
        status: string;
        paciente: {
            name: string;
        };
    }>;
    avaliacoes?: Array<{
        id: number;
        rating: number;
        comment?: string;
        created_at: string;
    }>;
}

interface Props {
    fisioterapeuta: Fisioterapeuta;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Fisioterapeutas',
        href: '/admin/fisioterapeutas',
    },
    {
        title: 'Detalhes do Fisioterapeuta',
        href: '#',
    },
];

export default function FisioterapeutaShow({ fisioterapeuta }: Props) {
    const getStatusBadge = (active: boolean) => {
        return active ? (
            <Badge variant="default" className="bg-green-100 text-green-800">
                Ativo
            </Badge>
        ) : (
            <Badge variant="secondary" className="bg-red-100 text-red-800">
                Inativo
            </Badge>
        );
    };

    const averageRating = fisioterapeuta.avaliacoes?.length 
        ? fisioterapeuta.avaliacoes.reduce((sum, av) => sum + av.rating, 0) / fisioterapeuta.avaliacoes.length
        : 0;

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${fisioterapeuta.user.name} - Fisioterapeuta`} />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{fisioterapeuta.user.name}</h1>
                        <p className="text-muted-foreground">
                            CREFITO: {fisioterapeuta.crefito}
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Link href={safeRoute('admin.fisioterapeutas.edit', fisioterapeuta.id)} preserveState>
                            <Button>
                                <Edit className="mr-2 h-4 w-4" />
                                Editar
                            </Button>
                        </Link>
                        <Link href={safeRoute('admin.fisioterapeutas.index')} preserveState>
                            <Button variant="ghost">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Informações Principais */}
                    <div className="lg:col-span-2 space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center justify-between">
                                    Informações Pessoais
                                    {getStatusBadge(fisioterapeuta.user.active)}
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="flex items-center space-x-2">
                                        <User className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Nome:</span>
                                        <span>{fisioterapeuta.user.name}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Mail className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Email:</span>
                                        <span>{fisioterapeuta.user.email}</span>
                                    </div>
                                    {fisioterapeuta.user.phone && (
                                        <div className="flex items-center space-x-2">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-medium">Telefone:</span>
                                            <span>{fisioterapeuta.user.phone}</span>
                                        </div>
                                    )}
                                    <div className="flex items-center space-x-2">
                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Cadastrado em:</span>
                                        <span>{new Date(fisioterapeuta.created_at).toLocaleDateString('pt-BR')}</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Informações Profissionais</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <span className="font-medium">CREFITO:</span>
                                        <p className="text-muted-foreground">{fisioterapeuta.crefito}</p>
                                    </div>
                                    {fisioterapeuta.experience_years && (
                                        <div>
                                            <span className="font-medium">Anos de Experiência:</span>
                                            <p className="text-muted-foreground">{fisioterapeuta.experience_years} anos</p>
                                        </div>
                                    )}
                                    <div className="flex items-center space-x-2">
                                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Valor por Hora:</span>
                                        <span className="text-green-600 font-bold">
                                            {new Intl.NumberFormat('pt-BR', {
                                                style: 'currency',
                                                currency: 'BRL',
                                            }).format(fisioterapeuta.hourly_rate)}
                                        </span>
                                    </div>
                                </div>

                                <div>
                                    <span className="font-medium">Especializações:</span>
                                    <div className="flex flex-wrap gap-2 mt-2">
                                        {fisioterapeuta.specializations.map((spec, index) => (
                                            <Badge key={index} variant="secondary">
                                                {spec}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>

                                {fisioterapeuta.bio && (
                                    <div>
                                        <span className="font-medium">Biografia:</span>
                                        <p className="text-muted-foreground mt-1">{fisioterapeuta.bio}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Estatísticas */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Estatísticas</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-primary">
                                        {fisioterapeuta.agendamentos?.length || 0}
                                    </div>
                                    <p className="text-sm text-muted-foreground">Agendamentos</p>
                                </div>
                                
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-yellow-600">
                                        {averageRating.toFixed(1)}
                                    </div>
                                    <p className="text-sm text-muted-foreground">
                                        Avaliação Média ({fisioterapeuta.avaliacoes?.length || 0} avaliações)
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        {fisioterapeuta.agendamentos && fisioterapeuta.agendamentos.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Últimos Agendamentos</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {fisioterapeuta.agendamentos.slice(0, 5).map((agendamento) => (
                                            <div key={agendamento.id} className="flex justify-between items-center text-sm">
                                                <div>
                                                    <p className="font-medium">{agendamento.paciente.name}</p>
                                                    <p className="text-muted-foreground">
                                                        {new Date(agendamento.data_hora).toLocaleDateString('pt-BR')}
                                                    </p>
                                                </div>
                                                <Badge variant="outline">
                                                    {agendamento.status}
                                                </Badge>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
