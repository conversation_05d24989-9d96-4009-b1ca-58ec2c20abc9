import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Activity, Calendar, Clock, Download, FileText, Filter, Heart, Home, TrendingUp, User } from 'lucide-react';
import { useState } from 'react';

interface Props {
    agendamentos: {
        data: Array<{
            id: number;
            data_agendamento: string;
            horario: string;
            status: string;
            tipo: string;
            fisioterapeuta: {
                user: {
                    name: string;
                };
            };
            relatorio_sessao?: {
                observacoes: string;
                exercicios_realizados: string;
            };
        }>;
        links: any;
        meta: any;
    };
    stats: {
        totalSessoes: number;
        sessoesUltimoMes: number;
        avaliacoesRealizadas: number;
        tempoTratamento: number;
    };
    evolucaoMensal: Array<{
        mes: string;
        sessoes: number;
    }>;
    avaliacoes: Array<{
        id: number;
        created_at: string;
        observacoes: string;
        fisioterapeuta: {
            user: {
                name: string;
            };
        };
    }>;
    prescricoes: Array<{
        id: number;
        titulo: string;
        descricao: string;
        medicamentos?: string;
        exercicios?: string;
        cuidados_especiais?: string;
        data_inicio: string;
        data_fim?: string;
        status: string;
        created_at: string;
        fisioterapeuta: {
            name: string;
        };
    }>;
    orientacoes: Array<{
        id: number;
        titulo: string;
        descricao: string;
        exercicios_recomendados?: string;
        cuidados_posturais?: string;
        atividades_evitar?: string;
        dicas_gerais?: string;
        frequencia_dias?: number;
        horario_recomendado?: string;
        prioridade: string;
        created_at: string;
        fisioterapeuta: {
            name: string;
        };
    }>;
    filtros: {
        periodo: string;
        tipo: string;
        status: string;
    };
}

export default function PacienteHistorico() {
    const pageProps = usePage().props as any;
    const { agendamentos, stats, evolucaoMensal, avaliacoes, prescricoes, orientacoes, filtros } = pageProps;
    const [filtrosPendentes, setFiltrosPendentes] = useState(filtros);

    const aplicarFiltros = () => {
        router.get(route('paciente.historico.index'), filtrosPendentes, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            agendado: 'default',
            concluido: 'default',
            cancelado: 'destructive',
            em_andamento: 'secondary',
        } as const;

        const labels = {
            agendado: 'Agendado',
            concluido: 'Concluído',
            cancelado: 'Cancelado',
            em_andamento: 'Em Andamento',
        };

        return <Badge variant={variants[status as keyof typeof variants] || 'default'}>{labels[status as keyof typeof labels] || status}</Badge>;
    };

    const formatDate = (dateString: string | null | undefined) => {
        if (!dateString) return 'Data não informada';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Data inválida';
            return date.toLocaleDateString('pt-BR');
        } catch {
            return 'Data inválida';
        }
    };

    const formatTime = (timeString: string | null | undefined) => {
        return timeString ? timeString.substring(0, 5) : '--:--';
    };

    return (
        <AppLayout>
            <Head title="Histórico de Tratamento" />
            <div className="min-h-screen bg-background">
                {/* Header */}
                <section className="bg-gradient-to-b from-background to-muted/30 py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 className="text-3xl font-bold">Histórico de Tratamento</h1>
                                <p className="text-muted-foreground">Acompanhe sua evolução e histórico de sessões</p>
                            </div>
                            <Button
                                variant="outline"
                                onClick={() => window.open(route('paciente.historico.export'), '_blank')}
                                className="w-full sm:w-auto"
                            >
                                <Download className="mr-2 h-4 w-4" />
                                <span className="hidden sm:inline">Exportar Histórico</span>
                                <span className="sm:hidden">Exportar</span>
                            </Button>
                        </div>
                    </div>
                </section>

                {/* Stats Cards */}
                <section className="-mx-4 bg-muted/30 py-12 sm:-mx-6 lg:-mx-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
                            {/* Total de Sessões */}
                            <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                <div className="mb-4 flex items-center gap-3">
                                    <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                        <Activity className="h-5 w-5" />
                                    </Badge>
                                    <div>
                                        <p className="text-sm font-medium text-foreground">Total de Sessões</p>
                                        <p className="text-xs text-muted-foreground">Desde o início</p>
                                    </div>
                                </div>
                                <div>
                                    <div className="mb-1 text-2xl font-semibold text-primary">{stats.totalSessoes}</div>
                                    <p className="text-sm text-muted-foreground">Sessões realizadas</p>
                                </div>
                            </div>

                            {/* Último Mês */}
                            <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                <div className="mb-4 flex items-center gap-3">
                                    <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                        <Calendar className="h-5 w-5" />
                                    </Badge>
                                    <div>
                                        <p className="text-sm font-medium text-foreground">Último Mês</p>
                                        <p className="text-xs text-muted-foreground">Sessões realizadas</p>
                                    </div>
                                </div>
                                <div>
                                    <div className="mb-1 text-2xl font-semibold text-primary">{stats.sessoesUltimoMes}</div>
                                    <p className="text-sm text-muted-foreground">No último mês</p>
                                </div>
                            </div>

                            {/* Avaliações */}
                            <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                <div className="mb-4 flex items-center gap-3">
                                    <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                        <FileText className="h-5 w-5" />
                                    </Badge>
                                    <div>
                                        <p className="text-sm font-medium text-foreground">Avaliações</p>
                                        <p className="text-xs text-muted-foreground">Realizadas</p>
                                    </div>
                                </div>
                                <div>
                                    <div className="mb-1 text-2xl font-semibold text-primary">{stats.avaliacoesRealizadas}</div>
                                    <p className="text-sm text-muted-foreground">Avaliações completas</p>
                                </div>
                            </div>

                            {/* Tempo de Tratamento */}
                            <div className="rounded-2xl border border-transparent bg-background px-6 py-5 shadow-sm ring-1 ring-foreground/10">
                                <div className="mb-4 flex items-center gap-3">
                                    <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                        <TrendingUp className="h-5 w-5" />
                                    </Badge>
                                    <div>
                                        <p className="text-sm font-medium text-foreground">Tempo de Tratamento</p>
                                        <p className="text-xs text-muted-foreground">Duração</p>
                                    </div>
                                </div>
                                <div>
                                    <div className="mb-1 text-2xl font-semibold text-primary">{stats.tempoTratamento}</div>
                                    <p className="text-sm text-muted-foreground">meses de tratamento</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Filtros */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Filter className="h-5 w-5" />
                                    Filtros
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex gap-4">
                                    <div className="flex-1">
                                        <Select
                                            value={filtrosPendentes.periodo}
                                            onValueChange={(value) => setFiltrosPendentes({ ...filtrosPendentes, periodo: value })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Período" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="1_mes">Último mês</SelectItem>
                                                <SelectItem value="3_meses">Últimos 3 meses</SelectItem>
                                                <SelectItem value="6_meses">Últimos 6 meses</SelectItem>
                                                <SelectItem value="1_ano">Último ano</SelectItem>
                                                <SelectItem value="todos">Todos</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="flex-1">
                                        <Select
                                            value={filtrosPendentes.tipo}
                                            onValueChange={(value) => setFiltrosPendentes({ ...filtrosPendentes, tipo: value })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Tipo" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="todos">Todos</SelectItem>
                                                <SelectItem value="sessoes">Sessões</SelectItem>
                                                <SelectItem value="avaliacoes">Avaliações</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="flex-1">
                                        <Select
                                            value={filtrosPendentes.status}
                                            onValueChange={(value) => setFiltrosPendentes({ ...filtrosPendentes, status: value })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="todos">Todos</SelectItem>
                                                <SelectItem value="concluido">Concluído</SelectItem>
                                                <SelectItem value="cancelado">Cancelado</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <Button onClick={aplicarFiltros}>Aplicar Filtros</Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Lista de Agendamentos */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle>Histórico de Sessões</CardTitle>
                            </CardHeader>
                            <CardContent>
                                {agendamentos.data.length > 0 ? (
                                    <div className="overflow-x-auto">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>Data/Hora</TableHead>
                                                    <TableHead>Fisioterapeuta</TableHead>
                                                    <TableHead>Tipo</TableHead>
                                                    <TableHead>Status</TableHead>
                                                    <TableHead className="text-right">Ações</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {agendamentos.data.map((agendamento: any) => (
                                                    <TableRow key={agendamento.id}>
                                                        <TableCell>
                                                            <div className="flex flex-col">
                                                                <div className="flex items-center gap-2">
                                                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                                                    <span className="font-medium">{formatDate(agendamento.data_agendamento)}</span>
                                                                </div>
                                                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                                                    <Clock className="h-4 w-4" />
                                                                    <span>{formatTime(agendamento.horario)}</span>
                                                                </div>
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-2">
                                                                <User className="h-4 w-4 text-muted-foreground" />
                                                                <span className="font-medium">{agendamento.fisioterapeuta.user.name}</span>
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <span className="text-sm">
                                                                {agendamento.tipo === 'sessao' ? 'Sessão de Fisioterapia' : 'Avaliação'}
                                                            </span>
                                                        </TableCell>
                                                        <TableCell>{getStatusBadge(agendamento.status)}</TableCell>
                                                        <TableCell className="text-right">
                                                            {agendamento.status === 'concluido' && (
                                                                <Link href={route('paciente.historico.show', agendamento.id)}>
                                                                    <Button variant="outline" size="sm">
                                                                        <FileText className="mr-2 h-4 w-4" />
                                                                        Ver Detalhes
                                                                    </Button>
                                                                </Link>
                                                            )}
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                ) : (
                                    <div className="py-8 text-center">
                                        <p className="text-muted-foreground">Nenhum agendamento encontrado</p>
                                    </div>
                                )}

                                {/* Paginação */}
                                {agendamentos.meta && agendamentos.meta.last_page > 1 && (
                                    <div className="mt-6 flex justify-center">
                                        <div className="flex gap-2">
                                            {agendamentos.links.map((link: any, index: number) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    size="sm"
                                                    onClick={() => link.url && router.get(link.url)}
                                                    disabled={!link.url}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Prescrições Recebidas */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Heart className="h-5 w-5 text-blue-600" />
                                    Prescrições Recebidas
                                </CardTitle>
                                <CardDescription>Medicamentos e tratamentos prescritos pelos fisioterapeutas</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {prescricoes && prescricoes.length > 0 ? (
                                    <div className="space-y-4">
                                        {prescricoes.map((prescricao: any) => (
                                            <div key={prescricao.id} className="space-y-3 rounded-lg border p-4">
                                                <div className="flex items-start justify-between">
                                                    <div>
                                                        <h4 className="text-lg font-semibold">{prescricao.titulo}</h4>
                                                        <p className="text-sm text-muted-foreground">
                                                            Por {prescricao.fisioterapeuta.name} • {formatDate(prescricao.created_at)}
                                                        </p>
                                                    </div>
                                                    <span
                                                        className={`rounded-full px-2 py-1 text-xs font-medium ${
                                                            prescricao.status === 'ativa'
                                                                ? 'bg-green-100 text-green-800'
                                                                : prescricao.status === 'concluida'
                                                                  ? 'bg-blue-100 text-blue-800'
                                                                  : 'bg-gray-100 text-gray-800'
                                                        }`}
                                                    >
                                                        {prescricao.status === 'ativa'
                                                            ? 'Ativa'
                                                            : prescricao.status === 'concluida'
                                                              ? 'Concluída'
                                                              : 'Cancelada'}
                                                    </span>
                                                </div>

                                                <p className="text-gray-700">{prescricao.descricao}</p>

                                                {prescricao.medicamentos && (
                                                    <div>
                                                        <h5 className="mb-1 text-sm font-medium text-gray-900">Medicamentos:</h5>
                                                        <p className="text-sm text-gray-600">{prescricao.medicamentos}</p>
                                                    </div>
                                                )}

                                                {prescricao.exercicios && (
                                                    <div>
                                                        <h5 className="mb-1 text-sm font-medium text-gray-900">Exercícios:</h5>
                                                        <p className="text-sm text-gray-600">{prescricao.exercicios}</p>
                                                    </div>
                                                )}

                                                {prescricao.cuidados_especiais && (
                                                    <div>
                                                        <h5 className="mb-1 text-sm font-medium text-gray-900">Cuidados Especiais:</h5>
                                                        <p className="text-sm text-gray-600">{prescricao.cuidados_especiais}</p>
                                                    </div>
                                                )}

                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    <span>Início: {formatDate(prescricao.data_inicio)}</span>
                                                    {prescricao.data_fim && <span>Fim: {formatDate(prescricao.data_fim)}</span>}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="py-8 text-center">
                                        <Heart className="mx-auto h-12 w-12 text-gray-400" />
                                        <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma prescrição</h3>
                                        <p className="mt-1 text-sm text-gray-500">Suas prescrições médicas aparecerão aqui.</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Orientações Domiciliares */}
                <section className="py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Home className="h-5 w-5 text-green-600" />
                                    Orientações Domiciliares
                                </CardTitle>
                                <CardDescription>Exercícios e cuidados para realizar em casa</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {orientacoes && orientacoes.length > 0 ? (
                                    <div className="space-y-4">
                                        {orientacoes.map((orientacao: any) => (
                                            <div key={orientacao.id} className="space-y-3 rounded-lg border p-4">
                                                <div className="flex items-start justify-between">
                                                    <div>
                                                        <h4 className="text-lg font-semibold">{orientacao.titulo}</h4>
                                                        <p className="text-sm text-muted-foreground">
                                                            Por {orientacao.fisioterapeuta.name} • {formatDate(orientacao.created_at)}
                                                        </p>
                                                    </div>
                                                    <span
                                                        className={`rounded-full px-2 py-1 text-xs font-medium ${
                                                            orientacao.prioridade === 'alta'
                                                                ? 'bg-red-100 text-red-800'
                                                                : orientacao.prioridade === 'media'
                                                                  ? 'bg-yellow-100 text-yellow-800'
                                                                  : 'bg-green-100 text-green-800'
                                                        }`}
                                                    >
                                                        Prioridade {orientacao.prioridade}
                                                    </span>
                                                </div>

                                                <p className="text-gray-700">{orientacao.descricao}</p>

                                                {orientacao.exercicios_recomendados && (
                                                    <div>
                                                        <h5 className="mb-1 text-sm font-medium text-gray-900">Exercícios Recomendados:</h5>
                                                        <pre className="text-sm whitespace-pre-wrap text-gray-600">
                                                            {orientacao.exercicios_recomendados}
                                                        </pre>
                                                    </div>
                                                )}

                                                {orientacao.cuidados_posturais && (
                                                    <div>
                                                        <h5 className="mb-1 text-sm font-medium text-gray-900">Cuidados Posturais:</h5>
                                                        <p className="text-sm text-gray-600">{orientacao.cuidados_posturais}</p>
                                                    </div>
                                                )}

                                                {orientacao.atividades_evitar && (
                                                    <div>
                                                        <h5 className="mb-1 text-sm font-medium text-gray-900">Atividades a Evitar:</h5>
                                                        <p className="text-sm text-gray-600">{orientacao.atividades_evitar}</p>
                                                    </div>
                                                )}

                                                {orientacao.dicas_gerais && (
                                                    <div>
                                                        <h5 className="mb-1 text-sm font-medium text-gray-900">Dicas Gerais:</h5>
                                                        <p className="text-sm text-gray-600">{orientacao.dicas_gerais}</p>
                                                    </div>
                                                )}

                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    {orientacao.frequencia_dias && <span>Frequência: {orientacao.frequencia_dias}x por semana</span>}
                                                    {orientacao.horario_recomendado && <span>Horário: {orientacao.horario_recomendado}</span>}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="py-8 text-center">
                                        <Home className="mx-auto h-12 w-12 text-gray-400" />
                                        <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma orientação</h3>
                                        <p className="mt-1 text-sm text-gray-500">Suas orientações domiciliares aparecerão aqui.</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </section>
            </div>
        </AppLayout>
    );
}
