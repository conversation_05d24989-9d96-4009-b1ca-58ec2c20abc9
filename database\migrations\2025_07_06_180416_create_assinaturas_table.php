<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assinaturas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Paciente
            $table->foreignId('plano_id')->constrained()->onDelete('cascade');
            $table->enum('status', ['ativa', 'suspensa', 'cancelada'])->default('ativa');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->integer('sessions_used')->default(0); // Sessões utilizadas no mês atual
            $table->date('current_period_start'); // Início do período atual
            $table->date('current_period_end'); // Fim do período atual
            $table->decimal('monthly_price', 8, 2); // Preço fixo da assinatura
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assinaturas');
    }
};
