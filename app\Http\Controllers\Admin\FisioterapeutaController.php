<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Fisioterapeuta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class FisioterapeutaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Fisioterapeuta::with('user');

        // Filtro por status
        if ($request->filled('active') && $request->active !== 'all') {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('active', $request->boolean('active'));
            });
        }

        // Busca por nome ou CREFITO
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('crefito', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function ($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $fisioterapeutas = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('admin/fisioterapeutas', [
            'fisioterapeutas' => $fisioterapeutas,
            'filters' => $request->only(['active', 'search']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Buscar usuários com role fisioterapeuta que ainda não têm perfil
        $usuariosDisponiveis = User::where('role', 'fisioterapeuta')
            ->whereDoesntHave('fisioterapeuta')
            ->get(['id', 'name', 'email']);

        return Inertia::render('admin/fisioterapeutas/create', [
            'usuariosDisponiveis' => $usuariosDisponiveis,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'crefito' => 'required|string|unique:fisioterapeutas',
            'specializations' => 'required|array|min:1',
            'bio' => 'nullable|string',
            'hourly_rate' => 'required|numeric|min:0',
            'available_areas' => 'required|array|min:1',
            'working_hours' => 'required|array',
        ]);

        DB::transaction(function () use ($validated) {
            Fisioterapeuta::create($validated);
        });

        return redirect()->route('admin.fisioterapeutas.index')
            ->with('success', 'Fisioterapeuta cadastrado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Fisioterapeuta $fisioterapeuta)
    {
        $fisioterapeuta->load([
            'user',
            'agendamentos' => function ($query) {
                $query->with('paciente')->orderBy('data_hora', 'desc')->limit(10);
            },
            'avaliacoes' => function ($query) {
                $query->with('paciente')->orderBy('created_at', 'desc')->limit(5);
            }
        ]);

        return Inertia::render('admin/fisioterapeutas/show', [
            'fisioterapeuta' => $fisioterapeuta,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Fisioterapeuta $fisioterapeuta)
    {
        $fisioterapeuta->load('user');

        return Inertia::render('admin/fisioterapeutas/edit', [
            'fisioterapeuta' => $fisioterapeuta,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Fisioterapeuta $fisioterapeuta)
    {
        $validated = $request->validate([
            'crefito' => 'required|string|unique:fisioterapeutas,crefito,' . $fisioterapeuta->id,
            'specializations' => 'required|array|min:1',
            'bio' => 'nullable|string',
            'hourly_rate' => 'required|numeric|min:0',
            'available_areas' => 'required|array|min:1',
            'working_hours' => 'required|array',
        ]);

        $fisioterapeuta->update($validated);

        return redirect()->route('admin.fisioterapeutas.index')
            ->with('success', 'Fisioterapeuta atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Fisioterapeuta $fisioterapeuta)
    {
        $fisioterapeuta->delete();

        return redirect()->route('admin.fisioterapeutas.index')
            ->with('success', 'Fisioterapeuta removido com sucesso!');
    }
}
