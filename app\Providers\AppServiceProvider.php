<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Forçar HTTPS em produção
        if (config('app.env') === 'production') {
            URL::forceScheme('https');

            // Configurar proxy para HTTPS (necessário para Sevalla)
            $this->app['request']->server->set('HTTPS', 'on');
        }
    }
}
