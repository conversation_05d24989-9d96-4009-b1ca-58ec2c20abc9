<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckEmpresaAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Verificar se o usuário é uma empresa
        if (!$user->isEmpresa()) {
            abort(403, 'Acesso negado. Esta área é restrita para empresas.');
        }

        // Verificar se a empresa tem estabelecimento vinculado
        if (!$user->estabelecimento) {
            return redirect()->route('empresa.setup')
                ->with('warning', 'Você precisa configurar seu estabelecimento para continuar.');
        }

        return $next($request);
    }
}
