import { test, expect } from '@playwright/test';
import { 
  ensureFisioterapeutaAuthenticated,
  navigateToFisioterapeutaPage,
  waitForPageLoad,
  fillForm,
  checkFormValidation,
  waitForNotification,
  defaultFisioterapeutaData
} from './helpers/fisioterapeuta-helpers';

test.describe('Fisioterapeuta - Perfil Profissional', () => {
  
  test.beforeEach(async ({ page }) => {
    await ensureFisioterapeutaAuthenticated(page);
    await navigateToFisioterapeutaPage(page, 'perfil');
  });

  test('deve carregar página de perfil corretamente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Verificar se o título da página está correto
    await expect(page.locator('h1, h2')).toContainText(/perfil|dados/i);
    
    // Verificar se os campos principais estão visíveis
    await expect(page.locator('input[name="crefito"], input[id="crefito"]')).toBeVisible();
    await expect(page.locator('textarea[name="bio"], textarea[id="bio"]')).toBeVisible();
    await expect(page.locator('input[name="hourly_rate"], input[id="hourly_rate"]')).toBeVisible();
  });

  test('deve exibir dados atuais do perfil', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Verificar se os campos estão preenchidos com dados existentes
    const crefitoField = page.locator('input[name="crefito"], input[id="crefito"]');
    const bioField = page.locator('textarea[name="bio"], textarea[id="bio"]');
    const hourlyRateField = page.locator('input[name="hourly_rate"], input[id="hourly_rate"]');
    
    // Verificar se os campos não estão vazios (assumindo que o perfil já foi configurado)
    const crefitoValue = await crefitoField.inputValue();
    const bioValue = await bioField.inputValue();
    const hourlyRateValue = await hourlyRateField.inputValue();
    
    if (crefitoValue) {
      expect(crefitoValue.length).toBeGreaterThan(0);
    }
    if (bioValue) {
      expect(bioValue.length).toBeGreaterThan(0);
    }
    if (hourlyRateValue) {
      expect(parseFloat(hourlyRateValue)).toBeGreaterThan(0);
    }
  });

  test('deve permitir edição dos dados básicos', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Dados para teste
    const newData = {
      crefito: '654321-F',
      bio: 'Bio atualizada para teste de edição do perfil profissional.',
      hourly_rate: '150.00'
    };
    
    // Preencher formulário com novos dados
    await fillForm(page, newData);
    
    // Submeter formulário
    const submitButton = page.locator('button[type="submit"], button:has-text("Salvar"), button:has-text("Atualizar")');
    await submitButton.click();
    
    // Aguardar notificação de sucesso
    await waitForNotification(page);
    
    // Verificar se os dados foram salvos
    await page.reload();
    await waitForPageLoad(page);
    
    const crefitoField = page.locator('input[name="crefito"], input[id="crefito"]');
    const bioField = page.locator('textarea[name="bio"], textarea[id="bio"]');
    const hourlyRateField = page.locator('input[name="hourly_rate"], input[id="hourly_rate"]');
    
    await expect(crefitoField).toHaveValue(newData.crefito);
    await expect(bioField).toHaveValue(newData.bio);
    await expect(hourlyRateField).toHaveValue(newData.hourly_rate);
  });

  test('deve validar campos obrigatórios', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Limpar campos obrigatórios
    await page.fill('input[name="crefito"], input[id="crefito"]', '');
    await page.fill('textarea[name="bio"], textarea[id="bio"]', '');
    await page.fill('input[name="hourly_rate"], input[id="hourly_rate"]', '');
    
    // Tentar submeter formulário
    const submitButton = page.locator('button[type="submit"], button:has-text("Salvar"), button:has-text("Atualizar")');
    await submitButton.click();
    
    // Verificar se há mensagens de validação
    const errorCount = await checkFormValidation(page);
    expect(errorCount).toBeGreaterThan(0);
  });

  test('deve validar formato do CREFITO', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Testar CREFITO com formato inválido
    await page.fill('input[name="crefito"], input[id="crefito"]', 'FORMATO_INVALIDO');
    
    const submitButton = page.locator('button[type="submit"], button:has-text("Salvar"), button:has-text("Atualizar")');
    await submitButton.click();
    
    // Verificar se há erro de validação específico para CREFITO
    const crefitoError = page.locator('input[name="crefito"] ~ .error, input[name="crefito"] + .error, .field-error');
    if (await crefitoError.count() > 0) {
      await expect(crefitoError).toBeVisible();
    }
  });

  test('deve validar valor por hora', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Testar valores inválidos
    const invalidValues = ['0', '-10', 'abc', ''];
    
    for (const value of invalidValues) {
      await page.fill('input[name="hourly_rate"], input[id="hourly_rate"]', value);
      
      const submitButton = page.locator('button[type="submit"], button:has-text("Salvar"), button:has-text("Atualizar")');
      await submitButton.click();
      
      // Verificar se há erro de validação
      const errorCount = await checkFormValidation(page);
      if (value === '0' || value === '-10' || value === 'abc' || value === '') {
        expect(errorCount).toBeGreaterThan(0);
      }
    }
  });

  test('deve permitir gerenciar especializações', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar seção de especializações
    const especializacoesSection = page.locator('text="Especializações", [data-testid="especializacoes"]');
    
    if (await especializacoesSection.count() > 0) {
      await expect(especializacoesSection).toBeVisible();
      
      // Procurar campo para adicionar especialização
      const addEspecializacaoField = page.locator('input[placeholder*="especialização"], input[placeholder*="Especialização"]');
      
      if (await addEspecializacaoField.count() > 0) {
        // Adicionar nova especialização
        await addEspecializacaoField.fill('Fisioterapia Respiratória');
        await page.keyboard.press('Enter');
        
        // Verificar se foi adicionada
        await expect(page.locator('text="Fisioterapia Respiratória"')).toBeVisible();
      }
    }
  });

  test('deve permitir gerenciar áreas de atendimento', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar seção de áreas de atendimento
    const areasSection = page.locator('text="Áreas de Atendimento", text="Locais de Atendimento", [data-testid="areas-atendimento"]');
    
    if (await areasSection.count() > 0) {
      await expect(areasSection).toBeVisible();
      
      // Procurar checkboxes ou opções de área
      const areaOptions = page.locator('input[type="checkbox"][value*="Domiciliar"], input[type="checkbox"][value*="Clínica"]');
      
      if (await areaOptions.count() > 0) {
        // Testar seleção de áreas
        await areaOptions.first().check();
        await expect(areaOptions.first()).toBeChecked();
      }
    }
  });

  test('deve exibir seção de avatar/foto', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar seção de avatar
    const avatarSection = page.locator('[data-testid="avatar"], .avatar, text="Foto", text="Avatar"');
    
    if (await avatarSection.count() > 0) {
      await expect(avatarSection).toBeVisible();
      
      // Procurar botão de upload ou input de arquivo
      const uploadButton = page.locator('input[type="file"], button:has-text("Upload"), button:has-text("Enviar")');
      
      if (await uploadButton.count() > 0) {
        await expect(uploadButton.first()).toBeVisible();
      }
    }
  });

  test('deve permitir upload de avatar', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar input de arquivo para avatar
    const fileInput = page.locator('input[type="file"]');
    
    if (await fileInput.count() > 0) {
      // Simular upload de arquivo (criar um arquivo temporário)
      const testImagePath = 'tests/fixtures/test-avatar.jpg';
      
      // Verificar se o arquivo existe ou criar um mock
      try {
        await fileInput.setInputFiles(testImagePath);
        
        // Procurar botão de upload/salvar
        const uploadButton = page.locator('button:has-text("Upload"), button:has-text("Salvar Avatar")');
        
        if (await uploadButton.count() > 0) {
          await uploadButton.click();
          
          // Aguardar notificação de sucesso
          await waitForNotification(page);
        }
      } catch (error) {
        // Se não conseguir fazer upload, apenas verificar se o input está presente
        await expect(fileInput).toBeVisible();
      }
    }
  });

  test('deve permitir remoção de avatar', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar botão de remover avatar
    const removeButton = page.locator('button:has-text("Remover"), button:has-text("Excluir Avatar")');
    
    if (await removeButton.count() > 0) {
      await removeButton.click();
      
      // Aguardar confirmação se houver
      const confirmButton = page.locator('button:has-text("Confirmar"), button:has-text("Sim")');
      if (await confirmButton.count() > 0) {
        await confirmButton.click();
      }
      
      // Aguardar notificação
      await waitForNotification(page);
    }
  });

  test('deve exibir horários de trabalho', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar seção de horários
    const horariosSection = page.locator('text="Horários", text="Disponibilidade", [data-testid="horarios"]');
    
    if (await horariosSection.count() > 0) {
      await expect(horariosSection).toBeVisible();
    }
  });

  test('deve permitir alternar disponibilidade', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar toggle de disponibilidade
    const availabilityToggle = page.locator('input[type="checkbox"][name*="available"], input[type="checkbox"][name*="disponivel"]');
    
    if (await availabilityToggle.count() > 0) {
      const isChecked = await availabilityToggle.isChecked();
      
      // Alternar estado
      await availabilityToggle.click();
      
      // Verificar se mudou
      const newState = await availabilityToggle.isChecked();
      expect(newState).toBe(!isChecked);
      
      // Salvar alteração
      const submitButton = page.locator('button[type="submit"], button:has-text("Salvar")');
      await submitButton.click();
      
      await waitForNotification(page);
    }
  });

  test('deve exibir estatísticas do perfil', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar estatísticas como rating, número de avaliações, etc.
    const stats = page.locator('text=/\d+\.\d+/, text="avaliações", text="sessões", text="pacientes"');
    
    if (await stats.count() > 0) {
      await expect(stats.first()).toBeVisible();
    }
  });

  test('deve permitir cancelar edições', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Fazer alterações
    await page.fill('textarea[name="bio"], textarea[id="bio"]', 'Bio temporária para teste de cancelamento');
    
    // Procurar botão de cancelar
    const cancelButton = page.locator('button:has-text("Cancelar"), button:has-text("Voltar")');
    
    if (await cancelButton.count() > 0) {
      await cancelButton.click();
      
      // Verificar se as alterações foram descartadas
      await page.reload();
      await waitForPageLoad(page);
      
      const bioField = page.locator('textarea[name="bio"], textarea[id="bio"]');
      const bioValue = await bioField.inputValue();
      expect(bioValue).not.toBe('Bio temporária para teste de cancelamento');
    }
  });
});
