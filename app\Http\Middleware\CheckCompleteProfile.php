<?php

namespace App\Http\Middleware;

use App\Services\ProfileCompletionService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckCompleteProfile
{
    protected ProfileCompletionService $profileService;

    public function __construct(ProfileCompletionService $profileService)
    {
        $this->profileService = $profileService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$parameters): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Verificar se o usuário está ativo
        if (!$user->active) {
            auth()->logout();
            return redirect()->route('login')
                ->with('error', 'Sua conta está inativa. Entre em contato com o suporte.');
        }

        // Parâmetros do middleware
        $enforceCompletion = in_array('enforce', $parameters);
        $minPercentage = $this->getMinPercentage($parameters);

        // Verificar completude do perfil
        $status = $this->profileService->getCompletionStatus($user);

        // Se enforcement está ativo e perfil não está completo
        if ($enforceCompletion && !$status['is_complete']) {
            return $this->handleIncompleteProfile($request, $user, $status);
        }

        // Se há percentual mínimo e não atende
        if ($minPercentage > 0 && $status['percentage'] < $minPercentage) {
            return $this->handleInsufficientCompletion($request, $user, $status, $minPercentage);
        }

        // Adicionar status do perfil à resposta para uso no frontend
        $request->attributes->set('profile_status', $status);

        return $next($request);
    }

    /**
     * Lidar com perfil incompleto quando enforcement está ativo
     */
    private function handleIncompleteProfile(Request $request, $user, array $status): Response
    {
        // Rotas permitidas mesmo com perfil incompleto
        $allowedRoutes = $this->getAllowedRoutes($user->role);

        if (!in_array($request->route()->getName(), $allowedRoutes)) {
            $completionUrl = $this->profileService->getCompletionUrl($user);
            
            return redirect($completionUrl)
                ->with('warning', $this->getCompletionMessage($user->role, $status));
        }

        return redirect()->route('login');
    }

    /**
     * Lidar com percentual insuficiente
     */
    private function handleInsufficientCompletion(Request $request, $user, array $status, int $minPercentage): Response
    {
        // Para percentual insuficiente, apenas adicionar aviso sem bloquear
        session()->flash('profile_warning', [
            'message' => "Seu perfil está {$status['percentage']}% completo. Complete mais informações para melhor experiência.",
            'percentage' => $status['percentage'],
            'required' => $minPercentage,
            'completion_url' => $this->profileService->getCompletionUrl($user),
        ]);

        return redirect()->route('login');
    }

    /**
     * Obter percentual mínimo dos parâmetros
     */
    private function getMinPercentage(array $parameters): int
    {
        foreach ($parameters as $param) {
            if (is_numeric($param)) {
                return (int) $param;
            }
        }
        return 0;
    }

    /**
     * Obter rotas permitidas por role
     */
    private function getAllowedRoutes(string $role): array
    {
        $baseRoutes = [
            'logout',
            'profile.edit',
            'profile.update',
            'profile.destroy',
            'password.edit',
            'password.update',
            'appearance',
        ];

        $roleRoutes = [
            'fisioterapeuta' => [
                'fisioterapeuta.setup',
                'fisioterapeuta.setup.store',
                'fisioterapeuta.perfil',
                'fisioterapeuta.perfil.update',
                'fisioterapeuta.perfil.avatar.upload',
                'fisioterapeuta.perfil.avatar.remove',
            ],
            'paciente' => [
                'paciente.onboarding',
                'paciente.onboarding.store',
                'paciente.perfil',
                'paciente.perfil.update',
                'paciente.perfil.avatar.upload',
                'paciente.perfil.avatar.remove',
                'paciente.planos',
                'paciente.plano.index',
                'paciente.plano.subscribe',
                'paciente.plano.cancel',
                'paciente.plano.change',
                'paciente.afiliados',
            ],
            'empresa' => [
                'empresa.setup',
                'empresa.setup.store',
                'empresa.perfil',
                'empresa.perfil.update',
            ],
            'afiliado' => [
                'afiliado.perfil',
                'afiliado.perfil.update',
            ],
            'admin' => [
                // Admins têm acesso total
            ],
        ];

        return array_merge($baseRoutes, $roleRoutes[$role] ?? []);
    }

    /**
     * Obter mensagem de completude por role
     */
    private function getCompletionMessage(string $role, array $status): string
    {
        $messages = [
            'fisioterapeuta' => 'Complete seu perfil profissional para começar a atender pacientes.',
            'paciente' => 'Complete seus dados médicos para agendar consultas.',
            'empresa' => 'Configure seu estabelecimento para começar a usar a plataforma.',
            'afiliado' => 'Complete seu perfil de afiliado para começar a vender.',
        ];

        $baseMessage = $messages[$role] ?? 'Complete seu perfil para continuar.';
        
        if ($status['missing_count'] > 0) {
            $baseMessage .= " Faltam {$status['missing_count']} informações.";
        }

        return $baseMessage;
    }
}
