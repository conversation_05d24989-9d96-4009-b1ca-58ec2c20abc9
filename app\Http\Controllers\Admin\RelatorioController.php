<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Models\Assinatura;
use App\Models\Fisioterapeuta;
use App\Models\Pagamento;
use App\Models\RelatorioSessao;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class RelatorioController extends Controller
{
    use RelatorioControllerMethods;
    public function index(Request $request)
    {
        // Período padrão: último mês
        $dataInicio = $request->get('data_inicio', Carbon::now()->subMonth()->format('Y-m-d'));
        $dataFim = $request->get('data_fim', Carbon::now()->format('Y-m-d'));
        $tipoRelatorio = $request->get('tipo', 'geral');

        $stats = $this->getEstatisticasGerais($dataInicio, $dataFim);
        $chartData = $this->getChartData($dataInicio, $dataFim);

        return Inertia::render('admin/relatorios/index', [
            'stats' => $stats,
            'chartData' => $chartData,
            'filtros' => $request->only(['data_inicio', 'data_fim', 'tipo']),
            'tiposRelatorio' => [
                'geral' => 'Relatório Geral',
                'financeiro' => 'Relatório Financeiro',
                'operacional' => 'Relatório Operacional',
                'pacientes' => 'Relatório de Pacientes',
                'fisioterapeutas' => 'Relatório de Fisioterapeutas',
            ],
        ]);
    }

    public function financeiro(Request $request)
    {
        $dataInicio = $request->get('data_inicio', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $dataFim = $request->get('data_fim', Carbon::now()->endOfMonth()->format('Y-m-d'));

        // Estatísticas financeiras
        $stats = [
            'receita_total' => $this->getReceitaTotal($dataInicio, $dataFim),
            'receita_recorrente' => $this->getReceitaRecorrente($dataInicio, $dataFim),
            'pagamentos_pendentes' => $this->getPagamentosPendentes(),
            'pagamentos_vencidos' => $this->getPagamentosVencidos(),
            'ticket_medio' => $this->getTicketMedio($dataInicio, $dataFim),
            'churn_rate' => $this->getChurnRate($dataInicio, $dataFim),
            'mrr' => $this->getMRR(), // Monthly Recurring Revenue
            'ltv' => $this->getLTV(), // Lifetime Value
        ];

        // Evolução da receita por mês
        $evolucaoReceita = $this->getEvolucaoReceita($dataInicio, $dataFim);

        // Top planos por receita
        $topPlanos = $this->getTopPlanosPorReceita($dataInicio, $dataFim);

        // Análise de inadimplência
        $inadimplencia = $this->getAnaliseInadimplencia($dataInicio, $dataFim);

        return Inertia::render('admin/relatorios/financeiro', [
            'stats' => $stats,
            'evolucaoReceita' => $evolucaoReceita,
            'topPlanos' => $topPlanos,
            'inadimplencia' => $inadimplencia,
            'filtros' => $request->only(['data_inicio', 'data_fim']),
        ]);
    }

    public function operacional(Request $request)
    {
        $dataInicio = $request->get('data_inicio', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $dataFim = $request->get('data_fim', Carbon::now()->endOfMonth()->format('Y-m-d'));

        // Estatísticas operacionais
        $stats = [
            'total_sessoes' => $this->getTotalSessoes($dataInicio, $dataFim),
            'sessoes_concluidas' => $this->getSessoesConcluidas($dataInicio, $dataFim),
            'sessoes_canceladas' => $this->getSessoesCanceladas($dataInicio, $dataFim),
            'taxa_conclusao' => $this->getTaxaConclusao($dataInicio, $dataFim),
            'tempo_medio_sessao' => $this->getTempoMedioSessao($dataInicio, $dataFim),
            'satisfacao_media' => $this->getSatisfacaoMedia($dataInicio, $dataFim),
            'relatorios_pendentes' => $this->getRelatoriosPendentes(),
            'utilizacao_fisioterapeutas' => $this->getUtilizacaoFisioterapeutas($dataInicio, $dataFim),
        ];

        // Performance por fisioterapeuta
        $performanceFisioterapeutas = $this->getPerformanceFisioterapeutas($dataInicio, $dataFim);

        // Horários mais demandados
        $horariosDemanda = $this->getHorariosDemanda($dataInicio, $dataFim);

        // Regiões com mais atendimentos
        $regioesDemanda = $this->getRegioesDemanda($dataInicio, $dataFim);

        return Inertia::render('admin/relatorios/operacional', [
            'stats' => $stats,
            'performanceFisioterapeutas' => $performanceFisioterapeutas,
            'horariosDemanda' => $horariosDemanda,
            'regioesDemanda' => $regioesDemanda,
            'filtros' => $request->only(['data_inicio', 'data_fim']),
        ]);
    }

    public function pacientes(Request $request)
    {
        $dataInicio = $request->get('data_inicio', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $dataFim = $request->get('data_fim', Carbon::now()->endOfMonth()->format('Y-m-d'));

        // Estatísticas de pacientes
        $stats = [
            'total_pacientes' => $this->getTotalPacientes(),
            'novos_pacientes' => $this->getNovosPacientes($dataInicio, $dataFim),
            'pacientes_ativos' => $this->getPacientesAtivos($dataInicio, $dataFim),
            'pacientes_inativos' => $this->getPacientesInativos($dataInicio, $dataFim),
            'taxa_retencao' => $this->getTaxaRetencao($dataInicio, $dataFim),
            'sessoes_por_paciente' => $this->getSessoesPorPaciente($dataInicio, $dataFim),
            'tempo_medio_tratamento' => $this->getTempoMedioTratamento(),
            'satisfacao_geral' => $this->getSatisfacaoGeral($dataInicio, $dataFim),
        ];

        // Evolução de novos pacientes
        $evolucaoNovos = $this->getEvolucaoNovosPacientes($dataInicio, $dataFim);

        // Distribuição por faixa etária
        $distribuicaoIdade = $this->getDistribuicaoIdade();

        // Top pacientes por sessões
        $topPacientes = $this->getTopPacientesPorSessoes($dataInicio, $dataFim);

        return Inertia::render('admin/relatorios/pacientes', [
            'stats' => $stats,
            'evolucaoNovos' => $evolucaoNovos,
            'distribuicaoIdade' => $distribuicaoIdade,
            'topPacientes' => $topPacientes,
            'filtros' => $request->only(['data_inicio', 'data_fim']),
        ]);
    }

    public function fisioterapeutas(Request $request)
    {
        $dataInicio = $request->get('data_inicio', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $dataFim = $request->get('data_fim', Carbon::now()->endOfMonth()->format('Y-m-d'));

        // Estatísticas de fisioterapeutas
        $stats = [
            'total_fisioterapeutas' => $this->getTotalFisioterapeutas(),
            'fisioterapeutas_ativos' => $this->getFisioterapeutasAtivos($dataInicio, $dataFim),
            'media_sessoes_fisio' => $this->getMediaSessoesFisio($dataInicio, $dataFim),
            'receita_por_fisio' => $this->getReceitaPorFisio($dataInicio, $dataFim),
            'satisfacao_fisios' => $this->getSatisfacaoFisios($dataInicio, $dataFim),
            'tempo_resposta_medio' => $this->getTempoRespostaMedio($dataInicio, $dataFim),
            'taxa_cancelamento_fisio' => $this->getTaxaCancelamentoFisio($dataInicio, $dataFim),
            'especializacoes_demanda' => $this->getEspecializacoesDemanda($dataInicio, $dataFim),
        ];

        // Ranking de fisioterapeutas
        $rankingFisioterapeutas = $this->getRankingFisioterapeutas($dataInicio, $dataFim);

        // Distribuição geográfica
        $distribuicaoGeografica = $this->getDistribuicaoGeograficaFisios();

        // Análise de especialidades
        $analiseEspecialidades = $this->getAnaliseEspecialidades($dataInicio, $dataFim);

        return Inertia::render('admin/relatorios/fisioterapeutas', [
            'stats' => $stats,
            'rankingFisioterapeutas' => $rankingFisioterapeutas,
            'distribuicaoGeografica' => $distribuicaoGeografica,
            'analiseEspecialidades' => $analiseEspecialidades,
            'filtros' => $request->only(['data_inicio', 'data_fim']),
        ]);
    }

    public function export(Request $request)
    {
        $tipo = $request->get('tipo', 'geral');
        $formato = $request->get('formato', 'csv');
        $dataInicio = $request->get('data_inicio', Carbon::now()->subMonth()->format('Y-m-d'));
        $dataFim = $request->get('data_fim', Carbon::now()->format('Y-m-d'));

        switch ($tipo) {
            case 'financeiro':
                return $this->exportFinanceiro($formato, $dataInicio, $dataFim);
            case 'operacional':
                return $this->exportOperacional($formato, $dataInicio, $dataFim);
            case 'pacientes':
                return $this->exportPacientes($formato, $dataInicio, $dataFim);
            case 'fisioterapeutas':
                return $this->exportFisioterapeutas($formato, $dataInicio, $dataFim);
            default:
                return $this->exportGeral($formato, $dataInicio, $dataFim);
        }
    }

    // Métodos auxiliares para cálculos estatísticos
    private function getEstatisticasGerais($dataInicio, $dataFim)
    {
        $inicio = Carbon::parse($dataInicio)->startOfDay();
        $fim = Carbon::parse($dataFim)->endOfDay();

        return [
            'total_pacientes' => User::where('role', 'paciente')->count(),
            'total_fisioterapeutas' => User::where('role', 'fisioterapeuta')->count(),
            'sessoes_periodo' => Agendamento::whereBetween('scheduled_at', [$inicio, $fim])->count(),
            'receita_periodo' => Pagamento::where('status', 'pago')
                ->whereBetween('data_pagamento', [$inicio, $fim])
                ->sum('valor'),
            'assinaturas_ativas' => Assinatura::where('status', 'ativa')->count(),
            'taxa_crescimento' => $this->getTaxaCrescimento($dataInicio, $dataFim),
        ];
    }

    private function getChartData($dataInicio, $dataFim)
    {
        $inicio = Carbon::parse($dataInicio);
        $fim = Carbon::parse($dataFim);
        
        $data = [];
        $current = $inicio->copy();
        
        while ($current <= $fim) {
            $nextDay = $current->copy()->addDay();
            
            $data[] = [
                'date' => $current->format('Y-m-d'),
                'sessoes' => Agendamento::whereBetween('scheduled_at', [$current, $nextDay])->count(),
                'receita' => Pagamento::where('status', 'pago')
                    ->whereBetween('paid_at', [$current, $nextDay])
                    ->sum('amount'),
            ];
            
            $current->addDay();
        }
        
        return $data;
    }

    private function getTaxaCrescimento($dataInicio, $dataFim)
    {
        $periodoAtual = Carbon::parse($dataInicio)->diffInDays(Carbon::parse($dataFim));
        $inicioAnterior = Carbon::parse($dataInicio)->subDays($periodoAtual);
        
        $sessoesPeriodoAtual = Agendamento::whereBetween('scheduled_at', [
            Carbon::parse($dataInicio), Carbon::parse($dataFim)
        ])->count();
        
        $sessoesPeriodoAnterior = Agendamento::whereBetween('scheduled_at', [
            $inicioAnterior, Carbon::parse($dataInicio)
        ])->count();
        
        if ($sessoesPeriodoAnterior == 0) {
            return $sessoesPeriodoAtual > 0 ? 100 : 0;
        }
        
        return round((($sessoesPeriodoAtual - $sessoesPeriodoAnterior) / $sessoesPeriodoAnterior) * 100, 2);
    }

    // Métodos para relatório financeiro
    private function getReceitaTotal($dataInicio, $dataFim)
    {
        return Pagamento::where('status', 'pago')
            ->whereBetween('paid_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->sum('amount');
    }

    private function getReceitaRecorrente($dataInicio, $dataFim)
    {
        return Assinatura::where('status', 'ativa')
            ->with('plano')
            ->get()
            ->sum(function ($assinatura) {
                return $assinatura->plano->price ?? 0;
            });
    }

    private function getPagamentosPendentes()
    {
        return Pagamento::where('status', 'pendente')->sum('amount');
    }

    private function getPagamentosVencidos()
    {
        return Pagamento::where('status', 'pendente')
            ->where('due_date', '<', Carbon::now())
            ->sum('amount');
    }

    private function getTicketMedio($dataInicio, $dataFim)
    {
        $pagamentos = Pagamento::where('status', 'pago')
            ->whereBetween('paid_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ]);

        $total = $pagamentos->sum('valor');
        $count = $pagamentos->count();

        return $count > 0 ? round($total / $count, 2) : 0;
    }

    private function getChurnRate($dataInicio, $dataFim)
    {
        $inicioMes = Carbon::parse($dataInicio)->startOfMonth();
        $fimMes = Carbon::parse($dataFim)->endOfMonth();

        $assinaturasInicioMes = Assinatura::where('created_at', '<', $inicioMes)
            ->where('status', 'ativa')
            ->count();

        $assinaturasCanceladas = Assinatura::whereBetween('updated_at', [$inicioMes, $fimMes])
            ->where('status', 'cancelada')
            ->count();

        return $assinaturasInicioMes > 0 ? round(($assinaturasCanceladas / $assinaturasInicioMes) * 100, 2) : 0;
    }

    private function getMRR()
    {
        return Assinatura::where('status', 'ativa')
            ->with('plano')
            ->get()
            ->sum(function ($assinatura) {
                return $assinatura->plano->price ?? 0;
            });
    }

    private function getLTV()
    {
        $ticketMedio = $this->getTicketMedio(
            Carbon::now()->subYear()->format('Y-m-d'),
            Carbon::now()->format('Y-m-d')
        );

        // Compatível com SQLite - usar julianday para calcular diferença de dias
        $tempoMedioAssinatura = Assinatura::selectRaw('AVG(julianday(COALESCE(updated_at, datetime("now"))) - julianday(created_at)) as media_dias')
            ->where('status', '!=', 'ativa')
            ->first()
            ->media_dias ?? 365;

        $frequenciaPagamento = 30; // mensal

        return round(($ticketMedio * ($tempoMedioAssinatura / $frequenciaPagamento)), 2);
    }

    private function getEvolucaoReceita($dataInicio, $dataFim)
    {
        return Pagamento::selectRaw('DATE(paid_at) as data, SUM(amount) as receita')
            ->where('status', 'pago')
            ->whereBetween('paid_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->groupBy('data')
            ->orderBy('data')
            ->get()
            ->map(function ($item) {
                return [
                    'data' => Carbon::parse($item->data)->format('d/m'),
                    'receita' => (float) $item->receita,
                ];
            });
    }

    private function getTopPlanosPorReceita($dataInicio, $dataFim)
    {
        return DB::table('pagamentos')
            ->join('assinaturas', 'pagamentos.assinatura_id', '=', 'assinaturas.id')
            ->join('planos', 'assinaturas.plano_id', '=', 'planos.id')
            ->select('planos.name', DB::raw('SUM(pagamentos.amount) as receita_total'), DB::raw('COUNT(pagamentos.id) as total_pagamentos'))
            ->where('pagamentos.status', 'pago')
            ->whereBetween('pagamentos.paid_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->groupBy('planos.id', 'planos.name')
            ->orderBy('receita_total', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'plano' => $item->name,
                    'receita' => (float) $item->receita_total,
                    'pagamentos' => (int) $item->total_pagamentos,
                ];
            });
    }

    private function getAnaliseInadimplencia($dataInicio, $dataFim)
    {
        $totalPendentes = Pagamento::where('status', 'pendente')->count();
        $totalVencidos = Pagamento::where('status', 'pendente')
            ->where('due_date', '<', Carbon::now())
            ->count();

        $inadimplenciaPorIdade = Pagamento::selectRaw('
                CASE
                    WHEN julianday(datetime("now")) - julianday(due_date) <= 30 THEN "0-30 dias"
                    WHEN julianday(datetime("now")) - julianday(due_date) <= 60 THEN "31-60 dias"
                    WHEN julianday(datetime("now")) - julianday(due_date) <= 90 THEN "61-90 dias"
                    ELSE "90+ dias"
                END as faixa,
                COUNT(*) as quantidade,
                SUM(amount) as valor_total
            ')
            ->where('status', 'pendente')
            ->where('due_date', '<', Carbon::now())
            ->groupBy('faixa')
            ->get();

        return [
            'total_pendentes' => $totalPendentes,
            'total_vencidos' => $totalVencidos,
            'taxa_inadimplencia' => $totalPendentes > 0 ? round(($totalVencidos / $totalPendentes) * 100, 2) : 0,
            'por_idade' => $inadimplenciaPorIdade,
        ];
    }

    // Métodos para relatório operacional
    private function getTotalSessoes($dataInicio, $dataFim)
    {
        return Agendamento::whereBetween('scheduled_at', [
            Carbon::parse($dataInicio)->startOfDay(),
            Carbon::parse($dataFim)->endOfDay()
        ])->count();
    }

    private function getSessoesConcluidas($dataInicio, $dataFim)
    {
        return Agendamento::where('status', 'concluido')
            ->whereBetween('finished_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])->count();
    }

    private function getSessoesCanceladas($dataInicio, $dataFim)
    {
        return Agendamento::where('status', 'cancelado')
            ->whereBetween('scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])->count();
    }

    private function getTaxaConclusao($dataInicio, $dataFim)
    {
        $total = $this->getTotalSessoes($dataInicio, $dataFim);
        $concluidas = $this->getSessoesConcluidas($dataInicio, $dataFim);

        return $total > 0 ? round(($concluidas / $total) * 100, 2) : 0;
    }

    private function getTempoMedioSessao($dataInicio, $dataFim)
    {
        $sessoes = Agendamento::where('status', 'concluido')
            ->whereNotNull('started_at')
            ->whereNotNull('finished_at')
            ->whereBetween('finished_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->get(['started_at', 'finished_at']);

        if ($sessoes->isEmpty()) {
            return 0;
        }

        $totalMinutos = $sessoes->sum(function ($sessao) {
            return Carbon::parse($sessao->finished_at)->diffInMinutes(Carbon::parse($sessao->started_at));
        });

        $tempoMedio = $totalMinutos / $sessoes->count();

        return round($tempoMedio, 0);
    }

    private function getSatisfacaoMedia($dataInicio, $dataFim)
    {
        $satisfacao = RelatorioSessao::whereHas('agendamento', function ($query) use ($dataInicio, $dataFim) {
                $query->whereBetween('finished_at', [
                    Carbon::parse($dataInicio)->startOfDay(),
                    Carbon::parse($dataFim)->endOfDay()
                ]);
            })
            ->whereNotNull('patient_satisfaction')
            ->avg('patient_satisfaction');

        return round($satisfacao ?? 0, 1);
    }

    private function getRelatoriosPendentes()
    {
        return Agendamento::where('status', 'concluido')
            ->whereDoesntHave('relatorioSessao')
            ->count();
    }

    private function getUtilizacaoFisioterapeutas($dataInicio, $dataFim)
    {
        $totalFisioterapeutas = User::where('role', 'fisioterapeuta')->count();
        $fisioterapeutasAtivos = Agendamento::whereBetween('scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->distinct('fisioterapeuta_id')
            ->count();

        return $totalFisioterapeutas > 0 ? round(($fisioterapeutasAtivos / $totalFisioterapeutas) * 100, 2) : 0;
    }

    private function getPerformanceFisioterapeutas($dataInicio, $dataFim)
    {
        return DB::table('agendamentos')
            ->join('users', 'agendamentos.fisioterapeuta_id', '=', 'users.id')
            ->select(
                'users.name as fisioterapeuta',
                DB::raw('COUNT(agendamentos.id) as total_sessoes'),
                DB::raw('COUNT(CASE WHEN agendamentos.status = "concluido" THEN 1 END) as sessoes_concluidas'),
                DB::raw('COUNT(CASE WHEN agendamentos.status = "cancelado" THEN 1 END) as sessoes_canceladas'),
                DB::raw('ROUND(AVG(CASE WHEN agendamentos.status = "concluido" THEN agendamentos.duration END), 0) as tempo_medio')
            )
            ->whereBetween('agendamentos.scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->groupBy('users.id', 'users.name')
            ->orderBy('total_sessoes', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'fisioterapeuta' => $item->fisioterapeuta,
                    'total_sessoes' => (int) $item->total_sessoes,
                    'sessoes_concluidas' => (int) $item->sessoes_concluidas,
                    'sessoes_canceladas' => (int) $item->sessoes_canceladas,
                    'taxa_conclusao' => $item->total_sessoes > 0 ? round(($item->sessoes_concluidas / $item->total_sessoes) * 100, 2) : 0,
                    'tempo_medio' => (int) ($item->tempo_medio ?? 0),
                ];
            });
    }

    private function getHorariosDemanda($dataInicio, $dataFim)
    {
        return Agendamento::selectRaw('HOUR(scheduled_at) as hora, COUNT(*) as quantidade')
            ->whereBetween('scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->groupBy('hora')
            ->orderBy('hora')
            ->get()
            ->map(function ($item) {
                return [
                    'hora' => sprintf('%02d:00', $item->hora),
                    'quantidade' => (int) $item->quantidade,
                ];
            });
    }

    private function getRegioesDemanda($dataInicio, $dataFim)
    {
        // Simulação de regiões baseada no endereço
        return Agendamento::selectRaw('
                CASE
                    WHEN address LIKE "%Centro%" THEN "Centro"
                    WHEN address LIKE "%Zona Norte%" THEN "Zona Norte"
                    WHEN address LIKE "%Zona Sul%" THEN "Zona Sul"
                    WHEN address LIKE "%Zona Leste%" THEN "Zona Leste"
                    WHEN address LIKE "%Zona Oeste%" THEN "Zona Oeste"
                    ELSE "Outras Regiões"
                END as regiao,
                COUNT(*) as quantidade
            ')
            ->whereBetween('scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->whereNotNull('address')
            ->groupBy('regiao')
            ->orderBy('quantidade', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'regiao' => $item->regiao,
                    'quantidade' => (int) $item->quantidade,
                ];
            });
    }

    // Métodos restantes para fisioterapeutas
    private function getDistribuicaoGeograficaFisios()
    {
        return User::selectRaw('
                CASE
                    WHEN address LIKE "%Centro%" THEN "Centro"
                    WHEN address LIKE "%Zona Norte%" THEN "Zona Norte"
                    WHEN address LIKE "%Zona Sul%" THEN "Zona Sul"
                    WHEN address LIKE "%Zona Leste%" THEN "Zona Leste"
                    WHEN address LIKE "%Zona Oeste%" THEN "Zona Oeste"
                    ELSE "Outras Regiões"
                END as regiao,
                COUNT(*) as quantidade
            ')
            ->where('role', 'fisioterapeuta')
            ->whereNotNull('address')
            ->groupBy('regiao')
            ->orderBy('quantidade', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'regiao' => $item->regiao,
                    'quantidade' => (int) $item->quantidade,
                ];
            });
    }

    private function getAnaliseEspecialidades($dataInicio, $dataFim)
    {
        // Buscar dados dos agendamentos com fisioterapeutas
        $agendamentos = DB::table('fisioterapeutas')
            ->join('agendamentos', 'fisioterapeutas.user_id', '=', 'agendamentos.fisioterapeuta_id')
            ->select(
                'fisioterapeutas.specializations',
                'agendamentos.id',
                'agendamentos.status',
                'agendamentos.price'
            )
            ->whereBetween('agendamentos.scheduled_at', [
                Carbon::parse($dataInicio)->startOfDay(),
                Carbon::parse($dataFim)->endOfDay()
            ])
            ->whereNotNull('fisioterapeutas.specializations')
            ->get();

        // Processar dados manualmente
        $especialidades = [];
        foreach ($agendamentos as $agendamento) {
            $specs = json_decode($agendamento->specializations, true);
            if (is_array($specs) && !empty($specs)) {
                $primeiraEspec = $specs[0];

                if (!isset($especialidades[$primeiraEspec])) {
                    $especialidades[$primeiraEspec] = [
                        'total_sessoes' => 0,
                        'sessoes_concluidas' => 0,
                        'receita_total' => 0,
                    ];
                }

                $especialidades[$primeiraEspec]['total_sessoes']++;
                if ($agendamento->status === 'concluido') {
                    $especialidades[$primeiraEspec]['sessoes_concluidas']++;
                }
                $especialidades[$primeiraEspec]['receita_total'] += (float) $agendamento->price;
            }
        }

        // Converter para formato esperado e ordenar
        $resultado = [];
        foreach ($especialidades as $especializacao => $dados) {
            $resultado[] = [
                'especializacao' => $especializacao,
                'total_sessoes' => $dados['total_sessoes'],
                'sessoes_concluidas' => $dados['sessoes_concluidas'],
                'receita_total' => $dados['receita_total'],
                'taxa_conclusao' => $dados['total_sessoes'] > 0 ? round(($dados['sessoes_concluidas'] / $dados['total_sessoes']) * 100, 2) : 0,
            ];
        }

        // Ordenar por total de sessões
        usort($resultado, function ($a, $b) {
            return $b['total_sessoes'] - $a['total_sessoes'];
        });

        return collect($resultado);
    }

    // Métodos de exportação
    private function exportGeral($formato, $dataInicio, $dataFim)
    {
        $stats = $this->getEstatisticasGerais($dataInicio, $dataFim);

        $data = [
            ['Métrica', 'Valor'],
            ['Total de Pacientes', $stats['total_pacientes']],
            ['Total de Fisioterapeutas', $stats['total_fisioterapeutas']],
            ['Sessões no Período', $stats['sessoes_periodo']],
            ['Receita no Período', 'R$ ' . number_format($stats['receita_periodo'], 2, ',', '.')],
            ['Assinaturas Ativas', $stats['assinaturas_ativas']],
            ['Taxa de Crescimento (%)', $stats['taxa_crescimento']],
        ];

        return $this->generateExport($data, 'relatorio_geral', $formato);
    }

    private function exportFinanceiro($formato, $dataInicio, $dataFim)
    {
        $stats = [
            'receita_total' => $this->getReceitaTotal($dataInicio, $dataFim),
            'receita_recorrente' => $this->getReceitaRecorrente($dataInicio, $dataFim),
            'pagamentos_pendentes' => $this->getPagamentosPendentes(),
            'pagamentos_vencidos' => $this->getPagamentosVencidos(),
            'ticket_medio' => $this->getTicketMedio($dataInicio, $dataFim),
            'churn_rate' => $this->getChurnRate($dataInicio, $dataFim),
            'mrr' => $this->getMRR(),
            'ltv' => $this->getLTV(),
        ];

        $data = [
            ['Métrica Financeira', 'Valor'],
            ['Receita Total', 'R$ ' . number_format($stats['receita_total'], 2, ',', '.')],
            ['Receita Recorrente', 'R$ ' . number_format($stats['receita_recorrente'], 2, ',', '.')],
            ['Pagamentos Pendentes', 'R$ ' . number_format($stats['pagamentos_pendentes'], 2, ',', '.')],
            ['Pagamentos Vencidos', 'R$ ' . number_format($stats['pagamentos_vencidos'], 2, ',', '.')],
            ['Ticket Médio', 'R$ ' . number_format($stats['ticket_medio'], 2, ',', '.')],
            ['Taxa de Churn (%)', $stats['churn_rate']],
            ['MRR', 'R$ ' . number_format($stats['mrr'], 2, ',', '.')],
            ['LTV', 'R$ ' . number_format($stats['ltv'], 2, ',', '.')],
        ];

        return $this->generateExport($data, 'relatorio_financeiro', $formato);
    }

    private function exportOperacional($formato, $dataInicio, $dataFim)
    {
        $stats = [
            'total_sessoes' => $this->getTotalSessoes($dataInicio, $dataFim),
            'sessoes_concluidas' => $this->getSessoesConcluidas($dataInicio, $dataFim),
            'sessoes_canceladas' => $this->getSessoesCanceladas($dataInicio, $dataFim),
            'taxa_conclusao' => $this->getTaxaConclusao($dataInicio, $dataFim),
            'tempo_medio_sessao' => $this->getTempoMedioSessao($dataInicio, $dataFim),
            'satisfacao_media' => $this->getSatisfacaoMedia($dataInicio, $dataFim),
            'relatorios_pendentes' => $this->getRelatoriosPendentes(),
            'utilizacao_fisioterapeutas' => $this->getUtilizacaoFisioterapeutas($dataInicio, $dataFim),
        ];

        $data = [
            ['Métrica Operacional', 'Valor'],
            ['Total de Sessões', $stats['total_sessoes']],
            ['Sessões Concluídas', $stats['sessoes_concluidas']],
            ['Sessões Canceladas', $stats['sessoes_canceladas']],
            ['Taxa de Conclusão (%)', $stats['taxa_conclusao']],
            ['Tempo Médio de Sessão (min)', $stats['tempo_medio_sessao']],
            ['Satisfação Média', $stats['satisfacao_media']],
            ['Relatórios Pendentes', $stats['relatorios_pendentes']],
            ['Utilização de Fisioterapeutas (%)', $stats['utilizacao_fisioterapeutas']],
        ];

        return $this->generateExport($data, 'relatorio_operacional', $formato);
    }

    private function exportPacientes($formato, $dataInicio, $dataFim)
    {
        $topPacientes = $this->getTopPacientesPorSessoes($dataInicio, $dataFim);

        $data = [
            ['Paciente', 'Email', 'Total Sessões', 'Sessões Concluídas', 'Valor Total']
        ];

        foreach ($topPacientes as $paciente) {
            $data[] = [
                $paciente['paciente'],
                $paciente['email'],
                $paciente['total_sessoes'],
                $paciente['sessoes_concluidas'],
                'R$ ' . number_format($paciente['valor_total'], 2, ',', '.')
            ];
        }

        return $this->generateExport($data, 'relatorio_pacientes', $formato);
    }

    private function exportFisioterapeutas($formato, $dataInicio, $dataFim)
    {
        $ranking = $this->getRankingFisioterapeutas($dataInicio, $dataFim);

        $data = [
            ['Posição', 'Fisioterapeuta', 'Email', 'Total Sessões', 'Sessões Concluídas', 'Receita Total', 'Satisfação Média', 'Taxa Conclusão (%)']
        ];

        foreach ($ranking as $fisio) {
            $data[] = [
                $fisio['posicao'],
                $fisio['fisioterapeuta'],
                $fisio['email'],
                $fisio['total_sessoes'],
                $fisio['sessoes_concluidas'],
                'R$ ' . number_format($fisio['receita_total'], 2, ',', '.'),
                $fisio['satisfacao_media'],
                $fisio['taxa_conclusao']
            ];
        }

        return $this->generateExport($data, 'relatorio_fisioterapeutas', $formato);
    }

    private function generateExport($data, $filename, $formato)
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $fullFilename = "{$filename}_{$timestamp}";

        if ($formato === 'csv') {
            return $this->generateCSV($data, $fullFilename);
        } else {
            return $this->generateExcel($data, $fullFilename);
        }
    }

    private function generateCSV($data, $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}.csv\"",
        ];

        $callback = function () use ($data) {
            $file = fopen('php://output', 'w');

            foreach ($data as $row) {
                fputcsv($file, $row, ';');
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function generateExcel($data, $filename)
    {
        // Implementação básica para Excel - pode ser expandida com bibliotecas como PhpSpreadsheet
        return $this->generateCSV($data, $filename);
    }
}
