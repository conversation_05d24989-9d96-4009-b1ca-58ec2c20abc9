<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Relatório de Usuários - F4 Fisio</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #0066cc;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .filters {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .filters h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .filters p {
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #0066cc;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .summary {
            background-color: #e7f3ff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .summary h3 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>F4 Fisio - Relatório de Usuários</h1>
        <p>Gerado em: {{ $generated_at->format('d/m/Y H:i:s') }}</p>
    </div>

    @if(!empty($filters))
    <div class="filters">
        <h3>Filtros Aplicados:</h3>
        @if(isset($filters['role']))
            <p><strong>Tipo de Usuário:</strong> {{ ucfirst($filters['role']) }}</p>
        @endif
        @if(isset($filters['active']))
            <p><strong>Status:</strong> {{ $filters['active'] ? 'Ativo' : 'Inativo' }}</p>
        @endif
        @if(isset($filters['date_from']))
            <p><strong>Data Inicial:</strong> {{ \Carbon\Carbon::parse($filters['date_from'])->format('d/m/Y') }}</p>
        @endif
        @if(isset($filters['date_to']))
            <p><strong>Data Final:</strong> {{ \Carbon\Carbon::parse($filters['date_to'])->format('d/m/Y') }}</p>
        @endif
    </div>
    @endif

    <div class="summary">
        <h3>Resumo</h3>
        <p><strong>Total de Usuários:</strong> {{ count($users) }}</p>
        <p><strong>Usuários Ativos:</strong> {{ $users->where('active', true)->count() }}</p>
        <p><strong>Usuários Inativos:</strong> {{ $users->where('active', false)->count() }}</p>
        <p><strong>Por Tipo:</strong></p>
        <ul>
            @foreach($users->groupBy('role') as $role => $roleUsers)
                <li>{{ ucfirst($role) }}: {{ $roleUsers->count() }}</li>
            @endforeach
        </ul>
    </div>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Nome</th>
                <th>Email</th>
                <th>Telefone</th>
                <th>Tipo</th>
                <th>Status</th>
                <th>Data Cadastro</th>
            </tr>
        </thead>
        <tbody>
            @foreach($users as $user)
            <tr>
                <td>{{ $user->id }}</td>
                <td>{{ $user->name }}</td>
                <td>{{ $user->email }}</td>
                <td>{{ $user->phone ?? 'N/A' }}</td>
                <td>{{ ucfirst($user->role) }}</td>
                <td class="{{ $user->active ? 'status-active' : 'status-inactive' }}">
                    {{ $user->active ? 'Ativo' : 'Inativo' }}
                </td>
                <td>{{ $user->created_at->format('d/m/Y H:i') }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>F4 Fisio - Sistema de Gestão de Fisioterapia</p>
        <p>Este relatório foi gerado automaticamente pelo sistema</p>
    </div>
</body>
</html>
