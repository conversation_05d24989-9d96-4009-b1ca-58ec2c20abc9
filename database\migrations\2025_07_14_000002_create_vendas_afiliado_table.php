<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendas_afiliado', function (Blueprint $table) {
            $table->id();
            $table->foreignId('afiliado_id')->constrained('afiliados')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade'); // Cliente que comprou
            $table->foreignId('assinatura_id')->nullable()->constrained('assinaturas')->onDelete('set null');
            $table->string('plano_tipo'); // empresarial, pessoal, busca
            $table->decimal('valor_venda', 10, 2);
            $table->decimal('comissao', 8, 2);
            $table->decimal('percentual_comissao', 5, 2);
            $table->enum('status', ['pendente', 'confirmada', 'cancelada'])->default('pendente');
            $table->timestamp('data_confirmacao')->nullable();
            $table->text('observacoes')->nullable();
            $table->timestamps();
            
            // Índices
            $table->index(['afiliado_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index('plano_tipo');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendas_afiliado');
    }
};
