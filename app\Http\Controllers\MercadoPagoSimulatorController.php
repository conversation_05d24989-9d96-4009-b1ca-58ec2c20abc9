<?php

namespace App\Http\Controllers;

use App\Models\Pagamento;
use App\Services\MercadoPagoSimulatorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class MercadoPagoSimulatorController extends Controller
{
    protected $simulatorService;

    public function __construct(MercadoPagoSimulatorService $simulatorService)
    {
        $this->simulatorService = $simulatorService;
    }

    /**
     * Página de pagamento simulado
     */
    public function paymentPage($transactionId)
    {
        $pagamento = Pagamento::where('transaction_id', $transactionId)
            ->with(['assinatura.user', 'assinatura.plano'])
            ->first();

        if (!$pagamento) {
            return redirect()->route('dashboard')
                ->with('error', 'Pagamento não encontrado');
        }

        return Inertia::render('Simulator/MercadoPagoPayment', [
            'pagamento' => [
                'id' => $pagamento->id,
                'transaction_id' => $transactionId,
                'amount' => $pagamento->amount,
                'formatted_amount' => 'R$ ' . number_format($pagamento->amount, 2, ',', '.'),
                'status' => $pagamento->status,
                'assinatura' => [
                    'id' => $pagamento->assinatura->id,
                    'user' => [
                        'name' => $pagamento->assinatura->user->name,
                        'email' => $pagamento->assinatura->user->email,
                    ],
                    'plano' => [
                        'name' => $pagamento->assinatura->plano->name,
                        'price' => $pagamento->assinatura->plano->price,
                    ],
                ],
            ],
        ]);
    }

    /**
     * Processar ação de pagamento simulado
     */
    public function processPayment(Request $request, $transactionId)
    {
        $request->validate([
            'action' => 'required|in:approve,reject,cancel',
        ]);

        $result = $this->simulatorService->processSimulatedPayment(
            $transactionId, 
            $request->action
        );

        if ($result['success']) {
            // Simular webhook
            $this->triggerSimulatedWebhook($transactionId);

            return redirect()->route('mercadopago.simulator.success', ['transaction_id' => $transactionId])
                ->with('success', $result['message']);
        }

        return back()->with('error', $result['message']);
    }

    /**
     * Página de sucesso
     */
    public function success($transactionId)
    {
        $pagamento = Pagamento::where('transaction_id', $transactionId)
            ->with(['assinatura.user'])
            ->first();

        if (!$pagamento) {
            return redirect()->route('dashboard');
        }

        return Inertia::render('Simulator/PaymentSuccess', [
            'pagamento' => [
                'id' => $pagamento->id,
                'transaction_id' => $transactionId,
                'amount' => $pagamento->amount,
                'formatted_amount' => 'R$ ' . number_format($pagamento->amount, 2, ',', '.'),
                'status' => $pagamento->status,
                'paid_at' => $pagamento->paid_at?->format('d/m/Y H:i:s'),
            ],
        ]);
    }

    /**
     * Página de falha
     */
    public function failure($transactionId)
    {
        $pagamento = Pagamento::where('transaction_id', $transactionId)->first();

        return Inertia::render('Simulator/PaymentFailure', [
            'pagamento' => [
                'id' => $pagamento->id ?? null,
                'transaction_id' => $transactionId,
                'message' => 'Pagamento rejeitado ou cancelado',
            ],
        ]);
    }

    /**
     * Disparar webhook simulado
     */
    private function triggerSimulatedWebhook($transactionId)
    {
        try {
            $webhookData = $this->simulatorService->simulateWebhook($transactionId);
            
            if ($webhookData) {
                // Fazer uma requisição HTTP para o próprio webhook
                $webhookUrl = route('mercadopago.webhook');
                
                Http::timeout(10)->post($webhookUrl, $webhookData);
                
                Log::info('Webhook simulado disparado', [
                    'transaction_id' => $transactionId,
                    'webhook_url' => $webhookUrl,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao disparar webhook simulado', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Interface administrativa para simular webhooks
     */
    public function adminInterface()
    {
        if (!auth()->user() || !auth()->user()->isAdmin()) {
            abort(403, 'Acesso negado');
        }

        $pagamentosPendentes = Pagamento::where('status', 'pendente')
            ->whereNotNull('transaction_id')
            ->where('transaction_id', 'like', 'SIM_%')
            ->with(['assinatura.user', 'assinatura.plano'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return Inertia::render('Admin/MercadoPagoSimulator', [
            'pagamentos' => $pagamentosPendentes->map(function ($pagamento) {
                return [
                    'id' => $pagamento->id,
                    'transaction_id' => $pagamento->transaction_id,
                    'amount' => $pagamento->amount,
                    'formatted_amount' => 'R$ ' . number_format($pagamento->amount, 2, ',', '.'),
                    'status' => $pagamento->status,
                    'created_at' => $pagamento->created_at->format('d/m/Y H:i:s'),
                    'user' => [
                        'name' => $pagamento->assinatura->user->name,
                        'email' => $pagamento->assinatura->user->email,
                    ],
                    'plano' => [
                        'name' => $pagamento->assinatura->plano->name,
                    ],
                ];
            }),
        ]);
    }

    /**
     * Simular webhook via admin
     */
    public function simulateWebhookAdmin(Request $request)
    {
        if (!auth()->user() || !auth()->user()->isAdmin()) {
            abort(403, 'Acesso negado');
        }

        $request->validate([
            'transaction_id' => 'required|string',
            'action' => 'required|in:approve,reject,cancel',
        ]);

        $result = $this->simulatorService->processSimulatedPayment(
            $request->transaction_id,
            $request->action
        );

        if ($result['success']) {
            $this->triggerSimulatedWebhook($request->transaction_id);
        }

        return response()->json($result);
    }
}
