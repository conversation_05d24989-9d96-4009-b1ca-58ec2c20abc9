<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckFisioterapeutaProfile
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Só aplica para fisioterapeutas
        if ($user->role !== 'fisioterapeuta') {
            return $next($request);
        }

        // Verificar se o fisioterapeuta tem perfil completo
        if (!$this->hasCompleteProfile($user)) {
            // Permitir acesso apenas às rotas de setup, perfil, logout e configurações
            $allowedRoutes = [
                'fisioterapeuta.setup',
                'fisioterapeuta.setup.store',
                'fisioterapeuta.perfil',
                'fisioterapeuta.perfil.update',
                'fisioterapeuta.perfil.avatar.upload',
                'fisioterapeuta.perfil.avatar.remove',
                'logout',
                'profile.edit',
                'profile.update',
                'profile.destroy',
                'password.edit',
                'password.update',
                'appearance'
            ];

            if (!in_array($request->route()->getName(), $allowedRoutes)) {
                return redirect()->route('fisioterapeuta.setup')
                    ->with('warning', 'Você precisa completar seu perfil profissional para continuar.');
            }
        }

        return $next($request);
    }

    /**
     * Check if fisioterapeuta has complete profile
     */
    private function hasCompleteProfile($user): bool
    {
        $fisioterapeuta = $user->fisioterapeuta;

        // Se não tem perfil de fisioterapeuta, não está completo
        if (!$fisioterapeuta) {
            return false;
        }

        // Verificar campos obrigatórios
        $requiredFields = [
            'crefito',
            'specializations',
            'bio',
            'hourly_rate',
            'available_areas'
        ];

        foreach ($requiredFields as $field) {
            $value = $fisioterapeuta->$field;
            
            // Verificar se o campo está vazio
            if (empty($value)) {
                return false;
            }

            // Para arrays, verificar se tem pelo menos um item
            if (is_array($value) && count($value) === 0) {
                return false;
            }
        }

        // Verificar se o valor da hora é maior que 0
        if ($fisioterapeuta->hourly_rate <= 0) {
            return false;
        }

        // Verificar se a bio tem pelo menos 50 caracteres
        if (strlen($fisioterapeuta->bio) < 50) {
            return false;
        }

        return true;
    }
}
