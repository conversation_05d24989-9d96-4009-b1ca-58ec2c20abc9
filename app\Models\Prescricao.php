<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Prescricao extends Model
{
    use HasFactory;

    protected $fillable = [
        'paciente_id',
        'fisioterapeuta_id',
        'agendamento_id',
        'titulo',
        'descricao',
        'medicamentos',
        'exercicios',
        'cuidados_especiais',
        'data_inicio',
        'data_fim',
        'status',
        'observacoes',
    ];

    protected $casts = [
        'data_inicio' => 'date',
        'data_fim' => 'date',
    ];

    // Relacionamentos
    public function paciente()
    {
        return $this->belongsTo(User::class, 'paciente_id');
    }

    public function fisioterapeuta()
    {
        return $this->belongsTo(User::class, 'fisioterapeuta_id');
    }

    public function agendamento()
    {
        return $this->belongsTo(Agendamento::class);
    }
}
