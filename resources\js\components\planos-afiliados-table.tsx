import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign } from 'lucide-react';

interface PlanoAfiliado {
    nome: string;
    valorMensal: number;
    rendimentoMensal: number;
    rendimentoValor: number;
    comissaoMensal: number;
    cor: string;
    destaque: boolean;
}

// Função para obter o ícone correto para cada plano
const getPlanoIcon = (nome: string) => {
    switch (nome.toLowerCase()) {
        case 'empresarial':
            return DollarSign; // Vou usar DollarSign por enquanto
        case 'pessoal':
            return DollarSign;
        case 'busca':
            return DollarSign;
        default:
            return DollarSign;
    }
};

interface PlanosAfiliadosTableProps {
    planos: PlanoAfiliado[];
    className?: string;
}

export default function PlanosAfiliadosTable({ planos, className = '' }: PlanosAfiliadosTableProps) {
    return (
        <div className={`${className}`}>
            <div className="grid gap-8 md:grid-cols-3">
                {/* Cards dos planos */}
                {planos.map((plano, index) => {
                    const IconComponent = getPlanoIcon(plano.nome);
                    return (
                        <div key={index} className="relative">
                            {plano.destaque && <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 transform bg-primary">Mais Popular</Badge>}
                            <Card className={`h-full ${plano.destaque ? 'ring-2 ring-primary' : ''}`}>
                                <CardHeader className="text-center">
                                    <Badge variant="gradient" size="size-12" className="mx-auto mb-4">
                                        <IconComponent className="h-6 w-6" />
                                    </Badge>
                                    <CardTitle className="text-xl">{plano.nome}</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Valor Mensal */}
                                    <div className="text-center">
                                        <p className="mb-1 text-sm text-muted-foreground">Valor Mensal</p>
                                        <p className="text-2xl font-bold text-primary">R$ {plano.valorMensal.toFixed(2).replace('.', ',')}</p>
                                    </div>

                                    {/* Rendimento */}
                                    <div className="rounded-lg border bg-muted/30 py-3 text-center">
                                        <p className="mb-1 text-sm text-muted-foreground">Rendimento Mensal</p>
                                        <p className="text-lg font-semibold">{plano.rendimentoMensal.toFixed(2).replace('.', ',')}%</p>
                                        <p className="text-sm text-muted-foreground">(R$ {plano.rendimentoValor.toFixed(2).replace('.', ',')})</p>
                                    </div>

                                    {/* Comissão */}
                                    <div className="rounded-lg border bg-green-50 py-4 text-center dark:bg-green-950/20">
                                        <p className="mb-1 text-sm text-muted-foreground">Comissão p/ Venda</p>
                                        <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                                            R$ {plano.comissaoMensal.toFixed(2).replace('.', ',')}
                                        </p>
                                        <p className="text-xs text-muted-foreground">por venda/mês</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    );
                })}
            </div>
        </div>
    );
}

// Dados padrão dos planos
export const planosAfiliadosData: PlanoAfiliado[] = [
    {
        nome: 'Empresarial',
        valorMensal: 640.0,
        rendimentoMensal: 2.89,
        rendimentoValor: 18.5,
        comissaoMensal: 18.5,
        cor: 'bg-blue-500',
        destaque: true,
    },
    {
        nome: 'Pessoal',
        valorMensal: 180.0,
        rendimentoMensal: 4.44,
        rendimentoValor: 8.0,
        comissaoMensal: 8.0,
        cor: 'bg-green-500',
        destaque: false,
    },
    {
        nome: 'Busca',
        valorMensal: 14.8,
        rendimentoMensal: 5.41,
        rendimentoValor: 0.8,
        comissaoMensal: 0.8,
        cor: 'bg-purple-500',
        destaque: false,
    },
];

// Hook para calcular ganhos potenciais
export function useCalcularGanhos() {
    const calcularGanhosMensais = (vendas: { empresarial: number; pessoal: number; busca: number }) => {
        const ganhoEmpresarial = vendas.empresarial * planosAfiliadosData[0].comissaoMensal;
        const ganhoPessoal = vendas.pessoal * planosAfiliadosData[1].comissaoMensal;
        const ganhoBusca = vendas.busca * planosAfiliadosData[2].comissaoMensal;

        return {
            empresarial: ganhoEmpresarial,
            pessoal: ganhoPessoal,
            busca: ganhoBusca,
            total: ganhoEmpresarial + ganhoPessoal + ganhoBusca,
        };
    };

    const calcularGanhosAnuais = (vendas: { empresarial: number; pessoal: number; busca: number }) => {
        const ganhosMensais = calcularGanhosMensais(vendas);
        return {
            empresarial: ganhosMensais.empresarial * 12,
            pessoal: ganhosMensais.pessoal * 12,
            busca: ganhosMensais.busca * 12,
            total: ganhosMensais.total * 12,
        };
    };

    return {
        calcularGanhosMensais,
        calcularGanhosAnuais,
        planos: planosAfiliadosData,
    };
}
