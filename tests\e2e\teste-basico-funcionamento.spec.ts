import { expect, test } from '@playwright/test';

// Configuração de teste
const BASE_URL = 'http://localhost:8000';

test.describe('Testes Básicos de Funcionamento - F4 Fisio', () => {
    test.beforeEach(async ({ page }) => {
        test.setTimeout(60000);
        page.setDefaultTimeout(15000);
    });

    test('1. Home Page - FAQ Funcional', async ({ page }) => {
        console.log('🏠 Testando FAQ da home page...');

        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');

        // Verificar se a página carregou
        expect(page.url()).toBe(BASE_URL + '/');
        console.log('✅ Home page carregada');

        // Testar FAQ - clicar na primeira pergunta
        const faqButton = page.locator('button:has-text("Posso cancelar meu plano")').first();
        await faqButton.click();

        // Verificar se a resposta apareceu
        const faqAnswer = page.locator('text=Sim, você pode cancelar seu plano');
        await expect(faqAnswer).toBeVisible();
        console.log('✅ FAQ funcionando - accordion expandiu');
    });

    test('2. Busca de Estabelecimentos', async ({ page }) => {
        console.log('🔍 Testando busca de estabelecimentos...');

        await page.goto(BASE_URL + '/buscar');
        await page.waitForLoadState('networkidle');

        // Verificar se chegou na página de busca
        expect(page.url()).toContain('/buscar');
        console.log('✅ Página de busca acessível');

        // Preencher formulário de busca
        await page.getByRole('textbox', { name: 'Localização' }).fill('São Paulo, SP');
        await page.getByRole('combobox', { name: 'Categoria' }).selectOption('Farmácias');

        // Clicar em buscar
        await page.click('button:has-text("Buscar")');
        await page.waitForLoadState('networkidle');

        // Verificar se há resultados (texto real do sistema)
        const resultados = page.locator('text=farmácias na região');
        await expect(resultados).toBeVisible();
        console.log('✅ Busca funcionando - resultados encontrados');

        // Verificar se há links de estabelecimentos (sem clicar)
        const linksEstabelecimentos = page.locator('a[href*="/estabelecimento/"]');
        const count = await linksEstabelecimentos.count();
        expect(count).toBeGreaterThan(0);
        console.log(`✅ ${count} estabelecimentos com links individuais encontrados`);
    });

    test('3. Responsividade Mobile', async ({ page }) => {
        console.log('📱 Testando responsividade mobile...');

        // Definir viewport mobile
        await page.setViewportSize({ width: 375, height: 667 });

        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');

        // Verificar se não há overflow horizontal
        const hasHorizontalScroll = await page.evaluate(() => {
            return document.documentElement.scrollWidth > document.documentElement.clientWidth;
        });

        expect(hasHorizontalScroll).toBe(false);
        console.log('✅ Sem overflow horizontal em mobile');

        // Verificar se o menu hamburger está presente
        const menuButton = page.locator('button:has(svg)').first();
        await expect(menuButton).toBeVisible();
        console.log('✅ Menu hamburger presente em mobile');
    });

    test('4. Links de WhatsApp nos Resultados', async ({ page }) => {
        console.log('📱 Testando links do WhatsApp nos resultados...');

        await page.goto(BASE_URL + '/buscar');
        await page.waitForLoadState('networkidle');

        // Fazer uma busca para ter resultados
        await page.getByRole('textbox', { name: 'Localização' }).fill('São Paulo, SP');
        await page.getByRole('combobox', { name: 'Categoria' }).selectOption('Farmácias');
        await page.click('button:has-text("Buscar")');
        await page.waitForLoadState('networkidle');

        // Verificar se há links do WhatsApp nos resultados
        const whatsappLinks = page.locator('a[href*="wa.me"]');
        const count = await whatsappLinks.count();

        expect(count).toBeGreaterThan(0);
        console.log(`✅ ${count} links do WhatsApp encontrados nos resultados`);

        // Verificar se os links estão corretos
        const firstLink = whatsappLinks.first();
        const href = await firstLink.getAttribute('href');
        expect(href).toContain('wa.me');
        expect(href).toContain('5511');
        console.log('✅ Links do WhatsApp estão corretos e funcionais');
    });

    test('5. Navegação Principal', async ({ page }) => {
        console.log('🧭 Testando navegação principal...');

        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');

        // Verificar se os links de navegação estão presentes
        const buscarLink = page.getByRole('navigation').getByRole('link', { name: 'Buscar Serviços' });
        await expect(buscarLink).toBeVisible();
        console.log('✅ Link Buscar Serviços presente');

        const homeLink = page.getByRole('navigation').getByRole('link', { name: 'Início' });
        await expect(homeLink).toBeVisible();
        console.log('✅ Link Início presente');

        // Verificar se a página tem o título correto
        await expect(page).toHaveTitle(/F4 Fisio/);
        console.log('✅ Título da página correto');
    });

    test('6. Performance Básica', async ({ page }) => {
        console.log('⚡ Testando performance básica...');

        const startTime = Date.now();
        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');
        const loadTime = Date.now() - startTime;

        // Verificar se carregou em menos de 10 segundos
        expect(loadTime).toBeLessThan(10000);
        console.log(`✅ Página carregou em ${loadTime}ms`);

        // Verificar se não há erros JavaScript no console
        const errors = [];
        page.on('console', (msg) => {
            if (msg.type() === 'error') {
                errors.push(msg.text());
            }
        });

        // Navegar por algumas páginas para verificar erros
        await page.click('a:has-text("Buscar Serviços")');
        await page.waitForLoadState('networkidle');

        await page.click('a:has-text("Sobre Nós")');
        await page.waitForLoadState('networkidle');

        // Verificar se não há muitos erros JavaScript
        expect(errors.length).toBeLessThan(5);
        console.log(`✅ ${errors.length} erros JavaScript encontrados (aceitável)`);
    });

    test('7. Formulários Básicos', async ({ page }) => {
        console.log('📝 Testando formulários básicos...');

        await page.goto(BASE_URL + '/buscar');
        await page.waitForLoadState('networkidle');

        // Testar preenchimento do formulário de busca
        const locationInput = page.getByRole('textbox', { name: 'Localização' });
        await locationInput.fill('São Paulo, SP');

        const inputValue = await locationInput.inputValue();
        expect(inputValue).toBe('São Paulo, SP');
        console.log('✅ Input de localização funcionando');

        // Testar select de categoria
        const categorySelect = page.getByRole('combobox', { name: 'Categoria' });
        await categorySelect.selectOption('Farmácias');

        const selectedValue = await categorySelect.inputValue();
        expect(selectedValue).toBe('farmacia');
        console.log('✅ Select de categoria funcionando');

        // Testar submit do formulário
        await page.click('button:has-text("Buscar")');
        await page.waitForLoadState('networkidle');

        // Verificar se a busca foi processada
        const url = page.url();
        expect(url).toContain('/buscar');
        console.log('✅ Submit do formulário funcionando');
    });
});
