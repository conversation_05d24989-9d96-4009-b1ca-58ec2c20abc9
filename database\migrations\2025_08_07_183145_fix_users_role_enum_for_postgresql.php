<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Esta migração corrige o problema com enum de roles no PostgreSQL
     * que ocorreu nas migrações anteriores.
     */
    public function up(): void
    {
        if (DB::getDriverName() === 'pgsql') {
            // Verificar se a coluna role existe e qual é seu tipo atual
            $columnInfo = DB::selectOne("
                SELECT data_type, character_maximum_length
                FROM information_schema.columns
                WHERE table_name = 'users'
                AND column_name = 'role'
                AND table_schema = current_schema()
            ");

            if ($columnInfo) {
                // Remover qualquer constraint existente relacionada ao role
                $constraints = DB::select("
                    SELECT constraint_name
                    FROM information_schema.check_constraints
                    WHERE constraint_schema = current_schema()
                    AND constraint_name LIKE '%users_role%'
                ");

                foreach ($constraints as $constraint) {
                    DB::statement("ALTER TABLE users DROP CONSTRAINT IF EXISTS {$constraint->constraint_name}");
                }

                // Garantir que a coluna seja varchar se não for
                if ($columnInfo->data_type !== 'character varying') {
                    DB::statement("ALTER TABLE users ALTER COLUMN role TYPE VARCHAR(255)");
                }

                // Adicionar a constraint correta com todos os valores necessários
                DB::statement("
                    ALTER TABLE users
                    ADD CONSTRAINT users_role_check
                    CHECK (role IN ('admin', 'fisioterapeuta', 'paciente', 'afiliado', 'empresa'))
                ");

                // Garantir que a coluna tenha um default
                DB::statement("ALTER TABLE users ALTER COLUMN role SET DEFAULT 'paciente'");

                // Garantir que a coluna seja NOT NULL
                DB::statement("ALTER TABLE users ALTER COLUMN role SET NOT NULL");

                echo "✅ Role enum corrigido para PostgreSQL\n";
            }
        } else {
            echo "ℹ️  Esta migração é específica para PostgreSQL. Banco atual: " . DB::getDriverName() . "\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (DB::getDriverName() === 'pgsql') {
            // Remover a constraint
            DB::statement("ALTER TABLE users DROP CONSTRAINT IF EXISTS users_role_check");

            // Voltar para a constraint original (sem afiliado e empresa)
            DB::statement("
                ALTER TABLE users
                ADD CONSTRAINT users_role_check
                CHECK (role IN ('admin', 'fisioterapeuta', 'paciente'))
            ");

            echo "✅ Role enum revertido para valores originais\n";
        }
    }
};
