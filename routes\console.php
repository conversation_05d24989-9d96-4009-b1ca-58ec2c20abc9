<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Configurar schedule de backups automáticos
Schedule::command('backup:schedule full')
    ->dailyAt('02:00')
    ->withoutOverlapping()
    ->runInBackground();

// Limpeza de backups antigos semanalmente
Schedule::job(new \App\Jobs\CleanupOldBackupsJob(30))
    ->weekly()
    ->sundays()
    ->at('03:00');

// Backup apenas do banco de dados a cada 6 horas
Schedule::command('backup:schedule database')
    ->everySixHours()
    ->withoutOverlapping()
    ->runInBackground();
