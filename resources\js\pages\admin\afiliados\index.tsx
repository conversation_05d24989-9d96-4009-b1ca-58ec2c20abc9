import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { CheckCircle, Eye, Plus, Search, Users, XCircle, Clock, UserCheck } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/admin/dashboard' },
    { title: 'Afiliados', href: '/admin/afiliados' },
];

interface Afiliado {
    id: number;
    codigo_afiliado: string;
    nome: string;
    email: string;
    telefone: string;
    status: 'pendente' | 'aprovado' | 'rejeitado' | 'suspenso';
    ativo: boolean;
    total_vendas: number;
    total_comissoes: number;
    vendas_mes_atual: number;
    comissoes_mes_atual: number;
    created_at: string;
    data_aprovacao?: string;
    aprovado_por?: {
        id: number;
        name: string;
    };
}

interface Props {
    afiliados: {
        data: Afiliado[];
        links: any[];
        meta: any;
    };
    filters: {
        status?: string;
        ativo?: boolean;
        search?: string;
    };
    stats: {
        total: number;
        pendentes: number;
        aprovados: number;
        ativos: number;
    };
}

export default function AfiliadosIndex({ afiliados, filters, stats }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('admin.afiliados.index'), {
            ...filters,
            search: searchTerm,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const handleFilterChange = (key: string, value: string) => {
        router.get(route('admin.afiliados.index'), {
            ...filters,
            [key]: value === filters[key as keyof typeof filters] ? '' : value,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: { variant: 'secondary' as const, icon: Clock, text: 'Pendente' },
            aprovado: { variant: 'default' as const, icon: CheckCircle, text: 'Aprovado' },
            rejeitado: { variant: 'destructive' as const, icon: XCircle, text: 'Rejeitado' },
            suspenso: { variant: 'outline' as const, icon: XCircle, text: 'Suspenso' },
        };

        const config = variants[status as keyof typeof variants] || variants.pendente;
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-3 w-3" />
                {config.text}
            </Badge>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Gerenciar Afiliados" />
            
            <div className="flex h-full flex-1 flex-col gap-6 overflow-x-auto p-4 md:p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Gerenciar Afiliados</h1>
                        <p className="text-muted-foreground">
                            Gerencie cadastros, aprovações e performance dos afiliados
                        </p>
                    </div>
                    <Button asChild>
                        <Link href={route('admin.afiliados.create')}>
                            <Plus className="mr-2 h-4 w-4" />
                            Novo Afiliado
                        </Link>
                    </Button>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
                            <Clock className="h-4 w-4 text-yellow-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600">{stats.pendentes}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Aprovados</CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.aprovados}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Ativos</CardTitle>
                            <UserCheck className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-600">{stats.ativos}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filtros</CardTitle>
                        <CardDescription>Filtre e pesquise afiliados</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col gap-4 sm:flex-row">
                            <form onSubmit={handleSearch} className="flex flex-1 gap-2">
                                <Input
                                    placeholder="Buscar por nome, email ou código..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="flex-1"
                                />
                                <Button type="submit" variant="outline">
                                    <Search className="h-4 w-4" />
                                </Button>
                            </form>
                            
                            <Select
                                value={filters.status || 'all'}
                                onValueChange={(value) => handleFilterChange('status', value)}
                            >
                                <SelectTrigger className="w-40">
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todos</SelectItem>
                                    <SelectItem value="pendente">Pendente</SelectItem>
                                    <SelectItem value="aprovado">Aprovado</SelectItem>
                                    <SelectItem value="rejeitado">Rejeitado</SelectItem>
                                    <SelectItem value="suspenso">Suspenso</SelectItem>
                                </SelectContent>
                            </Select>
                            
                            <Select
                                value={filters.ativo?.toString() || 'all'}
                                onValueChange={(value) => handleFilterChange('ativo', value)}
                            >
                                <SelectTrigger className="w-32">
                                    <SelectValue placeholder="Ativo" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todos</SelectItem>
                                    <SelectItem value="true">Ativo</SelectItem>
                                    <SelectItem value="false">Inativo</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {/* Afiliados Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Afiliados ({afiliados.meta.total})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-2">Código</th>
                                        <th className="text-left p-2">Nome</th>
                                        <th className="text-left p-2">Email</th>
                                        <th className="text-left p-2">Status</th>
                                        <th className="text-left p-2">Vendas/Mês</th>
                                        <th className="text-left p-2">Comissões/Mês</th>
                                        <th className="text-left p-2">Cadastro</th>
                                        <th className="text-left p-2">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {afiliados.data.map((afiliado) => (
                                        <tr key={afiliado.id} className="border-b hover:bg-muted/50">
                                            <td className="p-2 font-mono text-sm">{afiliado.codigo_afiliado}</td>
                                            <td className="p-2">
                                                <div>
                                                    <div className="font-medium">{afiliado.nome}</div>
                                                    <div className="text-sm text-muted-foreground">{afiliado.telefone}</div>
                                                </div>
                                            </td>
                                            <td className="p-2 text-sm">{afiliado.email}</td>
                                            <td className="p-2">
                                                <div className="flex flex-col gap-1">
                                                    {getStatusBadge(afiliado.status)}
                                                    {!afiliado.ativo && (
                                                        <Badge variant="outline" className="text-xs">Inativo</Badge>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="p-2 text-sm">
                                                <div className="font-medium">{afiliado.vendas_mes_atual}</div>
                                                <div className="text-xs text-muted-foreground">
                                                    Total: {afiliado.total_vendas}
                                                </div>
                                            </td>
                                            <td className="p-2 text-sm">
                                                <div className="font-medium">
                                                    R$ {afiliado.comissoes_mes_atual.toFixed(2).replace('.', ',')}
                                                </div>
                                                <div className="text-xs text-muted-foreground">
                                                    Total: R$ {afiliado.total_comissoes.toFixed(2).replace('.', ',')}
                                                </div>
                                            </td>
                                            <td className="p-2 text-sm text-muted-foreground">
                                                {new Date(afiliado.created_at).toLocaleDateString('pt-BR')}
                                            </td>
                                            <td className="p-2">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={route('admin.afiliados.show', afiliado.id)}>
                                                        <Eye className="h-4 w-4" />
                                                    </Link>
                                                </Button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {afiliados.links && (
                            <div className="mt-4 flex justify-center">
                                <div className="flex gap-1">
                                    {afiliados.links.map((link, index) => (
                                        <Button
                                            key={index}
                                            variant={link.active ? "default" : "outline"}
                                            size="sm"
                                            disabled={!link.url}
                                            onClick={() => link.url && router.get(link.url)}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
