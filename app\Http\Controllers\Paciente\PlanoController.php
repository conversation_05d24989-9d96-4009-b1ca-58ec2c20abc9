<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Plano;
use App\Models\Assinatura;
use App\Models\Pagamento;
use App\Services\AffiliateTrackingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class PlanoController extends Controller
{
    /**
     * Display patient's subscription management
     */
    public function index()
    {
        $user = Auth::user();
        
        // Assinatura atual
        $assinaturaAtual = Assinatura::where('paciente_id', $user->id)
            ->where('status', 'ativa')
            ->with('plano')
            ->first();
            
        // Todos os planos disponíveis
        $planosDisponiveis = Plano::where('ativo', true)
            ->orderBy('preco', 'asc')
            ->get();
            
        // Histórico de assinaturas
        $historicoAssinaturas = Assinatura::where('paciente_id', $user->id)
            ->with('plano')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
            
        // Pagamentos pendentes
        $pagamentosPendentes = Pagamento::where('paciente_id', $user->id)
            ->where('status', 'pendente')
            ->with('assinatura.plano')
            ->orderBy('data_vencimento', 'asc')
            ->get();
            
        // Próximo pagamento
        $proximoPagamento = null;
        if ($assinaturaAtual) {
            $proximoPagamento = Pagamento::where('assinatura_id', $assinaturaAtual->id)
                ->where('status', 'pendente')
                ->orderBy('data_vencimento', 'asc')
                ->first();
        }
        
        // Estatísticas de uso
        $statsUso = $this->getStatsUso($user->id, $assinaturaAtual);
        
        return Inertia::render('paciente/plano', [
            'assinaturaAtual' => $assinaturaAtual,
            'planosDisponiveis' => $planosDisponiveis,
            'historicoAssinaturas' => $historicoAssinaturas,
            'pagamentosPendentes' => $pagamentosPendentes,
            'proximoPagamento' => $proximoPagamento,
            'statsUso' => $statsUso,
        ]);
    }
    
    /**
     * Subscribe to a plan
     */
    public function subscribe(Request $request)
    {
        $request->validate([
            'plano_id' => 'required|exists:planos,id',
            'forma_pagamento' => 'required|in:cartao_credito,pix,boleto',
        ]);
        
        $user = Auth::user();
        $plano = Plano::findOrFail($request->plano_id);
        
        // Verificar se já tem assinatura ativa
        $assinaturaAtiva = Assinatura::where('paciente_id', $user->id)
            ->where('status', 'ativa')
            ->first();
            
        if ($assinaturaAtiva) {
            return back()->withErrors([
                'plano' => 'Você já possui uma assinatura ativa. Cancele a atual antes de assinar um novo plano.'
            ]);
        }
        
        DB::beginTransaction();
        
        try {
            // Criar nova assinatura
            $assinatura = Assinatura::create([
                'paciente_id' => $user->id,
                'plano_id' => $plano->id,
                'status' => 'ativa',
                'data_inicio' => Carbon::now(),
                'data_fim' => Carbon::now()->addMonth(),
                'sessoes_restantes' => $plano->sessoes_mes,
                'valor_mensal' => $plano->preco,
            ]);
            
            // Criar primeiro pagamento
            $pagamento = Pagamento::create([
                'paciente_id' => $user->id,
                'assinatura_id' => $assinatura->id,
                'valor' => $plano->preco,
                'data_vencimento' => Carbon::now()->addDays(7), // 7 dias para pagamento
                'status' => 'pendente',
                'forma_pagamento' => $request->forma_pagamento,
            ]);

            // Processar venda de afiliado se houver referência
            $affiliateService = new AffiliateTrackingService();
            $affiliateService->createAffiliateSale(
                $user,
                $assinatura->id,
                'mensal', // ou determinar dinamicamente baseado no plano
                $plano->preco,
                $request
            );

            DB::commit();

            return redirect()->route('paciente.plano.index')
                ->with('success', 'Assinatura realizada com sucesso! Efetue o pagamento para ativar seu plano.');
                
        } catch (\Exception $e) {
            DB::rollback();
            
            return back()->withErrors([
                'plano' => 'Erro ao processar assinatura. Tente novamente.'
            ]);
        }
    }
    
    /**
     * Cancel subscription
     */
    public function cancel(Request $request)
    {
        $user = Auth::user();
        
        $assinatura = Assinatura::where('paciente_id', $user->id)
            ->where('status', 'ativa')
            ->first();
            
        if (!$assinatura) {
            return back()->withErrors([
                'plano' => 'Nenhuma assinatura ativa encontrada.'
            ]);
        }
        
        // Cancelar assinatura (manter até o fim do período pago)
        $assinatura->update([
            'status' => 'cancelada',
            'data_cancelamento' => Carbon::now(),
        ]);
        
        return redirect()->route('paciente.plano.index')
            ->with('success', 'Assinatura cancelada com sucesso. Você pode usar o plano até ' . 
                   $assinatura->data_fim->format('d/m/Y') . '.');
    }
    
    /**
     * Change plan
     */
    public function change(Request $request)
    {
        $request->validate([
            'novo_plano_id' => 'required|exists:planos,id',
        ]);
        
        $user = Auth::user();
        $novoPlano = Plano::findOrFail($request->novo_plano_id);
        
        $assinaturaAtual = Assinatura::where('paciente_id', $user->id)
            ->where('status', 'ativa')
            ->first();
            
        if (!$assinaturaAtual) {
            return back()->withErrors([
                'plano' => 'Nenhuma assinatura ativa encontrada.'
            ]);
        }
        
        DB::beginTransaction();
        
        try {
            // Cancelar assinatura atual
            $assinaturaAtual->update([
                'status' => 'cancelada',
                'data_cancelamento' => Carbon::now(),
            ]);
            
            // Criar nova assinatura
            $novaAssinatura = Assinatura::create([
                'paciente_id' => $user->id,
                'plano_id' => $novoPlano->id,
                'status' => 'ativa',
                'data_inicio' => Carbon::now(),
                'data_fim' => Carbon::now()->addMonth(),
                'sessoes_restantes' => $novoPlano->sessoes_mes,
                'valor_mensal' => $novoPlano->preco,
            ]);
            
            // Calcular valor proporcional (implementação futura)
            $valorProporcional = $this->calcularValorProporcional($assinaturaAtual, $novoPlano);
            
            // Criar pagamento para diferença
            if ($valorProporcional > 0) {
                Pagamento::create([
                    'paciente_id' => $user->id,
                    'assinatura_id' => $novaAssinatura->id,
                    'valor' => $valorProporcional,
                    'data_vencimento' => Carbon::now()->addDays(7),
                    'status' => 'pendente',
                    'forma_pagamento' => 'cartao_credito', // Default
                ]);
            }
            
            DB::commit();
            
            return redirect()->route('paciente.plano.index')
                ->with('success', 'Plano alterado com sucesso!');
                
        } catch (\Exception $e) {
            DB::rollback();
            
            return back()->withErrors([
                'plano' => 'Erro ao alterar plano. Tente novamente.'
            ]);
        }
    }
    
    /**
     * Get usage statistics
     */
    private function getStatsUso($pacienteId, $assinatura)
    {
        if (!$assinatura) {
            return [
                'sessoesUsadas' => 0,
                'sessoesRestantes' => 0,
                'percentualUso' => 0,
                'diasRestantes' => 0,
            ];
        }
        
        $sessoesUsadas = $assinatura->plano->sessoes_mes - $assinatura->sessoes_restantes;
        $percentualUso = $assinatura->plano->sessoes_mes > 0 
            ? ($sessoesUsadas / $assinatura->plano->sessoes_mes) * 100 
            : 0;
        $diasRestantes = Carbon::now()->diffInDays($assinatura->data_fim, false);
        
        return [
            'sessoesUsadas' => $sessoesUsadas,
            'sessoesRestantes' => $assinatura->sessoes_restantes,
            'percentualUso' => round($percentualUso, 1),
            'diasRestantes' => max(0, $diasRestantes),
        ];
    }
    
    /**
     * Calculate proportional value for plan change
     */
    private function calcularValorProporcional($assinaturaAtual, $novoPlano)
    {
        // Implementação simplificada - calcular diferença proporcional
        $diasRestantes = Carbon::now()->diffInDays($assinaturaAtual->data_fim, false);
        $diasTotais = Carbon::parse($assinaturaAtual->data_inicio)->diffInDays($assinaturaAtual->data_fim);
        
        if ($diasTotais <= 0) return $novoPlano->preco;
        
        $valorRestanteAtual = ($assinaturaAtual->valor_mensal / $diasTotais) * max(0, $diasRestantes);
        $valorNovoPlano = ($novoPlano->preco / $diasTotais) * max(0, $diasRestantes);
        
        return max(0, $valorNovoPlano - $valorRestanteAtual);
    }
}
