<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('estabelecimentos', function (Blueprint $table) {
            // Adicionar campo user_id (nullable para compatibilidade com estabelecimentos existentes)
            $table->foreignId('user_id')->nullable()->after('id')->constrained('users')->onDelete('cascade');
            
            // Adicionar campo slug para SEO
            $table->string('slug')->nullable()->after('nome');
            
            // Adicionar índices para performance
            $table->index('user_id');
            $table->unique('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('estabelecimentos', function (Blueprint $table) {
            // Remover foreign key e campos
            $table->dropForeign(['user_id']);
            $table->dropIndex(['user_id']);
            $table->dropUnique(['slug']);
            $table->dropColumn(['user_id', 'slug']);
        });
    }
};
