import { expect, test } from '@playwright/test';

test.describe('Fluxo de Agendamento do Paciente', () => {
    let patientEmail: string;
    let patientPassword = 'password123';

    test.beforeAll(async () => {
        const timestamp = Date.now();
        patientEmail = `paciente.agendamento.${timestamp}@example.com`;
    });

    test.beforeEach(async ({ page }) => {
        // Registrar e fazer login do paciente
        await page.goto('/register');
        await page.fill('input[name="name"]', 'Paciente Agendamento');
        await page.fill('input[name="email"]', patientEmail);
        await page.fill('input[name="password"]', patientPassword);
        await page.fill('input[name="password_confirmation"]', patientPassword);
        await page.selectOption('select[name="role"]', 'paciente');
        await page.click('button[type="submit"]');
        
        // Aguardar redirecionamento
        await page.waitForURL(/.*paciente/);
    });

    test.describe('1. Acesso à Funcionalidade de Agendamento', () => {
        test('deve acessar página de agendamentos via sidebar', async ({ page }) => {
            await page.goto('/paciente/dashboard');
            
            // Clicar no link de agendamentos na sidebar
            const agendamentosLink = page.locator('a:has-text("Agendamentos"), button:has-text("Agendamentos")');
            await expect(agendamentosLink).toBeVisible();
            await agendamentosLink.click();
            
            // Verificar se chegou na página correta
            await expect(page).toHaveURL(/.*paciente\/agendamentos/);
            await expect(page.locator('h1, h2')).toContainText(/agendamento/i);
        });

        test('deve exibir botão para novo agendamento', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Verificar se há botão para criar novo agendamento
            const novoAgendamentoButton = page.locator(
                'button:has-text("Novo"), button:has-text("Agendar"), ' +
                'a:has-text("Novo"), a:has-text("Agendar"), ' +
                '[data-testid="novo-agendamento"]'
            );
            
            if (await novoAgendamentoButton.count() > 0) {
                await expect(novoAgendamentoButton.first()).toBeVisible();
            }
        });

        test('deve exibir lista de agendamentos existentes', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Verificar se há uma tabela ou lista de agendamentos
            const agendamentosList = page.locator(
                'table, .agendamento-item, [data-testid="agendamentos-list"], ' +
                '.card:has-text("agendamento"), .list-item'
            );
            
            // Pode não haver agendamentos para um usuário novo, mas a estrutura deve estar presente
            const hasAgendamentos = await agendamentosList.count() > 0;
            const hasEmptyState = await page.locator('text=Nenhum agendamento, text=Sem agendamentos').isVisible();
            
            expect(hasAgendamentos || hasEmptyState).toBeTruthy();
        });
    });

    test.describe('2. Criação de Novo Agendamento', () => {
        test('deve acessar formulário de novo agendamento', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Clicar no botão de novo agendamento
            const novoButton = page.locator(
                'button:has-text("Novo"), button:has-text("Agendar"), ' +
                'a:has-text("Novo"), a:has-text("Agendar")'
            ).first();
            
            if (await novoButton.isVisible()) {
                await novoButton.click();
                
                // Verificar se chegou na página de criação
                await expect(page).toHaveURL(/.*agendamentos\/create/);
                await expect(page.locator('h1, h2')).toContainText(/novo|agendar|criar/i);
            } else {
                // Se não há botão, tentar acessar diretamente
                await page.goto('/paciente/agendamentos/create');
                await expect(page.locator('h1, h2')).toContainText(/novo|agendar|criar/i);
            }
        });

        test('deve exibir campos obrigatórios do formulário', async ({ page }) => {
            await page.goto('/paciente/agendamentos/create');
            
            // Verificar campos essenciais do formulário
            const expectedFields = [
                'select[name="fisioterapeuta_id"], input[name="fisioterapeuta_id"]', // Seleção de fisioterapeuta
                'input[name="data_agendamento"], input[type="date"]', // Data
                'select[name="horario"], input[name="horario"]', // Horário
                'select[name="tipo"]', // Tipo de agendamento
            ];

            for (const fieldSelector of expectedFields) {
                const field = page.locator(fieldSelector);
                if (await field.count() > 0) {
                    await expect(field.first()).toBeVisible();
                }
            }

            // Verificar se há botão de submissão
            const submitButton = page.locator('button[type="submit"], button:has-text("Agendar"), button:has-text("Confirmar")');
            await expect(submitButton).toBeVisible();
        });

        test('deve validar campos obrigatórios', async ({ page }) => {
            await page.goto('/paciente/agendamentos/create');
            
            // Tentar submeter formulário vazio
            const submitButton = page.locator('button[type="submit"], button:has-text("Agendar")');
            if (await submitButton.isVisible()) {
                await submitButton.click();
                
                // Verificar se aparecem mensagens de erro
                const errorMessages = page.locator(
                    '.error, .text-red-500, .text-danger, ' +
                    '[data-testid="error"], .invalid-feedback'
                );
                
                if (await errorMessages.count() > 0) {
                    await expect(errorMessages.first()).toBeVisible();
                }
            }
        });

        test('deve permitir seleção de fisioterapeuta', async ({ page }) => {
            await page.goto('/paciente/agendamentos/create');
            
            // Verificar seleção de fisioterapeuta
            const fisioterapeutaSelect = page.locator('select[name="fisioterapeuta_id"]');
            if (await fisioterapeutaSelect.isVisible()) {
                // Verificar se há opções disponíveis
                const options = fisioterapeutaSelect.locator('option');
                const optionCount = await options.count();
                
                if (optionCount > 1) { // Mais de 1 porque geralmente há uma opção vazia
                    await fisioterapeutaSelect.selectOption({ index: 1 });
                    
                    // Verificar se a seleção foi feita
                    const selectedValue = await fisioterapeutaSelect.inputValue();
                    expect(selectedValue).not.toBe('');
                }
            }
        });

        test('deve permitir seleção de data futura', async ({ page }) => {
            await page.goto('/paciente/agendamentos/create');
            
            const dateField = page.locator('input[name="data_agendamento"], input[type="date"]');
            if (await dateField.isVisible()) {
                // Selecionar data futura (amanhã)
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                const tomorrowStr = tomorrow.toISOString().split('T')[0];
                
                await dateField.fill(tomorrowStr);
                
                // Verificar se a data foi preenchida
                const fieldValue = await dateField.inputValue();
                expect(fieldValue).toBe(tomorrowStr);
            }
        });

        test('deve carregar horários disponíveis após seleção de fisioterapeuta e data', async ({ page }) => {
            await page.goto('/paciente/agendamentos/create');
            
            // Selecionar fisioterapeuta
            const fisioterapeutaSelect = page.locator('select[name="fisioterapeuta_id"]');
            if (await fisioterapeutaSelect.isVisible()) {
                const options = await fisioterapeutaSelect.locator('option').count();
                if (options > 1) {
                    await fisioterapeutaSelect.selectOption({ index: 1 });
                }
            }
            
            // Selecionar data
            const dateField = page.locator('input[name="data_agendamento"], input[type="date"]');
            if (await dateField.isVisible()) {
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                const tomorrowStr = tomorrow.toISOString().split('T')[0];
                await dateField.fill(tomorrowStr);
            }
            
            // Aguardar carregamento de horários
            await page.waitForTimeout(2000);
            
            // Verificar se horários foram carregados
            const horarioSelect = page.locator('select[name="horario"]');
            if (await horarioSelect.isVisible()) {
                const horarioOptions = await horarioSelect.locator('option').count();
                // Deve ter pelo menos a opção vazia + horários disponíveis
                expect(horarioOptions).toBeGreaterThan(0);
            }
        });
    });

    test.describe('3. Gestão de Agendamentos Existentes', () => {
        test('deve permitir visualizar detalhes de agendamento', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Procurar por links de detalhes
            const detailsLinks = page.locator(
                'a:has-text("Ver"), a:has-text("Detalhes"), ' +
                'button:has-text("Ver"), button:has-text("Detalhes"), ' +
                '.agendamento-item a, [data-testid="view-agendamento"]'
            );
            
            if (await detailsLinks.count() > 0) {
                await detailsLinks.first().click();
                
                // Verificar se chegou na página de detalhes
                await expect(page).toHaveURL(/.*agendamentos\/\d+/);
                await expect(page.locator('h1, h2')).toContainText(/agendamento|detalhes/i);
            }
        });

        test('deve permitir cancelar agendamento', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Procurar por botões de cancelar
            const cancelButtons = page.locator(
                'button:has-text("Cancelar"), a:has-text("Cancelar"), ' +
                '[data-testid="cancel-agendamento"]'
            );
            
            if (await cancelButtons.count() > 0) {
                // Clicar no primeiro botão de cancelar
                await cancelButtons.first().click();
                
                // Verificar se aparece confirmação
                const confirmDialog = page.locator('text=tem certeza, text=confirmar, text=cancelar');
                if (await confirmDialog.isVisible()) {
                    // Confirmar cancelamento
                    const confirmButton = page.locator('button:has-text("Sim"), button:has-text("Confirmar")');
                    if (await confirmButton.isVisible()) {
                        await confirmButton.click();
                        
                        // Verificar mensagem de sucesso
                        await expect(page.locator('text=cancelado, text=sucesso')).toBeVisible({ timeout: 5000 });
                    }
                }
            }
        });

        test('deve permitir reagendar agendamento', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Procurar por botões de reagendar
            const rescheduleButtons = page.locator(
                'button:has-text("Reagendar"), a:has-text("Reagendar"), ' +
                'button:has-text("Remarcar"), a:has-text("Remarcar"), ' +
                '[data-testid="reschedule-agendamento"]'
            );
            
            if (await rescheduleButtons.count() > 0) {
                await rescheduleButtons.first().click();
                
                // Verificar se chegou na página de reagendamento
                await expect(page).toHaveURL(/.*agendamentos\/\d+\/reschedule/);
                await expect(page.locator('h1, h2')).toContainText(/reagendar|remarcar/i);
                
                // Verificar se há campos para nova data/horário
                const newDateField = page.locator('input[name="data_agendamento"], input[type="date"]');
                const newTimeField = page.locator('select[name="horario"], input[name="horario"]');
                
                if (await newDateField.isVisible()) {
                    await expect(newDateField).toBeVisible();
                }
                if (await newTimeField.isVisible()) {
                    await expect(newTimeField).toBeVisible();
                }
            }
        });
    });

    test.describe('4. Filtros e Busca', () => {
        test('deve permitir filtrar agendamentos por status', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Procurar por filtro de status
            const statusFilter = page.locator('select[name="status"], select:has(option:has-text("Agendado"))');
            if (await statusFilter.isVisible()) {
                // Testar diferentes opções de status
                const options = statusFilter.locator('option');
                const optionCount = await options.count();
                
                if (optionCount > 1) {
                    await statusFilter.selectOption({ index: 1 });
                    
                    // Verificar se a página foi atualizada
                    await page.waitForTimeout(1000);
                    await expect(page).toHaveURL(/.*status=/);
                }
            }
        });

        test('deve permitir filtrar agendamentos por período', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Procurar por filtro de período
            const periodFilter = page.locator('select[name="periodo"], select:has(option:has-text("mês"))');
            if (await periodFilter.isVisible()) {
                const options = periodFilter.locator('option');
                const optionCount = await options.count();
                
                if (optionCount > 1) {
                    await periodFilter.selectOption({ index: 1 });
                    
                    // Verificar se a página foi atualizada
                    await page.waitForTimeout(1000);
                    await expect(page).toHaveURL(/.*periodo=/);
                }
            }
        });
    });

    test.describe('5. Responsividade do Agendamento', () => {
        test('deve funcionar em dispositivos móveis', async ({ page }) => {
            // Definir viewport mobile
            await page.setViewportSize({ width: 375, height: 667 });
            
            await page.goto('/paciente/agendamentos');
            
            // Verificar se a página é responsiva
            await expect(page.locator('h1, h2')).toBeVisible();
            
            // Verificar se botões são acessíveis em mobile
            const actionButtons = page.locator('button, a[role="button"]');
            if (await actionButtons.count() > 0) {
                await expect(actionButtons.first()).toBeVisible();
            }
            
            // Testar formulário de agendamento em mobile
            await page.goto('/paciente/agendamentos/create');
            
            // Verificar se campos são acessíveis
            const formFields = page.locator('input, select, textarea');
            if (await formFields.count() > 0) {
                await expect(formFields.first()).toBeVisible();
            }
        });
    });
});
