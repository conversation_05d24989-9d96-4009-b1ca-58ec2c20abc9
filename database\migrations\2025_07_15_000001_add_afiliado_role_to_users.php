<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (DB::getDriverName() === 'pgsql') {
            // Para PostgreSQL, precisamos trabalhar com check constraints

            // Primeiro, encontrar o nome da constraint existente
            $constraintName = DB::selectOne("
                SELECT constraint_name
                FROM information_schema.check_constraints
                WHERE constraint_schema = current_schema()
                AND constraint_name LIKE '%users_role_check%'
            ")?->constraint_name;

            if ($constraintName) {
                // Remover a constraint existente
                DB::statement("ALTER TABLE users DROP CONSTRAINT {$constraintName}");
            }

            // Adicionar nova constraint com 'afiliado'
            DB::statement("
                ALTER TABLE users
                ADD CONSTRAINT users_role_check
                CHECK (role IN ('admin', 'fisioterapeuta', 'paciente', 'afiliado'))
            ");

        } else {
            // Para outros bancos (MySQL), usar o método tradicional
            Schema::table('users', function (Blueprint $table) {
                $table->enum('role', ['admin', 'fisioterapeuta', 'paciente', 'afiliado'])->default('paciente')->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (DB::getDriverName() === 'pgsql') {
            // Verificar se existem usuários com role 'afiliado'
            $afiliadoUsers = DB::table('users')->where('role', 'afiliado')->count();

            if ($afiliadoUsers > 0) {
                throw new \Exception("Não é possível reverter: existem {$afiliadoUsers} usuários com role 'afiliado'. Remova-os primeiro.");
            }

            // Remover a constraint atual
            DB::statement("ALTER TABLE users DROP CONSTRAINT IF EXISTS users_role_check");

            // Adicionar constraint sem 'afiliado'
            DB::statement("
                ALTER TABLE users
                ADD CONSTRAINT users_role_check
                CHECK (role IN ('admin', 'fisioterapeuta', 'paciente'))
            ");

        } else {
            // Para outros bancos (MySQL)
            Schema::table('users', function (Blueprint $table) {
                $table->enum('role', ['admin', 'fisioterapeuta', 'paciente'])->default('paciente')->change();
            });
        }
    }
};
