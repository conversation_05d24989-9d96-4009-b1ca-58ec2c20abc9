import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, usePage } from '@inertiajs/react';
import { Activity, AlertCircle, ArrowLeft, Calendar, Clock, FileText, Target, User } from 'lucide-react';

interface Props {
    agendamento: {
        id: number;
        data_agendamento: string;
        horario: string;
        status: string;
        tipo: string;
        observacoes?: string;
        fisioterapeuta: {
            user: {
                name: string;
                email: string;
            };
            especialidades: string[];
        };
        relatorio_sessao?: {
            observacoes: string;
            exercicios_realizados: string;
            evolucao_paciente: string;
            recomendacoes: string;
            proximos_passos: string;
            created_at: string;
        };
        avaliacao?: {
            observacoes: string;
            diagnostico: string;
            objetivos_tratamento: string;
            plano_tratamento: string;
            created_at: string;
        };
    };
}

export default function PacienteHistoricoShow() {
    const pageProps = usePage().props as any;
    const { agendamento } = pageProps;

    const getStatusBadge = (status: string) => {
        const variants = {
            agendado: 'default',
            concluido: 'default',
            cancelado: 'destructive',
            em_andamento: 'secondary',
        } as const;

        const labels = {
            agendado: 'Agendado',
            concluido: 'Concluído',
            cancelado: 'Cancelado',
            em_andamento: 'Em Andamento',
        };

        return <Badge variant={variants[status as keyof typeof variants] || 'default'}>{labels[status as keyof typeof labels] || status}</Badge>;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
        });
    };

    const formatTime = (timeString: string | null | undefined) => {
        if (!timeString) return '--:--';
        return timeString.substring(0, 5);
    };

    const formatDateTime = (dateTimeString: string) => {
        return new Date(dateTimeString).toLocaleString('pt-BR');
    };

    return (
        <AppLayout>
            <Head title={`Detalhes da ${agendamento.tipo === 'sessao' ? 'Sessão' : 'Avaliação'}`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                <div className="flex items-center gap-4">
                    <Link href={route('paciente.historico.index')}>
                        <Button variant="ghost" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar ao Histórico
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Detalhes da {agendamento.tipo === 'sessao' ? 'Sessão' : 'Avaliação'}</h1>
                        <p className="text-muted-foreground">
                            {formatDate(agendamento.data_agendamento)} às {formatTime(agendamento.horario)}
                        </p>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-3">
                    {/* Informações Gerais */}
                    <Card className="md:col-span-1">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Informações Gerais
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <div>
                                    <p className="text-sm text-muted-foreground">Data</p>
                                    <p className="font-medium">{formatDate(agendamento.data_agendamento)}</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-muted-foreground" />
                                <div>
                                    <p className="text-sm text-muted-foreground">Horário</p>
                                    <p className="font-medium">{formatTime(agendamento.horario)}</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                <Activity className="h-4 w-4 text-muted-foreground" />
                                <div>
                                    <p className="text-sm text-muted-foreground">Tipo</p>
                                    <p className="font-medium">{agendamento.tipo === 'sessao' ? 'Sessão de Fisioterapia' : 'Avaliação'}</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                <div>
                                    <p className="text-sm text-muted-foreground">Status</p>
                                    {getStatusBadge(agendamento.status)}
                                </div>
                            </div>

                            <Separator />

                            <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-muted-foreground" />
                                <div>
                                    <p className="text-sm text-muted-foreground">Fisioterapeuta</p>
                                    <p className="font-medium">{agendamento.fisioterapeuta.user.name}</p>
                                    <p className="text-sm text-muted-foreground">{agendamento.fisioterapeuta.user.email}</p>
                                </div>
                            </div>

                            {agendamento.fisioterapeuta.especialidades && agendamento.fisioterapeuta.especialidades.length > 0 && (
                                <div>
                                    <p className="mb-2 text-sm text-muted-foreground">Especialidades</p>
                                    <div className="flex flex-wrap gap-1">
                                        {agendamento.fisioterapeuta.especialidades.map((especialidade: string, index: number) => (
                                            <Badge key={index} variant="secondary" className="text-xs">
                                                {especialidade}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Relatório da Sessão ou Avaliação */}
                    <div className="space-y-6 md:col-span-2">
                        {agendamento.relatorio_sessao && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Relatório da Sessão
                                    </CardTitle>
                                    <p className="text-sm text-muted-foreground">
                                        Criado em {formatDateTime(agendamento.relatorio_sessao.created_at)}
                                    </p>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {agendamento.relatorio_sessao.observacoes && (
                                        <div>
                                            <h4 className="mb-2 flex items-center gap-2 font-medium">
                                                <AlertCircle className="h-4 w-4" />
                                                Observações Gerais
                                            </h4>
                                            <p className="rounded-md bg-muted p-3 text-sm text-muted-foreground">
                                                {agendamento.relatorio_sessao.observacoes}
                                            </p>
                                        </div>
                                    )}

                                    {agendamento.relatorio_sessao.exercicios_realizados && (
                                        <div>
                                            <h4 className="mb-2 flex items-center gap-2 font-medium">
                                                <Activity className="h-4 w-4" />
                                                Exercícios Realizados
                                            </h4>
                                            <p className="rounded-md bg-muted p-3 text-sm text-muted-foreground">
                                                {agendamento.relatorio_sessao.exercicios_realizados}
                                            </p>
                                        </div>
                                    )}

                                    {agendamento.relatorio_sessao.evolucao_paciente && (
                                        <div>
                                            <h4 className="mb-2 flex items-center gap-2 font-medium">
                                                <Target className="h-4 w-4" />
                                                Evolução do Paciente
                                            </h4>
                                            <p className="rounded-md bg-muted p-3 text-sm text-muted-foreground">
                                                {agendamento.relatorio_sessao.evolucao_paciente}
                                            </p>
                                        </div>
                                    )}

                                    {agendamento.relatorio_sessao.recomendacoes && (
                                        <div>
                                            <h4 className="mb-2 font-medium">Recomendações</h4>
                                            <p className="rounded-md bg-muted p-3 text-sm text-muted-foreground">
                                                {agendamento.relatorio_sessao.recomendacoes}
                                            </p>
                                        </div>
                                    )}

                                    {agendamento.relatorio_sessao.proximos_passos && (
                                        <div>
                                            <h4 className="mb-2 font-medium">Próximos Passos</h4>
                                            <p className="rounded-md bg-muted p-3 text-sm text-muted-foreground">
                                                {agendamento.relatorio_sessao.proximos_passos}
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}

                        {agendamento.avaliacao && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Relatório de Avaliação
                                    </CardTitle>
                                    <p className="text-sm text-muted-foreground">Criado em {formatDateTime(agendamento.avaliacao.created_at)}</p>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {agendamento.avaliacao.observacoes && (
                                        <div>
                                            <h4 className="mb-2 font-medium">Observações</h4>
                                            <p className="rounded-md bg-muted p-3 text-sm text-muted-foreground">
                                                {agendamento.avaliacao.observacoes}
                                            </p>
                                        </div>
                                    )}

                                    {agendamento.avaliacao.diagnostico && (
                                        <div>
                                            <h4 className="mb-2 font-medium">Diagnóstico</h4>
                                            <p className="rounded-md bg-muted p-3 text-sm text-muted-foreground">
                                                {agendamento.avaliacao.diagnostico}
                                            </p>
                                        </div>
                                    )}

                                    {agendamento.avaliacao.objetivos_tratamento && (
                                        <div>
                                            <h4 className="mb-2 font-medium">Objetivos do Tratamento</h4>
                                            <p className="rounded-md bg-muted p-3 text-sm text-muted-foreground">
                                                {agendamento.avaliacao.objetivos_tratamento}
                                            </p>
                                        </div>
                                    )}

                                    {agendamento.avaliacao.plano_tratamento && (
                                        <div>
                                            <h4 className="mb-2 font-medium">Plano de Tratamento</h4>
                                            <p className="rounded-md bg-muted p-3 text-sm text-muted-foreground">
                                                {agendamento.avaliacao.plano_tratamento}
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}

                        {!agendamento.relatorio_sessao && !agendamento.avaliacao && agendamento.status === 'concluido' && (
                            <Card>
                                <CardContent className="p-6 text-center">
                                    <AlertCircle className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                    <h3 className="mb-2 text-lg font-medium">Relatório não disponível</h3>
                                    <p className="text-muted-foreground">O relatório desta sessão ainda não foi criado pelo fisioterapeuta.</p>
                                </CardContent>
                            </Card>
                        )}

                        {agendamento.status !== 'concluido' && (
                            <Card>
                                <CardContent className="p-6 text-center">
                                    <AlertCircle className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                    <h3 className="mb-2 text-lg font-medium">Sessão não concluída</h3>
                                    <p className="text-muted-foreground">O relatório estará disponível após a conclusão da sessão.</p>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
