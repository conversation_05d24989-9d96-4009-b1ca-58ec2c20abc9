<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\Afiliado;

class AfiliadoController extends Controller
{
    /**
     * Store a newly created affiliate registration.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nome' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'telefone' => 'required|string|max:20',
            'cpf' => 'required|string|max:14',
            'endereco' => 'required|string|max:500',
            'cidade' => 'required|string|max:100',
            'estado' => 'required|string|size:2',
            'cep' => 'required|string|max:9',
            'experiencia' => 'nullable|string|in:nenhuma,iniciante,intermediario,avancado',
            'motivacao' => 'required|string|max:1000',
            'canais_divulgacao' => 'required|array|min:1',
            'canais_divulgacao.*' => 'string|in:Instagram,Facebook,WhatsApp,LinkedIn,TikTok,YouTube,Blog/Site,Indicação Pessoal,Outros',
            'aceita_termos' => 'required|accepted',
        ], [
            'nome.required' => 'O nome é obrigatório.',
            'email.required' => 'O e-mail é obrigatório.',
            'email.email' => 'O e-mail deve ter um formato válido.',
            'telefone.required' => 'O telefone é obrigatório.',
            'cpf.required' => 'O CPF é obrigatório.',
            'endereco.required' => 'O endereço é obrigatório.',
            'cidade.required' => 'A cidade é obrigatória.',
            'estado.required' => 'O estado é obrigatório.',
            'cep.required' => 'O CEP é obrigatório.',
            'motivacao.required' => 'A motivação é obrigatória.',
            'canais_divulgacao.required' => 'Selecione pelo menos um canal de divulgação.',
            'canais_divulgacao.min' => 'Selecione pelo menos um canal de divulgação.',
            'aceita_termos.accepted' => 'Você deve aceitar os termos e condições.',
        ]);

        try {
            // Verificar se usuário está logado (obrigatório agora)
            if (!auth()->check()) {
                return redirect()->route('login')->with('error', 'Você precisa estar logado para se cadastrar como afiliado.');
            }

            $user = auth()->user();

            // Verificar se usuário já tem perfil de afiliado
            if ($user->hasAfiliadoProfile()) {
                return redirect()->back()->with('error', 'Você já possui um perfil de afiliado.');
            }

            // Criar novo afiliado
            $afiliado = new Afiliado();
            $afiliado->codigo_afiliado = $afiliado->gerarCodigoAfiliado();
            $afiliado->fill($validated);
            $afiliado->status = 'pendente';
            $afiliado->user_id = auth()->id();

            $afiliado->save();

            // Log para auditoria
            Log::info('Novo cadastro de afiliado', [
                'afiliado_id' => $afiliado->id,
                'codigo' => $afiliado->codigo_afiliado,
                'nome' => $afiliado->nome,
                'email' => $afiliado->email,
            ]);

            // Enviar e-mail de notificação para a equipe
            $this->enviarNotificacaoEquipe($afiliado);

            // Enviar e-mail de confirmação para o afiliado
            $this->enviarConfirmacaoAfiliado($afiliado);

            return redirect()->route('dashboard')->with('success', 'Cadastro de afiliado realizado com sucesso! Aguarde a aprovação da nossa equipe.');

        } catch (\Exception $e) {
            Log::error('Erro ao processar cadastro de afiliado', [
                'error' => $e->getMessage(),
                'data' => $validated
            ]);

            return redirect()->back()
                ->withErrors(['error' => 'Ocorreu um erro ao processar seu cadastro. Tente novamente.'])
                ->withInput();
        }
    }

    /**
     * Enviar notificação para a equipe sobre novo cadastro
     */
    private function enviarNotificacaoEquipe(Afiliado $afiliado)
    {
        try {
            $emailEquipe = config('mail.admin_email', '<EMAIL>');

            // Implementar envio de e-mail aqui se necessário
            Log::info('Notificação de novo afiliado enviada para equipe', [
                'email_equipe' => $emailEquipe,
                'afiliado_id' => $afiliado->id,
                'afiliado' => $afiliado->nome,
                'codigo' => $afiliado->codigo_afiliado
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao enviar notificação para equipe', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Enviar e-mail de confirmação para o afiliado
     */
    private function enviarConfirmacaoAfiliado(Afiliado $afiliado)
    {
        try {
            // Implementar envio de e-mail de confirmação aqui se necessário
            Log::info('E-mail de confirmação enviado para afiliado', [
                'afiliado_id' => $afiliado->id,
                'email' => $afiliado->email,
                'nome' => $afiliado->nome
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao enviar confirmação para afiliado', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Calcular ganhos potenciais (método auxiliar para futuras funcionalidades)
     */
    public function calcularGanhos(Request $request)
    {
        $validated = $request->validate([
            'vendas_empresarial' => 'required|integer|min:0|max:1000',
            'vendas_pessoal' => 'required|integer|min:0|max:1000',
            'vendas_busca' => 'required|integer|min:0|max:1000',
        ]);

        $comissoes = [
            'empresarial' => 18.50,
            'pessoal' => 8.00,
            'busca' => 0.80,
        ];

        $ganhoMensal = (
            ($validated['vendas_empresarial'] * $comissoes['empresarial']) +
            ($validated['vendas_pessoal'] * $comissoes['pessoal']) +
            ($validated['vendas_busca'] * $comissoes['busca'])
        );

        $ganhoAnual = $ganhoMensal * 12;

        return response()->json([
            'ganho_mensal' => $ganhoMensal,
            'ganho_anual' => $ganhoAnual,
            'detalhes' => [
                'empresarial' => $validated['vendas_empresarial'] * $comissoes['empresarial'],
                'pessoal' => $validated['vendas_pessoal'] * $comissoes['pessoal'],
                'busca' => $validated['vendas_busca'] * $comissoes['busca'],
            ]
        ]);
    }
}
