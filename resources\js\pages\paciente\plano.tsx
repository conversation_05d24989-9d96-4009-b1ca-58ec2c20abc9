import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import { Activity, AlertTriangle, CheckCircle, Clock, DollarSign, Package } from 'lucide-react';
import { useState } from 'react';

interface Props {
    assinaturaAtual?: {
        id: number;
        status: string;
        data_inicio: string;
        data_fim: string;
        sessoes_restantes: number;
        valor_mensal: number;
        plano: {
            id: number;
            nome: string;
            descricao: string;
            preco: number;
            sessoes_mes: number;
        };
    };
    planosDisponiveis: Array<{
        id: number;
        nome: string;
        descricao: string;
        preco: number;
        sessoes_mes: number;
        beneficios: string[];
    }>;
    historicoAssinaturas: Array<{
        id: number;
        status: string;
        data_inicio: string;
        data_fim: string;
        plano: {
            nome: string;
        };
    }>;
    pagamentosPendentes: Array<{
        id: number;
        valor: number;
        data_vencimento: string;
        status: string;
        forma_pagamento: string;
    }>;
    proximoPagamento?: {
        id: number;
        valor: number;
        data_vencimento: string;
        forma_pagamento: string;
    };
    statsUso: {
        sessoesUsadas: number;
        sessoesRestantes: number;
        percentualUso: number;
        diasRestantes: number;
    };
}

export default function PacientePlano() {
    const pageProps = usePage().props as any;
    const { assinaturaAtual, planosDisponiveis, historicoAssinaturas, pagamentosPendentes, proximoPagamento, statsUso } = pageProps;

    const [showCancelDialog, setShowCancelDialog] = useState(false);
    const [showChangeDialog, setShowChangeDialog] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState<number | null>(null);

    const { data, setData, post, processing } = useForm({
        plano_id: '',
        forma_pagamento: 'cartao_credito',
    });

    const handleSubscribe = (planoId: number) => {
        setData('plano_id', planoId.toString());
        post(route('paciente.plano.subscribe'));
    };

    const handleCancel = () => {
        post(route('paciente.plano.cancel'));
        setShowCancelDialog(false);
    };

    const handleChange = () => {
        if (selectedPlan) {
            post(route('paciente.plano.change'), {
                novo_plano_id: selectedPlan,
            } as any);
        }
        setShowChangeDialog(false);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            ativa: 'default',
            cancelada: 'destructive',
            suspensa: 'secondary',
            expirada: 'outline',
        } as const;

        const labels = {
            ativa: 'Ativa',
            cancelada: 'Cancelada',
            suspensa: 'Suspensa',
            expirada: 'Expirada',
        };

        return <Badge variant={variants[status as keyof typeof variants] || 'default'}>{labels[status as keyof typeof labels] || status}</Badge>;
    };

    return (
        <AppLayout>
            <Head title="Gerenciar Plano" />

            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">Gerenciar Plano</h1>
                    <p className="text-muted-foreground">Gerencie sua assinatura e acompanhe o uso do seu plano</p>
                </div>

                {/* Alertas */}
                {pagamentosPendentes.length > 0 && (
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            Você possui {pagamentosPendentes.length} pagamento(s) pendente(s). Regularize sua situação para continuar usando o
                            serviço.
                        </AlertDescription>
                    </Alert>
                )}

                {assinaturaAtual ? (
                    <>
                        {/* Plano Atual */}
                        <div className="grid gap-6 md:grid-cols-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Package className="h-5 w-5" />
                                        Plano Atual
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h3 className="text-lg font-semibold">{assinaturaAtual.plano.nome}</h3>
                                            <p className="text-sm text-muted-foreground">{assinaturaAtual.plano.descricao}</p>
                                        </div>
                                        {getStatusBadge(assinaturaAtual.status)}
                                    </div>

                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <p className="text-muted-foreground">Valor Mensal</p>
                                            <p className="text-lg font-semibold">{formatCurrency(assinaturaAtual.valor_mensal)}</p>
                                        </div>
                                        <div>
                                            <p className="text-muted-foreground">Sessões/Mês</p>
                                            <p className="text-lg font-semibold">{assinaturaAtual.plano.sessoes_mes}</p>
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>Período</span>
                                            <span>
                                                {formatDate(assinaturaAtual.data_inicio)} - {formatDate(assinaturaAtual.data_fim)}
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span>Dias Restantes</span>
                                            <span className="font-medium">{statsUso.diasRestantes} dias</span>
                                        </div>
                                    </div>

                                    <div className="flex gap-2">
                                        <Button variant="outline" size="sm" onClick={() => setShowChangeDialog(true)}>
                                            Alterar Plano
                                        </Button>
                                        <Button variant="destructive" size="sm" onClick={() => setShowCancelDialog(true)}>
                                            Cancelar
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Uso do Plano */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Activity className="h-5 w-5" />
                                        Uso do Plano
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>Sessões Utilizadas</span>
                                            <span>
                                                {statsUso.sessoesUsadas} de {assinaturaAtual.plano.sessoes_mes}
                                            </span>
                                        </div>
                                        <Progress value={statsUso.percentualUso} className="h-2" />
                                        <p className="text-center text-xs text-muted-foreground">{statsUso.percentualUso}% utilizado</p>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="rounded-lg bg-muted p-3 text-center">
                                            <p className="text-2xl font-bold text-green-600">{statsUso.sessoesRestantes}</p>
                                            <p className="text-xs text-muted-foreground">Sessões Restantes</p>
                                        </div>
                                        <div className="rounded-lg bg-muted p-3 text-center">
                                            <p className="text-2xl font-bold text-blue-600">{statsUso.diasRestantes}</p>
                                            <p className="text-xs text-muted-foreground">Dias Restantes</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Próximo Pagamento */}
                        {proximoPagamento && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <DollarSign className="h-5 w-5" />
                                        Próximo Pagamento
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-lg font-semibold">{formatCurrency(proximoPagamento.valor)}</p>
                                            <p className="text-sm text-muted-foreground">
                                                Vencimento: {formatDate(proximoPagamento.data_vencimento)}
                                            </p>
                                            <p className="text-sm text-muted-foreground">Forma de pagamento: {proximoPagamento.forma_pagamento}</p>
                                        </div>
                                        <Button>Pagar Agora</Button>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </>
                ) : (
                    /* Escolher Plano */
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Escolha seu Plano</CardTitle>
                                <p className="text-muted-foreground">Selecione o plano que melhor atende às suas necessidades</p>
                            </CardHeader>
                        </Card>

                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                            {planosDisponiveis.map((plano: any) => (
                                <Card key={plano.id} className="relative">
                                    <CardHeader>
                                        <CardTitle>{plano.nome}</CardTitle>
                                        <p className="text-sm text-muted-foreground">{plano.descricao}</p>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="text-center">
                                            <p className="text-3xl font-bold">{formatCurrency(plano.preco)}</p>
                                            <p className="text-sm text-muted-foreground">por mês</p>
                                        </div>

                                        <div className="text-center">
                                            <p className="text-lg font-semibold">{plano.sessoes_mes} sessões</p>
                                            <p className="text-sm text-muted-foreground">por mês</p>
                                        </div>

                                        {plano.beneficios && plano.beneficios.length > 0 && (
                                            <div className="space-y-2">
                                                <p className="text-sm font-medium">Benefícios:</p>
                                                <ul className="space-y-1 text-sm text-muted-foreground">
                                                    {plano.beneficios.map((beneficio: string, index: number) => (
                                                        <li key={index} className="flex items-center gap-2">
                                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                                            {beneficio}
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}

                                        <Button className="w-full" onClick={() => handleSubscribe(plano.id)} disabled={processing}>
                                            {processing ? 'Processando...' : 'Escolher Plano'}
                                        </Button>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                )}

                {/* Histórico de Assinaturas */}
                {historicoAssinaturas.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5" />
                                Histórico de Assinaturas
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {historicoAssinaturas.map((assinatura: any) => (
                                    <div key={assinatura.id} className="flex items-center justify-between rounded-lg border p-3">
                                        <div>
                                            <p className="font-medium">{assinatura.plano.nome}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatDate(assinatura.data_inicio)} - {formatDate(assinatura.data_fim)}
                                            </p>
                                        </div>
                                        {getStatusBadge(assinatura.status)}
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
