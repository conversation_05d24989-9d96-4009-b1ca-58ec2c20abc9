<?php

namespace App\Http\Requests;

use App\Models\Avaliacao;
use Illuminate\Foundation\Http\FormRequest;

class AvaliacaoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->role === 'paciente';
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'agendamento_id' => 'required|exists:agendamentos,id',
            'nota_geral' => 'required|integer|min:1|max:5',
            'nota_pontualidade' => 'nullable|integer|min:1|max:5',
            'nota_profissionalismo' => 'nullable|integer|min:1|max:5',
            'nota_eficacia' => 'nullable|integer|min:1|max:5',
            'comentario' => 'nullable|string|max:1000',
            'recomendaria' => 'boolean',
            'pontos_positivos' => 'nullable|array',
            'pontos_positivos.*' => 'string|in:' . implode(',', array_keys(Avaliacao::getPontosPositivos())),
            'pontos_melhorar' => 'nullable|array',
            'pontos_melhorar.*' => 'string|in:' . implode(',', array_keys(Avaliacao::getPontosMelhorar())),
            'anonima' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'agendamento_id.required' => 'O agendamento é obrigatório.',
            'agendamento_id.exists' => 'Agendamento inválido.',
            'nota_geral.required' => 'A nota geral é obrigatória.',
            'nota_geral.integer' => 'A nota geral deve ser um número.',
            'nota_geral.min' => 'A nota geral deve ser no mínimo 1.',
            'nota_geral.max' => 'A nota geral deve ser no máximo 5.',
            'nota_pontualidade.integer' => 'A nota de pontualidade deve ser um número.',
            'nota_pontualidade.min' => 'A nota de pontualidade deve ser no mínimo 1.',
            'nota_pontualidade.max' => 'A nota de pontualidade deve ser no máximo 5.',
            'nota_profissionalismo.integer' => 'A nota de profissionalismo deve ser um número.',
            'nota_profissionalismo.min' => 'A nota de profissionalismo deve ser no mínimo 1.',
            'nota_profissionalismo.max' => 'A nota de profissionalismo deve ser no máximo 5.',
            'nota_eficacia.integer' => 'A nota de eficácia deve ser um número.',
            'nota_eficacia.min' => 'A nota de eficácia deve ser no mínimo 1.',
            'nota_eficacia.max' => 'A nota de eficácia deve ser no máximo 5.',
            'comentario.max' => 'O comentário não pode ter mais de 1000 caracteres.',
            'recomendaria.boolean' => 'O campo recomendaria deve ser verdadeiro ou falso.',
            'pontos_positivos.array' => 'Os pontos positivos devem ser uma lista.',
            'pontos_positivos.*.in' => 'Ponto positivo inválido.',
            'pontos_melhorar.array' => 'Os pontos a melhorar devem ser uma lista.',
            'pontos_melhorar.*.in' => 'Ponto a melhorar inválido.',
            'anonima.boolean' => 'O campo anônima deve ser verdadeiro ou falso.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'agendamento_id' => 'agendamento',
            'nota_geral' => 'nota geral',
            'nota_pontualidade' => 'nota de pontualidade',
            'nota_profissionalismo' => 'nota de profissionalismo',
            'nota_eficacia' => 'nota de eficácia',
            'comentario' => 'comentário',
            'recomendaria' => 'recomendaria',
            'pontos_positivos' => 'pontos positivos',
            'pontos_melhorar' => 'pontos a melhorar',
            'anonima' => 'anônima',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Converter strings para boolean se necessário
        if ($this->has('recomendaria')) {
            $this->merge([
                'recomendaria' => filter_var($this->recomendaria, FILTER_VALIDATE_BOOLEAN),
            ]);
        }

        if ($this->has('anonima')) {
            $this->merge([
                'anonima' => filter_var($this->anonima, FILTER_VALIDATE_BOOLEAN),
            ]);
        }

        // Garantir que arrays vazios sejam null
        if ($this->has('pontos_positivos') && empty($this->pontos_positivos)) {
            $this->merge(['pontos_positivos' => null]);
        }

        if ($this->has('pontos_melhorar') && empty($this->pontos_melhorar)) {
            $this->merge(['pontos_melhorar' => null]);
        }
    }
}
