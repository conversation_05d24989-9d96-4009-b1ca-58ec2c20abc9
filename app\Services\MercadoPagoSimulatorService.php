<?php

namespace App\Services;

use App\Models\Pagamento;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class MercadoPagoSimulatorService
{
    /**
     * Simular criação de preferência de pagamento
     */
    public function createPreference($pagamento)
    {
        try {
            // Gerar um ID de transação simulado
            $transactionId = 'SIM_' . time() . '_' . Str::random(8);
            
            // Simular resposta do Mercado Pago
            $simulatedResponse = [
                'id' => $transactionId,
                'init_point' => route('mercadopago.simulator.payment', ['transaction_id' => $transactionId]),
                'sandbox_init_point' => route('mercadopago.simulator.payment', ['transaction_id' => $transactionId]),
                'external_reference' => $pagamento->id,
                'status' => 'pending',
                'date_created' => now()->toISOString(),
            ];

            // Salvar o transaction_id no pagamento
            $pagamento->update([
                'transaction_id' => $transactionId,
                'gateway_response' => $simulatedResponse,
            ]);

            Log::info('Simulação Mercado Pago - Preferência criada', [
                'pagamento_id' => $pagamento->id,
                'transaction_id' => $transactionId,
                'valor' => $pagamento->amount,
            ]);

            return [
                'success' => true,
                'preference_id' => $transactionId,
                'init_point' => $simulatedResponse['init_point'],
                'sandbox_init_point' => $simulatedResponse['sandbox_init_point'],
            ];

        } catch (\Exception $e) {
            Log::error('Erro na simulação Mercado Pago', [
                'pagamento_id' => $pagamento->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Erro na simulação do pagamento',
            ];
        }
    }

    /**
     * Simular informações de um pagamento
     */
    public function getPayment($transactionId)
    {
        try {
            // Buscar o pagamento pelo transaction_id
            $pagamento = Pagamento::where('transaction_id', $transactionId)->first();
            
            if (!$pagamento) {
                return null;
            }

            // Simular resposta do Mercado Pago
            return [
                'id' => $transactionId,
                'external_reference' => $pagamento->id,
                'status' => $this->mapStatusToMercadoPago($pagamento->status),
                'status_detail' => 'accredited',
                'transaction_amount' => (float) $pagamento->amount,
                'currency_id' => 'BRL',
                'payment_method_id' => 'pix',
                'payment_type_id' => 'account_money',
                'date_created' => $pagamento->created_at->toISOString(),
                'date_approved' => $pagamento->paid_at ? $pagamento->paid_at->toISOString() : null,
                'payer' => [
                    'id' => $pagamento->assinatura->user->id,
                    'email' => $pagamento->assinatura->user->email,
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Erro ao simular getPayment', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Simular webhook de pagamento
     */
    public function simulateWebhook($transactionId, $status = 'approved')
    {
        try {
            $webhookData = [
                'id' => time(),
                'live_mode' => false,
                'type' => 'payment',
                'date_created' => now()->toISOString(),
                'application_id' => 'simulator',
                'user_id' => 'simulator',
                'version' => 1,
                'api_version' => 'v1',
                'action' => 'payment.updated',
                'data' => [
                    'id' => $transactionId,
                ],
            ];

            Log::info('Simulando webhook Mercado Pago', [
                'transaction_id' => $transactionId,
                'status' => $status,
                'webhook_data' => $webhookData,
            ]);

            return $webhookData;

        } catch (\Exception $e) {
            Log::error('Erro ao simular webhook', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Mapear status interno para status do Mercado Pago
     */
    private function mapStatusToMercadoPago($internalStatus)
    {
        $statusMap = [
            'pendente' => 'pending',
            'pago' => 'approved',
            'falhou' => 'rejected',
            'cancelado' => 'cancelled',
        ];

        return $statusMap[$internalStatus] ?? 'pending';
    }

    /**
     * Verificar se está em modo de simulação
     */
    public function isSimulationMode()
    {
        return config('app.env') === 'local' || config('services.mercadopago.simulate', false);
    }

    /**
     * Processar pagamento simulado
     */
    public function processSimulatedPayment($transactionId, $action = 'approve')
    {
        try {
            $pagamento = Pagamento::where('transaction_id', $transactionId)->first();
            
            if (!$pagamento) {
                return ['success' => false, 'message' => 'Pagamento não encontrado'];
            }

            $newStatus = match($action) {
                'approve' => 'pago',
                'reject' => 'falhou',
                'cancel' => 'cancelado',
                default => 'pendente',
            };

            // Atualizar o pagamento
            $updateData = ['status' => $newStatus];
            if ($newStatus === 'pago') {
                $updateData['paid_at'] = now();
                $updateData['method'] = 'pix';
            }

            $pagamento->update($updateData);

            Log::info('Pagamento simulado processado', [
                'transaction_id' => $transactionId,
                'action' => $action,
                'new_status' => $newStatus,
            ]);

            return [
                'success' => true,
                'status' => $newStatus,
                'message' => "Pagamento {$action} com sucesso",
            ];

        } catch (\Exception $e) {
            Log::error('Erro ao processar pagamento simulado', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao processar pagamento simulado',
            ];
        }
    }
}
