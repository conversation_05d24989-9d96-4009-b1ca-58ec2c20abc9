import {
  Button,
  Heading,
  Hr,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';
import { EmailLayout } from './components/layout';

interface AgendamentoCanceladoEmailProps {
  pacienteNome: string;
  fisioterapeutaNome: string;
  dataHora: string;
  motivo?: string;
  reagendarUrl?: string;
  buscarUrl?: string;
}

export const AgendamentoCanceladoEmail = ({
  pacienteNome = 'Paciente',
  fisioterapeutaNome = 'Fisioterapeuta',
  dataHora = '01/01/2025 às 10:00',
  motivo,
  reagendarUrl = 'https://f4fisio.com.br/agendamentos',
  buscarUrl = 'https://f4fisio.com.br/buscar',
}: AgendamentoCanceladoEmailProps) => {
  return (
    <EmailLayout preview={`Agendamento cancelado - ${dataHora}`}>
      <Section style={statusBanner}>
        <Text style={statusText}>
          ❌ AGENDAMENTO CANCELADO
        </Text>
      </Section>
      
      <Heading style={h1}>
        Agendamento Cancelado
      </Heading>
      
      <Text style={text}>
        Olá <strong>{pacienteNome}</strong>,
      </Text>
      
      <Text style={text}>
        Informamos que seu agendamento de fisioterapia foi cancelado. Veja os detalhes abaixo:
      </Text>
      
      <Section style={detailsCard}>
        <Heading style={cardTitle}>Detalhes do Agendamento Cancelado</Heading>
        
        <div style={detailRow}>
          <Text style={detailLabel}>📅 Data e Horário:</Text>
          <Text style={detailValue}>{dataHora}</Text>
        </div>
        
        <div style={detailRow}>
          <Text style={detailLabel}>👨‍⚕️ Fisioterapeuta:</Text>
          <Text style={detailValue}>{fisioterapeutaNome}</Text>
        </div>
      </Section>
      
      {motivo && (
        <>
          <Heading style={h2}>Motivo do Cancelamento</Heading>
          <Section style={motivoCard}>
            <Text style={text}>{motivo}</Text>
          </Section>
        </>
      )}
      
      <Hr style={hr} />
      
      <Heading style={h2}>O que fazer agora?</Heading>
      
      <Text style={text}>
        Não se preocupe! Você pode facilmente reagendar sua consulta ou encontrar outro profissional disponível.
      </Text>
      
      <Section style={actionsSection}>
        <div style={actionCard}>
          <Text style={actionIcon}>🔄</Text>
          <Heading style={actionTitle}>Reagendar com o mesmo profissional</Heading>
          <Text style={actionDescription}>
            Encontre um novo horário disponível com {fisioterapeutaNome}
          </Text>
          <Button style={primaryButton} href={reagendarUrl}>
            Reagendar Consulta
          </Button>
        </div>
        
        <div style={actionCard}>
          <Text style={actionIcon}>🔍</Text>
          <Heading style={actionTitle}>Buscar outros profissionais</Heading>
          <Text style={actionDescription}>
            Explore outros fisioterapeutas disponíveis na sua região
          </Text>
          <Button style={secondaryButton} href={buscarUrl}>
            Buscar Profissionais
          </Button>
        </div>
      </Section>
      
      <Hr style={hr} />
      
      <Section style={supportSection}>
        <Heading style={h2}>Precisa de Ajuda?</Heading>
        
        <Text style={text}>
          Nossa equipe de suporte está sempre disponível para ajudá-lo:
        </Text>
        
        <div style={supportItem}>
          <Text style={supportIcon}>💬</Text>
          <Text style={supportText}>
            <strong>WhatsApp:</strong> (11) 9 7819-6207
          </Text>
        </div>
        
        <div style={supportItem}>
          <Text style={supportIcon}>📧</Text>
          <Text style={supportText}>
            <strong>Email:</strong> <EMAIL>
          </Text>
        </div>
        
        <div style={supportItem}>
          <Text style={supportIcon}>🌐</Text>
          <Text style={supportText}>
            <strong>Site:</strong> f4fisio.com.br/contato
          </Text>
        </div>
      </Section>
      
      <Text style={text}>
        Lamentamos qualquer inconveniente causado e esperamos poder atendê-lo em breve.
      </Text>
      
      <Text style={signature}>
        Atenciosamente,<br />
        Equipe F4 Fisio
      </Text>
    </EmailLayout>
  );
};

export default AgendamentoCanceladoEmail;

const statusBanner = {
  backgroundColor: '#ef4444',
  padding: '12px',
  borderRadius: '6px',
  textAlign: 'center' as const,
  margin: '0 0 24px 0',
};

const statusText = {
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0',
};

const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 0 20px 0',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '24px 0 16px 0',
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px 0',
};

const detailsCard = {
  backgroundColor: '#f9fafb',
  border: '1px solid #e5e7eb',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
};

const cardTitle = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
};

const detailRow = {
  margin: '0 0 12px 0',
};

const detailLabel = {
  color: '#6b7280',
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0 0 4px 0',
};

const detailValue = {
  color: '#1f2937',
  fontSize: '16px',
  margin: '0',
};

const motivoCard = {
  backgroundColor: '#fef2f2',
  border: '1px solid #ef4444',
  borderRadius: '6px',
  padding: '16px',
  margin: '16px 0',
};

const actionsSection = {
  margin: '24px 0',
};

const actionCard = {
  backgroundColor: '#f9fafb',
  border: '1px solid #e5e7eb',
  borderRadius: '8px',
  padding: '20px',
  margin: '0 0 16px 0',
  textAlign: 'center' as const,
};

const actionIcon = {
  fontSize: '32px',
  margin: '0 0 12px 0',
};

const actionTitle = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 8px 0',
};

const actionDescription = {
  color: '#6b7280',
  fontSize: '14px',
  margin: '0 0 16px 0',
};

const primaryButton = {
  backgroundColor: '#3b82f6',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  margin: '8px',
};

const secondaryButton = {
  backgroundColor: '#6b7280',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  margin: '8px',
};

const supportSection = {
  backgroundColor: '#f0f9ff',
  border: '1px solid #0ea5e9',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
};

const supportItem = {
  display: 'flex',
  alignItems: 'center',
  margin: '0 0 12px 0',
};

const supportIcon = {
  fontSize: '16px',
  margin: '0 12px 0 0',
  minWidth: '20px',
};

const supportText = {
  color: '#374151',
  fontSize: '14px',
  margin: '0',
};

const signature = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '24px 0 0 0',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '24px 0',
};
