<?php

namespace App\Http\Middleware;

use App\Models\Afiliado;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class TrackAffiliateReferral
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Verificar se há parâmetro de referência na URL
        $refCode = $request->query('ref');
        $cupomCode = $request->query('cupom');
        
        if ($refCode) {
            $this->processAffiliateReferral($refCode, $request);
        }
        
        if ($cupomCode) {
            $this->processCouponCode($cupomCode, $request);
        }
        
        $response = $next($request);
        
        return $response;
    }
    
    /**
     * Process affiliate referral code
     */
    private function processAffiliateReferral(string $refCode, Request $request): void
    {
        try {
            // Verificar se o código de afiliado existe e está ativo
            $afiliado = Afiliado::where('codigo_afiliado', $refCode)
                ->where('status', 'aprovado')
                ->where('ativo', true)
                ->first();
            
            if ($afiliado) {
                // Salvar referência em cookie (30 dias)
                Cookie::queue('affiliate_ref', $refCode, 60 * 24 * 30);
                
                // Salvar também em sessão para acesso imediato
                session(['affiliate_ref' => $refCode]);
                
                // Log para auditoria
                Log::info('Affiliate referral tracked', [
                    'ref_code' => $refCode,
                    'afiliado_id' => $afiliado->id,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'url' => $request->fullUrl(),
                ]);
                
                // Incrementar contador de cliques (opcional - criar tabela se necessário)
                $this->trackClick($afiliado, $request);
            } else {
                Log::warning('Invalid affiliate referral code', [
                    'ref_code' => $refCode,
                    'ip' => $request->ip(),
                    'url' => $request->fullUrl(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error processing affiliate referral', [
                'ref_code' => $refCode,
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);
        }
    }
    
    /**
     * Process coupon code (may also have affiliate association)
     */
    private function processCouponCode(string $cupomCode, Request $request): void
    {
        try {
            // Salvar cupom em cookie e sessão
            Cookie::queue('coupon_code', $cupomCode, 60 * 24 * 7); // 7 dias
            session(['coupon_code' => $cupomCode]);
            
            Log::info('Coupon code tracked', [
                'coupon_code' => $cupomCode,
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
            ]);
        } catch (\Exception $e) {
            Log::error('Error processing coupon code', [
                'coupon_code' => $cupomCode,
                'error' => $e->getMessage(),
            ]);
        }
    }
    
    /**
     * Track click for analytics (optional)
     */
    private function trackClick(Afiliado $afiliado, Request $request): void
    {
        try {
            // Aqui você pode implementar tracking de cliques
            // Por exemplo, criar uma tabela affiliate_clicks
            
            // Por enquanto, apenas log
            Log::info('Affiliate click tracked', [
                'afiliado_id' => $afiliado->id,
                'codigo_afiliado' => $afiliado->codigo_afiliado,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referrer' => $request->header('referer'),
                'timestamp' => now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Error tracking affiliate click', [
                'afiliado_id' => $afiliado->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
