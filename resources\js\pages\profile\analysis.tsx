import { ProfileStatus } from '@/components/profile-status';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { CheckCircle, Clock, Target, TrendingUp, User } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Configurações',
        href: '/settings/profile',
    },
    {
        title: 'An<PERSON><PERSON><PERSON> do Perfil',
        href: '/profile-status/analysis',
    },
];

interface ProfileStatusData {
    is_complete: boolean;
    percentage: number;
    missing_fields: string[];
    missing_count: number;
    status: 'complete' | 'good' | 'fair' | 'poor' | 'incomplete';
    next_steps: string[];
}

interface Props {
    status: ProfileStatusData;
    completion_url: string;
    required_fields: string[];
    user_role: string;
}

export default function ProfileAnalysis({ status, completion_url, required_fields, user_role }: Props) {
    const getStatusColor = (statusLevel: string) => {
        switch (statusLevel) {
            case 'complete':
                return 'text-green-600 bg-green-50 border-green-200';
            case 'good':
                return 'text-blue-600 bg-blue-50 border-blue-200';
            case 'fair':
                return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            case 'poor':
                return 'text-orange-600 bg-orange-50 border-orange-200';
            case 'incomplete':
                return 'text-red-600 bg-red-50 border-red-200';
            default:
                return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    const getRoleDisplayName = (role: string) => {
        const names = {
            'admin': 'Administrador',
            'fisioterapeuta': 'Fisioterapeuta',
            'paciente': 'Paciente',
            'empresa': 'Empresa',
            'afiliado': 'Afiliado',
        };
        return names[role as keyof typeof names] || role;
    };

    const getCompletionMessage = () => {
        if (status.is_complete) {
            return 'Parabéns! Seu perfil está completo e você pode aproveitar todas as funcionalidades da plataforma.';
        }
        
        if (status.percentage >= 80) {
            return 'Seu perfil está quase completo! Complete as informações restantes para ter acesso total.';
        }
        
        if (status.percentage >= 60) {
            return 'Você está no caminho certo! Complete mais algumas informações para melhorar sua experiência.';
        }
        
        return 'Seu perfil precisa de mais informações. Complete os dados para ter acesso a todas as funcionalidades.';
    };

    const estimateTime = () => {
        const minutes = status.missing_count * 2;
        if (minutes < 5) return 'menos de 5 minutos';
        if (minutes < 15) return '5-15 minutos';
        if (minutes < 30) return '15-30 minutos';
        return 'mais de 30 minutos';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Análise do Perfil" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                            <User className="h-5 w-5" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold">Análise do Perfil</h1>
                            <p className="text-muted-foreground">
                                Veja o status detalhado do seu perfil como {getRoleDisplayName(user_role)}
                            </p>
                        </div>
                    </div>
                    {!status.is_complete && (
                        <Button asChild>
                            <Link href={completion_url}>
                                Completar Perfil
                            </Link>
                        </Button>
                    )}
                </div>

                {/* Status Overview */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Completude</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{status.percentage}%</div>
                            <Progress value={status.percentage} className="mt-2" />
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Campos Restantes</CardTitle>
                            <Target className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{status.missing_count}</div>
                            <p className="text-xs text-muted-foreground">
                                de {required_fields.length} campos
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Tempo Estimado</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{estimateTime()}</div>
                            <p className="text-xs text-muted-foreground">
                                para completar
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Status</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className={`text-sm font-medium px-2 py-1 rounded border ${getStatusColor(status.status)}`}>
                                {status.status === 'complete' && 'Completo'}
                                {status.status === 'good' && 'Quase Completo'}
                                {status.status === 'fair' && 'Parcial'}
                                {status.status === 'poor' && 'Incompleto'}
                                {status.status === 'incomplete' && 'Muito Incompleto'}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Main Status Card */}
                <ProfileStatus 
                    status={status} 
                    completionUrl={completion_url} 
                    showCard={true}
                    showAlert={false}
                />

                {/* Detailed Analysis */}
                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Next Steps */}
                    {status.next_steps.length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Próximos Passos</CardTitle>
                                <CardDescription>
                                    Complete estas informações para melhorar seu perfil
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {status.next_steps.map((step, index) => (
                                        <div key={index} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                                            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium">
                                                {index + 1}
                                            </div>
                                            <div className="flex-1">
                                                <p className="text-sm">{step}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                
                                {!status.is_complete && (
                                    <div className="mt-4 pt-4 border-t">
                                        <Button asChild className="w-full">
                                            <Link href={completion_url}>
                                                Começar Agora
                                            </Link>
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    )}

                    {/* Completion Message */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Status Atual</CardTitle>
                            <CardDescription>
                                Resumo da situação do seu perfil
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className={`p-4 rounded-lg border ${getStatusColor(status.status)}`}>
                                <p className="text-sm leading-relaxed">
                                    {getCompletionMessage()}
                                </p>
                            </div>

                            {status.is_complete && (
                                <div className="mt-4 space-y-3">
                                    <div className="flex items-center gap-2 text-green-600">
                                        <CheckCircle className="h-5 w-5" />
                                        <span className="font-medium">Perfil Completo!</span>
                                    </div>
                                    <p className="text-sm text-muted-foreground">
                                        Você tem acesso a todas as funcionalidades da plataforma. 
                                        Continue mantendo suas informações atualizadas.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Back to Profile */}
                <div className="flex justify-center">
                    <Button variant="outline" asChild>
                        <Link href={completion_url}>
                            Ir para Perfil
                        </Link>
                    </Button>
                </div>
            </div>
        </AppLayout>
    );
}
