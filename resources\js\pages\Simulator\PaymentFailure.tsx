import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { XCircle, Home, RotateCcw, MessageCircle } from 'lucide-react';

interface Pagamento {
    id?: number;
    transaction_id: string;
    message: string;
}

interface Props {
    pagamento: Pagamento;
}

export default function PaymentFailure({ pagamento }: Props) {
    return (
        <>
            <Head title="Pagamento Rejeitado - Simulador" />
            
            <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-100 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <div className="text-center mb-6">
                        <div className="mx-auto w-20 h-20 bg-red-600 rounded-full flex items-center justify-center mb-4">
                            <XCircle className="w-10 h-10 text-white" />
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Pagamento Não Aprovado
                        </h1>
                        <p className="text-gray-600">
                            Sua transação não pôde ser processada
                        </p>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-center text-red-600">
                                Transação Rejeitada
                            </CardTitle>
                            <CardDescription className="text-center">
                                ID: {pagamento.transaction_id}
                            </CardDescription>
                        </CardHeader>
                        
                        <CardContent className="space-y-4">
                            <div className="bg-red-50 p-4 rounded-lg text-center">
                                <p className="text-red-800 font-medium">
                                    {pagamento.message}
                                </p>
                            </div>

                            <div className="pt-4 border-t space-y-3">
                                <h4 className="font-semibold text-gray-900">
                                    O que fazer agora?
                                </h4>
                                <ul className="text-sm text-gray-600 space-y-2">
                                    <li className="flex items-start">
                                        <span className="text-blue-600 mr-2">•</span>
                                        Verifique os dados do seu cartão ou método de pagamento
                                    </li>
                                    <li className="flex items-start">
                                        <span className="text-blue-600 mr-2">•</span>
                                        Certifique-se de que há saldo suficiente
                                    </li>
                                    <li className="flex items-start">
                                        <span className="text-blue-600 mr-2">•</span>
                                        Tente novamente ou use outro método de pagamento
                                    </li>
                                    <li className="flex items-start">
                                        <span className="text-blue-600 mr-2">•</span>
                                        Entre em contato conosco se o problema persistir
                                    </li>
                                </ul>
                            </div>

                            <div className="grid grid-cols-1 gap-3 pt-4">
                                {pagamento.id && (
                                    <Button asChild>
                                        <Link href={route('paciente.pagamentos.show', pagamento.id)}>
                                            <RotateCcw className="w-4 h-4 mr-2" />
                                            Tentar Novamente
                                        </Link>
                                    </Button>
                                )}
                                
                                <Button asChild variant="outline">
                                    <Link href="https://wa.me/5511978196207" target="_blank">
                                        <MessageCircle className="w-4 h-4 mr-2" />
                                        Falar no WhatsApp
                                    </Link>
                                </Button>
                                
                                <Button asChild variant="ghost">
                                    <Link href={route('dashboard')}>
                                        <Home className="w-4 h-4 mr-2" />
                                        Voltar ao Dashboard
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="mt-6 text-center">
                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                            <p className="text-sm text-orange-800 font-medium mb-1">
                                ⚠️ Simulação de Falha
                            </p>
                            <p className="text-xs text-orange-600">
                                Este é um resultado simulado para desenvolvimento.
                                Em produção, você receberia o motivo real da rejeição.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
