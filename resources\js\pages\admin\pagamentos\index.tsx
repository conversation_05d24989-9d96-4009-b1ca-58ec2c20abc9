import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { AlertTriangle, Calendar, CheckCircle, Clock, CreditCard, DollarSign, Download, Eye, Filter, Search, User } from 'lucide-react';
import React from 'react';

interface Pagamento {
    id: number;
    valor: number;
    status: 'pendente' | 'pago' | 'falhou' | 'cancelado';
    forma_pagamento: string;
    data_vencimento: string;
    data_pagamento?: string;
    transaction_id?: string;
    observacoes?: string;
    formatted_valor: string;
    formatted_data_vencimento: string;
    formatted_data_pagamento?: string;
    assinatura: {
        id: number;
        status: string;
        user: {
            id: number;
            name: string;
            email: string;
        };
        plano: {
            id: number;
            name: string;
            price: number;
        };
    };
}

interface Stats {
    total_pendente: number;
    total_pago_mes: number;
    total_vencido: number;
    count_pendente: number;
    count_pago_mes: number;
    count_vencido: number;
}

interface Props {
    pagamentos: {
        data: Pagamento[];
        links: any[];
        meta?: {
            total?: number;
            current_page?: number;
            last_page?: number;
            per_page?: number;
            from?: number;
            to?: number;
        };
    };
    filters: {
        status?: string;
        forma_pagamento?: string;
        periodo?: string;
        search?: string;
    };
    stats: Stats;
}

export default function PagamentosIndex({ pagamentos, filters, stats }: Props) {
    const [searchTerm, setSearchTerm] = React.useState(filters.search || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(
            route('admin.pagamentos.index'),
            {
                ...filters,
                search: searchTerm,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleFilterChange = (key: string, value: string) => {
        router.get(
            route('admin.pagamentos.index'),
            {
                ...filters,
                [key]: value === filters[key as keyof typeof filters] ? '' : value,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleExport = () => {
        window.open(route('admin.pagamentos.export', filters));
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
            pago: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
            falhou: { variant: 'destructive' as const, icon: AlertTriangle, color: 'text-red-600' },
            cancelado: { variant: 'outline' as const, icon: AlertTriangle, color: 'text-gray-600' },
        };

        const config = variants[status as keyof typeof variants] || variants.pendente;
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-3 w-3" />
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getFormaPagamentoBadge = (forma: string) => {
        const formas = {
            cartao_credito: 'Cartão de Crédito',
            cartao_debito: 'Cartão de Débito',
            pix: 'PIX',
            boleto: 'Boleto',
        };

        return (
            <Badge variant="outline" className="flex items-center gap-1">
                <CreditCard className="h-3 w-3" />
                {formas[forma as keyof typeof formas] || forma}
            </Badge>
        );
    };

    return (
        <AppLayout>
            <Head title="Gestão de Pagamentos" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <h2 className="text-3xl font-bold tracking-tight">Gestão de Pagamentos</h2>
                        <p className="text-muted-foreground">Gerencie todos os pagamentos e cobranças do sistema</p>
                    </div>

                    {/* Estatísticas */}
                    <div className="mb-8 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Receita do Mês</CardTitle>
                                <DollarSign className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-600">
                                    R$ {stats.total_pago_mes.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                </div>
                                <p className="text-xs text-muted-foreground">{stats.count_pago_mes} pagamentos confirmados</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
                                <Clock className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-yellow-600">
                                    R$ {stats.total_pendente.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                </div>
                                <p className="text-xs text-muted-foreground">{stats.count_pendente} pagamentos pendentes</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Vencidos</CardTitle>
                                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-red-600">
                                    R$ {stats.total_vencido.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                                </div>
                                <p className="text-xs text-muted-foreground">{stats.count_vencido} pagamentos vencidos</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filtros e Busca */}
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Filter className="mr-2 h-5 w-5" />
                                Filtros
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-col gap-4 md:flex-row">
                                {/* Busca */}
                                <form onSubmit={handleSearch} className="flex-1">
                                    <div className="flex gap-2">
                                        <Input
                                            placeholder="Buscar por nome ou email do paciente..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Button type="submit">
                                            <Search className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </form>

                                {/* Filtros */}
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        variant={filters.status === 'pendente' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleFilterChange('status', 'pendente')}
                                    >
                                        Pendentes
                                    </Button>
                                    <Button
                                        variant={filters.status === 'pago' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleFilterChange('status', 'pago')}
                                    >
                                        Pagos
                                    </Button>
                                    <Button
                                        variant={filters.periodo === 'vencidos' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleFilterChange('periodo', 'vencidos')}
                                    >
                                        Vencidos
                                    </Button>
                                    <Button
                                        variant={filters.periodo === 'hoje' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleFilterChange('periodo', 'hoje')}
                                    >
                                        Hoje
                                    </Button>
                                    <Button
                                        variant={filters.periodo === 'mes' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleFilterChange('periodo', 'mes')}
                                    >
                                        Este Mês
                                    </Button>
                                </div>

                                <Button onClick={handleExport} variant="outline">
                                    <Download className="mr-2 h-4 w-4" />
                                    Exportar
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Lista de Pagamentos */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Pagamentos</CardTitle>
                            <CardDescription>{pagamentos.meta?.total ?? pagamentos.data.length} pagamentos encontrados</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {pagamentos.data.map((pagamento) => (
                                    <div key={pagamento.id} className="flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50">
                                        <div className="flex-1">
                                            <div className="mb-2 flex items-center gap-4">
                                                <div className="flex items-center text-sm text-gray-600">
                                                    <User className="mr-1 h-4 w-4" />
                                                    <span className="font-medium">{pagamento.assinatura.user.name}</span>
                                                </div>
                                                <div className="text-sm text-gray-500">{pagamento.assinatura.plano.name}</div>
                                                {getStatusBadge(pagamento.status)}
                                                {getFormaPagamentoBadge(pagamento.forma_pagamento)}
                                            </div>

                                            <div className="flex items-center gap-6 text-sm text-gray-600">
                                                <div className="flex items-center">
                                                    <Calendar className="mr-1 h-4 w-4" />
                                                    <span>Venc: {pagamento.formatted_data_vencimento}</span>
                                                </div>
                                                {pagamento.data_pagamento && (
                                                    <div className="flex items-center">
                                                        <CheckCircle className="mr-1 h-4 w-4 text-green-600" />
                                                        <span>Pago: {pagamento.formatted_data_pagamento}</span>
                                                    </div>
                                                )}
                                                <div className="text-lg font-bold text-green-600">{pagamento.formatted_valor}</div>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            <Link href={route('admin.pagamentos.show', pagamento.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}

                                {pagamentos.data.length === 0 && (
                                    <div className="py-8 text-center text-gray-500">Nenhum pagamento encontrado com os filtros aplicados.</div>
                                )}
                            </div>

                            {/* Paginação */}
                            {pagamentos.links && pagamentos.links.length > 3 && (
                                <div className="mt-6 flex justify-center">
                                    <div className="flex gap-2">
                                        {pagamentos.links.map((link, index) => (
                                            <Link
                                                key={index}
                                                href={link.url || '#'}
                                                className={`rounded-md px-3 py-2 text-sm ${
                                                    link.active ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                                } ${!link.url ? 'cursor-not-allowed opacity-50' : ''}`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
