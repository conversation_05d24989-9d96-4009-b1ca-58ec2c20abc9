<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Estabelecimento extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'nome',
        'slug',
        'categoria',
        'descricao',
        'telefone',
        'whatsapp',
        'email',
        'endereco',
        'cidade',
        'estado',
        'cep',
        'latitude',
        'longitude',
        'horario_funcionamento',
        'servicos_oferecidos',
        'site',
        'instagram',
        'facebook',
        'ativo',
        'plano_ativo',
        'plano_vencimento',
        'avaliacao_media',
        'total_avaliacoes',
        // Campos de métricas
        'total_views',
        'views_mes_atual',
        'views_semana_atual',
        'total_contatos',
        'contatos_mes_atual',
        'contatos_whatsapp',
        'contatos_telefone',
        // Campos de gestão
        'documentos',
        'servicos_lista',
        'precos_convenios',
        // Campos de pagamento
        'valor_plano',
        'ultimo_pagamento',
        'dias_inadimplencia',
    ];

    protected $casts = [
        'horario_funcionamento' => 'array',
        'ativo' => 'boolean',
        'plano_ativo' => 'boolean',
        'plano_vencimento' => 'datetime',
        'avaliacao_media' => 'decimal:2',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        // Casts para novos campos
        'documentos' => 'array',
        'servicos_lista' => 'array',
        'precos_convenios' => 'array',
        'valor_plano' => 'decimal:2',
        'ultimo_pagamento' => 'datetime',
    ];

    // Scopes para busca
    public function scopeAtivos($query)
    {
        return $query->where('ativo', true);
    }

    public function scopeComPlanoAtivo($query)
    {
        return $query->where('plano_ativo', true)
                    ->where(function($q) {
                        $q->whereNull('plano_vencimento')
                          ->orWhere('plano_vencimento', '>', now());
                    });
    }

    public function scopePorCategoria($query, $categoria)
    {
        return $query->where('categoria', $categoria);
    }

    public function scopeProximosDe($query, $latitude, $longitude, $raioKm = 10)
    {
        // Para SQLite, usar aproximação simples baseada em graus
        // 1 grau ≈ 111 km
        $grausPorKm = 1 / 111;
        $deltaLat = $raioKm * $grausPorKm;
        $deltaLng = $raioKm * $grausPorKm;

        return $query->selectRaw("
            *,
            (
                111.045 * (
                    ABS(latitude - ?) + ABS(longitude - ?)
                )
            ) AS distancia
        ", [$latitude, $longitude])
        ->where('latitude', '>=', $latitude - $deltaLat)
        ->where('latitude', '<=', $latitude + $deltaLat)
        ->where('longitude', '>=', $longitude - $deltaLng)
        ->where('longitude', '<=', $longitude + $deltaLng)
        ->orderBy('distancia');
    }

    // Accessor para WhatsApp formatado
    public function getWhatsappLinkAttribute()
    {
        $numero = preg_replace('/[^0-9]/', '', $this->whatsapp);
        return "https://wa.me/55{$numero}";
    }

    // Accessor para endereço completo
    public function getEnderecoCompletoAttribute()
    {
        return "{$this->endereco}, {$this->cidade} - {$this->estado}, {$this->cep}";
    }

    // Relacionamentos
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Métodos auxiliares
    public function generateSlug()
    {
        $baseSlug = \Str::slug($this->nome);
        $slug = $baseSlug;
        $counter = 1;

        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    // Boot method para gerar slug automaticamente
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($estabelecimento) {
            if (empty($estabelecimento->slug)) {
                $estabelecimento->slug = $estabelecimento->generateSlug();
            }
        });

        static::updating(function ($estabelecimento) {
            if ($estabelecimento->isDirty('nome') && empty($estabelecimento->slug)) {
                $estabelecimento->slug = $estabelecimento->generateSlug();
            }
        });
    }

    // Relacionamentos
    public function imagens(): HasMany
    {
        return $this->hasMany(EstabelecimentoImagem::class)->orderBy('ordem');
    }

    public function imagemPrincipal()
    {
        return $this->hasOne(EstabelecimentoImagem::class)->where('principal', true);
    }

    // Métodos para gerenciar métricas
    public function incrementarVisualizacao()
    {
        $this->increment('total_views');
        $this->increment('views_mes_atual');
        $this->increment('views_semana_atual');
    }

    public function incrementarContato($tipo = 'whatsapp')
    {
        $this->increment('total_contatos');
        $this->increment('contatos_mes_atual');

        if ($tipo === 'whatsapp') {
            $this->increment('contatos_whatsapp');
        } elseif ($tipo === 'telefone') {
            $this->increment('contatos_telefone');
        }
    }

    public function resetarMetricasMensais()
    {
        $this->update([
            'views_mes_atual' => 0,
            'contatos_mes_atual' => 0,
        ]);
    }

    public function resetarMetricasSemanais()
    {
        $this->update([
            'views_semana_atual' => 0,
        ]);
    }

    // Verificar se o plano está ativo e não vencido
    public function planoAtivo()
    {
        if (!$this->plano_ativo) {
            return false;
        }

        if ($this->plano_vencimento && $this->plano_vencimento->isPast()) {
            return false;
        }

        return true;
    }

    // Calcular dias de inadimplência
    public function calcularDiasInadimplencia()
    {
        if (!$this->plano_vencimento) {
            return 0;
        }

        if ($this->plano_vencimento->isFuture()) {
            return 0;
        }

        return $this->plano_vencimento->diffInDays(now());
    }
}
