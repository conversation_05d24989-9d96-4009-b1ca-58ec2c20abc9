import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from '@inertiajs/react';
import { CheckCircle, Loader2, Users } from 'lucide-react';
import { useState } from 'react';

interface FormularioAfiliadoProps {
    className?: string;
}

interface AfiliadoFormData {
    nome: string;
    email: string;
    telefone: string;
    cpf: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    experiencia: string;
    motivacao: string;
    canais_divulgacao: string[];
    aceita_termos: boolean;
    [key: string]: any;
}

export default function FormularioAfiliado({ className = '' }: FormularioAfiliadoProps) {
    const [showSuccess, setShowSuccess] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm<AfiliadoFormData>({
        nome: '',
        email: '',
        telefone: '',
        cpf: '',
        endereco: '',
        cidade: '',
        estado: '',
        cep: '',
        experiencia: '',
        motivacao: '',
        canais_divulgacao: [],
        aceita_termos: false,
    });

    const canaisDisponiveis = ['Instagram', 'Facebook', 'WhatsApp', 'LinkedIn', 'TikTok', 'YouTube', 'Blog/Site', 'Indicação Pessoal', 'Outros'];

    const estados = [
        'AC',
        'AL',
        'AP',
        'AM',
        'BA',
        'CE',
        'DF',
        'ES',
        'GO',
        'MA',
        'MT',
        'MS',
        'MG',
        'PA',
        'PB',
        'PR',
        'PE',
        'PI',
        'RJ',
        'RN',
        'RS',
        'RO',
        'RR',
        'SC',
        'SP',
        'SE',
        'TO',
    ];

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        post(route('afiliados.store'), {
            onSuccess: () => {
                setShowSuccess(true);
                reset();
                setTimeout(() => setShowSuccess(false), 5000);
            },
        });
    };

    const handleCanalChange = (canal: string, checked: boolean) => {
        if (checked) {
            setData('canais_divulgacao', [...data.canais_divulgacao, canal]);
        } else {
            setData(
                'canais_divulgacao',
                data.canais_divulgacao.filter((c) => c !== canal),
            );
        }
    };

    if (showSuccess) {
        return (
            <Card className={`${className}`}>
                <CardContent className="p-8 text-center">
                    <CheckCircle className="mx-auto mb-4 h-16 w-16 text-green-500" />
                    <h3 className="mb-2 text-2xl font-semibold">Cadastro Realizado!</h3>
                    <p className="mb-4 text-muted-foreground">
                        Seu cadastro foi enviado com sucesso. Nossa equipe entrará em contato em até 24 horas para finalizar seu cadastro e enviar seu
                        link exclusivo de afiliado.
                    </p>
                    <Button onClick={() => setShowSuccess(false)}>Fazer Novo Cadastro</Button>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Cadastro de Afiliado
                </CardTitle>
                <CardDescription>Preencha os dados abaixo para se tornar um afiliado F4 Fisio</CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Dados Pessoais */}
                    <div className="space-y-4">
                        <h4 className="text-lg font-medium">Dados Pessoais</h4>

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <Label htmlFor="nome">Nome Completo *</Label>
                                <Input
                                    id="nome"
                                    value={data.nome}
                                    onChange={(e) => setData('nome', e.target.value)}
                                    className={errors.nome ? 'border-red-500' : ''}
                                    required
                                />
                                {errors.nome && <p className="mt-1 text-sm text-red-500">{errors.nome}</p>}
                            </div>

                            <div>
                                <Label htmlFor="email">E-mail *</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    className={errors.email ? 'border-red-500' : ''}
                                    required
                                />
                                {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
                            </div>
                        </div>

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <Label htmlFor="telefone">Telefone/WhatsApp *</Label>
                                <Input
                                    id="telefone"
                                    value={data.telefone}
                                    onChange={(e) => setData('telefone', e.target.value)}
                                    placeholder="(11) 99999-9999"
                                    className={errors.telefone ? 'border-red-500' : ''}
                                    required
                                />
                                {errors.telefone && <p className="mt-1 text-sm text-red-500">{errors.telefone}</p>}
                            </div>

                            <div>
                                <Label htmlFor="cpf">CPF *</Label>
                                <Input
                                    id="cpf"
                                    value={data.cpf}
                                    onChange={(e) => setData('cpf', e.target.value)}
                                    placeholder="000.000.000-00"
                                    className={errors.cpf ? 'border-red-500' : ''}
                                    required
                                />
                                {errors.cpf && <p className="mt-1 text-sm text-red-500">{errors.cpf}</p>}
                            </div>
                        </div>
                    </div>

                    {/* Endereço */}
                    <div className="space-y-4">
                        <h4 className="text-lg font-medium">Endereço</h4>

                        <div>
                            <Label htmlFor="endereco">Endereço Completo *</Label>
                            <Input
                                id="endereco"
                                value={data.endereco}
                                onChange={(e) => setData('endereco', e.target.value)}
                                className={errors.endereco ? 'border-red-500' : ''}
                                required
                            />
                            {errors.endereco && <p className="mt-1 text-sm text-red-500">{errors.endereco}</p>}
                        </div>

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div>
                                <Label htmlFor="cidade">Cidade *</Label>
                                <Input
                                    id="cidade"
                                    value={data.cidade}
                                    onChange={(e) => setData('cidade', e.target.value)}
                                    className={errors.cidade ? 'border-red-500' : ''}
                                    required
                                />
                                {errors.cidade && <p className="mt-1 text-sm text-red-500">{errors.cidade}</p>}
                            </div>

                            <div>
                                <Label htmlFor="estado">Estado *</Label>
                                <Select value={data.estado} onValueChange={(value) => setData('estado', value)}>
                                    <SelectTrigger className={errors.estado ? 'border-red-500' : ''}>
                                        <SelectValue placeholder="Selecione" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {estados.map((estado) => (
                                            <SelectItem key={estado} value={estado}>
                                                {estado}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.estado && <p className="mt-1 text-sm text-red-500">{errors.estado}</p>}
                            </div>

                            <div>
                                <Label htmlFor="cep">CEP *</Label>
                                <Input
                                    id="cep"
                                    value={data.cep}
                                    onChange={(e) => setData('cep', e.target.value)}
                                    placeholder="00000-000"
                                    className={errors.cep ? 'border-red-500' : ''}
                                    required
                                />
                                {errors.cep && <p className="mt-1 text-sm text-red-500">{errors.cep}</p>}
                            </div>
                        </div>
                    </div>

                    {/* Experiência */}
                    <div className="space-y-4">
                        <h4 className="text-lg font-medium">Experiência em Vendas</h4>

                        <div>
                            <Label htmlFor="experiencia">Experiência Anterior</Label>
                            <Select value={data.experiencia} onValueChange={(value) => setData('experiencia', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Selecione sua experiência" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="nenhuma">Nenhuma experiência</SelectItem>
                                    <SelectItem value="iniciante">Iniciante (até 1 ano)</SelectItem>
                                    <SelectItem value="intermediario">Intermediário (1-3 anos)</SelectItem>
                                    <SelectItem value="avancado">Avançado (3+ anos)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label htmlFor="motivacao">Por que quer ser nosso afiliado? *</Label>
                            <Textarea
                                id="motivacao"
                                value={data.motivacao}
                                onChange={(e) => setData('motivacao', e.target.value)}
                                placeholder="Conte-nos sua motivação..."
                                className={errors.motivacao ? 'border-red-500' : ''}
                                required
                            />
                            {errors.motivacao && <p className="mt-1 text-sm text-red-500">{errors.motivacao}</p>}
                        </div>

                        <div>
                            <Label>Canais de Divulgação que Pretende Usar *</Label>
                            <div className="mt-2 grid grid-cols-2 gap-3 md:grid-cols-3">
                                {canaisDisponiveis.map((canal) => (
                                    <div key={canal} className="flex items-center space-x-2">
                                        <Checkbox
                                            id={canal}
                                            checked={data.canais_divulgacao.includes(canal)}
                                            onCheckedChange={(checked) => handleCanalChange(canal, checked as boolean)}
                                        />
                                        <Label htmlFor={canal} className="text-sm">
                                            {canal}
                                        </Label>
                                    </div>
                                ))}
                            </div>
                            {errors.canais_divulgacao && <p className="mt-1 text-sm text-red-500">{errors.canais_divulgacao}</p>}
                        </div>
                    </div>

                    {/* Termos */}
                    <div className="space-y-4">
                        <div className="flex items-start space-x-2">
                            <Checkbox
                                id="termos"
                                checked={data.aceita_termos}
                                onCheckedChange={(checked) => setData('aceita_termos', checked as boolean)}
                                className={errors.aceita_termos ? 'border-red-500' : ''}
                            />
                            <Label htmlFor="termos" className="text-sm leading-relaxed">
                                Aceito os termos e condições do programa de afiliados e autorizo o uso dos meus dados para fins de cadastro e
                                comunicação. *
                            </Label>
                        </div>
                        {errors.aceita_termos && <p className="text-sm text-red-500">{errors.aceita_termos}</p>}
                    </div>

                    <Button type="submit" className="w-full" disabled={processing}>
                        {processing ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Enviando...
                            </>
                        ) : (
                            'Cadastrar como Afiliado'
                        )}
                    </Button>
                </form>
            </CardContent>
        </Card>
    );
}
