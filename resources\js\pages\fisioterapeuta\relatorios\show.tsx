import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, <PERSON> } from '@inertiajs/react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Activity, ArrowLeft, Calendar, Clock, Download, Edit, Eye, File, FileText, Paperclip, Target, User } from 'lucide-react';

interface Anexo {
    id: number;
    nome_original: string;
    nome_arquivo: string;
    tamanho_formatado: string;
    tipo_anexo: string;
    descricao?: string;
    url: string;
    created_at: string;
}

interface RelatorioSessao {
    id: number;
    observacoes: string;
    exercicios_realizados: string[];
    proximos_passos: string;
    created_at: string;
    updated_at: string;
    anexos: Anexo[];
    agendamento: {
        id: number;
        data_hora: string;
        status: string;
        valor: number;
        endereco: string;
        observacoes?: string;
        paciente: {
            id: number;
            name: string;
            email: string;
            phone?: string;
        };
        formatted_data_hora: string;
        formatted_valor: string;
    };
}

interface Props {
    relatorio: RelatorioSessao;
}

export default function RelatorioShow({ relatorio }: Props) {
    const handleDownload = () => {
        window.open(route('fisioterapeuta.relatorios.download', relatorio.id));
    };

    return (
        <AppLayout>
            <Head title={`Relatório da Sessão #${relatorio.agendamento.id}`} />

            <div className="py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <Link href={route('fisioterapeuta.relatorios.index')}>
                                    <Button variant="ghost" size="sm">
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        Voltar
                                    </Button>
                                </Link>
                                <div>
                                    <h2 className="text-3xl font-bold tracking-tight">Relatório da Sessão #{relatorio.agendamento.id}</h2>
                                    <p className="text-muted-foreground">Relatório detalhado da sessão com {relatorio.agendamento.paciente.name}</p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Button onClick={handleDownload} variant="outline">
                                    <Download className="mr-2 h-4 w-4" />
                                    Download PDF
                                </Button>
                                <Link href={route('fisioterapeuta.relatorios.edit', relatorio.id)}>
                                    <Button variant="outline">
                                        <Edit className="mr-2 h-4 w-4" />
                                        Editar
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </div>

                    <div className="space-y-6">
                        {/* Informações da Sessão */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Calendar className="mr-2 h-5 w-5" />
                                    Informações da Sessão
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <Calendar className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Data e Hora:</span>
                                            <span className="ml-2">{relatorio.agendamento.formatted_data_hora}</span>
                                        </div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <User className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Paciente:</span>
                                            <span className="ml-2">{relatorio.agendamento.paciente.name}</span>
                                        </div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <span className="font-medium">Valor:</span>
                                            <span className="ml-2 font-bold text-green-600">{relatorio.agendamento.formatted_valor}</span>
                                        </div>
                                    </div>

                                    <div>
                                        <div className="mb-3 text-sm text-gray-600">
                                            <span className="font-medium">Endereço:</span>
                                            <span className="ml-2">{relatorio.agendamento.endereco}</span>
                                        </div>
                                        <div className="mb-3 text-sm text-gray-600">
                                            <span className="font-medium">Status:</span>
                                            <Badge className="ml-2" variant="outline">
                                                {relatorio.agendamento.status}
                                            </Badge>
                                        </div>
                                    </div>
                                </div>

                                {relatorio.agendamento.observacoes && (
                                    <div className="mt-6 border-t pt-6">
                                        <h4 className="mb-2 font-medium text-gray-900">Observações do Agendamento:</h4>
                                        <p className="text-gray-600">{relatorio.agendamento.observacoes}</p>
                                    </div>
                                )}

                                <div className="mt-6 border-t pt-6">
                                    <div className="flex justify-center">
                                        <Link href={route('fisioterapeuta.agenda.show', relatorio.agendamento.id)}>
                                            <Button variant="outline">
                                                <Eye className="mr-2 h-4 w-4" />
                                                Ver Detalhes da Sessão
                                            </Button>
                                        </Link>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Informações do Paciente */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <User className="mr-2 h-5 w-5" />
                                    Informações do Paciente
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <User className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Nome:</span>
                                            <span className="ml-2">{relatorio.agendamento.paciente.name}</span>
                                        </div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <span className="font-medium">Email:</span>
                                            <span className="ml-2">{relatorio.agendamento.paciente.email}</span>
                                        </div>
                                        {relatorio.agendamento.paciente.phone && (
                                            <div className="mb-3 flex items-center text-sm text-gray-600">
                                                <span className="font-medium">Telefone:</span>
                                                <span className="ml-2">{relatorio.agendamento.paciente.phone}</span>
                                            </div>
                                        )}
                                    </div>

                                    <div className="flex justify-end">
                                        <Link href={route('fisioterapeuta.pacientes.show', relatorio.agendamento.paciente.id)}>
                                            <Button variant="outline">
                                                <User className="mr-2 h-4 w-4" />
                                                Ver Perfil do Paciente
                                            </Button>
                                        </Link>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Observações da Sessão */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <FileText className="mr-2 h-5 w-5" />
                                    Observações da Sessão
                                </CardTitle>
                                <CardDescription>Detalhes sobre o que foi observado durante a sessão</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="prose max-w-none">
                                    <p className="leading-relaxed whitespace-pre-wrap text-gray-700">{relatorio.observacoes}</p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Exercícios Realizados */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Activity className="mr-2 h-5 w-5" />
                                    Exercícios Realizados
                                </CardTitle>
                                <CardDescription>Lista dos exercícios executados durante a sessão</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {!relatorio.exercicios_realizados || relatorio.exercicios_realizados.length === 0 ? (
                                    <div className="py-8 text-center">
                                        <Activity className="mx-auto h-12 w-12 text-gray-400" />
                                        <h3 className="mt-4 text-lg font-medium text-gray-900">Nenhum exercício registrado</h3>
                                        <p className="mt-2 text-gray-500">Não foram registrados exercícios para esta sessão.</p>
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        {relatorio.exercicios_realizados.map((exercicio, index) => (
                                            <div key={index} className="flex items-start space-x-3 rounded-lg bg-gray-50 p-4">
                                                <div className="flex-shrink-0">
                                                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-sm font-medium text-blue-600">
                                                        {index + 1}
                                                    </div>
                                                </div>
                                                <div className="flex-1">
                                                    <p className="text-gray-700">{exercicio}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Próximos Passos */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Target className="mr-2 h-5 w-5" />
                                    Próximos Passos
                                </CardTitle>
                                <CardDescription>Recomendações e plano para as próximas sessões</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="prose max-w-none">
                                    <p className="leading-relaxed whitespace-pre-wrap text-gray-700">{relatorio.proximos_passos}</p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Anexos */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Paperclip className="mr-2 h-5 w-5" />
                                    Anexos
                                </CardTitle>
                                <CardDescription>Arquivos relacionados à sessão</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {!relatorio.anexos || relatorio.anexos.length === 0 ? (
                                    <div className="py-8 text-center">
                                        <File className="mx-auto h-12 w-12 text-gray-400" />
                                        <h3 className="mt-4 text-lg font-medium text-gray-900">Nenhum anexo</h3>
                                        <p className="mt-2 text-gray-500">Não há arquivos anexados a este relatório.</p>
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        {relatorio.anexos.map((anexo) => (
                                            <div key={anexo.id} className="flex items-center justify-between rounded-lg border p-4">
                                                <div className="flex items-center space-x-3">
                                                    <div className="flex-shrink-0">
                                                        <File className="h-8 w-8 text-gray-400" />
                                                    </div>
                                                    <div className="flex-1">
                                                        <h4 className="text-sm font-medium text-gray-900">{anexo.nome_original}</h4>
                                                        {anexo.descricao && <p className="text-sm text-gray-500">{anexo.descricao}</p>}
                                                        <div className="flex items-center space-x-4 text-xs text-gray-400">
                                                            <span>{anexo.tamanho_formatado}</span>
                                                            <span>{anexo.tipo_anexo}</span>
                                                            <span>{format(new Date(anexo.created_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <Button variant="outline" size="sm" onClick={() => window.open(anexo.url, '_blank')}>
                                                        <Download className="mr-2 h-4 w-4" />
                                                        Download
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Informações do Relatório */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Clock className="mr-2 h-5 w-5" />
                                    Informações do Relatório
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div>
                                        <div className="mb-3 text-sm text-gray-600">
                                            <span className="font-medium">Criado em:</span>
                                            <span className="ml-2">
                                                {format(new Date(relatorio.created_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                                            </span>
                                        </div>
                                        <div className="mb-3 text-sm text-gray-600">
                                            <span className="font-medium">Última atualização:</span>
                                            <span className="ml-2">
                                                {format(new Date(relatorio.updated_at), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                                            </span>
                                        </div>
                                    </div>

                                    <div>
                                        <div className="mb-3 text-sm text-gray-600">
                                            <span className="font-medium">ID do Relatório:</span>
                                            <span className="ml-2 font-mono">#{relatorio.id}</span>
                                        </div>
                                        <div className="mb-3 text-sm text-gray-600">
                                            <span className="font-medium">ID da Sessão:</span>
                                            <span className="ml-2 font-mono">#{relatorio.agendamento.id}</span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
