# Sistema de Horários Customizados para Médicos

## Visão Geral

O sistema de horários customizados permite que médicos/fisioterapeutas configurem seus horários de atendimento com múltiplos níveis de complexidade e flexibilidade total.

## Funcionalidades Implementadas

### 1. Horários Base (Configuração Semanal Padrão)
- Configure horários diferentes para cada dia da semana
- Múltiplos períodos por dia (ex: manhã, tarde, noite)
- Exemplo: Segunda a sexta 10h-18h, sábado 10h-14h, domingo 10h-12h

### 2. Exceções Temporais
- **Data Específica**: Configuração para um dia específico
- **Semana Específica**: Configuração para uma semana inteira
- **Mês Específico**: Configuração para um mês inteiro
- **Período Personalizado**: Configuração para qualquer período

### 3. Sistema de Feriados
- Configuração global para trabalhar ou não em feriados
- Tipos de feriados: Nacional, Estadual, Municipal
- Exceções específicas por feriado
- Base de dados de feriados brasileiros pré-populada

### 4. Hierarquia de Prioridades
1. **Data Específica** (maior prioridade)
2. **Feriados** (se aplicável)
3. **Exceções Temporais** (semana/mês)
4. **Configuração Base** (menor prioridade)

## Estrutura do Banco de Dados

### Tabelas Criadas

#### `horarios_base`
- Configurações padrão por dia da semana
- Permite múltiplos períodos por dia
- Campos: `fisioterapeuta_id`, `dia_semana`, `hora_inicio`, `hora_fim`, `periodo_nome`, `ativo`

#### `horarios_excecoes`
- Exceções para períodos específicos
- Campos: `fisioterapeuta_id`, `tipo`, `data_inicio`, `data_fim`, `dia_semana`, `hora_inicio`, `hora_fim`, `acao`, `motivo`, `ativo`

#### `feriados`
- Lista de feriados nacionais, estaduais e municipais
- Campos: `nome`, `data`, `tipo`, `estado`, `cidade`, `recorrente`, `descricao`, `ativo`

#### `medico_feriados_config`
- Configuração individual sobre feriados
- Campos: `fisioterapeuta_id`, `trabalha_feriados_nacionais`, `trabalha_feriados_estaduais`, `trabalha_feriados_municipais`, `feriados_excecoes`

## Modelos Eloquent

### Novos Modelos
- `HorarioBase`: Gerencia horários base semanais
- `HorarioExcecao`: Gerencia exceções temporais
- `Feriado`: Gerencia feriados
- `MedicoFeriadosConfig`: Gerencia configurações de feriados

### Relacionamentos Adicionados ao User
```php
public function horariosBase()
public function horariosExcecoes()
public function configFeriados()
```

## Service Layer

### `HorarioDisponibilidadeService`
Centraliza toda a lógica de cálculo de disponibilidade:

- `calcularHorariosDisponiveis($fisioterapeutaId, $data)`: Calcula horários disponíveis para uma data
- `verificarDisponibilidade($fisioterapeutaId, $data, $horario)`: Verifica disponibilidade específica
- `obterProximosHorarios($fisioterapeutaId, $dias)`: Obtém próximos horários disponíveis

## Controllers

### `HorarioConfigController`
Gerencia todas as operações de configuração de horários:

#### Rotas Implementadas
- `GET /fisioterapeuta/horarios` - Página principal
- `POST /fisioterapeuta/horarios/base` - Criar horário base
- `PUT /fisioterapeuta/horarios/base/{id}` - Atualizar horário base
- `DELETE /fisioterapeuta/horarios/base/{id}` - Remover horário base
- `POST /fisioterapeuta/horarios/excecoes` - Criar exceção
- `PUT /fisioterapeuta/horarios/excecoes/{id}` - Atualizar exceção
- `DELETE /fisioterapeuta/horarios/excecoes/{id}` - Remover exceção
- `PUT /fisioterapeuta/horarios/feriados` - Configurar feriados
- `POST /fisioterapeuta/horarios/preview` - Gerar preview
- `GET /fisioterapeuta/horarios/feriados` - Listar feriados

## Interface React

### Componentes Criados

#### `horarios/index.tsx`
- Página principal com tabs para diferentes configurações
- Cards de resumo com estatísticas
- Navegação entre configurações

#### `horario-base-config.tsx`
- Grid visual dos dias da semana
- Formulário para adicionar/editar horários base
- Validação de conflitos

#### `excecoes-config.tsx`
- Lista de exceções com filtros
- Formulário para criar exceções complexas
- Badges coloridos por tipo e ação

#### `feriados-config.tsx`
- Configurações gerais de feriados
- Lista de feriados com exceções específicas
- Filtros por ano e tipo

#### `preview-calendario.tsx`
- Visualização de disponibilidade por período
- Estatísticas de disponibilidade
- Preview detalhado por dia

## Integração com Sistema Existente

### AgendamentoController Atualizado
- Integrado com `HorarioDisponibilidadeService`
- Mantém compatibilidade com sistema anterior
- Métodos atualizados:
  - `horariosDisponiveis()`
  - `proximosHorariosDisponiveis()`
  - `verificarDisponibilidade()`

## Exemplos de Uso

### Configuração Básica
```php
// Horário base: Segunda a sexta 8h-12h e 14h-18h
HorarioBase::create([
    'fisioterapeuta_id' => 1,
    'dia_semana' => 1, // Segunda
    'hora_inicio' => '08:00',
    'hora_fim' => '12:00',
    'periodo_nome' => 'Manhã'
]);
```

### Exceção Temporal
```php
// Indisponível em data específica
HorarioExcecao::create([
    'fisioterapeuta_id' => 1,
    'tipo' => 'data_especifica',
    'data_inicio' => '2025-02-15',
    'acao' => 'indisponivel',
    'motivo' => 'Consulta médica'
]);
```

### Configuração de Feriados
```php
// Não trabalha em feriados nacionais, mas trabalha em municipais
MedicoFeriadosConfig::create([
    'fisioterapeuta_id' => 1,
    'trabalha_feriados_nacionais' => false,
    'trabalha_feriados_municipais' => true
]);
```

## Seeders

### `FeriadosSeeder`
- Popula feriados brasileiros de 2025
- Inclui feriados nacionais, estaduais (SP) e municipais (São Paulo)
- Feriados móveis calculados

### `HorariosExemploSeeder`
- Cria configuração de exemplo para demonstração
- Horários realistas de fisioterapeuta
- Exceções de exemplo

## Como Usar

1. **Acesse a configuração**: `/fisioterapeuta/horarios`
2. **Configure horários base**: Tab "Horários Base"
3. **Adicione exceções**: Tab "Exceções"
4. **Configure feriados**: Tab "Feriados"
5. **Visualize resultado**: Tab "Preview"

## Benefícios

- ✅ **Flexibilidade Total**: Configure qualquer padrão de horários
- ✅ **Hierarquia Clara**: Sistema de prioridades bem definido
- ✅ **Interface Intuitiva**: Componentes React responsivos
- ✅ **Compatibilidade**: Integra com sistema existente
- ✅ **Performance**: Índices otimizados no banco
- ✅ **Validação**: Prevenção de conflitos automática
- ✅ **Feriados**: Sistema completo de feriados brasileiros

## Próximos Passos

- [ ] Implementar notificações de mudanças
- [ ] Adicionar templates de horários
- [ ] Integrar com calendário externo
- [ ] Relatórios de disponibilidade
- [ ] API para aplicativos móveis
