<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileUploadService
{
    private const ALLOWED_IMAGES = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    private const ALLOWED_DOCUMENTS = ['pdf', 'doc', 'docx', 'txt'];
    private const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB

    /**
     * Upload de foto de perfil
     */
    public function uploadProfilePhoto(UploadedFile $file, int $userId): array
    {
        $this->validateImage($file);

        $filename = $this->generateFilename($file, 'profile');
        $path = "profiles/{$userId}";
        
        // Redimensionar e otimizar imagem
        $image = Image::make($file)
            ->fit(300, 300)
            ->encode('jpg', 85);

        $fullPath = "{$path}/{$filename}";
        Storage::disk('public')->put($fullPath, $image->stream());

        return [
            'path' => $fullPath,
            'url' => Storage::disk('public')->url($fullPath),
            'filename' => $filename,
            'size' => $image->filesize(),
            'type' => 'image'
        ];
    }

    /**
     * Upload de documento
     */
    public function uploadDocument(UploadedFile $file, string $type, int $userId): array
    {
        $this->validateDocument($file);

        $filename = $this->generateFilename($file, $type);
        $path = "documents/{$userId}/{$type}";
        
        $fullPath = $file->storeAs($path, $filename, 'public');

        return [
            'path' => $fullPath,
            'url' => Storage::disk('public')->url($fullPath),
            'filename' => $filename,
            'size' => $file->getSize(),
            'type' => 'document',
            'mime_type' => $file->getMimeType(),
            'original_name' => $file->getClientOriginalName()
        ];
    }

    /**
     * Upload de comprovante de pagamento
     */
    public function uploadPaymentProof(UploadedFile $file, int $agendamentoId): array
    {
        $this->validateImage($file);

        $filename = $this->generateFilename($file, 'payment');
        $path = "payments/{$agendamentoId}";
        
        // Redimensionar se necessário
        $image = Image::make($file);
        if ($image->width() > 1200) {
            $image->resize(1200, null, function ($constraint) {
                $constraint->aspectRatio();
            });
        }

        $fullPath = "{$path}/{$filename}";
        Storage::disk('public')->put($fullPath, $image->encode('jpg', 90)->stream());

        return [
            'path' => $fullPath,
            'url' => Storage::disk('public')->url($fullPath),
            'filename' => $filename,
            'size' => $image->filesize(),
            'type' => 'payment_proof'
        ];
    }

    /**
     * Deletar arquivo
     */
    public function deleteFile(string $path): bool
    {
        if (Storage::disk('public')->exists($path)) {
            return Storage::disk('public')->delete($path);
        }
        return false;
    }

    /**
     * Validar imagem
     */
    private function validateImage(UploadedFile $file): void
    {
        if ($file->getSize() > self::MAX_IMAGE_SIZE) {
            throw new \InvalidArgumentException('Imagem muito grande. Tamanho máximo: 5MB');
        }

        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, self::ALLOWED_IMAGES)) {
            throw new \InvalidArgumentException('Formato de imagem não permitido. Use: ' . implode(', ', self::ALLOWED_IMAGES));
        }

        if (!$file->isValid()) {
            throw new \InvalidArgumentException('Arquivo inválido');
        }
    }

    /**
     * Validar documento
     */
    private function validateDocument(UploadedFile $file): void
    {
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            throw new \InvalidArgumentException('Arquivo muito grande. Tamanho máximo: 10MB');
        }

        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, self::ALLOWED_DOCUMENTS)) {
            throw new \InvalidArgumentException('Formato de documento não permitido. Use: ' . implode(', ', self::ALLOWED_DOCUMENTS));
        }

        if (!$file->isValid()) {
            throw new \InvalidArgumentException('Arquivo inválido');
        }
    }

    /**
     * Gerar nome único para arquivo
     */
    private function generateFilename(UploadedFile $file, string $prefix): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        
        return "{$prefix}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Obter informações do arquivo
     */
    public function getFileInfo(string $path): ?array
    {
        if (!Storage::disk('public')->exists($path)) {
            return null;
        }

        return [
            'path' => $path,
            'url' => Storage::disk('public')->url($path),
            'size' => Storage::disk('public')->size($path),
            'last_modified' => Storage::disk('public')->lastModified($path),
            'exists' => true
        ];
    }

    /**
     * Listar arquivos de um diretório
     */
    public function listFiles(string $directory): array
    {
        $files = Storage::disk('public')->files($directory);
        
        return array_map(function ($file) {
            return [
                'path' => $file,
                'url' => Storage::disk('public')->url($file),
                'size' => Storage::disk('public')->size($file),
                'name' => basename($file),
                'last_modified' => Storage::disk('public')->lastModified($file)
            ];
        }, $files);
    }
}
