import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, Mail, Phone, User, Calendar, MapPin, Heart } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import { safeRoute } from '@/utils/route-helper';

interface User {
    id: number;
    name: string;
    email: string;
    role: string;
    phone?: string;
    birth_date?: string;
    gender?: string;
    address?: string;
    medical_history?: string;
    emergency_contact?: string;
    active: boolean;
    created_at: string;
    assinaturas?: Array<{
        id: number;
        status: string;
        start_date: string;
        end_date?: string;
        plano: {
            name: string;
            price: number;
        };
    }>;
    agendamentos?: Array<{
        id: number;
        data_hora: string;
        status: string;
        fisioterapeuta?: {
            user: {
                name: string;
            };
        };
    }>;
    avaliacoes?: Array<{
        id: number;
        rating: number;
        comment?: string;
        created_at: string;
    }>;
}

interface Props {
    usuario: User;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Usuários',
        href: '/admin/usuarios',
    },
    {
        title: 'Detalhes do Usuário',
        href: '#',
    },
];

export default function UsuarioShow({ usuario }: Props) {
    const getRoleBadge = (role: string) => {
        const colors = {
            admin: 'bg-red-100 text-red-800',
            fisioterapeuta: 'bg-blue-100 text-blue-800',
            paciente: 'bg-green-100 text-green-800',
        };
        
        const labels = {
            admin: 'Administrador',
            fisioterapeuta: 'Fisioterapeuta',
            paciente: 'Paciente',
        };

        return (
            <Badge className={colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
                {labels[role as keyof typeof labels] || role}
            </Badge>
        );
    };

    const getStatusBadge = (active: boolean) => {
        return active ? (
            <Badge variant="default" className="bg-green-100 text-green-800">
                Ativo
            </Badge>
        ) : (
            <Badge variant="secondary" className="bg-red-100 text-red-800">
                Inativo
            </Badge>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${usuario.name} - Usuário`} />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{usuario.name}</h1>
                        <div className="flex items-center gap-2 mt-2">
                            {getRoleBadge(usuario.role)}
                            {getStatusBadge(usuario.active)}
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Link href={safeRoute('admin.usuarios.edit', usuario.id)} preserveState>
                            <Button>
                                <Edit className="mr-2 h-4 w-4" />
                                Editar
                            </Button>
                        </Link>
                        <Link href={safeRoute('admin.usuarios.index')} preserveState>
                            <Button variant="ghost">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Informações Principais */}
                    <div className="lg:col-span-2 space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Informações Pessoais</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="flex items-center space-x-2">
                                        <User className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Nome:</span>
                                        <span>{usuario.name}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Mail className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Email:</span>
                                        <span>{usuario.email}</span>
                                    </div>
                                    {usuario.phone && (
                                        <div className="flex items-center space-x-2">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-medium">Telefone:</span>
                                            <span>{usuario.phone}</span>
                                        </div>
                                    )}
                                    {usuario.birth_date && (
                                        <div className="flex items-center space-x-2">
                                            <Calendar className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-medium">Nascimento:</span>
                                            <span>{new Date(usuario.birth_date).toLocaleDateString('pt-BR')}</span>
                                        </div>
                                    )}
                                    {usuario.gender && (
                                        <div className="flex items-center space-x-2">
                                            <User className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-medium">Gênero:</span>
                                            <span className="capitalize">{usuario.gender}</span>
                                        </div>
                                    )}
                                    <div className="flex items-center space-x-2">
                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Cadastrado em:</span>
                                        <span>{new Date(usuario.created_at).toLocaleDateString('pt-BR')}</span>
                                    </div>
                                </div>

                                {usuario.address && (
                                    <div className="flex items-start space-x-2">
                                        <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                                        <div>
                                            <span className="font-medium">Endereço:</span>
                                            <p className="text-muted-foreground">{usuario.address}</p>
                                        </div>
                                    </div>
                                )}

                                {usuario.emergency_contact && (
                                    <div className="flex items-start space-x-2">
                                        <Heart className="h-4 w-4 text-muted-foreground mt-1" />
                                        <div>
                                            <span className="font-medium">Contato de Emergência:</span>
                                            <p className="text-muted-foreground">{usuario.emergency_contact}</p>
                                        </div>
                                    </div>
                                )}

                                {usuario.medical_history && (
                                    <div className="space-y-2">
                                        <span className="font-medium">Histórico Médico:</span>
                                        <p className="text-muted-foreground bg-gray-50 p-3 rounded-md">
                                            {usuario.medical_history}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {usuario.assinaturas && usuario.assinaturas.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Assinaturas</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {usuario.assinaturas.map((assinatura) => (
                                            <div key={assinatura.id} className="flex justify-between items-center p-3 border rounded-lg">
                                                <div>
                                                    <p className="font-medium">{assinatura.plano.name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        Início: {new Date(assinatura.start_date).toLocaleDateString('pt-BR')}
                                                        {assinatura.end_date && ` - Fim: ${new Date(assinatura.end_date).toLocaleDateString('pt-BR')}`}
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <Badge variant={assinatura.status === 'ativa' ? 'default' : 'secondary'}>
                                                        {assinatura.status}
                                                    </Badge>
                                                    <p className="text-sm font-medium text-green-600">
                                                        {new Intl.NumberFormat('pt-BR', {
                                                            style: 'currency',
                                                            currency: 'BRL',
                                                        }).format(assinatura.plano.price)}
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Estatísticas */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Estatísticas</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-primary">
                                        {usuario.agendamentos?.length || 0}
                                    </div>
                                    <p className="text-sm text-muted-foreground">Agendamentos</p>
                                </div>
                                
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-blue-600">
                                        {usuario.assinaturas?.length || 0}
                                    </div>
                                    <p className="text-sm text-muted-foreground">Assinaturas</p>
                                </div>

                                <div className="text-center">
                                    <div className="text-2xl font-bold text-yellow-600">
                                        {usuario.avaliacoes?.length || 0}
                                    </div>
                                    <p className="text-sm text-muted-foreground">Avaliações</p>
                                </div>
                            </CardContent>
                        </Card>

                        {usuario.agendamentos && usuario.agendamentos.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Últimos Agendamentos</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {usuario.agendamentos.slice(0, 5).map((agendamento) => (
                                            <div key={agendamento.id} className="flex justify-between items-center text-sm">
                                                <div>
                                                    <p className="font-medium">
                                                        {agendamento.fisioterapeuta?.user.name || 'Fisioterapeuta'}
                                                    </p>
                                                    <p className="text-muted-foreground">
                                                        {new Date(agendamento.data_hora).toLocaleDateString('pt-BR')}
                                                    </p>
                                                </div>
                                                <Badge variant="outline">
                                                    {agendamento.status}
                                                </Badge>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
