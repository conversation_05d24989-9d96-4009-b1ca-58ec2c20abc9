<?php

namespace App\Http\Controllers\Afiliado;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\VendaAfiliado;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the affiliate dashboard.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;
        
        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }
        
        // Estatísticas do afiliado
        $stats = [
            'vendasMes' => $afiliado->vendas_mes_atual,
            'comissoesMes' => $afiliado->comissoes_mes_atual,
            'totalVendas' => $afiliado->total_vendas,
            'totalComissoes' => $afiliado->total_comissoes,
        ];
        
        // Vendas recentes
        $vendasRecentes = VendaAfiliado::where('afiliado_id', $afiliado->id)
            ->with('cliente')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        
        // Gráfico de vendas (últimos 6 meses)
        $vendasPorMes = $this->getVendasPorMes($afiliado->id);
        
        return Inertia::render('afiliado/dashboard', [
            'afiliado' => $afiliado,
            'stats' => $stats,
            'vendas' => $vendasRecentes,
            'vendasPorMes' => $vendasPorMes,
        ]);
    }
    
    /**
     * Get sales data for the last 6 months
     */
    private function getVendasPorMes($afiliadoId)
    {
        $meses = collect();
        
        // Últimos 6 meses
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $mes = $date->format('M');
            $ano = $date->format('Y');
            
            $vendas = VendaAfiliado::where('afiliado_id', $afiliadoId)
                ->whereYear('created_at', $ano)
                ->whereMonth('created_at', $date->month)
                ->count();
                
            $comissoes = VendaAfiliado::where('afiliado_id', $afiliadoId)
                ->whereYear('created_at', $ano)
                ->whereMonth('created_at', $date->month)
                ->sum('comissao');
                
            $meses->push([
                'mes' => $mes,
                'vendas' => $vendas,
                'comissoes' => $comissoes,
            ]);
        }
        
        return $meses;
    }
}
