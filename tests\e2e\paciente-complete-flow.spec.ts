import { expect, test } from '@playwright/test';

test.describe('Fluxo Completo do Paciente', () => {
    let patientEmail: string;
    let patientPassword = 'password123';

    test.beforeAll(async () => {
        // Gerar email único para os testes
        const timestamp = Date.now();
        patientEmail = `paciente.completo.${timestamp}@example.com`;
    });

    test.describe('1. Registro e Autenticação', () => {
        test('deve registrar novo paciente com sucesso', async ({ page }) => {
            await page.goto('/register');

            // Verificar se a página de registro carrega
            await expect(page).toHaveTitle(/Registro|Register/);
            await expect(page.locator('h1, h2')).toContainText(/registr|criar conta/i);

            // Preencher formulário de registro
            await page.fill('input[name="name"]', 'Paciente Teste Completo');
            await page.fill('input[name="email"]', patientEmail);
            await page.fill('input[name="password"]', patientPassword);
            await page.fill('input[name="password_confirmation"]', patientPassword);
            await page.selectOption('select[name="role"]', 'paciente');

            // Submeter formulário
            await page.click('button[type="submit"]');

            // Deve redirecionar para seleção de planos
            await expect(page).toHaveURL(/.*paciente\/planos/);
            await expect(page.locator('h1, h2')).toContainText(/plano/i);
        });

        test('deve fazer login com paciente registrado', async ({ page }) => {
            await page.goto('/login');

            await page.fill('input[type="email"]', patientEmail);
            await page.fill('input[type="password"]', patientPassword);
            await page.click('button[type="submit"]');

            // Deve redirecionar para planos (paciente sem dados completos)
            await expect(page).toHaveURL(/.*paciente\/planos/);
        });
    });

    test.describe('2. Seleção de Planos', () => {
        test.beforeEach(async ({ page }) => {
            // Login antes de cada teste
            await page.goto('/login');
            await page.fill('input[type="email"]', patientEmail);
            await page.fill('input[type="password"]', patientPassword);
            await page.click('button[type="submit"]');
            await page.waitForURL(/.*paciente\/planos/);
        });

        test('deve exibir opções de planos disponíveis', async ({ page }) => {
            // Verificar se os planos são exibidos
            await expect(page.locator('h1, h2')).toContainText(/plano/i);
            
            // Verificar se há pelo menos uma opção de plano
            const planoCards = page.locator('[data-testid="plano-card"], .plano-card, .card');
            await expect(planoCards.first()).toBeVisible();

            // Verificar se há botões de seleção
            const selectButtons = page.locator('button:has-text("Escolher"), button:has-text("Selecionar"), button:has-text("Assinar")');
            if (await selectButtons.count() > 0) {
                await expect(selectButtons.first()).toBeVisible();
            }
        });

        test('deve selecionar plano avulso', async ({ page }) => {
            // Procurar por plano avulso
            const planoAvulso = page.locator('text=Sessão Avulsa, text=Avulsa').first();
            if (await planoAvulso.isVisible()) {
                const selectButton = page.locator('button:has-text("Escolher"), button:has-text("Selecionar")').first();
                if (await selectButton.isVisible()) {
                    await selectButton.click();
                    
                    // Verificar se foi selecionado com sucesso
                    await expect(page.locator('text=sucesso, text=selecionado')).toBeVisible({ timeout: 5000 });
                }
            }
        });
    });

    test.describe('3. Onboarding e Perfil', () => {
        test.beforeEach(async ({ page }) => {
            await page.goto('/login');
            await page.fill('input[type="email"]', patientEmail);
            await page.fill('input[type="password"]', patientPassword);
            await page.click('button[type="submit"]');
            await page.waitForURL(/.*paciente/);
        });

        test('deve completar onboarding médico', async ({ page }) => {
            await page.goto('/paciente/onboarding');
            
            // Verificar se a página de onboarding carrega
            await expect(page.locator('h1, h2')).toContainText(/onboarding|dados médicos|informações médicas/i);

            // Preencher dados médicos básicos (se houver formulário)
            const medicalHistoryField = page.locator('textarea[name="medical_history"], input[name="medical_history"]');
            if (await medicalHistoryField.isVisible()) {
                await medicalHistoryField.fill('Histórico médico de teste para automação');
            }

            const emergencyContactField = page.locator('input[name="emergency_contact"]');
            if (await emergencyContactField.isVisible()) {
                await emergencyContactField.fill('(11) 99999-9999');
            }

            // Submeter se houver botão
            const submitButton = page.locator('button[type="submit"], button:has-text("Salvar"), button:has-text("Continuar")');
            if (await submitButton.isVisible()) {
                await submitButton.click();
            }
        });

        test('deve editar perfil do paciente', async ({ page }) => {
            await page.goto('/paciente/perfil');
            
            // Verificar se a página de perfil carrega
            await expect(page.locator('h1, h2')).toContainText(/perfil/i);

            // Verificar campos do formulário
            const nameField = page.locator('input[name="name"]');
            if (await nameField.isVisible()) {
                await expect(nameField).toHaveValue(/Paciente Teste/);
                
                // Editar nome
                await nameField.clear();
                await nameField.fill('Paciente Teste Editado');
            }

            // Preencher telefone
            const phoneField = page.locator('input[name="phone"]');
            if (await phoneField.isVisible()) {
                await phoneField.fill('(11) 98765-4321');
            }

            // Preencher data de nascimento
            const birthDateField = page.locator('input[name="birth_date"], input[type="date"]');
            if (await birthDateField.isVisible()) {
                await birthDateField.fill('1990-01-01');
            }

            // Preencher endereço
            const addressField = page.locator('input[name="address"]');
            if (await addressField.isVisible()) {
                await addressField.fill('Rua Teste, 123');
            }

            // Salvar alterações
            const saveButton = page.locator('button[type="submit"], button:has-text("Salvar")');
            if (await saveButton.isVisible()) {
                await saveButton.click();
                
                // Verificar mensagem de sucesso
                await expect(page.locator('text=sucesso, text=atualizado')).toBeVisible({ timeout: 5000 });
            }
        });
    });

    test.describe('4. Navegação e Dashboard', () => {
        test.beforeEach(async ({ page }) => {
            await page.goto('/login');
            await page.fill('input[type="email"]', patientEmail);
            await page.fill('input[type="password"]', patientPassword);
            await page.click('button[type="submit"]');
            await page.waitForURL(/.*paciente/);
        });

        test('deve acessar dashboard do paciente', async ({ page }) => {
            await page.goto('/paciente/dashboard');
            
            // Verificar se o dashboard carrega
            await expect(page.locator('h1, h2')).toContainText(/dashboard|início/i);
            
            // Verificar elementos do dashboard
            await expect(page.locator('text=Bom dia, text=Boa tarde, text=Boa noite')).toBeVisible();
            
            // Verificar se há cards de estatísticas
            const statsCards = page.locator('.card, [data-testid="stat-card"]');
            if (await statsCards.count() > 0) {
                await expect(statsCards.first()).toBeVisible();
            }
        });

        test('deve navegar pela sidebar do paciente', async ({ page }) => {
            await page.goto('/paciente/dashboard');
            
            // Verificar se a sidebar está presente
            const sidebar = page.locator('aside, nav, [data-testid="sidebar"]');
            await expect(sidebar).toBeVisible();

            // Testar navegação para diferentes páginas
            const navItems = [
                { text: 'Perfil', url: '/paciente/perfil' },
                { text: 'Agendamentos', url: '/paciente/agendamentos' },
                { text: 'Histórico', url: '/paciente/historico' },
                { text: 'Pagamentos', url: '/paciente/pagamentos' },
                { text: 'Planos', url: '/paciente/planos' },
                { text: 'Avaliações', url: '/paciente/avaliacoes' }
            ];

            for (const item of navItems) {
                const navLink = page.locator(`a:has-text("${item.text}"), button:has-text("${item.text}")`);
                if (await navLink.isVisible()) {
                    await navLink.click();
                    await expect(page).toHaveURL(new RegExp(item.url.replace('/', '\\/')));
                    
                    // Verificar se a página carrega
                    await expect(page.locator('h1, h2')).toContainText(new RegExp(item.text, 'i'));
                }
            }
        });
    });

    test.describe('5. Funcionalidades Específicas', () => {
        test.beforeEach(async ({ page }) => {
            await page.goto('/login');
            await page.fill('input[type="email"]', patientEmail);
            await page.fill('input[type="password"]', patientPassword);
            await page.click('button[type="submit"]');
            await page.waitForURL(/.*paciente/);
        });

        test('deve acessar página de agendamentos', async ({ page }) => {
            await page.goto('/paciente/agendamentos');
            
            // Verificar se a página carrega
            await expect(page.locator('h1, h2')).toContainText(/agendamento/i);
            
            // Verificar se há botão para novo agendamento
            const newAppointmentButton = page.locator('button:has-text("Novo"), button:has-text("Agendar"), a:has-text("Novo"), a:has-text("Agendar")');
            if (await newAppointmentButton.isVisible()) {
                await expect(newAppointmentButton).toBeVisible();
            }
            
            // Verificar se há filtros
            const filters = page.locator('select, input[type="search"]');
            if (await filters.count() > 0) {
                await expect(filters.first()).toBeVisible();
            }
        });

        test('deve acessar página de histórico', async ({ page }) => {
            await page.goto('/paciente/historico');
            
            // Verificar se a página carrega
            await expect(page.locator('h1, h2')).toContainText(/histórico/i);
            
            // Verificar se há filtros de período
            const periodFilter = page.locator('select[name="periodo"], select:has(option:has-text("mês"))');
            if (await periodFilter.isVisible()) {
                await expect(periodFilter).toBeVisible();
            }
        });

        test('deve acessar página de pagamentos', async ({ page }) => {
            await page.goto('/paciente/pagamentos');
            
            // Verificar se a página carrega
            await expect(page.locator('h1, h2')).toContainText(/pagamento/i);
            
            // Verificar se há informações de pagamento
            const paymentInfo = page.locator('.card, [data-testid="payment-card"]');
            if (await paymentInfo.count() > 0) {
                await expect(paymentInfo.first()).toBeVisible();
            }
        });

        test('deve acessar página de avaliações', async ({ page }) => {
            await page.goto('/paciente/avaliacoes');
            
            // Verificar se a página carrega
            await expect(page.locator('h1, h2')).toContainText(/avaliação|avaliações/i);
        });
    });

    test.describe('6. Responsividade', () => {
        test('deve funcionar em dispositivos móveis', async ({ page }) => {
            // Definir viewport mobile
            await page.setViewportSize({ width: 375, height: 667 });
            
            // Login
            await page.goto('/login');
            await page.fill('input[type="email"]', patientEmail);
            await page.fill('input[type="password"]', patientPassword);
            await page.click('button[type="submit"]');
            
            // Verificar dashboard em mobile
            await page.goto('/paciente/dashboard');
            await expect(page.locator('h1, h2')).toBeVisible();
            
            // Verificar se o menu mobile funciona
            const menuButton = page.locator('button[aria-label*="menu"], button:has-text("☰"), [data-testid="mobile-menu"]');
            if (await menuButton.isVisible()) {
                await menuButton.click();
                await expect(page.locator('nav, aside')).toBeVisible();
            }
        });
    });
});
