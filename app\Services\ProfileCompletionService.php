<?php

namespace App\Services;

use App\Models\User;

class ProfileCompletionService
{
    /**
     * Campos obrigatórios por role
     */
    private const REQUIRED_FIELDS = [
        'admin' => [
            'basic' => ['name', 'email'],
            'specific' => [],
        ],
        'fisioterapeuta' => [
            'basic' => ['name', 'email', 'phone'],
            'specific' => [
                'fisioterapeuta.crefito',
                'fisioterapeuta.specializations',
                'fisioterapeuta.bio',
                'fisioterapeuta.hourly_rate',
                'fisioterapeuta.available_areas',
            ],
        ],
        'paciente' => [
            'basic' => ['name', 'email', 'phone'],
            'specific' => [
                'birth_date',
                'gender',
                'address',
                'city',
                'state',
                'zip_code',
                'medical_info', // medical_history OU emergency_contact
                'has_subscription',
            ],
        ],
        'empresa' => [
            'basic' => ['name', 'email', 'phone'],
            'specific' => [
                'estabelecimento.nome',
                'estabelecimento.endereco',
                'estabelecimento.telefone',
            ],
        ],
        'afiliado' => [
            'basic' => ['name', 'email', 'phone'],
            'specific' => [
                'afiliado.nome',
                'afiliado.cpf',
                'afiliado.telefone',
                'afiliado.endereco',
                'afiliado.status',
            ],
        ],
    ];

    /**
     * Verificar se o perfil está completo
     */
    public function isProfileComplete(User $user): bool
    {
        $missingFields = $this->getMissingFields($user);
        return empty($missingFields);
    }

    /**
     * Calcular percentual de completude do perfil
     */
    public function getCompletionPercentage(User $user): int
    {
        $requiredFields = $this->getRequiredFields($user->role);
        $totalFields = count($requiredFields);
        
        if ($totalFields === 0) {
            return 100;
        }

        $missingFields = $this->getMissingFields($user);
        $completedFields = $totalFields - count($missingFields);
        
        return (int) round(($completedFields / $totalFields) * 100);
    }

    /**
     * Obter campos faltantes
     */
    public function getMissingFields(User $user): array
    {
        $requiredFields = $this->getRequiredFields($user->role);
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (!$this->isFieldComplete($user, $field)) {
                $missingFields[] = $field;
            }
        }

        return $missingFields;
    }

    /**
     * Obter campos obrigatórios para um role
     */
    public function getRequiredFields(string $role): array
    {
        $config = self::REQUIRED_FIELDS[$role] ?? self::REQUIRED_FIELDS['admin'];
        return array_merge($config['basic'], $config['specific']);
    }

    /**
     * Obter status detalhado do perfil
     */
    public function getCompletionStatus(User $user): array
    {
        $missingFields = $this->getMissingFields($user);
        $percentage = $this->getCompletionPercentage($user);
        
        return [
            'is_complete' => empty($missingFields),
            'percentage' => $percentage,
            'missing_fields' => $missingFields,
            'missing_count' => count($missingFields),
            'status' => $this->getStatusLevel($percentage),
            'next_steps' => $this->getNextSteps($user, $missingFields),
        ];
    }

    /**
     * Verificar se um campo específico está completo
     */
    private function isFieldComplete(User $user, string $field): bool
    {
        switch ($field) {
            // Campos básicos do usuário
            case 'name':
            case 'email':
            case 'phone':
            case 'birth_date':
            case 'gender':
            case 'address':
            case 'city':
            case 'state':
            case 'zip_code':
                return !empty($user->$field);

            // Verificação especial para informações médicas
            case 'medical_info':
                return $this->hasMedicalInfo($user);

            // Verificação de assinatura
            case 'has_subscription':
                return (bool) $user->has_subscription;

            // Campos do fisioterapeuta
            case 'fisioterapeuta.crefito':
                return $user->fisioterapeuta && !empty($user->fisioterapeuta->crefito);
            
            case 'fisioterapeuta.specializations':
                return $user->fisioterapeuta && 
                       is_array($user->fisioterapeuta->specializations) && 
                       count($user->fisioterapeuta->specializations) > 0;
            
            case 'fisioterapeuta.bio':
                return $user->fisioterapeuta && 
                       !empty($user->fisioterapeuta->bio) && 
                       strlen($user->fisioterapeuta->bio) >= 50;
            
            case 'fisioterapeuta.hourly_rate':
                return $user->fisioterapeuta && $user->fisioterapeuta->hourly_rate > 0;
            
            case 'fisioterapeuta.available_areas':
                return $user->fisioterapeuta && 
                       is_array($user->fisioterapeuta->available_areas) && 
                       count($user->fisioterapeuta->available_areas) > 0;

            // Campos do estabelecimento (empresa)
            case 'estabelecimento.nome':
                return $user->estabelecimento && !empty($user->estabelecimento->nome);
            
            case 'estabelecimento.endereco':
                return $user->estabelecimento && !empty($user->estabelecimento->endereco);
            
            case 'estabelecimento.telefone':
                return $user->estabelecimento && !empty($user->estabelecimento->telefone);

            // Campos do afiliado
            case 'afiliado.nome':
                return $user->afiliado && !empty($user->afiliado->nome);
            
            case 'afiliado.cpf':
                return $user->afiliado && !empty($user->afiliado->cpf);
            
            case 'afiliado.telefone':
                return $user->afiliado && !empty($user->afiliado->telefone);
            
            case 'afiliado.endereco':
                return $user->afiliado && !empty($user->afiliado->endereco);
            
            case 'afiliado.status':
                return $user->afiliado && $user->afiliado->status === 'aprovado';

            default:
                return true;
        }
    }

    /**
     * Verificar se tem informações médicas
     */
    private function hasMedicalInfo(User $user): bool
    {
        $hasHistory = !empty($user->medical_history) && strlen($user->medical_history) >= 20;
        $hasEmergency = !empty($user->emergency_contact) && strlen($user->emergency_contact) >= 20;
        
        return $hasHistory || $hasEmergency;
    }

    /**
     * Obter nível de status baseado na porcentagem
     */
    private function getStatusLevel(int $percentage): string
    {
        if ($percentage >= 100) return 'complete';
        if ($percentage >= 80) return 'good';
        if ($percentage >= 60) return 'fair';
        if ($percentage >= 40) return 'poor';
        return 'incomplete';
    }

    /**
     * Obter próximos passos baseado nos campos faltantes
     */
    private function getNextSteps(User $user, array $missingFields): array
    {
        $steps = [];
        
        foreach ($missingFields as $field) {
            $steps[] = $this->getFieldDescription($field, $user->role);
        }

        return array_filter($steps);
    }

    /**
     * Obter descrição amigável do campo
     */
    private function getFieldDescription(string $field, string $role): string
    {
        $descriptions = [
            'name' => 'Complete seu nome completo',
            'email' => 'Confirme seu email',
            'phone' => 'Adicione seu telefone',
            'birth_date' => 'Informe sua data de nascimento',
            'gender' => 'Selecione seu gênero',
            'address' => 'Complete seu endereço',
            'city' => 'Informe sua cidade',
            'state' => 'Selecione seu estado',
            'zip_code' => 'Adicione seu CEP',
            'medical_info' => 'Complete suas informações médicas',
            'has_subscription' => 'Escolha um plano de assinatura',
            'fisioterapeuta.crefito' => 'Informe seu número do CREFITO',
            'fisioterapeuta.specializations' => 'Selecione suas especializações',
            'fisioterapeuta.bio' => 'Escreva sua biografia profissional',
            'fisioterapeuta.hourly_rate' => 'Defina seu valor por hora',
            'fisioterapeuta.available_areas' => 'Selecione suas áreas de atendimento',
            'estabelecimento.nome' => 'Informe o nome do estabelecimento',
            'estabelecimento.endereco' => 'Complete o endereço do estabelecimento',
            'estabelecimento.telefone' => 'Adicione o telefone do estabelecimento',
            'afiliado.nome' => 'Complete seu nome como afiliado',
            'afiliado.cpf' => 'Informe seu CPF',
            'afiliado.telefone' => 'Adicione seu telefone',
            'afiliado.endereco' => 'Complete seu endereço',
            'afiliado.status' => 'Aguarde aprovação como afiliado',
        ];

        return $descriptions[$field] ?? "Complete o campo: {$field}";
    }

    /**
     * Obter URL de redirecionamento para completar perfil
     */
    public function getCompletionUrl(User $user): string
    {
        switch ($user->role) {
            case 'fisioterapeuta':
                return route('fisioterapeuta.perfil');
            case 'paciente':
                return route('paciente.perfil');
            case 'empresa':
                return route('empresa.perfil');
            case 'afiliado':
                return route('afiliado.perfil');
            default:
                return route('profile.edit');
        }
    }
}
