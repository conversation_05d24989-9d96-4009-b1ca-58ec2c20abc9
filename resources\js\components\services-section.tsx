import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from '@inertiajs/react';
import { ArrowRight, Building2, Home, Search } from 'lucide-react';
import React from 'react';

export default function ServicesSection() {
    return (
        <section id="servicos" className="bg-background py-20">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="mb-16 text-center">
                    <h2 className="text-3xl font-medium text-balance md:text-4xl">Nossos Serviços</h2>
                    <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                        Oferecemos soluções completas de saúde para atender todas as suas necessidades
                    </p>
                </div>

                <div className="grid gap-8 lg:grid-cols-3">
                    {/* Busca de Estabelecimentos */}
                    <ServiceCard
                        icon={Search}
                        title="Busca de Estabelecimentos"
                        description="Encontre farmácias, dentistas e clínicas de fisioterapia próximas a você com facilidade."
                        features={['Localização inteligente', 'Contato direto via WhatsApp', 'Informações completas', 'Avaliações reais']}
                        ctaText="Buscar Agora"
                        ctaLink="/buscar"
                    />

                    {/* Planos de Atendimento Domiciliar */}
                    <ServiceCard
                        icon={Home}
                        title="Atendimento Domiciliar"
                        description="Fisioterapia especializada no conforto da sua casa com profissionais credenciados e equipamentos modernos."
                        features={['Fisioterapeutas credenciados', 'Equipamentos profissionais', 'Horários flexíveis', 'Avaliação gratuita']}
                        ctaText="Ver Planos"
                        ctaLink="/#planos"
                        highlight={true}
                    />

                    {/* Planos para Empresas */}
                    <ServiceCard
                        icon={Building2}
                        title="Planos Empresariais"
                        description="Soluções corporativas de fisioterapia para empresas que se preocupam com o bem-estar dos colaboradores."
                        features={['Atendimento para grupos', 'Desconto por volume', 'Gestão centralizada', 'Relatórios gerenciais']}
                        ctaText="Solicitar Orçamento"
                        ctaAction={() => {
                            const message = encodeURIComponent(
                                'Olá! Gostaria de saber mais sobre os planos empresariais de fisioterapia domiciliar.',
                            );
                            window.open(`https://wa.me/5511978196207?text=${message}`, '_blank');
                        }}
                    />
                </div>
            </div>
        </section>
    );
}

interface ServiceCardProps {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    features: string[];
    ctaText: string;
    ctaLink?: string;
    ctaAction?: () => void;
    highlight?: boolean;
}

const ServiceCard = ({ icon: Icon, title, description, features, ctaText, ctaLink, ctaAction, highlight = false }: ServiceCardProps) => {
    const cardContent = (
        <Card className={`relative h-full transition-all duration-300 hover:shadow-lg ${highlight ? 'border-primary/20 bg-primary/5' : ''}`}>
            {highlight && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-primary px-4 py-1 text-sm font-semibold text-primary-foreground">
                    Mais Popular
                </div>
            )}

            <CardHeader className="text-center">
                <div className="mx-auto mb-4">
                    <Badge variant="gradient" size="icon-lg" className="h-16 w-16">
                        <Icon className="h-8 w-8" />
                    </Badge>
                </div>
                <CardTitle className="text-xl font-medium">{title}</CardTitle>
                <CardDescription className="text-base leading-relaxed">{description}</CardDescription>
            </CardHeader>

            <CardContent className="flex flex-grow flex-col">
                <ul className="mb-8 flex-grow space-y-3">
                    {features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-3">
                            <Badge variant="gradient" size="icon-sm" className="h-5 w-5">
                                <div className="h-2 w-2 rounded-full bg-primary" />
                            </Badge>
                            <span className="text-sm text-muted-foreground">{feature}</span>
                        </li>
                    ))}
                </ul>

                {ctaLink ? (
                    <Link href={ctaLink}>
                        <Button className={`w-full ${highlight ? 'bg-primary hover:bg-primary/90' : ''}`} variant={highlight ? 'default' : 'outline'}>
                            {ctaText}
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                    </Link>
                ) : (
                    <Button
                        onClick={ctaAction}
                        className={`w-full ${highlight ? 'bg-primary hover:bg-primary/90' : ''}`}
                        variant={highlight ? 'default' : 'outline'}
                    >
                        {ctaText}
                        <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                )}
            </CardContent>
        </Card>
    );

    return cardContent;
};
