@extends('emails.layout')

@section('title', 'Novo Contato Recebido - F4 Fisio')

@section('content')
    <h2 style="color: #1e293b; margin-bottom: 20px;">Novo Contato Recebido</h2>
    
    <p>Um novo contato foi enviado através do site:</p>

    <div class="highlight">
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Nome</div>
                <div class="info-value">{{ $dados['nome'] }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Email</div>
                <div class="info-value">{{ $dados['email'] }}</div>
            </div>
            @if(!empty($dados['telefone']))
            <div class="info-item">
                <div class="info-label">Telefone</div>
                <div class="info-value">{{ $dados['telefone'] }}</div>
            </div>
            @endif
            <div class="info-item">
                <div class="info-label">Assunto</div>
                <div class="info-value">
                    @switch($dados['assunto'])
                        @case('agendamento')
                            Agendamento de Consulta
                            @break
                        @case('planos')
                            Informações sobre Planos
                            @break
                        @case('duvidas')
                            Dúvidas Gerais
                            @break
                        @case('orcamento')
                            Solicitação de Orçamento
                            @break
                        @case('suporte')
                            Suporte Técnico
                            @break
                        @default
                            Outros
                    @endswitch
                </div>
            </div>
        </div>
    </div>

    <div style="margin: 25px 0;">
        <div class="info-label">Mensagem</div>
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 6px; margin-top: 10px; border-left: 4px solid #0ea5e9;">
            {!! nl2br(e($dados['mensagem'])) !!}
        </div>
    </div>

    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
        <h3 style="color: #475569; font-size: 14px; margin-bottom: 15px;">Informações Técnicas</h3>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Data/Hora</div>
                <div class="info-value">{{ $dados['data_envio']->format('d/m/Y H:i:s') }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">IP</div>
                <div class="info-value">{{ $dados['ip'] }}</div>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <a href="mailto:{{ $dados['email'] }}?subject=Re: {{ urlencode($dados['assunto']) }}" class="button">
            Responder por Email
        </a>
        @if(!empty($dados['telefone']))
        <a href="https://wa.me/55{{ preg_replace('/\D/', '', $dados['telefone']) }}?text={{ urlencode('Olá ' . $dados['nome'] . '! Recebemos seu contato através do site da F4 Fisio.') }}" class="button" style="margin-left: 10px; background-color: #22c55e;">
            Responder por WhatsApp
        </a>
        @endif
    </div>
@endsection
