import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

/**
 * Formatar valor monetário em Real brasileiro
 */
export function formatCurrency(value: number | null | undefined): string {
    if (value === null || value === undefined || isNaN(value)) {
        return 'R$ 0,00';
    }

    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
    }).format(value);
}

/**
 * Formatar data e hora no padrão brasileiro
 */
export function formatDateTime(dateString: string | null | undefined): string {
    if (!dateString) {
        return 'Data não informada';
    }

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return 'Data inválida';
        }

        return date.toLocaleString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    } catch {
        return 'Data inválida';
    }
}

/**
 * Formatar apenas a hora
 */
export function formatTime(dateString: string | null | undefined): string {
    if (!dateString) {
        return '--:--';
    }

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return '--:--';
        }

        return date.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit',
        });
    } catch {
        return '--:--';
    }
}

/**
 * Formatar apenas a data
 */
export function formatDate(dateString: string | null | undefined): string {
    if (!dateString) {
        return 'Data não informada';
    }

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return 'Data inválida';
        }

        return date.toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
        });
    } catch {
        return 'Data inválida';
    }
}
