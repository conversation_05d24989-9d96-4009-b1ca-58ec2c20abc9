import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { Building2, ExternalLink, Globe, MapPin, Phone, Save } from 'lucide-react';

interface Estabelecimento {
    id: number;
    nome: string;
    categoria: string;
    descricao?: string;
    telefone?: string;
    whatsapp: string;
    email?: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    slug?: string;
    servicos_oferecidos?: string;
    site?: string;
    instagram?: string;
    facebook?: string;
    horario_funcionamento?: Record<string, any>;
}

interface EmpresaFormData {
    nome: string;
    categoria: string;
    descricao: string;
    telefone: string;
    whatsapp: string;
    email: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    servicos_oferecidos: string;
    site: string;
    instagram: string;
    facebook: string;
    horario_funcionamento: Record<string, any>;
    [key: string]: any;
}

interface Props {
    estabelecimento: Estabelecimento;
}

export default function EmpresaPerfil({ estabelecimento }: Props) {
    const { data, setData, put, processing, errors, recentlySuccessful } = useForm<EmpresaFormData>({
        nome: estabelecimento.nome || '',
        categoria: estabelecimento.categoria || '',
        descricao: estabelecimento.descricao || '',
        telefone: estabelecimento.telefone || '',
        whatsapp: estabelecimento.whatsapp || '',
        email: estabelecimento.email || '',
        endereco: estabelecimento.endereco || '',
        cidade: estabelecimento.cidade || '',
        estado: estabelecimento.estado || '',
        cep: estabelecimento.cep || '',
        servicos_oferecidos: estabelecimento.servicos_oferecidos || '',
        site: estabelecimento.site || '',
        instagram: estabelecimento.instagram || '',
        facebook: estabelecimento.facebook || '',
        horario_funcionamento: estabelecimento.horario_funcionamento || {},
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('empresa.perfil.update'));
    };

    const estados = [
        'AC',
        'AL',
        'AP',
        'AM',
        'BA',
        'CE',
        'DF',
        'ES',
        'GO',
        'MA',
        'MT',
        'MS',
        'MG',
        'PA',
        'PB',
        'PR',
        'PE',
        'PI',
        'RJ',
        'RN',
        'RS',
        'RO',
        'RR',
        'SC',
        'SP',
        'SE',
        'TO',
    ];

    return (
        <AppLayout>
            <Head title="Perfil da Empresa" />

            <div className="mx-auto max-w-4xl space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Perfil da Empresa</h1>
                        <p className="text-muted-foreground">Gerencie as informações do seu estabelecimento</p>
                    </div>
                    <div className="flex gap-2">
                        {estabelecimento.slug && (
                            <Button variant="outline" asChild>
                                <a href={`/empresa/${estabelecimento.slug}`} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="mr-2 h-4 w-4" />
                                    Ver Página Pública
                                </a>
                            </Button>
                        )}
                    </div>
                </div>

                {recentlySuccessful && (
                    <Alert className="border-green-200 bg-green-50">
                        <AlertDescription className="text-green-800">Perfil atualizado com sucesso!</AlertDescription>
                    </Alert>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Informações Básicas */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Building2 className="h-5 w-5" />
                                Informações Básicas
                            </CardTitle>
                            <CardDescription>Dados principais do seu estabelecimento</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="nome">Nome do Estabelecimento *</Label>
                                    <Input
                                        id="nome"
                                        value={data.nome}
                                        onChange={(e) => setData('nome', e.target.value)}
                                        placeholder="Ex: Clínica Dental Sorriso"
                                        className={errors.nome ? 'border-red-500' : ''}
                                    />
                                    {errors.nome && <p className="text-sm text-red-500">{errors.nome}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="categoria">Categoria *</Label>
                                    <Select value={data.categoria} onValueChange={(value) => setData('categoria', value)}>
                                        <SelectTrigger className={errors.categoria ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Selecione a categoria" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="dentista">Dentista</SelectItem>
                                            <SelectItem value="farmacia">Farmácia</SelectItem>
                                            <SelectItem value="fisioterapia">Fisioterapia</SelectItem>
                                            <SelectItem value="outros">Outros</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.categoria && <p className="text-sm text-red-500">{errors.categoria}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="descricao">Descrição</Label>
                                <Textarea
                                    id="descricao"
                                    value={data.descricao}
                                    onChange={(e) => setData('descricao', e.target.value)}
                                    placeholder="Descreva seu estabelecimento, especialidades e diferenciais..."
                                    rows={3}
                                    className={errors.descricao ? 'border-red-500' : ''}
                                />
                                {errors.descricao && <p className="text-sm text-red-500">{errors.descricao}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="servicos_oferecidos">Serviços Oferecidos</Label>
                                <Textarea
                                    id="servicos_oferecidos"
                                    value={data.servicos_oferecidos}
                                    onChange={(e) => setData('servicos_oferecidos', e.target.value)}
                                    placeholder="Liste os principais serviços que você oferece..."
                                    rows={3}
                                    className={errors.servicos_oferecidos ? 'border-red-500' : ''}
                                />
                                {errors.servicos_oferecidos && <p className="text-sm text-red-500">{errors.servicos_oferecidos}</p>}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Localização */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <MapPin className="h-5 w-5" />
                                Localização
                            </CardTitle>
                            <CardDescription>Endereço para que os clientes possam encontrar você</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="endereco">Endereço *</Label>
                                <Input
                                    id="endereco"
                                    value={data.endereco}
                                    onChange={(e) => setData('endereco', e.target.value)}
                                    placeholder="Rua, número, bairro"
                                    className={errors.endereco ? 'border-red-500' : ''}
                                />
                                {errors.endereco && <p className="text-sm text-red-500">{errors.endereco}</p>}
                            </div>

                            <div className="grid gap-4 md:grid-cols-3">
                                <div className="space-y-2">
                                    <Label htmlFor="cidade">Cidade *</Label>
                                    <Input
                                        id="cidade"
                                        value={data.cidade}
                                        onChange={(e) => setData('cidade', e.target.value)}
                                        placeholder="Nome da cidade"
                                        className={errors.cidade ? 'border-red-500' : ''}
                                    />
                                    {errors.cidade && <p className="text-sm text-red-500">{errors.cidade}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="estado">Estado *</Label>
                                    <Select value={data.estado} onValueChange={(value) => setData('estado', value)}>
                                        <SelectTrigger className={errors.estado ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="UF" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {estados.map((estado) => (
                                                <SelectItem key={estado} value={estado}>
                                                    {estado}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.estado && <p className="text-sm text-red-500">{errors.estado}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="cep">CEP *</Label>
                                    <Input
                                        id="cep"
                                        value={data.cep}
                                        onChange={(e) => setData('cep', e.target.value)}
                                        placeholder="00000-000"
                                        className={errors.cep ? 'border-red-500' : ''}
                                    />
                                    {errors.cep && <p className="text-sm text-red-500">{errors.cep}</p>}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Contato */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Phone className="h-5 w-5" />
                                Contato
                            </CardTitle>
                            <CardDescription>Formas de contato para seus clientes</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="whatsapp">WhatsApp *</Label>
                                    <Input
                                        id="whatsapp"
                                        value={data.whatsapp}
                                        onChange={(e) => setData('whatsapp', e.target.value)}
                                        placeholder="(11) 99999-9999"
                                        className={errors.whatsapp ? 'border-red-500' : ''}
                                    />
                                    {errors.whatsapp && <p className="text-sm text-red-500">{errors.whatsapp}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="telefone">Telefone</Label>
                                    <Input
                                        id="telefone"
                                        value={data.telefone}
                                        onChange={(e) => setData('telefone', e.target.value)}
                                        placeholder="(11) 3333-3333"
                                        className={errors.telefone ? 'border-red-500' : ''}
                                    />
                                    {errors.telefone && <p className="text-sm text-red-500">{errors.telefone}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    placeholder="<EMAIL>"
                                    className={errors.email ? 'border-red-500' : ''}
                                />
                                {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Redes Sociais */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Globe className="h-5 w-5" />
                                Redes Sociais e Site
                            </CardTitle>
                            <CardDescription>Links para suas redes sociais e site (opcional)</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="site">Site</Label>
                                <Input
                                    id="site"
                                    type="url"
                                    value={data.site}
                                    onChange={(e) => setData('site', e.target.value)}
                                    placeholder="https://www.seusite.com"
                                    className={errors.site ? 'border-red-500' : ''}
                                />
                                {errors.site && <p className="text-sm text-red-500">{errors.site}</p>}
                            </div>

                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="instagram">Instagram</Label>
                                    <Input
                                        id="instagram"
                                        value={data.instagram}
                                        onChange={(e) => setData('instagram', e.target.value)}
                                        placeholder="@seuestablecimento"
                                        className={errors.instagram ? 'border-red-500' : ''}
                                    />
                                    {errors.instagram && <p className="text-sm text-red-500">{errors.instagram}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="facebook">Facebook</Label>
                                    <Input
                                        id="facebook"
                                        value={data.facebook}
                                        onChange={(e) => setData('facebook', e.target.value)}
                                        placeholder="facebook.com/seuestablecimento"
                                        className={errors.facebook ? 'border-red-500' : ''}
                                    />
                                    {errors.facebook && <p className="text-sm text-red-500">{errors.facebook}</p>}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Botão de Salvar */}
                    <div className="flex justify-end">
                        <Button type="submit" disabled={processing} size="lg">
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Salvando...' : 'Salvar Alterações'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
