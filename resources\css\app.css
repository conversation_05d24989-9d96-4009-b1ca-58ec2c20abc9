@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

/* Custom styles for modern search */
.bg-grid-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgba(0, 0, 0, 0.15) 1px, transparent 0);
    background-size: 20px 20px;
}

.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {

    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    --background: oklch(1.0000 0 0);
    --foreground: oklch(0 0 0);
    --card: oklch(0.9834 0.0042 236.4956);
    --card-foreground: oklch(0 0 0);
    --popover: oklch(0.9834 0.0042 236.4956);
    --popover-foreground: oklch(0 0 0);
    --primary: oklch(0.8542 0.2851 143.0785);
    --primary-foreground: oklch(0 0 0);
    --secondary: oklch(0.9588 0.0086 247.9151);
    --secondary-foreground: oklch(0.3211 0 0);
    --muted: oklch(0.9588 0.0086 247.9151);
    --muted-foreground: oklch(0.5956 0.0381 257.8663);
    --accent: oklch(0.6650 0.2208 143.2396);
    --accent-foreground: oklch(1.0000 0 0);
    --destructive: oklch(0.6137 0.2039 25.5645);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.9288 0.0126 255.5078);
    --input: oklch(0.9834 0.0042 236.4956);
    --ring: oklch(0.8542 0.2851 143.0785);
    --chart-1: oklch(0.7357 0.2444 143.2345);
    --chart-2: oklch(0.5946 0.1398 250.2957);
    --chart-3: oklch(0.7343 0.1377 80.0061);
    --chart-4: oklch(0.8208 0.0833 237.1834);
    --chart-5: oklch(0.6533 0.1638 48.4063);
    --sidebar: oklch(0.9834 0.0042 236.4956);
    --sidebar-foreground: oklch(0 0 0);
    --sidebar-primary: oklch(0.7357 0.2444 143.2345);
    --sidebar-primary-foreground: oklch(0 0 0);
    --sidebar-accent: oklch(0.9588 0.0086 247.9151);
    --sidebar-accent-foreground: oklch(0 0 0);
    --sidebar-border: oklch(0.9288 0.0126 255.5078);
    --sidebar-ring: oklch(0.7357 0.2444 143.2345);
    --font-sans: Inter, sans-serif;
    --font-serif: ui-serif, Georgia, Cambria, Times New Roman, Times, serif;
    --font-mono: Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    --radius: 0.5rem;
    --shadow-2xs: 0px 2px 12px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 2px 12px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0px 2px 12px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0.025em;
    --spacing: 0.25rem;
}

.dark {
    --background: oklch(0.1647 0.0042 285.9351);
    --foreground: oklch(1.0000 0 0);
    --card: oklch(0.2433 0.0247 263.9506);
    --card-foreground: oklch(1.0000 0 0);
    --popover: oklch(0.2433 0.0247 263.9506);
    --popover-foreground: oklch(1.0000 0 0);
    --primary: oklch(0.8542 0.2851 143.0785);
    --primary-foreground: oklch(0 0 0);
    --secondary: oklch(0.4474 0.0343 261.3244);
    --secondary-foreground: oklch(1.0000 0 0);
    --muted: oklch(0.4474 0.0343 261.3244);
    --muted-foreground: oklch(0.7457 0.0304 254.7208);
    --accent: oklch(0.6650 0.2208 143.2396);
    --accent-foreground: oklch(1.0000 0 0);
    --destructive: oklch(0.6137 0.2039 25.5645);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3351 0.0331 260.9120);
    --input: oklch(0.2433 0.0247 263.9506);
    --ring: oklch(0.8542 0.2851 143.0785);
    --chart-1: oklch(0.7357 0.2444 143.2345);
    --chart-2: oklch(0.5946 0.1398 250.2957);
    --chart-3: oklch(0.7343 0.1377 80.0061);
    --chart-4: oklch(0.8208 0.0833 237.1834);
    --chart-5: oklch(0.6533 0.1638 48.4063);
    --sidebar: oklch(0.2433 0.0247 263.9506);
    --sidebar-foreground: oklch(1.0000 0 0);
    --sidebar-primary: oklch(0.7357 0.2444 143.2345);
    --sidebar-primary-foreground: oklch(0 0 0);
    --sidebar-accent: oklch(0.3351 0.0331 260.9120);
    --sidebar-accent-foreground: oklch(1.0000 0 0);
    --sidebar-border: oklch(0.3351 0.0331 260.9120);
    --sidebar-ring: oklch(0.7357 0.2444 143.2345);
    --font-sans: Inter, sans-serif;
    --font-serif: ui-serif, Georgia, Cambria, Times New Roman, Times, serif;
    --font-mono: Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    --radius: 0.5rem;
    --shadow-2xs: 0px 2px 12px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 2px 12px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0px 2px 12px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0px 2px 12px 0px hsl(0 0% 0% / 0.25);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/*
  ---break---
*/

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);

    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-normal: var(--tracking-normal);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
    letter-spacing: var(--tracking-normal);
}

/*
  ---break---
*/

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    html {
        scroll-behavior: smooth;
    }

    body {
        @apply bg-background text-foreground;
    }
}