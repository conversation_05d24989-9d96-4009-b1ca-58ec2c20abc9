<?php

namespace App\Http\Controllers;

use App\Models\Estabelecimento;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EmpresaPublicaController extends Controller
{
    public function show($slug)
    {
        $estabelecimento = Estabelecimento::where('slug', $slug)
            ->where('ativo', true)
            ->where('plano_ativo', true)
            ->firstOrFail();

        // Incrementar visualizações
        $estabelecimento->incrementarVisualizacao();

        // Dados estruturados para SEO
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => $this->getSchemaType($estabelecimento->categoria),
            'name' => $estabelecimento->nome,
            'description' => $estabelecimento->descricao,
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => $estabelecimento->endereco,
                'addressLocality' => $estabelecimento->cidade,
                'addressRegion' => $estabelecimento->estado,
                'postalCode' => $estabelecimento->cep,
                'addressCountry' => 'BR'
            ],
            'telephone' => $estabelecimento->telefone,
            'url' => $estabelecimento->site,
            'aggregateRating' => [
                '@type' => 'AggregateRating',
                'ratingValue' => $estabelecimento->avaliacao_media,
                'reviewCount' => $estabelecimento->total_avaliacoes
            ]
        ];

        if ($estabelecimento->latitude && $estabelecimento->longitude) {
            $structuredData['geo'] = [
                '@type' => 'GeoCoordinates',
                'latitude' => $estabelecimento->latitude,
                'longitude' => $estabelecimento->longitude
            ];
        }

        // Meta tags para SEO
        $metaTags = [
            'title' => $estabelecimento->nome . ' - ' . $this->getCategoriaLabel($estabelecimento->categoria) . ' em ' . $estabelecimento->cidade,
            'description' => $estabelecimento->descricao ?: 
                $this->getCategoriaLabel($estabelecimento->categoria) . ' em ' . $estabelecimento->cidade . '. ' .
                'Contato via WhatsApp: ' . $estabelecimento->whatsapp,
            'keywords' => implode(', ', [
                $this->getCategoriaLabel($estabelecimento->categoria),
                $estabelecimento->cidade,
                $estabelecimento->estado,
                $estabelecimento->nome,
                'saúde',
                'atendimento'
            ]),
            'og:title' => $estabelecimento->nome,
            'og:description' => $estabelecimento->descricao,
            'og:type' => 'business.business',
            'og:url' => request()->url(),
            'twitter:card' => 'summary',
            'twitter:title' => $estabelecimento->nome,
            'twitter:description' => $estabelecimento->descricao,
        ];

        return Inertia::render('empresa-publica/show', [
            'estabelecimento' => $estabelecimento,
            'structuredData' => $structuredData,
            'metaTags' => $metaTags,
        ]);
    }

    private function getSchemaType($categoria)
    {
        $types = [
            'dentista' => 'Dentist',
            'farmacia' => 'Pharmacy',
            'fisioterapia' => 'MedicalBusiness',
            'outros' => 'LocalBusiness'
        ];

        return $types[$categoria] ?? 'LocalBusiness';
    }

    private function getCategoriaLabel($categoria)
    {
        $labels = [
            'dentista' => 'Dentista',
            'farmacia' => 'Farmácia',
            'fisioterapia' => 'Fisioterapia',
            'outros' => 'Estabelecimento de Saúde'
        ];

        return $labels[$categoria] ?? 'Estabelecimento';
    }
}
