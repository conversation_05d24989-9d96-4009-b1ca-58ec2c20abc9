<?php

namespace App\Jobs;

use App\Models\Agendamento;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessarLembretesAgendamento implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $horasAntecedencia = 24
    ) {}

    public function handle(): void
    {
        try {
            $dataLimite = Carbon::now()->addHours($this->horasAntecedencia);
            
            // Buscar agendamentos que precisam de lembrete
            $agendamentos = Agendamento::where('status', 'confirmado')
                ->where('scheduled_at', '<=', $dataLimite)
                ->where('scheduled_at', '>', Carbon::now())
                ->whereDoesntHave('notificacoes', function ($query) {
                    $query->where('tipo', 'lembrete_sessao')
                          ->where('created_at', '>=', Carbon::now()->subHours(25));
                })
                ->with(['paciente', 'fisioterapeuta'])
                ->get();

            Log::info('Processando lembretes de agendamento', [
                'total_agendamentos' => $agendamentos->count(),
                'horas_antecedencia' => $this->horasAntecedencia
            ]);

            foreach ($agendamentos as $agendamento) {
                // Enviar lembrete para o paciente
                EnviarEmailAgendamento::dispatch(
                    $agendamento,
                    'lembrete',
                    $agendamento->paciente_id
                )->delay(now()->addSeconds(rand(1, 30))); // Pequeno delay para evitar spam

                // Enviar lembrete para o fisioterapeuta (1 hora antes)
                if ($agendamento->scheduled_at <= Carbon::now()->addHour()) {
                    EnviarEmailAgendamento::dispatch(
                        $agendamento,
                        'lembrete',
                        $agendamento->fisioterapeuta_id
                    )->delay(now()->addSeconds(rand(31, 60)));
                }
            }

            Log::info('Lembretes de agendamento processados com sucesso', [
                'total_processados' => $agendamentos->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao processar lembretes de agendamento', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Job de processamento de lembretes falhou', [
            'error' => $exception->getMessage()
        ]);
    }
}
