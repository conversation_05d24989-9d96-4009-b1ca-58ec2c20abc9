<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'F4 Fisio')</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #0ea5e9;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #0ea5e9;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 14px;
        }
        .content {
            margin-bottom: 30px;
        }
        .highlight {
            background-color: #f1f5f9;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #0ea5e9;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 0;
        }
        .button:hover {
            background-color: #0284c7;
        }
        .footer {
            text-align: center;
            border-top: 1px solid #e2e8f0;
            padding-top: 20px;
            color: #64748b;
            font-size: 12px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .info-item {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 4px;
        }
        .info-label {
            font-weight: 600;
            color: #475569;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .info-value {
            color: #1e293b;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">F4 Fisio</div>
            <div class="subtitle">Fisioterapia Especializada</div>
        </div>

        <div class="content">
            @yield('content')
        </div>

        <div class="footer">
            <p>Este é um email automático, não responda a esta mensagem.</p>
            <p>F4 Fisio - Cuidando da sua saúde com excelência</p>
            <p>
                <a href="{{ config('app.url') }}" style="color: #0ea5e9;">www.f4fisio.com</a> | 
                <a href="mailto:<EMAIL>" style="color: #0ea5e9;"><EMAIL></a>
            </p>
        </div>
    </div>
</body>
</html>
