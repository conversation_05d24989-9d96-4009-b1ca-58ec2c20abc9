<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\User;
use App\Models\Plano;

class PrepareProduction extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:prepare-production {--keep-admin : Manter usuário admin existente}';

    /**
     * The console command description.
     */
    protected $description = 'Prepara a aplicação para produção removendo dados de desenvolvimento';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->confirm('⚠️  ATENÇÃO: Este comando irá remover TODOS os dados de desenvolvimento. Continuar?')) {
            $this->info('Operação cancelada.');
            return;
        }

        $this->info('🧹 Iniciando limpeza dos dados de desenvolvimento...');

        // Desabilitar verificações de chave estrangeira temporariamente (apenas MySQL)
        if (DB::getDriverName() === 'mysql') {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        }

        try {
            // Limpar tabelas de dados de desenvolvimento
            $this->cleanDevelopmentData();

            // Criar dados essenciais para produção
            $this->createProductionData();

            $this->info('✅ Aplicação preparada para produção com sucesso!');
            $this->displayCredentials();

        } catch (\Exception $e) {
            $this->error('❌ Erro ao preparar aplicação: ' . $e->getMessage());
            return 1;
        } finally {
            // Reabilitar verificações de chave estrangeira (apenas MySQL)
            if (DB::getDriverName() === 'mysql') {
                DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            }
        }

        return 0;
    }

    private function cleanDevelopmentData()
    {
        $this->info('🗑️  Removendo dados de desenvolvimento...');

        // Lista de tabelas para limpar (mantendo estrutura)
        $tablesToClean = [
            'notificacoes',
            'relatorios_sessao',
            'avaliacoes',
            'agendamentos',
            'disponibilidades',
            'pagamentos',
            'assinaturas',
        ];

        foreach ($tablesToClean as $table) {
            if (Schema::hasTable($table)) {
                // Para SQLite, usar delete em vez de truncate
                if (DB::getDriverName() === 'sqlite') {
                    DB::table($table)->delete();
                } else {
                    DB::table($table)->truncate();
                }
                $this->line("   ✓ Tabela {$table} limpa");
            }
        }

        // Limpar usuários de desenvolvimento (exceto admin se solicitado)
        if ($this->option('keep-admin')) {
            DB::table('users')
                ->where('role', '!=', 'admin')
                ->delete();
            $this->line("   ✓ Usuários de desenvolvimento removidos (admin mantido)");
        } else {
            if (DB::getDriverName() === 'sqlite') {
                DB::table('users')->delete();
            } else {
                DB::table('users')->truncate();
            }
            $this->line("   ✓ Todos os usuários removidos");
        }

        // Limpar fisioterapeutas
        if (Schema::hasTable('fisioterapeutas')) {
            if (DB::getDriverName() === 'sqlite') {
                DB::table('fisioterapeutas')->delete();
            } else {
                DB::table('fisioterapeutas')->truncate();
            }
            $this->line("   ✓ Perfis de fisioterapeutas limpos");
        }
    }

    private function createProductionData()
    {
        $this->info('📋 Criando dados essenciais para produção...');

        // Criar usuário admin se não existir
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::create([
                'name' => 'Administrador F4 Fisio',
                'email' => '<EMAIL>',
                'password' => bcrypt('admin123'),
                'role' => 'admin',
                'active' => true,
                'has_subscription' => true,
                'email_verified_at' => now(),
            ]);
            $this->line("   ✓ Usuário administrador criado");
        }

        // Criar planos padrão se não existirem
        if (Plano::count() === 0) {
            $planos = [
                [
                    'name' => 'Plano Mensal',
                    'description' => 'Acesso completo à plataforma com sessões ilimitadas',
                    'price' => 64.00,
                    'sessions_per_month' => null,
                    'active' => true,
                    'features' => json_encode([
                        'Sessões ilimitadas',
                        'Agendamento online',
                        'Relatórios detalhados',
                        'Suporte prioritário'
                    ])
                ],
                [
                    'name' => 'Sessão Avulsa',
                    'description' => 'Pagamento por sessão individual',
                    'price' => 80.00,
                    'sessions_per_month' => 1,
                    'active' => true,
                    'features' => json_encode([
                        'Pagamento por sessão',
                        'Agendamento online',
                        'Relatório da sessão'
                    ])
                ],
                [
                    'name' => 'Plano Empresarial',
                    'description' => 'Solução personalizada para empresas e grupos',
                    'price' => 0.00,
                    'sessions_per_month' => null,
                    'active' => true,
                    'features' => json_encode([
                        'Sessões ilimitadas',
                        'Múltiplos usuários',
                        'Relatórios avançados',
                        'Suporte dedicado',
                        'Integração personalizada'
                    ])
                ]
            ];

            foreach ($planos as $plano) {
                Plano::create($plano);
            }
            $this->line("   ✓ Planos padrão criados");
        }

        // Criar alguns estabelecimentos de exemplo (opcional)
        if ($this->confirm('Deseja criar alguns estabelecimentos de exemplo?')) {
            $this->createSampleEstablishments();
        }
    }

    private function createSampleEstablishments()
    {
        $estabelecimentos = [
            [
                'nome' => 'Clínica Exemplo - Fisioterapia',
                'categoria' => 'fisioterapia',
                'descricao' => 'Clínica de fisioterapia especializada em reabilitação',
                'telefone' => '(11) 99999-0001',
                'whatsapp' => '11999990001',
                'email' => '<EMAIL>',
                'endereco' => 'Rua Exemplo, 123',
                'cidade' => 'São Paulo',
                'estado' => 'SP',
                'cep' => '01000-000',
                'latitude' => -23.5505,
                'longitude' => -46.6333,
                'ativo' => true,
                'plano_ativo' => true,
                'plano_vencimento' => now()->addMonth(),
                'avaliacao_media' => 4.5,
                'total_avaliacoes' => 10,
            ]
        ];

        foreach ($estabelecimentos as $estabelecimento) {
            DB::table('estabelecimentos')->insert(array_merge($estabelecimento, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        $this->line("   ✓ Estabelecimentos de exemplo criados");
    }

    private function displayCredentials()
    {
        $this->info('');
        $this->info('🔑 CREDENCIAIS DE ACESSO:');
        $this->info('   Email: <EMAIL>');
        $this->info('   Senha: admin123');
        $this->info('');
        $this->warn('⚠️  IMPORTANTE: Altere a senha do administrador após o primeiro login!');
        $this->info('');
        $this->info('📋 PRÓXIMOS PASSOS:');
        $this->info('   1. Configure as variáveis de ambiente (.env)');
        $this->info('   2. Configure o Mercado Pago (se necessário)');
        $this->info('   3. Configure o email (SMTP)');
        $this->info('   4. Teste todas as funcionalidades');
        $this->info('   5. Altere a senha do administrador');
    }
}
