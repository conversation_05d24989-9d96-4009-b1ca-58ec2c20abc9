import React, { useEffect } from 'react';
import { toast, Toaster } from 'sonner';
import { CheckCircle, XCircle, AlertTriangle, Info, X } from 'lucide-react';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastOptions {
    title?: string;
    description?: string;
    duration?: number;
    action?: {
        label: string;
        onClick: () => void;
    };
    cancel?: {
        label: string;
        onClick?: () => void;
    };
}

const toastIcons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertTriangle,
    info: Info
};

export function showToast(type: ToastType, message: string, options?: ToastOptions) {
    const Icon = toastIcons[type];
    
    const toastOptions = {
        duration: options?.duration || (type === 'error' ? 6000 : 4000),
        icon: <Icon className="h-4 w-4" />,
        action: options?.action ? {
            label: options.action.label,
            onClick: options.action.onClick
        } : undefined,
        cancel: options?.cancel && options.cancel.onClick ? {
            label: options.cancel.label,
            onClick: options.cancel.onClick
        } : undefined,
        description: options?.description,
        className: `toast-${type}`
    };

    switch (type) {
        case 'success':
            return toast.success(options?.title || message, toastOptions);
        case 'error':
            return toast.error(options?.title || message, toastOptions);
        case 'warning':
            return toast.warning(options?.title || message, toastOptions);
        case 'info':
            return toast.info(options?.title || message, toastOptions);
        default:
            return toast(message, toastOptions);
    }
}

export function ToastProvider() {
    return (
        <Toaster
            position="top-right"
            expand={true}
            richColors={true}
            closeButton={true}
            toastOptions={{
                style: {
                    background: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    color: 'hsl(var(--foreground))',
                },
                className: 'toast',
                descriptionClassName: 'toast-description',
                actionButtonStyle: {
                    background: 'hsl(var(--primary))',
                    color: 'hsl(var(--primary-foreground))',
                },
                cancelButtonStyle: {
                    background: 'hsl(var(--muted))',
                    color: 'hsl(var(--muted-foreground))',
                },
            }}
        />
    );
}

// Hook para usar toasts
export function useToast() {
    const showSuccess = React.useCallback((message: string, options?: ToastOptions) => {
        return showToast('success', message, options);
    }, []);

    const showError = React.useCallback((message: string, options?: ToastOptions) => {
        return showToast('error', message, options);
    }, []);

    const showWarning = React.useCallback((message: string, options?: ToastOptions) => {
        return showToast('warning', message, options);
    }, []);

    const showInfo = React.useCallback((message: string, options?: ToastOptions) => {
        return showToast('info', message, options);
    }, []);

    const dismiss = React.useCallback((toastId?: string | number) => {
        toast.dismiss(toastId);
    }, []);

    const dismissAll = React.useCallback(() => {
        toast.dismiss();
    }, []);

    return {
        showSuccess,
        showError,
        showWarning,
        showInfo,
        dismiss,
        dismissAll,
        toast: showToast
    };
}

// Hook para mostrar toasts baseados em flash messages do Laravel
export function useFlashToasts(flash?: { success?: string; error?: string; warning?: string; info?: string }) {
    const { showSuccess, showError, showWarning, showInfo } = useToast();

    useEffect(() => {
        if (flash?.success) {
            showSuccess(flash.success);
        }
        if (flash?.error) {
            showError(flash.error);
        }
        if (flash?.warning) {
            showWarning(flash.warning);
        }
        if (flash?.info) {
            showInfo(flash.info);
        }
    }, [flash, showSuccess, showError, showWarning, showInfo]);
}
