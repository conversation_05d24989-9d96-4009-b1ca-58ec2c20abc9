import { expect, test } from '@playwright/test';

test.describe('Fluxo do Usuário Paciente', () => {
    test.beforeEach(async ({ page }) => {
        // Navegar para a página inicial
        await page.goto('/');
    });

    test('deve navegar pelas páginas públicas', async ({ page }) => {
        // Verificar se a página inicial carrega
        await expect(page).toHaveTitle(/F4 Fisio|Laravel/);

        // Verificar se o header está presente
        await expect(page.getByRole('banner')).toBeVisible();

        // Navegar para a página de planos
        await page.getByRole('link', { name: 'Planos' }).click();
        await expect(page).toHaveURL(/.*planos/);

        // Navegar para a página sobre
        await page.getByRole('link', { name: 'Sobre Nós' }).click();
        await expect(page).toHaveURL(/.*sobre/);

        // Navegar para a página de serviços
        await page.getByRole('link', { name: 'Buscar Serviços' }).click();
        await expect(page).toHaveURL(/.*buscar/);

        // Navegar para a página de contato
        await page.getByRole('link', { name: 'Contato' }).click();
        await expect(page).toHaveURL(/.*contato/);
    });

    test('deve acessar a página de login', async ({ page }) => {
        // Clicar no botão de login
        await page.getByRole('link', { name: 'Dashboard' }).click();
        await expect(page).toHaveURL(/.*login/);

        // Verificar se os campos de login estão presentes
        await expect(page.getByRole('textbox', { name: 'Endereço de email' })).toBeVisible();
        await expect(page.getByRole('textbox', { name: 'Senha' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Entrar' })).toBeVisible();
    });

    test('deve acessar a página de registro', async ({ page }) => {
        // Navegar para login primeiro
        await page.click('text=Entrar');

        // Clicar no link de registro
        await page.click('text=Criar conta');
        await expect(page).toHaveURL(/.*register/);

        // Verificar se os campos de registro estão presentes
        await expect(page.locator('input[name="name"]')).toBeVisible();
        await expect(page.locator('input[name="email"]')).toBeVisible();
        await expect(page.locator('input[name="password"]')).toBeVisible();
        await expect(page.locator('input[name="password_confirmation"]')).toBeVisible();
        await expect(page.locator('select[name="role"]')).toBeVisible();
    });

    test('deve registrar um novo paciente e seguir o fluxo completo', async ({ page }) => {
        // Navegar para registro
        await page.goto('/register');

        // Preencher formulário de registro
        const timestamp = Date.now();
        const email = `paciente.teste.${timestamp}@example.com`;

        await page.fill('input[name="name"]', 'Paciente Teste');
        await page.fill('input[name="email"]', email);
        await page.fill('input[name="password"]', 'password123');
        await page.fill('input[name="password_confirmation"]', 'password123');
        await page.selectOption('select[name="role"]', 'paciente');

        // Submeter formulário
        await page.click('button[type="submit"]');

        // Deve redirecionar para seleção de planos (paciente sem assinatura)
        await expect(page).toHaveURL(/.*paciente\/planos/);

        // Verificar se a página de planos do paciente carrega
        await expect(page.locator('h1, h2')).toContainText(/plano/i);

        // Simular seleção de um plano (se houver botões de plano)
        const planoButtons = page.locator('button:has-text("Escolher"), button:has-text("Selecionar")');
        if ((await planoButtons.count()) > 0) {
            await planoButtons.first().click();
        }

        // Verificar se pode acessar outras páginas do paciente
        await page.goto('/paciente/onboarding');
        await expect(page).toHaveURL(/.*paciente\/onboarding/);

        // Verificar se a página de onboarding carrega
        await expect(page.locator('h1, h2')).toBeVisible();
    });

    test('deve fazer login com paciente existente', async ({ page }) => {
        // Primeiro, criar um paciente via registro
        await page.goto('/register');

        const timestamp = Date.now();
        const email = `paciente.login.${timestamp}@example.com`;

        await page.fill('input[name="name"]', 'Paciente Login');
        await page.fill('input[name="email"]', email);
        await page.fill('input[name="password"]', 'password123');
        await page.fill('input[name="password_confirmation"]', 'password123');
        await page.selectOption('select[name="role"]', 'paciente');
        await page.click('button[type="submit"]');

        // Fazer logout
        await page.click('text=Sair');

        // Fazer login novamente
        await page.goto('/login');
        await page.fill('input[type="email"]', email);
        await page.fill('input[type="password"]', 'password123');
        await page.click('button[type="submit"]');

        // Deve redirecionar para planos (paciente sem assinatura)
        await expect(page).toHaveURL(/.*paciente\/planos/);
    });

    test('deve verificar responsividade em mobile', async ({ page }) => {
        // Definir viewport mobile
        await page.setViewportSize({ width: 375, height: 667 });

        // Verificar se a página inicial é responsiva
        await expect(page.locator('header')).toBeVisible();

        // Verificar se o menu mobile funciona (se houver)
        const menuButton = page.locator('button[aria-label*="menu"], button:has-text("☰")');
        if (await menuButton.isVisible()) {
            await menuButton.click();
            await expect(page.locator('nav')).toBeVisible();
        }

        // Testar navegação em mobile
        await page.goto('/login');
        await expect(page.locator('input[type="email"]')).toBeVisible();
        await expect(page.locator('input[type="password"]')).toBeVisible();
    });

    test('deve verificar acessibilidade básica', async ({ page }) => {
        // Verificar se há elementos com aria-labels apropriados
        const buttons = page.locator('button');
        const buttonCount = await buttons.count();

        if (buttonCount > 0) {
            // Verificar se pelo menos alguns botões têm texto ou aria-label
            const firstButton = buttons.first();
            const hasText = await firstButton.textContent();
            const hasAriaLabel = await firstButton.getAttribute('aria-label');

            expect(hasText || hasAriaLabel).toBeTruthy();
        }

        // Verificar se inputs têm labels
        const inputs = page.locator('input');
        const inputCount = await inputs.count();

        if (inputCount > 0) {
            const firstInput = inputs.first();
            const hasLabel = (await page.locator(`label[for="${await firstInput.getAttribute('id')}"]`).count()) > 0;
            const hasAriaLabel = await firstInput.getAttribute('aria-label');
            const hasPlaceholder = await firstInput.getAttribute('placeholder');

            // Pelo menos uma forma de identificação deve estar presente
            expect(hasLabel || hasAriaLabel || hasPlaceholder).toBeTruthy();
        }
    });
});
