<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class UsuarioController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Filtro por role
        if ($request->filled('role') && $request->role !== 'all') {
            $query->where('role', $request->role);
        }

        // Filtro por status
        if ($request->filled('active') && $request->active !== 'all') {
            $query->where('active', $request->boolean('active'));
        }

        // Busca por nome ou email
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $usuarios = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('admin/usuarios', [
            'usuarios' => $usuarios,
            'filters' => $request->only(['role', 'active', 'search']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('admin/usuarios/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,fisioterapeuta,paciente',
            'phone' => 'nullable|string|max:20',
            'birth_date' => 'nullable|date',
            'gender' => 'nullable|in:masculino,feminino,outro',
            'address' => 'nullable|string',
            'medical_history' => 'nullable|string',
            'emergency_contact' => 'nullable|string',
            'active' => 'boolean',
        ]);

        $validated['password'] = Hash::make($validated['password']);

        $user = User::create($validated);

        // Registrar atividade
        UserActivityLog::create([
            'user_id' => $user->id,
            'admin_user_id' => auth()->id(),
            'action' => 'created',
            'description' => "Usuário {$user->name} foi criado",
            'new_values' => $validated,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        return redirect()->route('admin.usuarios.index')
            ->with('success', 'Usuário criado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $usuario)
    {
        $usuario->load(['assinaturas.plano', 'agendamentosComoPaciente.fisioterapeuta', 'agendamentosComoFisioterapeuta.paciente', 'avaliacoesRecebidas', 'avaliacoesFeitas']);

        return Inertia::render('admin/usuarios/show', [
            'usuario' => $usuario,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $usuario)
    {
        return Inertia::render('admin/usuarios/edit', [
            'usuario' => $usuario,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $usuario)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($usuario->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:admin,fisioterapeuta,paciente',
            'phone' => 'nullable|string|max:20',
            'birth_date' => 'nullable|date',
            'gender' => 'nullable|in:masculino,feminino,outro',
            'address' => 'nullable|string',
            'medical_history' => 'nullable|string',
            'emergency_contact' => 'nullable|string',
            'active' => 'boolean',
        ]);

        // Capturar valores antigos para o log
        $oldValues = $usuario->toArray();

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $usuario->update($validated);

        // Registrar atividade
        UserActivityLog::create([
            'user_id' => $usuario->id,
            'admin_user_id' => auth()->id(),
            'action' => 'updated',
            'description' => "Usuário {$usuario->name} foi atualizado",
            'old_values' => $oldValues,
            'new_values' => $validated,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        return redirect()->route('admin.usuarios.index')
            ->with('success', 'Usuário atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $usuario)
    {
        // Não permitir deletar o próprio usuário
        if ($usuario->id === auth()->id()) {
            return back()->with('error', 'Você não pode deletar sua própria conta.');
        }

        // Registrar atividade antes de deletar
        UserActivityLog::create([
            'user_id' => $usuario->id,
            'admin_user_id' => auth()->id(),
            'action' => 'deleted',
            'description' => "Usuário {$usuario->name} foi removido",
            'old_values' => $usuario->toArray(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        $usuario->delete();

        return redirect()->route('admin.usuarios.index')
            ->with('success', 'Usuário removido com sucesso!');
    }

    public function history(User $usuario)
    {
        $logs = UserActivityLog::where('user_id', $usuario->id)
            ->with('adminUser')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return Inertia::render('admin/usuarios/history', [
            'usuario' => $usuario,
            'logs' => $logs,
        ]);
    }

    public function export(Request $request)
    {
        $query = User::query();

        // Aplicar filtros se existirem
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        $usuarios = $query->orderBy('created_at', 'desc')->get();

        $csvData = [];
        $csvData[] = ['ID', 'Nome', 'Email', 'Tipo', 'Status', 'Data de Criação'];

        foreach ($usuarios as $usuario) {
            $csvData[] = [
                $usuario->id,
                $usuario->name,
                $usuario->email,
                $usuario->role,
                $usuario->active ? 'Ativo' : 'Inativo',
                $usuario->created_at->format('d/m/Y H:i:s'),
            ];
        }

        $filename = 'usuarios_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            foreach ($csvData as $row) {
                fputcsv($file, $row, ';');
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
