<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Cupom;
use App\Models\Afiliado;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CupomController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Cupom::with(['afiliado', 'createdBy']);

        // Filtros
        if ($request->filled('tipo') && $request->tipo !== 'all') {
            $query->where('tipo', $request->tipo);
        }

        if ($request->filled('status') && $request->status !== 'all') {
            if ($request->status === 'ativo') {
                $query->where('ativo', true);
            } elseif ($request->status === 'inativo') {
                $query->where('ativo', false);
            }
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('codigo', 'like', '%' . $request->search . '%')
                  ->orWhere('nome', 'like', '%' . $request->search . '%');
            });
        }

        $cupons = $query->orderBy('created_at', 'desc')
                       ->paginate(15)
                       ->withQueryString();

        return Inertia::render('admin/cupons/index', [
            'cupons' => $cupons,
            'filters' => $request->only(['tipo', 'status', 'search']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $afiliados = Afiliado::aprovados()->ativos()->get(['id', 'nome', 'codigo_afiliado']);

        return Inertia::render('admin/cupons/create', [
            'afiliados' => $afiliados,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'codigo' => 'required|string|max:50|unique:cupons,codigo',
            'tipo' => 'required|in:publico,afiliado_exclusivo',
            'afiliado_id' => 'nullable|exists:afiliados,id|required_if:tipo,afiliado_exclusivo',
            'nome' => 'required|string|max:255',
            'descricao' => 'nullable|string',
            'tipo_desconto' => 'required|in:percentual,valor_fixo',
            'valor_desconto' => 'required|numeric|min:0',
            'valor_minimo_pedido' => 'nullable|numeric|min:0',
            'data_inicio' => 'required|date',
            'data_fim' => 'nullable|date|after_or_equal:data_inicio',
            'limite_uso' => 'nullable|integer|min:1',
            'ativo' => 'boolean',
        ]);

        // Validações adicionais
        if ($validated['tipo_desconto'] === 'percentual' && $validated['valor_desconto'] > 100) {
            return back()->withErrors(['valor_desconto' => 'O desconto percentual não pode ser maior que 100%.']);
        }

        $validated['created_by'] = auth()->id();
        $validated['codigo'] = strtoupper($validated['codigo']);

        Cupom::create($validated);

        return redirect()->route('admin.cupons.index')
                        ->with('success', 'Cupom criado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Cupom $cupom)
    {
        $cupom->load(['afiliado', 'createdBy']);

        return Inertia::render('admin/cupons/show', [
            'cupom' => $cupom,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Cupom $cupom)
    {
        $afiliados = Afiliado::aprovados()->ativos()->get(['id', 'nome', 'codigo_afiliado']);

        return Inertia::render('admin/cupons/edit', [
            'cupom' => $cupom,
            'afiliados' => $afiliados,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Cupom $cupom)
    {
        $validated = $request->validate([
            'codigo' => 'required|string|max:50|unique:cupons,codigo,' . $cupom->id,
            'tipo' => 'required|in:publico,afiliado_exclusivo',
            'afiliado_id' => 'nullable|exists:afiliados,id|required_if:tipo,afiliado_exclusivo',
            'nome' => 'required|string|max:255',
            'descricao' => 'nullable|string',
            'tipo_desconto' => 'required|in:percentual,valor_fixo',
            'valor_desconto' => 'required|numeric|min:0',
            'valor_minimo_pedido' => 'nullable|numeric|min:0',
            'data_inicio' => 'required|date',
            'data_fim' => 'nullable|date|after_or_equal:data_inicio',
            'limite_uso' => 'nullable|integer|min:1',
            'ativo' => 'boolean',
        ]);

        // Validações adicionais
        if ($validated['tipo_desconto'] === 'percentual' && $validated['valor_desconto'] > 100) {
            return back()->withErrors(['valor_desconto' => 'O desconto percentual não pode ser maior que 100%.']);
        }

        $validated['codigo'] = strtoupper($validated['codigo']);

        $cupom->update($validated);

        return redirect()->route('admin.cupons.index')
                        ->with('success', 'Cupom atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Cupom $cupom)
    {
        $cupom->delete();

        return redirect()->route('admin.cupons.index')
                        ->with('success', 'Cupom removido com sucesso!');
    }

    /**
     * Toggle cupom status
     */
    public function toggleStatus(Cupom $cupom)
    {
        $cupom->update(['ativo' => !$cupom->ativo]);

        $status = $cupom->ativo ? 'ativado' : 'desativado';

        return redirect()->route('admin.cupons.index')
                        ->with('success', "Cupom {$status} com sucesso!");
    }
}
