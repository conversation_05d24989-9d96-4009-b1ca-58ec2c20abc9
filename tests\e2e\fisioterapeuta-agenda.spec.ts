import { test, expect } from '@playwright/test';
import { 
  ensureFisioterapeutaAuthenticated,
  navigateToFisioterapeutaPage,
  waitForPageLoad,
  waitForNotification,
  checkResponsiveness
} from './helpers/fisioterapeuta-helpers';

test.describe('Fisioterapeuta - Agenda e Agendamentos', () => {
  
  test.beforeEach(async ({ page }) => {
    await ensureFisioterapeutaAuthenticated(page);
    await navigateToFisioterapeutaPage(page, 'agenda');
  });

  test('deve carregar página de agenda corretamente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Verificar se o título da página está correto
    await expect(page.locator('h1, h2')).toContainText(/agenda|agendamentos/i);
    
    // Verificar se há um calendário ou lista de agendamentos
    const calendar = page.locator('.calendar, [data-testid="calendar"], .agenda-view');
    const agendamentosList = page.locator('.agendamentos-list, [data-testid="agendamentos-list"]');
    
    const hasCalendar = await calendar.count() > 0;
    const hasAgendamentosList = await agendamentosList.count() > 0;
    
    expect(hasCalendar || hasAgendamentosList).toBeTruthy();
  });

  test('deve exibir filtros de agenda', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar filtros de data
    const dateFilters = page.locator('input[type="date"], input[name*="data"], [data-testid="date-filter"]');
    
    if (await dateFilters.count() > 0) {
      await expect(dateFilters.first()).toBeVisible();
    }
    
    // Procurar filtros de status
    const statusFilters = page.locator('select[name*="status"], [data-testid="status-filter"]');
    
    if (await statusFilters.count() > 0) {
      await expect(statusFilters.first()).toBeVisible();
    }
  });

  test('deve permitir filtrar agendamentos por data', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar campo de data
    const dateInput = page.locator('input[type="date"], input[name*="data"]');
    
    if (await dateInput.count() > 0) {
      // Definir data para hoje
      const today = new Date().toISOString().split('T')[0];
      await dateInput.fill(today);
      
      // Aguardar atualização da lista
      await page.waitForTimeout(1000);
      
      // Verificar se a página foi atualizada
      await expect(page.locator('body')).toBeVisible();
    }
  });

  test('deve permitir filtrar agendamentos por status', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar filtro de status
    const statusFilter = page.locator('select[name*="status"], [data-testid="status-filter"]');
    
    if (await statusFilter.count() > 0) {
      // Testar diferentes status
      const statusOptions = ['agendado', 'confirmado', 'em_andamento', 'concluido', 'cancelado'];
      
      for (const status of statusOptions) {
        await statusFilter.selectOption(status);
        await page.waitForTimeout(1000);
        
        // Verificar se a página foi atualizada
        await expect(page.locator('body')).toBeVisible();
      }
    }
  });

  test('deve permitir buscar agendamentos por nome do paciente', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar campo de busca
    const searchInput = page.locator('input[name="search"], input[placeholder*="buscar"], input[placeholder*="paciente"]');
    
    if (await searchInput.count() > 0) {
      await searchInput.fill('João');
      await page.keyboard.press('Enter');
      
      // Aguardar resultados
      await page.waitForTimeout(1000);
      
      // Verificar se a busca foi executada
      await expect(page.locator('body')).toBeVisible();
    }
  });

  test('deve exibir detalhes dos agendamentos', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar agendamentos na lista
    const agendamentos = page.locator('.agendamento-item, .appointment-item, [data-testid="agendamento"]');
    
    if (await agendamentos.count() > 0) {
      const firstAgendamento = agendamentos.first();
      
      // Verificar se há informações básicas do agendamento
      await expect(firstAgendamento).toBeVisible();
      
      // Procurar informações como data, hora, paciente, status
      const agendamentoText = await firstAgendamento.textContent();
      
      // Verificar se contém pelo menos algumas informações esperadas
      const hasTimeInfo = /\d{2}:\d{2}/.test(agendamentoText || '');
      const hasDateInfo = /\d{2}\/\d{2}/.test(agendamentoText || '');
      
      expect(hasTimeInfo || hasDateInfo).toBeTruthy();
    }
  });

  test('deve permitir aceitar agendamento', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar agendamentos pendentes
    const agendamentos = page.locator('.agendamento-item, .appointment-item, [data-testid="agendamento"]');
    
    if (await agendamentos.count() > 0) {
      // Procurar botão de aceitar
      const aceitarButton = page.locator('button:has-text("Aceitar"), button:has-text("Confirmar")');
      
      if (await aceitarButton.count() > 0) {
        await aceitarButton.first().click();
        
        // Aguardar confirmação se houver
        const confirmButton = page.locator('button:has-text("Sim"), button:has-text("Confirmar")');
        if (await confirmButton.count() > 0) {
          await confirmButton.click();
        }
        
        // Aguardar notificação
        await waitForNotification(page);
      }
    }
  });

  test('deve permitir recusar agendamento', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar agendamentos pendentes
    const agendamentos = page.locator('.agendamento-item, .appointment-item, [data-testid="agendamento"]');
    
    if (await agendamentos.count() > 0) {
      // Procurar botão de recusar
      const recusarButton = page.locator('button:has-text("Recusar"), button:has-text("Rejeitar")');
      
      if (await recusarButton.count() > 0) {
        await recusarButton.first().click();
        
        // Preencher motivo se solicitado
        const motivoField = page.locator('textarea[name="motivo"], input[name="motivo"]');
        if (await motivoField.count() > 0) {
          await motivoField.fill('Conflito de horário');
        }
        
        // Confirmar recusa
        const confirmButton = page.locator('button:has-text("Confirmar"), button:has-text("Recusar")');
        if (await confirmButton.count() > 0) {
          await confirmButton.click();
        }
        
        // Aguardar notificação
        await waitForNotification(page);
      }
    }
  });

  test('deve permitir reagendar agendamento', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar agendamentos que podem ser reagendados
    const reagendarButton = page.locator('button:has-text("Reagendar"), button:has-text("Alterar Data")');
    
    if (await reagendarButton.count() > 0) {
      await reagendarButton.first().click();
      
      // Preencher nova data/hora
      const dataHoraField = page.locator('input[name="data_hora"], input[type="datetime-local"]');
      if (await dataHoraField.count() > 0) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowString = tomorrow.toISOString().slice(0, 16);
        
        await dataHoraField.fill(tomorrowString);
      }
      
      // Preencher motivo
      const motivoField = page.locator('textarea[name="motivo"], input[name="motivo"]');
      if (await motivoField.count() > 0) {
        await motivoField.fill('Reagendamento solicitado pelo fisioterapeuta');
      }
      
      // Confirmar reagendamento
      const confirmButton = page.locator('button:has-text("Confirmar"), button:has-text("Reagendar")');
      if (await confirmButton.count() > 0) {
        await confirmButton.click();
      }
      
      // Aguardar notificação
      await waitForNotification(page);
    }
  });

  test('deve permitir iniciar sessão', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar agendamentos confirmados que podem ser iniciados
    const iniciarButton = page.locator('button:has-text("Iniciar"), button:has-text("Começar Sessão")');
    
    if (await iniciarButton.count() > 0) {
      await iniciarButton.first().click();
      
      // Aguardar confirmação
      const confirmButton = page.locator('button:has-text("Sim"), button:has-text("Iniciar")');
      if (await confirmButton.count() > 0) {
        await confirmButton.click();
      }
      
      // Aguardar notificação
      await waitForNotification(page);
    }
  });

  test('deve permitir finalizar sessão', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar sessões em andamento
    const finalizarButton = page.locator('button:has-text("Finalizar"), button:has-text("Concluir")');
    
    if (await finalizarButton.count() > 0) {
      await finalizarButton.first().click();
      
      // Preencher observações se solicitado
      const observacoesField = page.locator('textarea[name="observacoes"], textarea[name="notes"]');
      if (await observacoesField.count() > 0) {
        await observacoesField.fill('Sessão concluída com sucesso. Paciente apresentou boa evolução.');
      }
      
      // Confirmar finalização
      const confirmButton = page.locator('button:has-text("Finalizar"), button:has-text("Concluir")');
      if (await confirmButton.count() > 0) {
        await confirmButton.click();
      }
      
      // Aguardar notificação
      await waitForNotification(page);
    }
  });

  test('deve permitir cancelar agendamento', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar botão de cancelar
    const cancelarButton = page.locator('button:has-text("Cancelar"), button:has-text("Cancelar Agendamento")');
    
    if (await cancelarButton.count() > 0) {
      await cancelarButton.first().click();
      
      // Preencher motivo do cancelamento
      const motivoField = page.locator('textarea[name="motivo"], input[name="motivo"]');
      if (await motivoField.count() > 0) {
        await motivoField.fill('Cancelamento por motivo de força maior');
      }
      
      // Confirmar cancelamento
      const confirmButton = page.locator('button:has-text("Confirmar"), button:has-text("Cancelar")');
      if (await confirmButton.count() > 0) {
        await confirmButton.click();
      }
      
      // Aguardar notificação
      await waitForNotification(page);
    }
  });

  test('deve exibir detalhes do agendamento ao clicar', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar agendamentos clicáveis
    const agendamentos = page.locator('.agendamento-item, .appointment-item, [data-testid="agendamento"]');
    
    if (await agendamentos.count() > 0) {
      await agendamentos.first().click();
      
      // Verificar se abriu modal ou navegou para página de detalhes
      const modal = page.locator('.modal, .dialog, [role="dialog"]');
      const detailsPage = page.locator('h1:has-text("Detalhes"), h2:has-text("Agendamento")');
      
      const hasModal = await modal.count() > 0;
      const hasDetailsPage = await detailsPage.count() > 0;
      
      expect(hasModal || hasDetailsPage).toBeTruthy();
    }
  });

  test('deve permitir navegação entre diferentes visualizações', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar botões de visualização (lista, calendário, etc.)
    const viewButtons = page.locator('button:has-text("Lista"), button:has-text("Calendário"), button:has-text("Semana"), button:has-text("Mês")');
    
    if (await viewButtons.count() > 0) {
      for (let i = 0; i < await viewButtons.count(); i++) {
        await viewButtons.nth(i).click();
        await page.waitForTimeout(1000);
        
        // Verificar se a visualização mudou
        await expect(page.locator('body')).toBeVisible();
      }
    }
  });

  test('deve ser responsivo em diferentes tamanhos de tela', async ({ page }) => {
    await checkResponsiveness(page);
    
    // Verificar se elementos principais ainda estão visíveis em mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await waitForPageLoad(page);
    
    await expect(page.locator('h1, h2')).toBeVisible();
  });

  test('deve exibir indicadores de status dos agendamentos', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar indicadores visuais de status
    const statusIndicators = page.locator('.status, .badge, .chip, [data-testid="status"]');
    
    if (await statusIndicators.count() > 0) {
      await expect(statusIndicators.first()).toBeVisible();
    }
  });

  test('deve permitir exportar agenda', async ({ page }) => {
    await waitForPageLoad(page);
    
    // Procurar botão de exportar
    const exportButton = page.locator('button:has-text("Exportar"), button:has-text("Download")');
    
    if (await exportButton.count() > 0) {
      await exportButton.click();
      
      // Aguardar download ou modal de exportação
      await page.waitForTimeout(2000);
    }
  });
});
