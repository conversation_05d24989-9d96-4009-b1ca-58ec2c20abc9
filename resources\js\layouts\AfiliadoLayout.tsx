import React from 'react';
import AppLayout from '@/layouts/app-layout';

interface AfiliadoLayoutProps {
    children: React.ReactNode;
}

export default function AfiliadoLayout({ children }: AfiliadoLayoutProps) {
    const breadcrumbs = [
        { title: 'Afiliado', href: '/afiliado/dashboard' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            {children}
        </AppLayout>
    );
}
