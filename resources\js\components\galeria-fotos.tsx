import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Camera } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface ImagemEstabelecimento {
    id: number;
    nome_arquivo: string;
    nome_original: string;
    descricao?: string;
    principal: boolean;
    url: string;
}

interface GaleriaFotosProps {
    imagens: ImagemEstabelecimento[];
    nomeEstabelecimento: string;
    autoPlay?: boolean;
    autoPlayInterval?: number;
    showIndicators?: boolean;
    showNavigation?: boolean;
    className?: string;
}

export default function GaleriaFotos({
    imagens,
    nomeEstabelecimento,
    autoPlay = false,
    autoPlayInterval = 5000,
    showIndicators = true,
    showNavigation = true,
    className = ''
}: GaleriaFotosProps) {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isHovered, setIsHovered] = useState(false);

    useEffect(() => {
        if (!autoPlay || isHovered || imagens.length <= 1) return;

        const interval = setInterval(() => {
            setCurrentIndex((prevIndex) => 
                prevIndex === imagens.length - 1 ? 0 : prevIndex + 1
            );
        }, autoPlayInterval);

        return () => clearInterval(interval);
    }, [autoPlay, autoPlayInterval, isHovered, imagens.length]);

    const goToPrevious = () => {
        setCurrentIndex(currentIndex === 0 ? imagens.length - 1 : currentIndex - 1);
    };

    const goToNext = () => {
        setCurrentIndex(currentIndex === imagens.length - 1 ? 0 : currentIndex + 1);
    };

    const goToSlide = (index: number) => {
        setCurrentIndex(index);
    };

    if (!imagens || imagens.length === 0) {
        return (
            <Card className="w-full">
                <CardContent className="flex items-center justify-center h-64 bg-muted">
                    <div className="text-center">
                        <Camera className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                        <p className="text-muted-foreground">Nenhuma foto disponível</p>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className={`w-full ${className}`}>
            <Card className="relative overflow-hidden">
                <CardContent className="p-0">
                    {/* Container principal da imagem */}
                    <div 
                        className="relative h-64 md:h-80 lg:h-96 overflow-hidden group"
                        onMouseEnter={() => setIsHovered(true)}
                        onMouseLeave={() => setIsHovered(false)}
                    >
                        {/* Imagens */}
                        <div 
                            className="flex transition-transform duration-500 ease-in-out h-full"
                            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
                        >
                            {imagens.map((imagem, index) => (
                                <div key={imagem.id} className="w-full h-full flex-shrink-0 relative">
                                    <img
                                        src={imagem.url}
                                        alt={imagem.descricao || `Foto ${index + 1} de ${nomeEstabelecimento}`}
                                        className="w-full h-full object-cover"
                                        loading={index === 0 ? "eager" : "lazy"}
                                    />
                                    {/* Overlay com descrição */}
                                    {imagem.descricao && (
                                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                                            <p className="text-white text-sm md:text-base">
                                                {imagem.descricao}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>

                        {/* Setas de navegação */}
                        {showNavigation && imagens.length > 1 && (
                            <>
                                <Button
                                    variant="outline"
                                    size="icon"
                                    className="absolute left-2 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm border-border hover:bg-background/90 transition-opacity duration-300 opacity-0 group-hover:opacity-100"
                                    onClick={goToPrevious}
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    <span className="sr-only">Foto anterior</span>
                                </Button>
                                <Button
                                    variant="outline"
                                    size="icon"
                                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm border-border hover:bg-background/90 transition-opacity duration-300 opacity-0 group-hover:opacity-100"
                                    onClick={goToNext}
                                >
                                    <ChevronRight className="h-4 w-4" />
                                    <span className="sr-only">Próxima foto</span>
                                </Button>
                            </>
                        )}

                        {/* Indicadores */}
                        {showIndicators && imagens.length > 1 && (
                            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
                                {imagens.map((_, index) => (
                                    <button
                                        key={index}
                                        className={`w-2 h-2 rounded-full transition-all duration-300 ${
                                            index === currentIndex 
                                                ? 'bg-white w-6' 
                                                : 'bg-white/50 hover:bg-white/75'
                                        }`}
                                        onClick={() => goToSlide(index)}
                                        aria-label={`Ir para foto ${index + 1}`}
                                    />
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Miniaturas */}
                    {imagens.length > 1 && (
                        <div className="p-4">
                            <div className="grid grid-cols-4 md:grid-cols-6 gap-2">
                                {imagens.map((imagem, index) => (
                                    <button
                                        key={imagem.id}
                                        className={`relative aspect-video overflow-hidden rounded border-2 transition-all duration-300 ${
                                            index === currentIndex 
                                                ? 'border-primary ring-2 ring-primary/20' 
                                                : 'border-border hover:border-primary/50'
                                        }`}
                                        onClick={() => goToSlide(index)}
                                    >
                                        <img
                                            src={imagem.url}
                                            alt={imagem.descricao || `Miniatura ${index + 1}`}
                                            className="w-full h-full object-cover"
                                            loading="lazy"
                                        />
                                        {index === currentIndex && (
                                            <div className="absolute inset-0 bg-primary/20" />
                                        )}
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Contador de imagens */}
                    {imagens.length > 1 && (
                        <div className="px-4 pb-4 text-center">
                            <span className="text-sm text-muted-foreground">
                                {currentIndex + 1} de {imagens.length} fotos
                            </span>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
