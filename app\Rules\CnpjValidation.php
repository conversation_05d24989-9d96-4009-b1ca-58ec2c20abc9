<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CnpjValidation implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->isValidCnpj($value)) {
            $fail('O campo :attribute deve ser um CNPJ válido.');
        }
    }

    /**
     * Valida se o CNPJ é válido
     */
    private function isValidCnpj(string $cnpj): bool
    {
        // Remove caracteres não numéricos
        $cnpj = preg_replace('/\D/', '', $cnpj);

        // Verifica se tem 14 dígitos
        if (strlen($cnpj) !== 14) {
            return false;
        }

        // Verifica se todos os dígitos são iguais
        if (preg_match('/^(\d)\1{13}$/', $cnpj)) {
            return false;
        }

        // Validação do primeiro dígito verificador
        $sum = 0;
        $weight = 2;
        for ($i = 11; $i >= 0; $i--) {
            $sum += intval($cnpj[$i]) * $weight;
            $weight = $weight === 9 ? 2 : $weight + 1;
        }
        $remainder = $sum % 11;
        $firstDigit = $remainder < 2 ? 0 : 11 - $remainder;
        if ($firstDigit !== intval($cnpj[12])) {
            return false;
        }

        // Validação do segundo dígito verificador
        $sum = 0;
        $weight = 2;
        for ($i = 12; $i >= 0; $i--) {
            $sum += intval($cnpj[$i]) * $weight;
            $weight = $weight === 9 ? 2 : $weight + 1;
        }
        $remainder = $sum % 11;
        $secondDigit = $remainder < 2 ? 0 : 11 - $remainder;
        if ($secondDigit !== intval($cnpj[13])) {
            return false;
        }

        return true;
    }
}
