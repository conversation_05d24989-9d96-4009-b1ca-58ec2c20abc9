<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Plano;
use Illuminate\Database\Seeder;

class ProductionSeeder extends Seeder
{
    /**
     * Seed the application's database for production.
     */
    public function run(): void
    {
        // Criar usuário admin padrão se não existir
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::create([
                'name' => 'Administrador F4 Fisio',
                'email' => '<EMAIL>',
                'password' => bcrypt('admin123'),
                'role' => 'admin',
                'active' => true,
                'has_subscription' => true,
            ]);
        }

        // Criar planos padrão se não existirem
        if (Plano::count() === 0) {
            Plano::create([
                'name' => 'Plano Mensal',
                'description' => 'Acesso completo à plataforma com sessões ilimitadas',
                'price' => 64.00,
                'sessions_per_month' => null, // Ilimitado
                'active' => true,
                'features' => [
                    'Sessões ilimitadas',
                    'Agendamento online',
                    'Relatórios detalhados',
                    'Suporte prioritário'
                ]
            ]);

            Plano::create([
                'name' => 'Sessão Avulsa',
                'description' => 'Pagamento por sessão individual',
                'price' => 80.00,
                'sessions_per_month' => 1,
                'active' => true,
                'features' => [
                    'Pagamento por sessão',
                    'Agendamento online',
                    'Relatório da sessão'
                ]
            ]);

            Plano::create([
                'name' => 'Plano Empresarial',
                'description' => 'Solução personalizada para empresas e grupos',
                'price' => 0.00, // Preço sob consulta
                'sessions_per_month' => null,
                'active' => true,
                'features' => [
                    'Sessões ilimitadas',
                    'Múltiplos usuários',
                    'Relatórios avançados',
                    'Suporte dedicado',
                    'Integração personalizada'
                ]
            ]);
        }

        $this->command->info('✅ Dados de produção criados com sucesso!');
        $this->command->info('🔑 Credenciais de acesso:');
        $this->command->info('   Admin: <EMAIL> / admin123');
        $this->command->info('📋 Planos criados:');
        $this->command->info('   - Plano Mensal (R$ 64,00)');
        $this->command->info('   - Sessão Avulsa (R$ 80,00)');
        $this->command->info('   - Plano Empresarial (Sob consulta)');
    }
}
