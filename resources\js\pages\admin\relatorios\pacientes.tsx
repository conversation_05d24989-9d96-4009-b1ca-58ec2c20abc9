import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { 
    ArrowLeft,
    Users, 
    UserPlus,
    UserMinus,
    Activity,
    Calendar,
    Download,
    Filter,
    RefreshCw,
    TrendingUp,
    TrendingDown
} from 'lucide-react';
import { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

interface Stats {
    total_pacientes: number;
    pacientes_ativos: number;
    pacientes_inativos: number;
    novos_pacientes: number;
    taxa_retencao: number;
    sessoes_por_paciente: number;
    idade_media: number;
    distribuicao_genero: {
        masculino: number;
        feminino: number;
        outros: number;
    };
}

interface EvolucaoPacientes {
    data: string;
    novos: number;
    ativos: number;
    inativos: number;
}

interface DistribuicaoIdade {
    faixa: string;
    quantidade: number;
    percentual: number;
}

interface AtividadePacientes {
    paciente: string;
    total_sessoes: number;
    ultima_sessao: string;
    status: string;
}

interface Props {
    stats: Stats;
    evolucaoPacientes: EvolucaoPacientes[];
    distribuicaoIdade: DistribuicaoIdade[];
    atividadePacientes: AtividadePacientes[];
    filtros: {
        data_inicio?: string;
        data_fim?: string;
    };
}

const COLORS = ['#3b82f6', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6'];

export default function RelatorioPacientes({ 
    stats, 
    evolucaoPacientes, 
    distribuicaoIdade, 
    atividadePacientes, 
    filtros 
}: Props) {
    const [dataInicio, setDataInicio] = useState(filtros.data_inicio || '');
    const [dataFim, setDataFim] = useState(filtros.data_fim || '');
    const [isLoading, setIsLoading] = useState(false);

    const handleFilter = () => {
        setIsLoading(true);
        router.get(route('admin.relatorios.pacientes'), {
            data_inicio: dataInicio,
            data_fim: dataFim,
        }, {
            preserveState: true,
            onFinish: () => setIsLoading(false),
        });
    };

    const handleExport = (formato: string) => {
        const params = new URLSearchParams({
            tipo: 'pacientes',
            formato,
            data_inicio: dataInicio,
            data_fim: dataFim,
        });

        window.open(`${route('admin.relatorios.export')}?${params.toString()}`, '_blank');
    };

    const formatNumber = (value: number) => {
        return new Intl.NumberFormat('pt-BR').format(value);
    };

    const formatPercentage = (value: number) => {
        return `${value.toFixed(1)}%`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            ativo: { variant: 'default' as const, color: 'text-green-600' },
            inativo: { variant: 'secondary' as const, color: 'text-gray-600' },
            suspenso: { variant: 'destructive' as const, color: 'text-red-600' },
        };
        
        const config = variants[status as keyof typeof variants] || variants.ativo;
        
        return (
            <Badge variant={config.variant} className={config.color}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    // Dados para gráfico de gênero
    const generoData = [
        { genero: 'Masculino', quantidade: stats.distribuicao_genero.masculino },
        { genero: 'Feminino', quantidade: stats.distribuicao_genero.feminino },
        { genero: 'Outros', quantidade: stats.distribuicao_genero.outros },
    ].filter(item => item.quantidade > 0);

    return (
        <AppLayout>
            <Head title="Relatório de Pacientes" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href={route('admin.relatorios.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Relatório de Pacientes</h1>
                            <p className="text-muted-foreground">
                                Análise detalhada dos dados dos pacientes
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            onClick={() => handleExport('csv')}
                            className="gap-2"
                        >
                            <Download className="h-4 w-4" />
                            Exportar CSV
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => handleExport('excel')}
                            className="gap-2"
                        >
                            <Download className="h-4 w-4" />
                            Exportar Excel
                        </Button>
                    </div>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="data_inicio">Data Início</Label>
                                <Input
                                    id="data_inicio"
                                    type="date"
                                    value={dataInicio}
                                    onChange={(e) => setDataInicio(e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="data_fim">Data Fim</Label>
                                <Input
                                    id="data_fim"
                                    type="date"
                                    value={dataFim}
                                    onChange={(e) => setDataFim(e.target.value)}
                                />
                            </div>
                            <div className="flex items-end">
                                <Button 
                                    onClick={handleFilter} 
                                    disabled={isLoading}
                                    className="w-full gap-2"
                                >
                                    {isLoading ? (
                                        <RefreshCw className="h-4 w-4 animate-spin" />
                                    ) : (
                                        <Filter className="h-4 w-4" />
                                    )}
                                    Aplicar Filtros
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Métricas Principais */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Pacientes</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(stats.total_pacientes)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pacientes Ativos</CardTitle>
                            <Activity className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">
                                {formatNumber(stats.pacientes_ativos)}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Novos Pacientes</CardTitle>
                            <UserPlus className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-600">
                                {formatNumber(stats.novos_pacientes)}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Taxa de Retenção</CardTitle>
                            <TrendingUp className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">
                                {formatPercentage(stats.taxa_retencao)}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pacientes Inativos</CardTitle>
                            <UserMinus className="h-4 w-4 text-red-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">
                                {formatNumber(stats.pacientes_inativos)}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Sessões por Paciente</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {stats.sessoes_por_paciente.toFixed(1)}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Idade Média</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {stats.idade_media.toFixed(0)} anos
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Gráficos */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Evolução de Pacientes</CardTitle>
                            <CardDescription>Novos pacientes e atividade ao longo do tempo</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <LineChart data={evolucaoPacientes}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="data" />
                                    <YAxis />
                                    <Tooltip />
                                    <Line 
                                        type="monotone" 
                                        dataKey="novos" 
                                        stroke="#3b82f6" 
                                        strokeWidth={2}
                                        name="Novos"
                                    />
                                    <Line 
                                        type="monotone" 
                                        dataKey="ativos" 
                                        stroke="#22c55e" 
                                        strokeWidth={2}
                                        name="Ativos"
                                    />
                                    <Line 
                                        type="monotone" 
                                        dataKey="inativos" 
                                        stroke="#ef4444" 
                                        strokeWidth={2}
                                        name="Inativos"
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Distribuição por Gênero</CardTitle>
                            <CardDescription>Proporção de pacientes por gênero</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <PieChart>
                                    <Pie
                                        data={generoData}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ genero, quantidade }) => `${genero} (${quantidade})`}
                                        outerRadius={80}
                                        fill="#8884d8"
                                        dataKey="quantidade"
                                    >
                                        {generoData.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                        ))}
                                    </Pie>
                                    <Tooltip />
                                </PieChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Distribuição por Idade */}
                <Card>
                    <CardHeader>
                        <CardTitle>Distribuição por Faixa Etária</CardTitle>
                        <CardDescription>Quantidade de pacientes por faixa de idade</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={distribuicaoIdade}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="faixa" />
                                <YAxis />
                                <Tooltip 
                                    formatter={(value, name) => [
                                        formatNumber(Number(value)), 
                                        'Quantidade'
                                    ]}
                                />
                                <Bar dataKey="quantidade" fill="#3b82f6" />
                            </BarChart>
                        </ResponsiveContainer>
                    </CardContent>
                </Card>

                {/* Atividade dos Pacientes */}
                <Card>
                    <CardHeader>
                        <CardTitle>Atividade dos Pacientes</CardTitle>
                        <CardDescription>Pacientes mais ativos no período</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {atividadePacientes.map((paciente, index) => (
                                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex items-center gap-4">
                                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium">
                                            {index + 1}
                                        </div>
                                        <div>
                                            <p className="font-medium">{paciente.paciente}</p>
                                            <p className="text-sm text-muted-foreground">
                                                Última sessão: {formatDate(paciente.ultima_sessao)}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-4">
                                        <div className="text-right">
                                            <p className="font-medium">{formatNumber(paciente.total_sessoes)} sessões</p>
                                        </div>
                                        {getStatusBadge(paciente.status)}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
