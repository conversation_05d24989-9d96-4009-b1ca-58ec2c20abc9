<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pagamentos_comissao', function (Blueprint $table) {
            $table->id();
            $table->foreignId('afiliado_id')->constrained('afiliados')->onDelete('cascade');
            $table->decimal('valor_solicitado', 10, 2); // Valor que o afiliado quer receber
            $table->enum('status', ['pendente', 'aprovado', 'pago', 'rejeitado'])->default('pendente');
            $table->timestamp('data_solicitacao')->useCurrent(); // Data da solicitação
            $table->timestamp('data_aprovacao')->nullable(); // Data da aprovação
            $table->timestamp('data_pagamento')->nullable(); // Data do pagamento
            $table->foreignId('aprovado_por')->nullable()->constrained('users')->onDelete('set null'); // Admin que aprovou
            $table->enum('metodo_pagamento', ['pix', 'transferencia_bancaria']); // Método de pagamento
            $table->json('dados_pagamento'); // Dados para pagamento (chave PIX, dados bancários, etc)
            $table->text('observacoes_admin')->nullable(); // Observações do admin
            $table->text('observacoes_afiliado')->nullable(); // Observações do afiliado
            $table->string('comprovante_pagamento')->nullable(); // Caminho do comprovante
            $table->timestamps();

            // Índices para performance
            $table->index(['afiliado_id', 'status']);
            $table->index(['status', 'data_solicitacao']);
            $table->index('data_solicitacao');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pagamentos_comissao');
    }
};
