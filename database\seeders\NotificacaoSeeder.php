<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Notificacao;
use App\Models\User;
use Carbon\Carbon;

class NotificacaoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        if ($users->isEmpty()) {
            return;
        }

        $tipos = ['novo_agendamento', 'agendamento_confirmado', 'agendamento_cancelado', 'sessao_iniciada', 'sessao_finalizada', 'lembrete_sessao'];
        $notificacoes = [];

        foreach ($users as $user) {
            // Criar algumas notificações para cada usuário
            for ($i = 0; $i < rand(3, 8); $i++) {
                $tipo = $tipos[array_rand($tipos)];
                $lida = rand(0, 1) === 1;
                $dataEnvio = Carbon::now()->subDays(rand(0, 30));

                $notificacoes[] = [
                    'user_id' => $user->id,
                    'agendamento_id' => null, // Pode ser associado a um agendamento específico
                    'tipo' => $tipo,
                    'titulo' => $this->getTituloByTipo($tipo, $user->role),
                    'mensagem' => $this->getMensagemByTipo($tipo, $user->role),
                    'lida' => $lida,
                    'data_envio' => $dataEnvio,
                    'data_leitura' => $lida ? $dataEnvio->addMinutes(rand(5, 1440)) : null,
                    'created_at' => $dataEnvio,
                    'updated_at' => $lida ? $dataEnvio->addMinutes(rand(5, 1440)) : $dataEnvio,
                ];
            }
        }

        Notificacao::insert($notificacoes);
    }

    private function getTituloByTipo(string $tipo, string $userRole): string
    {
        $titulos = [
            'novo_agendamento' => [
                'paciente' => 'Novo Agendamento Criado',
                'fisioterapeuta' => 'Nova Consulta Agendada',
                'admin' => 'Novo Agendamento no Sistema'
            ],
            'agendamento_confirmado' => [
                'paciente' => 'Agendamento Confirmado',
                'fisioterapeuta' => 'Consulta Confirmada',
                'admin' => 'Agendamento Confirmado'
            ],
            'agendamento_cancelado' => [
                'paciente' => 'Consulta Cancelada',
                'fisioterapeuta' => 'Consulta Cancelada pelo Paciente',
                'admin' => 'Cancelamento de Consulta'
            ],
            'sessao_iniciada' => [
                'paciente' => 'Sessão Iniciada',
                'fisioterapeuta' => 'Sessão em Andamento',
                'admin' => 'Sessão Iniciada'
            ],
            'sessao_finalizada' => [
                'paciente' => 'Sessão Finalizada',
                'fisioterapeuta' => 'Sessão Concluída',
                'admin' => 'Sessão Finalizada'
            ],
            'lembrete_sessao' => [
                'paciente' => 'Lembrete de Consulta',
                'fisioterapeuta' => 'Lembrete de Atendimento',
                'admin' => 'Lembrete de Sessão'
            ]
        ];

        return $titulos[$tipo][$userRole] ?? $titulos[$tipo]['admin'] ?? 'Notificação';
    }

    private function getMensagemByTipo(string $tipo, string $userRole): string
    {
        $mensagens = [
            'novo_agendamento' => [
                'paciente' => 'Seu agendamento foi criado com sucesso.',
                'fisioterapeuta' => 'Você tem uma nova consulta agendada.',
                'admin' => 'Novo agendamento realizado no sistema.'
            ],
            'agendamento_confirmado' => [
                'paciente' => 'Sua consulta foi confirmada.',
                'fisioterapeuta' => 'A consulta foi confirmada pelo paciente.',
                'admin' => 'Agendamento confirmado no sistema.'
            ],
            'agendamento_cancelado' => [
                'paciente' => 'Sua consulta foi cancelada.',
                'fisioterapeuta' => 'O paciente cancelou a consulta.',
                'admin' => 'Consulta cancelada - verificar reagendamento.'
            ],
            'sessao_iniciada' => [
                'paciente' => 'Sua sessão foi iniciada.',
                'fisioterapeuta' => 'Sessão iniciada com sucesso.',
                'admin' => 'Sessão iniciada no sistema.'
            ],
            'sessao_finalizada' => [
                'paciente' => 'Sua sessão foi finalizada.',
                'fisioterapeuta' => 'Sessão concluída com sucesso.',
                'admin' => 'Sessão finalizada no sistema.'
            ],
            'lembrete_sessao' => [
                'paciente' => 'Lembre-se: você tem consulta hoje.',
                'fisioterapeuta' => 'Lembrete: você tem atendimento em breve.',
                'admin' => 'Lembrete de sessão agendada.'
            ]
        ];

        return $mensagens[$tipo][$userRole] ?? $mensagens[$tipo]['admin'] ?? 'Você tem uma nova notificação.';
    }
}
