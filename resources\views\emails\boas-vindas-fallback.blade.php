@extends('emails.layout')

@section('content')
<div style="text-align: center; margin-bottom: 30px;">
    <h1 style="color: #1f2937; font-size: 24px; margin-bottom: 10px;">
        Bem-vindo(a) à F4 Fisio! 🎉
    </h1>
</div>

<p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 16px;">
    Olá <strong>{{ $nome }}</strong>,
</p>

<p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 16px;">
    É com grande alegria que damos as boas-vindas a você como novo 
    @if($tipoUsuario === 'fisioterapeuta')
        fisioterapeuta
    @elseif($tipoUsuario === 'empresa')
        estabelecimento
    @else
        paciente
    @endif
    da F4 Fisio!
</p>

<p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 24px;">
    @if($tipoUsuario === 'fisioterapeuta')
        Agora você pode gerenciar seus agendamentos, atender pacientes e expandir sua prática profissional através da nossa plataforma.
    @elseif($tipoUsuario === 'empresa')
        Agora você pode gerenciar seu estabelecimento, receber agendamentos e aumentar sua visibilidade na plataforma.
    @else
        Agora você pode agendar consultas, encontrar profissionais qualificados e cuidar da sua saúde de forma prática e segura.
    @endif
</p>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ $loginUrl }}" 
       style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
        Acessar Minha Conta
    </a>
</div>

<hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">

<h2 style="color: #1f2937; font-size: 18px; margin-bottom: 16px;">
    Próximos Passos
</h2>

@if($tipoUsuario === 'fisioterapeuta')
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 8px;">
        1. Complete seu perfil profissional
    </p>
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 8px;">
        2. Configure sua disponibilidade
    </p>
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 8px;">
        3. Defina seus preços e serviços
    </p>
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 16px;">
        4. Comece a receber agendamentos
    </p>
@elseif($tipoUsuario === 'empresa')
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 8px;">
        1. Complete as informações do seu estabelecimento
    </p>
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 8px;">
        2. Adicione fotos e descrição dos serviços
    </p>
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 8px;">
        3. Configure horários de funcionamento
    </p>
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 16px;">
        4. Ative sua visibilidade nas buscas
    </p>
@else
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 8px;">
        1. Complete seu perfil
    </p>
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 8px;">
        2. Explore profissionais na sua região
    </p>
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 8px;">
        3. Agende sua primeira consulta
    </p>
    <p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 16px;">
        4. Avalie os serviços recebidos
    </p>
@endif

<hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">

<div style="background-color: #f9fafb; padding: 16px; border-radius: 6px; margin-bottom: 16px;">
    <p style="color: #374151; font-size: 16px; margin-bottom: 8px;">
        <strong>Dados da sua conta:</strong>
    </p>
    <p style="color: #6b7280; font-size: 14px; margin: 0;">
        Email: {{ $email }}
    </p>
</div>

<p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 16px;">
    Se você tiver alguma dúvida ou precisar de ajuda, nossa equipe de suporte está sempre disponível para ajudá-lo.
</p>

<p style="color: #374151; font-size: 16px; line-height: 24px; margin-bottom: 16px;">
    Mais uma vez, seja muito bem-vindo(a)!
</p>

<p style="color: #374151; font-size: 16px; line-height: 24px; margin-top: 24px;">
    Atenciosamente,<br>
    <strong>Equipe F4 Fisio</strong>
</p>
@endsection
