import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PublicLayout from '@/layouts/public-layout';
import { Download, Image, FileText, Video, Share2, Copy, CheckCircle } from 'lucide-react';
import { useState } from 'react';

export default function MateriaisDivulgacao() {
    const [copiedText, setCopiedText] = useState<string | null>(null);

    const banners = [
        {
            id: 1,
            nome: 'Banner Principal - Desktop',
            descricao: 'Banner principal para sites e blogs (1200x400px)',
            formato: 'PNG',
            tamanho: '2.1 MB',
            url: '/materiais/banner-principal-desktop.png',
            preview: '/materiais/previews/banner-principal-desktop-thumb.png'
        },
        {
            id: 2,
            nome: 'Banner Quadrado - Redes Sociais',
            descricao: 'Banner quadrado para Instagram e Facebook (1080x1080px)',
            formato: 'PNG',
            tamanho: '1.8 MB',
            url: '/materiais/banner-quadrado-redes.png',
            preview: '/materiais/previews/banner-quadrado-redes-thumb.png'
        },
        {
            id: 3,
            nome: 'Banner Stories',
            descricao: 'Banner vertical para Stories Instagram/WhatsApp (1080x1920px)',
            formato: 'PNG',
            tamanho: '2.5 MB',
            url: '/materiais/banner-stories.png',
            preview: '/materiais/previews/banner-stories-thumb.png'
        },
        {
            id: 4,
            nome: 'Banner Mobile',
            descricao: 'Banner otimizado para dispositivos móveis (800x600px)',
            formato: 'PNG',
            tamanho: '1.2 MB',
            url: '/materiais/banner-mobile.png',
            preview: '/materiais/previews/banner-mobile-thumb.png'
        }
    ];

    const textosPromocao = [
        {
            id: 1,
            titulo: 'Texto para WhatsApp - Apresentação',
            categoria: 'WhatsApp',
            texto: `🏥 *F4 FISIO - Fisioterapia Domiciliar*

Olá! 👋

Você sabia que pode ter fisioterapia profissional no conforto da sua casa?

✅ Atendimento domiciliar
✅ Fisioterapeutas qualificados
✅ Equipamentos profissionais
✅ Planos acessíveis a partir de R$ 14,80

🔗 Conheça nossos planos: [SEU_LINK_AFILIADO]

*Transforme sua recuperação em casa!* 🏠💪`
        },
        {
            id: 2,
            titulo: 'Post para Instagram - Benefícios',
            categoria: 'Instagram',
            texto: `🏠 FISIOTERAPIA EM CASA? SIM, É POSSÍVEL! 

Imagina ter um fisioterapeuta profissional na sua casa, com todos os equipamentos necessários? 

Com a F4 Fisio isso é realidade! 💪

✨ BENEFÍCIOS:
• Conforto do seu lar
• Horários flexíveis  
• Atendimento personalizado
• Equipamentos profissionais
• Planos a partir de R$ 14,80

👆 Link na bio para conhecer nossos planos!

#fisioterapia #fisioterapiadomiciliar #saude #bemestar #recuperacao #f4fisio`
        },
        {
            id: 3,
            titulo: 'E-mail Marketing - Apresentação',
            categoria: 'E-mail',
            texto: `Assunto: 🏠 Fisioterapia profissional na sua casa - Conheça a F4 Fisio

Olá!

Você já imaginou ter acesso a fisioterapia profissional sem sair de casa?

A F4 Fisio torna isso possível! Somos especialistas em atendimento domiciliar com:

• Fisioterapeutas qualificados e experientes
• Equipamentos profissionais levados até você
• Atendimento personalizado e humanizado
• Horários flexíveis que se adaptam à sua rotina

NOSSOS PLANOS:
🔹 Plano Busca: R$ 14,80/mês
🔹 Plano Pessoal: R$ 180,00/mês  
🔹 Plano Empresarial: R$ 640,00/mês

Clique aqui para conhecer todos os detalhes: [SEU_LINK_AFILIADO]

Transforme sua recuperação no conforto do seu lar!

Atenciosamente,
Equipe F4 Fisio`
        },
        {
            id: 4,
            titulo: 'Post LinkedIn - Profissional',
            categoria: 'LinkedIn',
            texto: `🏥 INOVAÇÃO EM FISIOTERAPIA: Atendimento Domiciliar Profissional

O mercado de saúde está evoluindo, e a fisioterapia domiciliar representa uma das principais tendências do setor.

A F4 Fisio está na vanguarda dessa transformação, oferecendo:

✅ Atendimento domiciliar especializado
✅ Fisioterapeutas qualificados
✅ Equipamentos de última geração
✅ Planos flexíveis para diferentes necessidades

Para empresas: nosso Plano Empresarial oferece cobertura completa para colaboradores, contribuindo para redução do absenteísmo e melhoria da qualidade de vida no trabalho.

Conheça nossas soluções: [SEU_LINK_AFILIADO]

#fisioterapia #saudeocupacional #inovacao #atendimentodomiciliar #bemestar`
        }
    ];

    const videos = [
        {
            id: 1,
            nome: 'Vídeo Institucional',
            descricao: 'Apresentação da F4 Fisio e serviços (2 min)',
            formato: 'MP4',
            tamanho: '45 MB',
            url: '/materiais/video-institucional.mp4',
            thumbnail: '/materiais/previews/video-institucional-thumb.jpg'
        },
        {
            id: 2,
            nome: 'Depoimento de Cliente',
            descricao: 'Depoimento real de cliente satisfeito (1 min)',
            formato: 'MP4',
            tamanho: '28 MB',
            url: '/materiais/depoimento-cliente.mp4',
            thumbnail: '/materiais/previews/depoimento-cliente-thumb.jpg'
        }
    ];

    const handleCopyText = (texto: string, id: number) => {
        navigator.clipboard.writeText(texto.replace('[SEU_LINK_AFILIADO]', 'https://f4fisio.com?ref=SEU_CODIGO'));
        setCopiedText(`texto-${id}`);
        setTimeout(() => setCopiedText(null), 2000);
    };

    const handleDownload = (url: string, nome: string) => {
        // Simular download - em produção, implementar download real
        const link = document.createElement('a');
        link.href = url;
        link.download = nome;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <PublicLayout
            title="Materiais de Divulgação - F4 Fisio"
            description="Baixe banners, textos e vídeos para divulgar os serviços da F4 Fisio como afiliado."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30 py-16">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h1 className="text-4xl font-medium text-balance md:text-5xl">
                            Materiais de<span className="block text-primary">Divulgação</span>
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-xl text-balance text-muted-foreground">
                            Baixe banners, textos prontos e vídeos para divulgar a F4 Fisio nas suas redes sociais e canais de comunicação.
                        </p>
                    </div>
                </div>
            </section>

            {/* Banners */}
            <section className="bg-background py-16">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-12">
                        <h2 className="text-3xl font-medium mb-4">Banners e Imagens</h2>
                        <p className="text-muted-foreground">
                            Banners profissionais em diferentes formatos para suas campanhas
                        </p>
                    </div>

                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                        {banners.map((banner) => (
                            <Card key={banner.id}>
                                <CardHeader>
                                    <div className="aspect-video bg-muted rounded-lg flex items-center justify-center mb-4">
                                        <Image className="h-12 w-12 text-muted-foreground" />
                                    </div>
                                    <CardTitle className="text-lg">{banner.nome}</CardTitle>
                                    <CardDescription>{banner.descricao}</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex gap-2">
                                            <Badge variant="outline">{banner.formato}</Badge>
                                            <Badge variant="outline">{banner.tamanho}</Badge>
                                        </div>
                                    </div>
                                    <Button 
                                        className="w-full" 
                                        onClick={() => handleDownload(banner.url, banner.nome)}
                                    >
                                        <Download className="mr-2 h-4 w-4" />
                                        Download
                                    </Button>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Textos */}
            <section className="bg-muted/30 py-16">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-12">
                        <h2 className="text-3xl font-medium mb-4">Textos Promocionais</h2>
                        <p className="text-muted-foreground">
                            Textos prontos para diferentes plataformas. Lembre-se de substituir [SEU_LINK_AFILIADO] pelo seu link exclusivo.
                        </p>
                    </div>

                    <div className="grid gap-6 lg:grid-cols-2">
                        {textosPromocao.map((item) => (
                            <Card key={item.id}>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-lg">{item.titulo}</CardTitle>
                                        <Badge variant="outline">{item.categoria}</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="bg-muted p-4 rounded-lg mb-4">
                                        <pre className="text-sm whitespace-pre-wrap font-sans">
                                            {item.texto}
                                        </pre>
                                    </div>
                                    <Button 
                                        variant="outline" 
                                        className="w-full"
                                        onClick={() => handleCopyText(item.texto, item.id)}
                                    >
                                        {copiedText === `texto-${item.id}` ? (
                                            <>
                                                <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                                Copiado!
                                            </>
                                        ) : (
                                            <>
                                                <Copy className="mr-2 h-4 w-4" />
                                                Copiar Texto
                                            </>
                                        )}
                                    </Button>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Vídeos */}
            <section className="bg-background py-16">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-12">
                        <h2 className="text-3xl font-medium mb-4">Vídeos Promocionais</h2>
                        <p className="text-muted-foreground">
                            Vídeos profissionais para usar em suas campanhas
                        </p>
                    </div>

                    <div className="grid gap-6 md:grid-cols-2">
                        {videos.map((video) => (
                            <Card key={video.id}>
                                <CardHeader>
                                    <div className="aspect-video bg-muted rounded-lg flex items-center justify-center mb-4">
                                        <Video className="h-12 w-12 text-muted-foreground" />
                                    </div>
                                    <CardTitle className="text-lg">{video.nome}</CardTitle>
                                    <CardDescription>{video.descricao}</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex gap-2">
                                            <Badge variant="outline">{video.formato}</Badge>
                                            <Badge variant="outline">{video.tamanho}</Badge>
                                        </div>
                                    </div>
                                    <Button 
                                        className="w-full" 
                                        onClick={() => handleDownload(video.url, video.nome)}
                                    >
                                        <Download className="mr-2 h-4 w-4" />
                                        Download
                                    </Button>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Dicas */}
            <section className="bg-muted/30 py-16">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mx-auto max-w-4xl">
                        <h2 className="text-3xl font-medium mb-8 text-center">Dicas de Divulgação</h2>
                        
                        <div className="grid gap-6 md:grid-cols-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Share2 className="h-5 w-5" />
                                        Redes Sociais
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <p className="text-sm">• Poste regularmente, mas sem spam</p>
                                    <p className="text-sm">• Use hashtags relevantes (#fisioterapia #saude)</p>
                                    <p className="text-sm">• Compartilhe depoimentos e casos de sucesso</p>
                                    <p className="text-sm">• Interaja com comentários e mensagens</p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Conteúdo
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <p className="text-sm">• Foque nos benefícios para o cliente</p>
                                    <p className="text-sm">• Use linguagem clara e acessível</p>
                                    <p className="text-sm">• Inclua sempre seu link de afiliado</p>
                                    <p className="text-sm">• Seja transparente sobre ser afiliado</p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
