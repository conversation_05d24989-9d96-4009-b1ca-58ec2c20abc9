import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import { safeRoute } from '@/utils/route-helper';

interface Fisioterapeuta {
    id: number;
    crefito: string;
    specializations: string[];
    bio?: string;
    experience_years?: number;
    hourly_rate: number;
    active: boolean;
    user: {
        id: number;
        name: string;
        email: string;
    };
}

interface Props {
    fisioterapeuta: Fisioterapeuta;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Fisioterapeutas',
        href: '/admin/fisioterapeutas',
    },
    {
        title: 'Editar Fisioterapeuta',
        href: '#',
    },
];

const specializations = [
    'Ortopedia',
    'Neurologia',
    'Cardiorrespiratória',
    'Pediatria',
    'Geriatria',
    'Esportiva',
    'Dermatofuncional',
    'Uroginecologia',
    'Traumatologia',
    'Reumatologia',
];

export default function FisioterapeutaEdit({ fisioterapeuta }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        crefito: fisioterapeuta.crefito,
        specializations: fisioterapeuta.specializations,
        bio: fisioterapeuta.bio || '',
        experience_years: fisioterapeuta.experience_years?.toString() || '',
        hourly_rate: fisioterapeuta.hourly_rate.toString(),
        active: fisioterapeuta.active,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const routeUrl = safeRoute('admin.fisioterapeutas.update', fisioterapeuta.id);
        if (routeUrl !== '#') {
            put(routeUrl);
        }
    };

    const handleSpecializationChange = (specialization: string, checked: boolean) => {
        if (checked) {
            setData('specializations', [...data.specializations, specialization]);
        } else {
            setData('specializations', data.specializations.filter(s => s !== specialization));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Editar ${fisioterapeuta.user.name}`} />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Editar Fisioterapeuta</h1>
                        <p className="text-muted-foreground">
                            Editando dados de {fisioterapeuta.user.name}
                        </p>
                    </div>
                    <Link href={safeRoute('admin.fisioterapeutas.index')} preserveState>
                        <Button variant="ghost">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar
                        </Button>
                    </Link>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Informações do Fisioterapeuta</CardTitle>
                        <CardDescription>
                            Edite os dados do fisioterapeuta
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label>Usuário</Label>
                                    <Input
                                        value={`${fisioterapeuta.user.name} (${fisioterapeuta.user.email})`}
                                        disabled
                                        className="bg-gray-50"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="crefito">CREFITO</Label>
                                    <Input
                                        id="crefito"
                                        value={data.crefito}
                                        onChange={(e) => setData('crefito', e.target.value)}
                                        placeholder="Ex: 123456-F"
                                    />
                                    {errors.crefito && <p className="text-sm text-red-600">{errors.crefito}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="experience_years">Anos de Experiência</Label>
                                    <Input
                                        id="experience_years"
                                        type="number"
                                        value={data.experience_years}
                                        onChange={(e) => setData('experience_years', e.target.value)}
                                        placeholder="Ex: 5"
                                    />
                                    {errors.experience_years && <p className="text-sm text-red-600">{errors.experience_years}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="hourly_rate">Valor por Hora (R$)</Label>
                                    <Input
                                        id="hourly_rate"
                                        type="number"
                                        step="0.01"
                                        value={data.hourly_rate}
                                        onChange={(e) => setData('hourly_rate', e.target.value)}
                                        placeholder="Ex: 80.00"
                                    />
                                    {errors.hourly_rate && <p className="text-sm text-red-600">{errors.hourly_rate}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label>Especializações</Label>
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                                    {specializations.map((specialization) => (
                                        <div key={specialization} className="flex items-center space-x-2">
                                            <Checkbox
                                                id={specialization}
                                                checked={data.specializations.includes(specialization)}
                                                onCheckedChange={(checked) => 
                                                    handleSpecializationChange(specialization, checked as boolean)
                                                }
                                            />
                                            <Label htmlFor={specialization} className="text-sm">
                                                {specialization}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                                {errors.specializations && <p className="text-sm text-red-600">{errors.specializations}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="bio">Biografia</Label>
                                <Textarea
                                    id="bio"
                                    value={data.bio}
                                    onChange={(e) => setData('bio', e.target.value)}
                                    placeholder="Descreva a experiência e especialidades do fisioterapeuta..."
                                    rows={4}
                                />
                                {errors.bio && <p className="text-sm text-red-600">{errors.bio}</p>}
                            </div>

                            <div className="flex items-center justify-end space-x-4">
                                <Link href={safeRoute('admin.fisioterapeutas.index')} preserveState>
                                    <Button type="button" variant="outline">
                                        Cancelar
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Salvando...' : 'Salvar Alterações'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
