<?php

namespace App\Console\Commands;

use App\Services\BackupService;
use Illuminate\Console\Command;

class CreateBackup extends Command
{
    protected $signature = 'backup:create {--type=full : Tipo de backup (full, database, files)}';
    protected $description = 'Criar backup do sistema';

    public function handle(BackupService $backupService)
    {
        $type = $this->option('type');
        
        $this->info("Iniciando backup do tipo: {$type}");
        
        $startTime = microtime(true);
        
        try {
            switch ($type) {
                case 'full':
                    $result = $backupService->createFullBackup();
                    break;
                    
                case 'database':
                    $path = $backupService->createDatabaseBackup();
                    $result = ['success' => true, 'backup_path' => $path];
                    break;
                    
                case 'files':
                    $path = $backupService->createFilesBackup();
                    $result = ['success' => true, 'backup_path' => $path];
                    break;
                    
                default:
                    $this->error("Tipo de backup inválido: {$type}");
                    return Command::FAILURE;
            }
            
            $executionTime = round(microtime(true) - $startTime, 2);
            
            if ($result['success']) {
                $this->info("✅ Backup criado com sucesso!");
                $this->info("📁 Caminho: {$result['backup_path']}");
                $this->info("⏱️  Tempo de execução: {$executionTime}s");
                
                if (isset($result['size'])) {
                    $size = round($result['size'] / 1024 / 1024, 2);
                    $this->info("📊 Tamanho: {$size} MB");
                }
            } else {
                $this->error("❌ Erro ao criar backup: " . ($result['error'] ?? 'Erro desconhecido'));
                return Command::FAILURE;
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Erro inesperado: " . $e->getMessage());
            return Command::FAILURE;
        }
        
        return Command::SUCCESS;
    }
}
