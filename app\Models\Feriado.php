<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Feriado extends Model
{
    use HasFactory;

    protected $fillable = [
        'nome',
        'data',
        'tipo',
        'estado',
        'cidade',
        'recorrente',
        'descricao',
        'ativo',
    ];

    protected $casts = [
        'data' => 'date',
        'recorrente' => 'boolean',
        'ativo' => 'boolean',
    ];

    // Scopes
    public function scopeAtivos($query)
    {
        return $query->where('ativo', true);
    }

    public function scopePorTipo($query, $tipo)
    {
        return $query->where('tipo', $tipo);
    }

    public function scopePorEstado($query, $estado)
    {
        return $query->where(function ($q) use ($estado) {
            $q->where('tipo', 'nacional')
              ->orWhere(function ($subQ) use ($estado) {
                  $subQ->where('tipo', 'estadual')
                       ->where('estado', $estado);
              });
        });
    }

    public function scopePorCidade($query, $estado, $cidade)
    {
        return $query->where(function ($q) use ($estado, $cidade) {
            $q->where('tipo', 'nacional')
              ->orWhere(function ($subQ) use ($estado) {
                  $subQ->where('tipo', 'estadual')
                       ->where('estado', $estado);
              })
              ->orWhere(function ($subQ) use ($estado, $cidade) {
                  $subQ->where('tipo', 'municipal')
                       ->where('estado', $estado)
                       ->where('cidade', $cidade);
              });
        });
    }

    public function scopePorData($query, $data)
    {
        $dataCarbon = Carbon::parse($data);
        
        return $query->where(function ($q) use ($dataCarbon) {
            $q->where('data', $dataCarbon->format('Y-m-d'))
              ->orWhere(function ($subQ) use ($dataCarbon) {
                  // Para feriados recorrentes, verifica apenas mês e dia
                  $subQ->where('recorrente', true)
                       ->whereRaw('MONTH(data) = ? AND DAY(data) = ?', [
                           $dataCarbon->month,
                           $dataCarbon->day
                       ]);
              });
        });
    }

    // Métodos auxiliares
    public function aplicaParaData($data)
    {
        $dataCarbon = Carbon::parse($data);
        $dataFeriado = Carbon::parse($this->data);

        if ($this->recorrente) {
            // Para feriados recorrentes, compara apenas mês e dia
            return $dataCarbon->month === $dataFeriado->month && 
                   $dataCarbon->day === $dataFeriado->day;
        }

        // Para feriados específicos, compara a data completa
        return $dataCarbon->isSameDay($dataFeriado);
    }

    public function getFormattedDataAttribute()
    {
        return $this->data->format('d/m/Y');
    }

    public function getTipoTextoAttribute()
    {
        $tipos = [
            'nacional' => 'Nacional',
            'estadual' => 'Estadual',
            'municipal' => 'Municipal',
        ];

        return $tipos[$this->tipo] ?? 'Desconhecido';
    }

    public function getLocalizacaoAttribute()
    {
        switch ($this->tipo) {
            case 'nacional':
                return 'Brasil';
            case 'estadual':
                return $this->estado;
            case 'municipal':
                return $this->cidade . ' - ' . $this->estado;
            default:
                return 'Não especificado';
        }
    }
}
