<?php

namespace App\Http\Controllers\Afiliado;

use App\Http\Controllers\Controller;
use App\Models\PagamentoComissao;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PagamentoComissaoController extends Controller
{
    /**
     * Display a listing of payment requests
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;

        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }

        $query = PagamentoComissao::where('afiliado_id', $afiliado->id)
                                 ->with(['aprovadoPor']);

        // Filtros
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        $pagamentos = $query->orderBy('created_at', 'desc')
                           ->paginate(10)
                           ->withQueryString();

        // Estatísticas
        $stats = [
            'total_solicitado' => PagamentoComissao::where('afiliado_id', $afiliado->id)->sum('valor_solicitado'),
            'total_pago' => PagamentoComissao::where('afiliado_id', $afiliado->id)->pagos()->sum('valor_solicitado'),
            'pendentes' => PagamentoComissao::where('afiliado_id', $afiliado->id)->pendentes()->count(),
            'saldo_disponivel' => $afiliado->total_comissoes,
        ];

        return Inertia::render('afiliado/pagamentos', [
            'pagamentos' => $pagamentos,
            'stats' => $stats,
            'filters' => $request->only(['status']),
            'afiliado' => $afiliado,
        ]);
    }

    /**
     * Show the form for creating a new payment request
     */
    public function create()
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;

        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }

        // Verificar se há saldo disponível
        $saldoDisponivel = $afiliado->total_comissoes;
        $valorMinimo = 50.00; // Valor mínimo para saque

        return Inertia::render('afiliado/pagamentos/create', [
            'afiliado' => $afiliado,
            'saldo_disponivel' => $saldoDisponivel,
            'valor_minimo' => $valorMinimo,
        ]);
    }

    /**
     * Store a newly created payment request
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;

        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }

        $valorMinimo = 50.00;
        $saldoDisponivel = $afiliado->total_comissoes;

        $validated = $request->validate([
            'valor_solicitado' => "required|numeric|min:{$valorMinimo}|max:{$saldoDisponivel}",
            'metodo_pagamento' => 'required|in:pix,transferencia_bancaria',
            'dados_pagamento' => 'required|array',
            'observacoes_afiliado' => 'nullable|string|max:1000',
        ]);

        // Validar dados específicos do método de pagamento
        if ($validated['metodo_pagamento'] === 'pix') {
            $request->validate([
                'dados_pagamento.chave_pix' => 'required|string',
                'dados_pagamento.tipo_chave' => 'required|in:cpf,email,telefone,aleatoria',
            ]);
        } else {
            $request->validate([
                'dados_pagamento.banco' => 'required|string',
                'dados_pagamento.agencia' => 'required|string',
                'dados_pagamento.conta' => 'required|string',
                'dados_pagamento.tipo_conta' => 'required|in:corrente,poupanca',
                'dados_pagamento.titular' => 'required|string',
                'dados_pagamento.cpf_titular' => 'required|string',
            ]);
        }

        // Verificar se não há solicitação pendente
        $solicitacaoPendente = PagamentoComissao::where('afiliado_id', $afiliado->id)
                                               ->whereIn('status', ['pendente', 'aprovado'])
                                               ->exists();

        if ($solicitacaoPendente) {
            return back()->withErrors(['valor_solicitado' => 'Você já possui uma solicitação de pagamento pendente.']);
        }

        $validated['afiliado_id'] = $afiliado->id;

        PagamentoComissao::create($validated);

        return redirect()->route('afiliado.pagamentos.index')
                        ->with('success', 'Solicitação de pagamento enviada com sucesso! Aguarde a análise.');
    }

    /**
     * Display the specified payment request
     */
    public function show(PagamentoComissao $pagamento)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;

        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }

        // Verificar se o pagamento pertence ao afiliado
        if ($pagamento->afiliado_id !== $afiliado->id) {
            abort(403, 'Você não tem acesso a esta solicitação.');
        }

        $pagamento->load(['aprovadoPor']);

        return Inertia::render('afiliado/pagamentos/show', [
            'pagamento' => $pagamento,
            'afiliado' => $afiliado,
        ]);
    }
}
