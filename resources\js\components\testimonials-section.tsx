import { Ava<PERSON>, AvatarFallback } from '@/components/ui/avatar';

export default function TestimonialsSection() {
    const testimonials = [
        {
            name: '<PERSON>',
            role: 'Pacient<PERSON>',
            content:
                'A fisioterapia domiciliar da F4 Fisio mudou minha vida. Depois da cirurgia no joelho, não conseguia me locomover para fazer fisioterapia. O atendimento em casa foi perfeito e minha recuperação foi muito mais rápida.',
        },
        {
            name: '<PERSON>',
            role: 'Paciente',
            content:
                'Excelente serviço! O fisioterapeuta é muito profissional e atencioso. O tratamento para minha lombalgia tem sido muito eficaz. Recomendo para todos que precisam de fisioterapia de qualidade.',
        },
        {
            name: '<PERSON>',
            role: 'Pacient<PERSON>',
            content:
                'Minha mãe idosa precisava de fisioterapia, mas tinha dificuldade para sair de casa. A F4 Fisio foi a solução perfeita. Profissionais qualificados e equipamentos de primeira qualidade.',
        },
        {
            name: '<PERSON>',
            role: '<PERSON><PERSON><PERSON>',
            content:
                'Após um AVC, precisei de fisioterapia intensiva. O plano mensal da F4 Fisio foi ideal para minha recuperação. Os relatórios detalhados me ajudaram a acompanhar meu progresso.',
        },
        {
            name: 'Lucia Ferreira',
            role: 'Paciente',
            content:
                'Profissionais extremamente competentes e pontuais. O atendimento domiciliar é muito conveniente e o resultado do tratamento superou minhas expectativas. Muito satisfeita!',
        },
        {
            name: 'Roberto Lima',
            role: 'Paciente',
            content:
                'Depois de uma lesão no ombro, a fisioterapia domiciliar foi essencial para minha recuperação. A F4 Fisio oferece um serviço de excelência com profissionais muito qualificados.',
        },
    ];

    return (
        <section>
            <div className="bg-muted/30 py-20">
                <div className="@container mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-16 text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">O que Nossos Pacientes Dizem</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Histórias reais de recuperação e satisfação. Descubra por que nossos pacientes confiam na F4 Fisio para sua fisioterapia
                            domiciliar.
                        </p>
                    </div>
                    <div className="grid gap-8 @lg:grid-cols-2 @3xl:grid-cols-3">
                        {testimonials.map((testimonial, index) => (
                            <div key={index}>
                                <div className="rounded-2xl rounded-bl border border-transparent bg-background px-6 py-5 ring-1 ring-foreground/10">
                                    <p className="text-base leading-relaxed text-foreground">{testimonial.content}</p>
                                </div>
                                <div className="mt-5 flex items-center gap-3">
                                    <Avatar className="size-8 border border-transparent shadow ring-1 ring-foreground/10">
                                        <AvatarFallback>
                                            {testimonial.name
                                                .split(' ')
                                                .map((n) => n[0])
                                                .join('')
                                                .toUpperCase()}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div className="text-base font-medium text-foreground">{testimonial.name}</div>
                                    <span aria-hidden className="size-1 rounded-full bg-foreground/25"></span>
                                    <span className="text-sm text-muted-foreground">{testimonial.role}</span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
}
