<?php

namespace App\Http\Controllers\Afiliado;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\VendaAfiliado;
use Carbon\Carbon;

class VendasController extends Controller
{
    /**
     * Display the affiliate sales.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $afiliado = $user->afiliado;
        
        if (!$afiliado || $afiliado->status !== 'aprovado' || !$afiliado->ativo) {
            return redirect()->route('dashboard')
                ->with('error', 'Você não tem acesso ao painel de afiliado.');
        }
        
        // Filtros
        $status = $request->input('status', 'all');
        $periodo = $request->input('periodo', 'all');
        $search = $request->input('search', '');
        
        $query = VendaAfiliado::where('afiliado_id', $afiliado->id);
        
        // Aplicar filtros
        if ($status !== 'all') {
            $query->where('status', $status);
        }
        
        if ($periodo !== 'all') {
            $date = Carbon::now();
            
            switch ($periodo) {
                case 'hoje':
                    $query->whereDate('created_at', $date);
                    break;
                case 'semana':
                    $query->whereBetween('created_at', [$date->copy()->startOfWeek(), $date->copy()->endOfWeek()]);
                    break;
                case 'mes':
                    $query->whereMonth('created_at', $date->month)
                          ->whereYear('created_at', $date->year);
                    break;
                case 'ano':
                    $query->whereYear('created_at', $date->year);
                    break;
            }
        }
        
        if (!empty($search)) {
            $query->whereHas('cliente', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }
        
        // Ordenar e paginar
        $vendas = $query->with(['cliente', 'assinatura.plano'])
                        ->orderBy('created_at', 'desc')
                        ->paginate(10)
                        ->withQueryString();
        
        // Estatísticas
        $stats = [
            'total' => VendaAfiliado::where('afiliado_id', $afiliado->id)->count(),
            'pendentes' => VendaAfiliado::where('afiliado_id', $afiliado->id)->where('status', 'pendente')->count(),
            'confirmadas' => VendaAfiliado::where('afiliado_id', $afiliado->id)->where('status', 'confirmada')->count(),
            'canceladas' => VendaAfiliado::where('afiliado_id', $afiliado->id)->where('status', 'cancelada')->count(),
        ];
        
        return Inertia::render('afiliado/vendas', [
            'vendas' => $vendas,
            'stats' => $stats,
            'filters' => [
                'status' => $status,
                'periodo' => $periodo,
                'search' => $search,
            ],
        ]);
    }
}
