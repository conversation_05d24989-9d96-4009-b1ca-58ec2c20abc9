<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class PhoneValidation implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->isValidPhone($value)) {
            $fail('O campo :attribute deve ser um telefone válido.');
        }
    }

    /**
     * Valida se o telefone é válido
     */
    private function isValidPhone(string $phone): bool
    {
        // Remove caracteres não numéricos
        $phone = preg_replace('/\D/', '', $phone);

        // Verifica se tem 10 ou 11 dígitos (com DDD)
        if (strlen($phone) < 10 || strlen($phone) > 11) {
            return false;
        }

        // Verifica se o DDD é válido (11-99)
        $ddd = intval(substr($phone, 0, 2));
        if ($ddd < 11 || $ddd > 99) {
            return false;
        }

        // Para celular (11 dígitos), o terceiro dígito deve ser 9
        if (strlen($phone) === 11 && $phone[2] !== '9') {
            return false;
        }

        return true;
    }
}
