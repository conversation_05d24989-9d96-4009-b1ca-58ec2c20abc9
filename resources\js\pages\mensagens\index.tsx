import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, usePage } from '@inertiajs/react';
import { MessageCircle, Search, User } from 'lucide-react';
import { useState } from 'react';

interface Conversa {
    id: number;
    nome: string;
    avatar?: string;
    role: string;
    ultima_mensagem: {
        conteudo: string;
        data: string;
        lida: boolean;
        tipo: string;
        eh_remetente: boolean;
    };
    nao_lidas: number;
}

interface Props {
    conversas: Conversa[];
    totalNaoLidas: number;
}

export default function MensagensIndex() {
    const { conversas, totalNaoLidas } = usePage().props as unknown as Props;
    const [busca, setBusca] = useState('');

    const conversasFiltradas = conversas.filter((conversa) => conversa.nome.toLowerCase().includes(busca.toLowerCase()));

    const formatarData = (data: string) => {
        const agora = new Date();
        const dataMsg = new Date(data);
        const diffMs = agora.getTime() - dataMsg.getTime();
        const diffHoras = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDias = Math.floor(diffHoras / 24);

        if (diffHoras < 1) {
            const diffMinutos = Math.floor(diffMs / (1000 * 60));
            return diffMinutos < 1 ? 'Agora' : `${diffMinutos}min`;
        } else if (diffHoras < 24) {
            return `${diffHoras}h`;
        } else if (diffDias < 7) {
            return `${diffDias}d`;
        } else {
            return dataMsg.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
        }
    };

    const getRoleBadge = (role: string) => {
        const roles = {
            paciente: { label: 'Paciente', variant: 'default' as const },
            fisioterapeuta: { label: 'Fisioterapeuta', variant: 'secondary' as const },
            admin: { label: 'Admin', variant: 'destructive' as const },
        };
        return roles[role as keyof typeof roles] || { label: role, variant: 'outline' as const };
    };

    const breadcrumbs = [
        { title: 'Início', href: '/dashboard' },
        { title: 'Mensagens', href: '/mensagens' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Mensagens" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Mensagens</h1>
                        <p className="text-muted-foreground">Converse com fisioterapeutas e pacientes</p>
                    </div>
                    {totalNaoLidas > 0 && (
                        <Badge variant="destructive" className="text-sm">
                            {totalNaoLidas} não lida{totalNaoLidas !== 1 ? 's' : ''}
                        </Badge>
                    )}
                </div>

                {/* Busca */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Search className="h-5 w-5" />
                            Buscar Conversas
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Input
                            placeholder="Digite o nome da pessoa..."
                            value={busca}
                            onChange={(e) => setBusca(e.target.value)}
                            className="max-w-md"
                        />
                    </CardContent>
                </Card>

                {/* Lista de Conversas */}
                <div className="grid gap-4">
                    {conversasFiltradas.length === 0 ? (
                        <Card>
                            <CardContent className="py-12 text-center">
                                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                                    <MessageCircle className="h-6 w-6 text-muted-foreground" />
                                </div>
                                <h3 className="mb-2 text-lg font-medium">{busca ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa ainda'}</h3>
                                <p className="text-muted-foreground">
                                    {busca
                                        ? 'Tente buscar por outro nome.'
                                        : 'Suas conversas aparecerão aqui quando você começar a trocar mensagens.'}
                                </p>
                            </CardContent>
                        </Card>
                    ) : (
                        conversasFiltradas.map((conversa) => (
                            <Card key={conversa.id} className="transition-shadow hover:shadow-md">
                                <Link href={`/mensagens/${conversa.id}`} className="block">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-4">
                                            {/* Avatar */}
                                            <div className="flex-shrink-0">
                                                {conversa.avatar ? (
                                                    <img src={conversa.avatar} alt={conversa.nome} className="h-12 w-12 rounded-full object-cover" />
                                                ) : (
                                                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                                                        <User className="h-6 w-6 text-muted-foreground" />
                                                    </div>
                                                )}
                                            </div>

                                            {/* Conteúdo */}
                                            <div className="min-w-0 flex-1">
                                                <div className="mb-1 flex items-center justify-between">
                                                    <h3 className="truncate font-medium text-foreground">{conversa.nome}</h3>
                                                    <div className="flex items-center gap-2">
                                                        {conversa.nao_lidas > 0 && (
                                                            <Badge variant="destructive" className="text-xs">
                                                                {conversa.nao_lidas}
                                                            </Badge>
                                                        )}
                                                        <span className="text-xs text-muted-foreground">
                                                            {formatarData(conversa.ultima_mensagem.data)}
                                                        </span>
                                                    </div>
                                                </div>

                                                <div className="flex items-center justify-between">
                                                    <p
                                                        className={`truncate text-sm ${
                                                            conversa.nao_lidas > 0 && !conversa.ultima_mensagem.eh_remetente
                                                                ? 'font-medium text-foreground'
                                                                : 'text-muted-foreground'
                                                        }`}
                                                    >
                                                        {conversa.ultima_mensagem.eh_remetente && 'Você: '}
                                                        {conversa.ultima_mensagem.conteudo}
                                                    </p>
                                                    <Badge {...getRoleBadge(conversa.role)} className="ml-2 text-xs">
                                                        {getRoleBadge(conversa.role).label}
                                                    </Badge>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Link>
                            </Card>
                        ))
                    )}
                </div>

                {/* Botão para Nova Conversa */}
                <Card>
                    <CardContent className="py-8 text-center">
                        <h3 className="mb-2 text-lg font-medium">Iniciar Nova Conversa</h3>
                        <p className="mb-4 text-muted-foreground">Precisa falar com alguém específico?</p>
                        <Button asChild>
                            <Link href="/mensagens/nova">
                                <MessageCircle className="mr-2 h-4 w-4" />
                                Nova Conversa
                            </Link>
                        </Button>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
