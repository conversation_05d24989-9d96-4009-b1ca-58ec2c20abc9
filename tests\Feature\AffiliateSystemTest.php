<?php

namespace Tests\Feature;

use App\Models\Afiliado;
use App\Models\Assinatura;
use App\Models\Pagamento;
use App\Models\User;
use App\Models\VendaAfiliado;
use App\Services\AffiliateTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class AffiliateSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $afiliado;
    protected $user;
    protected $customer;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Criar usuário afiliado
        $this->user = User::factory()->create([
            'role' => 'paciente',
        ]);
        
        // Criar afiliado
        $this->afiliado = Afiliado::create([
            'user_id' => $this->user->id,
            'codigo_afiliado' => 'AF12345678',
            'nome' => 'Afiliado Teste',
            'email' => '<EMAIL>',
            'telefone' => '11999999999',
            'cpf' => '12345678901',
            'endereco' => 'Rua Teste, 123',
            'cidade' => 'São Paulo',
            'estado' => 'SP',
            'cep' => '01234567',
            'experiencia' => 'iniciante',
            'motivacao' => 'Teste',
            'canais_divulgacao' => ['whatsapp'],
            'status' => 'aprovado',
            'ativo' => true,
            'data_aprovacao' => now(),
            'aprovado_por' => 1,
        ]);
        
        // Criar cliente que vai comprar
        $this->customer = User::factory()->create([
            'role' => 'paciente',
        ]);
    }

    /** @test */
    public function middleware_captures_affiliate_referral()
    {
        $response = $this->get('/?ref=' . $this->afiliado->codigo_afiliado);
        
        $response->assertStatus(200);
        
        // Verificar se o cookie foi definido
        $response->assertCookie('affiliate_ref', $this->afiliado->codigo_afiliado);
    }

    /** @test */
    public function affiliate_tracking_service_gets_current_referral()
    {
        // Simular cookie de referência
        $this->withCookie('affiliate_ref', $this->afiliado->codigo_afiliado);
        
        $service = new AffiliateTrackingService();
        $request = request();
        
        $currentRef = $service->getCurrentAffiliateRef($request);
        $currentAffiliate = $service->getCurrentAffiliate($request);
        
        $this->assertEquals($this->afiliado->codigo_afiliado, $currentRef);
        $this->assertEquals($this->afiliado->id, $currentAffiliate->id);
    }

    /** @test */
    public function affiliate_sale_is_created_when_user_subscribes()
    {
        // Simular referência de afiliado na sessão
        Session::put('affiliate_ref', $this->afiliado->codigo_afiliado);
        
        $service = new AffiliateTrackingService();
        
        // Simular venda
        $vendaAfiliado = $service->createAffiliateSale(
            $this->customer,
            123, // assinatura_id
            'mensal',
            640.00,
            request()
        );
        
        $this->assertNotNull($vendaAfiliado);
        $this->assertEquals($this->afiliado->id, $vendaAfiliado->afiliado_id);
        $this->assertEquals($this->customer->id, $vendaAfiliado->user_id);
        $this->assertEquals(640.00, $vendaAfiliado->valor_venda);
        $this->assertEquals('pendente', $vendaAfiliado->status);
        
        // Verificar se a comissão foi calculada (assumindo 10%)
        $this->assertEquals(64.00, $vendaAfiliado->comissao);
    }

    /** @test */
    public function affiliate_cannot_earn_commission_from_own_purchase()
    {
        // Simular referência do próprio afiliado
        Session::put('affiliate_ref', $this->afiliado->codigo_afiliado);
        
        $service = new AffiliateTrackingService();
        
        // Tentar criar venda para o próprio afiliado
        $vendaAfiliado = $service->createAffiliateSale(
            $this->user, // mesmo usuário do afiliado
            123,
            'mensal',
            640.00,
            request()
        );
        
        $this->assertNull($vendaAfiliado);
    }

    /** @test */
    public function affiliate_commission_is_confirmed_when_payment_approved()
    {
        // Criar assinatura
        $assinatura = Assinatura::create([
            'user_id' => $this->customer->id,
            'plano_id' => 1,
            'status' => 'ativa',
            'data_inicio' => now(),
            'data_fim' => now()->addMonth(),
            'valor' => 640.00,
        ]);
        
        // Criar pagamento
        $pagamento = Pagamento::create([
            'assinatura_id' => $assinatura->id,
            'valor' => 640.00,
            'status' => 'pendente',
            'metodo_pagamento' => 'pix',
            'mercado_pago_payment_id' => 'MP123456789',
        ]);
        
        // Criar venda de afiliado pendente
        $vendaAfiliado = VendaAfiliado::create([
            'afiliado_id' => $this->afiliado->id,
            'user_id' => $this->customer->id,
            'assinatura_id' => $assinatura->id,
            'plano_tipo' => 'mensal',
            'valor_venda' => 640.00,
            'comissao' => 64.00,
            'status' => 'pendente',
        ]);
        
        // Simular webhook do Mercado Pago
        $webhookController = new \App\Http\Controllers\MercadoPagoWebhookController();
        
        // Usar reflexão para chamar método privado
        $reflection = new \ReflectionClass($webhookController);
        $method = $reflection->getMethod('confirmAffiliateCommission');
        $method->setAccessible(true);
        
        // Atualizar status do pagamento para pago
        $pagamento->update(['status' => 'pago']);
        
        // Executar confirmação de comissão
        $method->invoke($webhookController, $pagamento);
        
        // Verificar se a venda foi confirmada
        $vendaAfiliado->refresh();
        $this->assertEquals('confirmada', $vendaAfiliado->status);
        $this->assertNotNull($vendaAfiliado->data_confirmacao);
    }

    /** @test */
    public function affiliate_stats_are_updated_after_confirmation()
    {
        // Criar venda confirmada
        $vendaAfiliado = VendaAfiliado::create([
            'afiliado_id' => $this->afiliado->id,
            'user_id' => $this->customer->id,
            'assinatura_id' => 123,
            'plano_tipo' => 'mensal',
            'valor_venda' => 640.00,
            'comissao' => 64.00,
            'status' => 'confirmada',
            'data_confirmacao' => now(),
        ]);
        
        $service = new AffiliateTrackingService();
        $service->updateAffiliateStats($this->afiliado);
        
        $this->afiliado->refresh();
        
        $this->assertEquals(1, $this->afiliado->total_vendas);
        $this->assertEquals(64.00, $this->afiliado->total_comissoes);
        $this->assertEquals(1, $this->afiliado->vendas_mes_atual);
        $this->assertEquals(64.00, $this->afiliado->comissoes_mes_atual);
    }

    /** @test */
    public function debug_endpoint_returns_tracking_info()
    {
        // Simular referência na sessão
        Session::put('affiliate_ref', $this->afiliado->codigo_afiliado);
        
        $response = $this->get('/debug/affiliate');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'tracking_info',
            'request_params',
            'headers',
            'cookies_raw',
            'session_data',
        ]);
        
        $data = $response->json();
        $this->assertEquals($this->afiliado->codigo_afiliado, $data['tracking_info']['affiliate_ref']);
    }
}
