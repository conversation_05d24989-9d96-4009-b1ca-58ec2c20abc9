<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendaAfiliado extends Model
{
    use HasFactory;

    protected $table = 'vendas_afiliado';

    protected $fillable = [
        'afiliado_id',
        'user_id',
        'assinatura_id',
        'transaction_id',
        'plano_tipo',
        'valor_venda',
        'comissao',
        'percentual_comissao',
        'status',
        'data_confirmacao',
        'observacoes',
    ];

    protected $casts = [
        'valor_venda' => 'decimal:2',
        'comissao' => 'decimal:2',
        'percentual_comissao' => 'decimal:2',
        'data_confirmacao' => 'datetime',
    ];

    // Relacionamentos
    public function afiliado()
    {
        return $this->belongsTo(Afiliado::class);
    }

    public function cliente()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function assinatura()
    {
        return $this->belongsTo(Assinatura::class);
    }

    // Scopes
    public function scopeConfirmadas($query)
    {
        return $query->where('status', 'confirmada');
    }

    public function scopePendentes($query)
    {
        return $query->where('status', 'pendente');
    }

    public function scopeDoMes($query, $mes = null, $ano = null)
    {
        $mes = $mes ?? now()->month;
        $ano = $ano ?? now()->year;
        
        return $query->whereMonth('created_at', $mes)
                    ->whereYear('created_at', $ano);
    }

    // Métodos auxiliares
    public function confirmar($observacoes = null)
    {
        $this->update([
            'status' => 'confirmada',
            'data_confirmacao' => now(),
            'observacoes' => $observacoes,
        ]);

        // Atualizar estatísticas do afiliado
        $this->afiliado->atualizarEstatisticas();
    }

    public function cancelar($observacoes = null)
    {
        $this->update([
            'status' => 'cancelada',
            'observacoes' => $observacoes,
        ]);

        // Atualizar estatísticas do afiliado
        $this->afiliado->atualizarEstatisticas();
    }

    // Accessors
    public function getFormattedValorVendaAttribute()
    {
        return 'R$ ' . number_format($this->valor_venda, 2, ',', '.');
    }

    public function getFormattedComissaoAttribute()
    {
        return 'R$ ' . number_format($this->comissao, 2, ',', '.');
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pendente' => ['text' => 'Pendente', 'color' => 'yellow'],
            'confirmada' => ['text' => 'Confirmada', 'color' => 'green'],
            'cancelada' => ['text' => 'Cancelada', 'color' => 'red'],
        ];

        return $badges[$this->status] ?? ['text' => 'Desconhecido', 'color' => 'gray'];
    }

    public function getPlanoNomeAttribute()
    {
        $nomes = [
            'empresarial' => 'Empresarial',
            'pessoal' => 'Pessoal',
            'busca' => 'Busca',
        ];

        return $nomes[$this->plano_tipo] ?? 'Desconhecido';
    }

    // Métodos estáticos para cálculos
    public static function calcularComissao($planoTipo, $valorVenda)
    {
        $comissoes = [
            'empresarial' => 18.50,
            'pessoal' => 8.00,
            'busca' => 0.80,
        ];

        $valorComissao = $comissoes[$planoTipo] ?? 0;
        $percentual = $valorVenda > 0 ? ($valorComissao / $valorVenda) * 100 : 0;

        return [
            'valor' => $valorComissao,
            'percentual' => $percentual,
        ];
    }

    public static function criarVenda($afiliadoId, $userId, $planoTipo, $valorVenda, $assinaturaId = null, $transactionId = null)
    {
        $comissao = self::calcularComissao($planoTipo, $valorVenda);

        return self::create([
            'afiliado_id' => $afiliadoId,
            'user_id' => $userId,
            'assinatura_id' => $assinaturaId,
            'transaction_id' => $transactionId,
            'plano_tipo' => $planoTipo,
            'valor_venda' => $valorVenda,
            'comissao' => $comissao['valor'],
            'percentual_comissao' => $comissao['percentual'],
            'status' => 'pendente',
        ]);
    }
}
