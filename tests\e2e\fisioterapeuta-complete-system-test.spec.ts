import { test, expect } from '@playwright/test';
import { 
  ensureFisioterapeutaAuthenticated,
  navigateToFisioterapeutaPage,
  waitForPageLoad,
  checkResponsiveness,
  verifyFisioterapeutaLayout
} from './helpers/fisioterapeuta-helpers';

test.describe('Fisioterapeuta - Teste Completo do Sistema', () => {
  
  let testResults: Array<{
    page: string;
    test: string;
    status: 'PASS' | 'FAIL';
    error?: string;
    duration?: number;
  }> = [];

  test.beforeEach(async ({ page }) => {
    await ensureFisioterapeutaAuthenticated(page);
  });

  test('deve testar navegação completa entre todas as páginas', async ({ page }) => {
    const pages = [
      'dashboard',
      'perfil', 
      'agenda',
      'pacientes',
      'disponibilidade',
      'horarios',
      'relatorios',
      'avaliacoes',
      'afiliados'
    ];

    for (const pageName of pages) {
      const startTime = Date.now();
      
      try {
        await navigateToFisioterapeutaPage(page, pageName);
        await waitForPageLoad(page);
        
        // Verificar se a página carregou corretamente
        await expect(page.locator('body')).toBeVisible();
        await verifyFisioterapeutaLayout(page);
        
        // Verificar se não há erros 404 ou 500
        const pageContent = await page.content();
        expect(pageContent).not.toContain('404');
        expect(pageContent).not.toContain('500');
        expect(pageContent).not.toContain('Error');
        
        const duration = Date.now() - startTime;
        testResults.push({
          page: pageName,
          test: 'Navegação e carregamento',
          status: 'PASS',
          duration
        });
        
      } catch (error) {
        testResults.push({
          page: pageName,
          test: 'Navegação e carregamento',
          status: 'FAIL',
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  });

  test('deve testar responsividade em todas as páginas principais', async ({ page }) => {
    const pages = ['dashboard', 'perfil', 'agenda', 'pacientes'];
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 1024, height: 768, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];

    for (const pageName of pages) {
      await navigateToFisioterapeutaPage(page, pageName);
      
      for (const viewport of viewports) {
        const startTime = Date.now();
        
        try {
          await page.setViewportSize({ width: viewport.width, height: viewport.height });
          await page.waitForTimeout(500);
          
          // Verificar se elementos principais estão visíveis
          await expect(page.locator('body')).toBeVisible();
          await expect(page.locator('h1, h2')).toBeVisible();
          
          const duration = Date.now() - startTime;
          testResults.push({
            page: `${pageName} (${viewport.name})`,
            test: 'Responsividade',
            status: 'PASS',
            duration
          });
          
        } catch (error) {
          testResults.push({
            page: `${pageName} (${viewport.name})`,
            test: 'Responsividade',
            status: 'FAIL',
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
    }
    
    // Voltar para desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
  });

  test('deve testar funcionalidades críticas de cada página', async ({ page }) => {
    // Dashboard - verificar estatísticas
    try {
      await navigateToFisioterapeutaPage(page, 'dashboard');
      await waitForPageLoad(page);
      
      const statsCards = page.locator('.stats-card, [data-testid="stats"], .card');
      if (await statsCards.count() > 0) {
        await expect(statsCards.first()).toBeVisible();
      }
      
      testResults.push({
        page: 'dashboard',
        test: 'Exibição de estatísticas',
        status: 'PASS'
      });
    } catch (error) {
      testResults.push({
        page: 'dashboard',
        test: 'Exibição de estatísticas',
        status: 'FAIL',
        error: error instanceof Error ? error.message : String(error)
      });
    }

    // Perfil - verificar campos principais
    try {
      await navigateToFisioterapeutaPage(page, 'perfil');
      await waitForPageLoad(page);
      
      await expect(page.locator('input[name="crefito"], input[id="crefito"]')).toBeVisible();
      await expect(page.locator('textarea[name="bio"], textarea[id="bio"]')).toBeVisible();
      
      testResults.push({
        page: 'perfil',
        test: 'Campos de perfil visíveis',
        status: 'PASS'
      });
    } catch (error) {
      testResults.push({
        page: 'perfil',
        test: 'Campos de perfil visíveis',
        status: 'FAIL',
        error: error instanceof Error ? error.message : String(error)
      });
    }

    // Agenda - verificar filtros
    try {
      await navigateToFisioterapeutaPage(page, 'agenda');
      await waitForPageLoad(page);
      
      const hasCalendarOrList = 
        (await page.locator('.calendar, [data-testid="calendar"]').count() > 0) ||
        (await page.locator('.agendamentos-list, [data-testid="agendamentos-list"]').count() > 0);
      
      expect(hasCalendarOrList).toBeTruthy();
      
      testResults.push({
        page: 'agenda',
        test: 'Visualização de agenda',
        status: 'PASS'
      });
    } catch (error) {
      testResults.push({
        page: 'agenda',
        test: 'Visualização de agenda',
        status: 'FAIL',
        error: error instanceof Error ? error.message : String(error)
      });
    }

    // Pacientes - verificar lista
    try {
      await navigateToFisioterapeutaPage(page, 'pacientes');
      await waitForPageLoad(page);
      
      const hasPacientesListOrMessage = 
        (await page.locator('.pacientes-list, [data-testid="pacientes-list"]').count() > 0) ||
        (await page.locator('text="Nenhum paciente", text="Sem pacientes"').count() > 0);
      
      expect(hasPacientesListOrMessage).toBeTruthy();
      
      testResults.push({
        page: 'pacientes',
        test: 'Lista de pacientes',
        status: 'PASS'
      });
    } catch (error) {
      testResults.push({
        page: 'pacientes',
        test: 'Lista de pacientes',
        status: 'FAIL',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  test('deve verificar segurança e performance básica', async ({ page }) => {
    const pages = ['dashboard', 'perfil', 'agenda'];
    
    for (const pageName of pages) {
      const startTime = Date.now();
      
      try {
        await navigateToFisioterapeutaPage(page, pageName);
        await waitForPageLoad(page);
        
        const loadTime = Date.now() - startTime;
        
        // Verificar se carregou em tempo razoável (menos de 5 segundos)
        expect(loadTime).toBeLessThan(5000);
        
        // Verificar se há token CSRF
        const pageContent = await page.content();
        const hasCsrfToken = pageContent.includes('csrf-token') || pageContent.includes('_token');
        
        // Verificar se não há informações sensíveis expostas
        expect(pageContent).not.toContain('password');
        expect(pageContent).not.toContain('secret');
        
        testResults.push({
          page: pageName,
          test: 'Segurança e performance',
          status: 'PASS',
          duration: loadTime
        });
        
      } catch (error) {
        testResults.push({
          page: pageName,
          test: 'Segurança e performance',
          status: 'FAIL',
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  });

  test('deve verificar acessibilidade básica', async ({ page }) => {
    const pages = ['dashboard', 'perfil'];
    
    for (const pageName of pages) {
      try {
        await navigateToFisioterapeutaPage(page, pageName);
        await waitForPageLoad(page);
        
        // Verificar se há elementos com roles apropriados
        const navigation = page.locator('[role="navigation"], nav');
        const main = page.locator('[role="main"], main');
        const buttons = page.locator('button, [role="button"]');
        
        if (await navigation.count() > 0) {
          await expect(navigation.first()).toBeVisible();
        }
        
        if (await buttons.count() > 0) {
          // Verificar se botões têm texto ou aria-label
          const firstButton = buttons.first();
          const buttonText = await firstButton.textContent();
          const ariaLabel = await firstButton.getAttribute('aria-label');
          
          expect(buttonText || ariaLabel).toBeTruthy();
        }
        
        testResults.push({
          page: pageName,
          test: 'Acessibilidade básica',
          status: 'PASS'
        });
        
      } catch (error) {
        testResults.push({
          page: pageName,
          test: 'Acessibilidade básica',
          status: 'FAIL',
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  });

  test.afterAll(async () => {
    // Gerar relatório final
    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.status === 'PASS').length;
    const failedTests = testResults.filter(r => r.status === 'FAIL').length;
    
    console.log('\n=== RELATÓRIO FINAL DE TESTES DO SISTEMA FISIOTERAPEUTA ===\n');
    console.log(`Total de testes: ${totalTests}`);
    console.log(`Testes aprovados: ${passedTests}`);
    console.log(`Testes falharam: ${failedTests}`);
    console.log(`Taxa de sucesso: ${((passedTests / totalTests) * 100).toFixed(2)}%\n`);
    
    if (failedTests > 0) {
      console.log('=== TESTES QUE FALHARAM ===\n');
      testResults
        .filter(r => r.status === 'FAIL')
        .forEach(result => {
          console.log(`❌ ${result.page} - ${result.test}`);
          if (result.error) {
            console.log(`   Erro: ${result.error}`);
          }
          console.log('');
        });
    }
    
    console.log('=== RESUMO POR PÁGINA ===\n');
    const pagesSummary = testResults.reduce((acc, result) => {
      const page = result.page.split(' (')[0]; // Remove viewport info
      if (!acc[page]) {
        acc[page] = { total: 0, passed: 0, failed: 0 };
      }
      acc[page].total++;
      if (result.status === 'PASS') {
        acc[page].passed++;
      } else {
        acc[page].failed++;
      }
      return acc;
    }, {} as Record<string, { total: number; passed: number; failed: number }>);
    
    Object.entries(pagesSummary).forEach(([page, stats]) => {
      const successRate = ((stats.passed / stats.total) * 100).toFixed(2);
      console.log(`📄 ${page}: ${stats.passed}/${stats.total} (${successRate}%)`);
    });
    
    console.log('\n=== SISTEMA FISIOTERAPEUTA - STATUS GERAL ===');
    if (failedTests === 0) {
      console.log('✅ SISTEMA 100% FUNCIONAL - PRONTO PARA PRODUÇÃO');
    } else if (failedTests <= totalTests * 0.1) {
      console.log('⚠️  SISTEMA FUNCIONAL COM PEQUENOS PROBLEMAS - REVISAR ITENS FALHADOS');
    } else {
      console.log('❌ SISTEMA COM PROBLEMAS CRÍTICOS - NECESSÁRIA CORREÇÃO ANTES DA PRODUÇÃO');
    }
  });
});
