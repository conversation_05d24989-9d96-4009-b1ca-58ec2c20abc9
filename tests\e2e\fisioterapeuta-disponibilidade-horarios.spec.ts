import { test, expect } from '@playwright/test';
import { 
  ensureFisioterapeutaAuthenticated,
  navigateToFisioterapeutaPage,
  waitForPageLoad,
  waitForNotification
} from './helpers/fisioterapeuta-helpers';

test.describe('Fisioterapeuta - Disponibilidade e Horários', () => {
  
  test.beforeEach(async ({ page }) => {
    await ensureFisioterapeutaAuthenticated(page);
  });

  test('deve carregar página de disponibilidade', async ({ page }) => {
    await navigateToFisioterapeutaPage(page, 'disponibilidade');
    await waitForPageLoad(page);
    
    await expect(page.locator('h1, h2')).toContainText(/disponibilidade/i);
  });

  test('deve carregar página de horários', async ({ page }) => {
    await navigateToFisioterapeutaPage(page, 'horarios');
    await waitForPageLoad(page);
    
    await expect(page.locator('h1, h2')).toContainText(/horários|configuração/i);
  });

  test('deve permitir configurar horários base', async ({ page }) => {
    await navigateToFisioterapeutaPage(page, 'horarios');
    await waitForPageLoad(page);
    
    // Procurar seção de horários base
    const horariosBaseSection = page.locator('text="Horários Base", [data-testid="horarios-base"]');
    
    if (await horariosBaseSection.count() > 0) {
      await expect(horariosBaseSection).toBeVisible();
      
      // Procurar campos de horário
      const horaInicioField = page.locator('input[name*="hora_inicio"], input[type="time"]');
      const horaFimField = page.locator('input[name*="hora_fim"], input[type="time"]');
      
      if (await horaInicioField.count() > 0 && await horaFimField.count() > 0) {
        await horaInicioField.first().fill('08:00');
        await horaFimField.first().fill('18:00');
        
        const salvarButton = page.locator('button:has-text("Salvar"), button[type="submit"]');
        if (await salvarButton.count() > 0) {
          await salvarButton.click();
          await waitForNotification(page);
        }
      }
    }
  });

  test('deve permitir criar exceções de horário', async ({ page }) => {
    await navigateToFisioterapeutaPage(page, 'horarios');
    await waitForPageLoad(page);
    
    // Procurar seção de exceções
    const excecoesSection = page.locator('text="Exceções", [data-testid="excecoes"]');
    
    if (await excecoesSection.count() > 0) {
      await expect(excecoesSection).toBeVisible();
      
      // Procurar botão de adicionar exceção
      const addExcecaoButton = page.locator('button:has-text("Adicionar"), button:has-text("Nova Exceção")');
      
      if (await addExcecaoButton.count() > 0) {
        await addExcecaoButton.click();
        
        // Preencher dados da exceção
        const dataField = page.locator('input[type="date"], input[name*="data"]');
        if (await dataField.count() > 0) {
          const tomorrow = new Date();
          tomorrow.setDate(tomorrow.getDate() + 1);
          await dataField.fill(tomorrow.toISOString().split('T')[0]);
        }
        
        const motivoField = page.locator('input[name*="motivo"], textarea[name*="motivo"]');
        if (await motivoField.count() > 0) {
          await motivoField.fill('Compromisso pessoal');
        }
        
        const salvarButton = page.locator('button:has-text("Salvar"), button:has-text("Confirmar")');
        if (await salvarButton.count() > 0) {
          await salvarButton.click();
          await waitForNotification(page);
        }
      }
    }
  });

  test('deve permitir configurar feriados', async ({ page }) => {
    await navigateToFisioterapeutaPage(page, 'horarios');
    await waitForPageLoad(page);
    
    // Procurar configuração de feriados
    const feriadosSection = page.locator('text="Feriados", [data-testid="feriados"]');
    
    if (await feriadosSection.count() > 0) {
      await expect(feriadosSection).toBeVisible();
      
      // Procurar toggle para considerar feriados
      const feriadosToggle = page.locator('input[type="checkbox"][name*="feriado"]');
      
      if (await feriadosToggle.count() > 0) {
        await feriadosToggle.check();
        
        const salvarButton = page.locator('button:has-text("Salvar")');
        if (await salvarButton.count() > 0) {
          await salvarButton.click();
          await waitForNotification(page);
        }
      }
    }
  });

  test('deve permitir preview de horários', async ({ page }) => {
    await navigateToFisioterapeutaPage(page, 'horarios');
    await waitForPageLoad(page);
    
    // Procurar botão de preview
    const previewButton = page.locator('button:has-text("Preview"), button:has-text("Visualizar")');
    
    if (await previewButton.count() > 0) {
      await previewButton.click();
      
      // Verificar se abriu modal ou seção de preview
      const previewSection = page.locator('.preview, [data-testid="preview"]');
      if (await previewSection.count() > 0) {
        await expect(previewSection).toBeVisible();
      }
    }
  });

  test('deve permitir alternar disponibilidade geral', async ({ page }) => {
    await navigateToFisioterapeutaPage(page, 'disponibilidade');
    await waitForPageLoad(page);
    
    // Procurar toggle de disponibilidade
    const availabilityToggle = page.locator('input[type="checkbox"][name*="available"], input[type="checkbox"][name*="disponivel"]');
    
    if (await availabilityToggle.count() > 0) {
      const isChecked = await availabilityToggle.isChecked();
      
      // Alternar estado
      await availabilityToggle.click();
      
      // Verificar se mudou
      const newState = await availabilityToggle.isChecked();
      expect(newState).toBe(!isChecked);
      
      // Salvar alteração
      const salvarButton = page.locator('button:has-text("Salvar")');
      if (await salvarButton.count() > 0) {
        await salvarButton.click();
        await waitForNotification(page);
      }
    }
  });
});
