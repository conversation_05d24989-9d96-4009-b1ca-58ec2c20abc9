import { useState } from 'react';
import { useForm, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Plus, Edit, Trash2, Clock } from 'lucide-react';
import { toast } from 'sonner';

interface HorarioBase {
    id: number;
    dia_semana: number;
    hora_inicio: string;
    hora_fim: string;
    periodo_nome?: string;
    ativo: boolean;
}

interface Props {
    horariosBase: { [key: number]: HorarioBase[] };
    diasSemana: { [key: number]: string };
}

export function HorarioBaseConfig({ horariosBase, diasSemana }: Props) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingHorario, setEditingHorario] = useState<HorarioBase | null>(null);

    const { data, setData, post, put, processing, errors, reset } = useForm({
        dia_semana: '',
        hora_inicio: '',
        hora_fim: '',
        periodo_nome: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        const url = editingHorario 
            ? route('fisioterapeuta.horarios.base.update', editingHorario.id)
            : route('fisioterapeuta.horarios.base.store');

        const method = editingHorario ? put : post;

        method(url, {
            onSuccess: () => {
                setIsDialogOpen(false);
                setEditingHorario(null);
                reset();
                toast.success(editingHorario ? 'Horário atualizado!' : 'Horário adicionado!');
            },
            onError: (errors) => {
                if (errors.conflito) {
                    toast.error(errors.conflito);
                }
            },
        });
    };

    const handleEdit = (horario: HorarioBase) => {
        setEditingHorario(horario);
        setData({
            dia_semana: horario.dia_semana.toString(),
            hora_inicio: horario.hora_inicio,
            hora_fim: horario.hora_fim,
            periodo_nome: horario.periodo_nome || '',
        });
        setIsDialogOpen(true);
    };

    const handleDelete = (horario: HorarioBase) => {
        if (confirm('Tem certeza que deseja remover este horário?')) {
            router.delete(route('fisioterapeuta.horarios.base.destroy', horario.id), {
                onSuccess: () => {
                    toast.success('Horário removido!');
                },
            });
        }
    };

    const openNewDialog = () => {
        setEditingHorario(null);
        reset();
        setIsDialogOpen(true);
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Horários Base</h2>
                    <p className="text-muted-foreground">
                        Configure seus horários padrão para cada dia da semana
                    </p>
                </div>
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                    <DialogTrigger asChild>
                        <Button onClick={openNewDialog} className="gap-2">
                            <Plus className="h-4 w-4" />
                            Adicionar Horário
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>
                                {editingHorario ? 'Editar Horário' : 'Novo Horário Base'}
                            </DialogTitle>
                            <DialogDescription>
                                Configure um período de atendimento para um dia da semana
                            </DialogDescription>
                        </DialogHeader>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="grid gap-2">
                                <Label htmlFor="dia_semana">Dia da Semana</Label>
                                <Select
                                    value={data.dia_semana}
                                    onValueChange={(value) => setData('dia_semana', value)}
                                    disabled={!!editingHorario}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Selecione o dia" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(diasSemana).map(([value, label]) => (
                                            <SelectItem key={value} value={value}>
                                                {label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.dia_semana && (
                                    <p className="text-sm text-destructive">{errors.dia_semana}</p>
                                )}
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="hora_inicio">Hora Início</Label>
                                    <Input
                                        id="hora_inicio"
                                        type="time"
                                        value={data.hora_inicio}
                                        onChange={(e) => setData('hora_inicio', e.target.value)}
                                        required
                                    />
                                    {errors.hora_inicio && (
                                        <p className="text-sm text-destructive">{errors.hora_inicio}</p>
                                    )}
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="hora_fim">Hora Fim</Label>
                                    <Input
                                        id="hora_fim"
                                        type="time"
                                        value={data.hora_fim}
                                        onChange={(e) => setData('hora_fim', e.target.value)}
                                        required
                                    />
                                    {errors.hora_fim && (
                                        <p className="text-sm text-destructive">{errors.hora_fim}</p>
                                    )}
                                </div>
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="periodo_nome">Nome do Período (opcional)</Label>
                                <Input
                                    id="periodo_nome"
                                    value={data.periodo_nome}
                                    onChange={(e) => setData('periodo_nome', e.target.value)}
                                    placeholder="Ex: Manhã, Tarde, Noite"
                                />
                                {errors.periodo_nome && (
                                    <p className="text-sm text-destructive">{errors.periodo_nome}</p>
                                )}
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setIsDialogOpen(false)}
                                >
                                    Cancelar
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {editingHorario ? 'Atualizar' : 'Adicionar'}
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Grid de Dias da Semana */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {Object.entries(diasSemana).map(([diaNumero, diaNome]) => {
                    const horariosDoDia = horariosBase[parseInt(diaNumero)] || [];
                    
                    return (
                        <Card key={diaNumero}>
                            <CardHeader className="pb-3">
                                <CardTitle className="text-lg">{diaNome}</CardTitle>
                                <CardDescription>
                                    {horariosDoDia.length === 0 
                                        ? 'Nenhum horário configurado'
                                        : `${horariosDoDia.length} período(s) configurado(s)`
                                    }
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-2">
                                {horariosDoDia.length === 0 ? (
                                    <div className="text-center py-4 text-muted-foreground">
                                        <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                        <p className="text-sm">Sem horários</p>
                                    </div>
                                ) : (
                                    horariosDoDia.map((horario) => (
                                        <div
                                            key={horario.id}
                                            className="flex items-center justify-between p-3 border rounded-lg"
                                        >
                                            <div className="flex-1">
                                                <div className="flex items-center gap-2">
                                                    <Badge variant={horario.ativo ? 'default' : 'secondary'}>
                                                        {horario.hora_inicio} - {horario.hora_fim}
                                                    </Badge>
                                                    {horario.periodo_nome && (
                                                        <span className="text-sm text-muted-foreground">
                                                            {horario.periodo_nome}
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex gap-1">
                                                <Button
                                                    size="sm"
                                                    variant="ghost"
                                                    onClick={() => handleEdit(horario)}
                                                >
                                                    <Edit className="h-3 w-3" />
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    variant="ghost"
                                                    onClick={() => handleDelete(horario)}
                                                >
                                                    <Trash2 className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        </div>
                                    ))
                                )}
                            </CardContent>
                        </Card>
                    );
                })}
            </div>
        </div>
    );
}
