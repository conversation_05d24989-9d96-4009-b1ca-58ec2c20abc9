<?php

use App\Models\User;

test('guests are redirected to the login page', function () {
    $this->get('/dashboard')->assertRedirect('/login');
});

test('authenticated users can visit the dashboard', function () {
    $user = User::factory()->create([
        'active' => true,
        'role' => 'paciente',
        'email_verified_at' => now(), // Email verificado
        'has_subscription' => false
    ]);

    $this->actingAs($user);

    $response = $this->get('/dashboard');

    // Pode redirecionar para seleção de planos se não tiver assinatura
    $response->assertStatus(302);

    // Verificar se redirecionou para uma página válida
    $location = $response->headers->get('Location');
    expect($location)->toContain('/');
});