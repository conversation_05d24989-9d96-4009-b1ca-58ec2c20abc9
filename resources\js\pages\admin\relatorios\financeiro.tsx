import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { AlertTriangle, ArrowLeft, CreditCard, DollarSign, Download, Filter, RefreshCw, TrendingDown, TrendingUp } from 'lucide-react';
import { useState } from 'react';
import { Bar, BarChart, CartesianGrid, Cell, Line, LineChart, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface Stats {
    receita_total: number;
    receita_recorrente: number;
    pagamentos_pendentes: number;
    pagamentos_vencidos: number;
    ticket_medio: number;
    churn_rate: number;
    mrr: number;
    ltv: number;
}

interface EvolucaoReceita {
    data: string;
    receita: number;
}

interface TopPlano {
    plano: string;
    receita: number;
    pagamentos: number;
}

interface InadimplenciaItem {
    faixa: string;
    quantidade: number;
    valor_total: number;
}

interface Inadimplencia {
    total_pendentes: number;
    total_vencidos: number;
    taxa_inadimplencia: number;
    por_idade: InadimplenciaItem[];
}

interface Props {
    stats: Stats;
    evolucaoReceita: EvolucaoReceita[];
    topPlanos: TopPlano[];
    inadimplencia: Inadimplencia;
    filtros: {
        data_inicio?: string;
        data_fim?: string;
    };
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function RelatorioFinanceiro({ stats, evolucaoReceita, topPlanos, inadimplencia, filtros }: Props) {
    const [dataInicio, setDataInicio] = useState(filtros.data_inicio || '');
    const [dataFim, setDataFim] = useState(filtros.data_fim || '');
    const [isLoading, setIsLoading] = useState(false);

    const handleFilter = () => {
        setIsLoading(true);
        router.get(
            route('admin.relatorios.financeiro'),
            {
                data_inicio: dataInicio,
                data_fim: dataFim,
            },
            {
                preserveState: true,
                onFinish: () => setIsLoading(false),
            },
        );
    };

    const handleExport = (formato: string) => {
        const params = new URLSearchParams({
            tipo: 'financeiro',
            formato,
            data_inicio: dataInicio,
            data_fim: dataFim,
        });

        window.open(`${route('admin.relatorios.export')}?${params.toString()}`, '_blank');
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const formatNumber = (value: number) => {
        return new Intl.NumberFormat('pt-BR').format(value);
    };

    const getTrendIcon = (value: number) => {
        if (value > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
        if (value < 0) return <TrendingDown className="h-4 w-4 text-red-600" />;
        return null;
    };

    const getTrendColor = (value: number) => {
        if (value > 0) return 'text-green-600';
        if (value < 0) return 'text-red-600';
        return 'text-gray-600';
    };

    return (
        <AppLayout>
            <Head title="Relatório Financeiro" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href={route('admin.relatorios.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Relatório Financeiro</h1>
                            <p className="text-muted-foreground">Análise detalhada das métricas financeiras</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={() => handleExport('csv')} className="gap-2">
                            <Download className="h-4 w-4" />
                            Exportar CSV
                        </Button>
                        <Button variant="outline" onClick={() => handleExport('excel')} className="gap-2">
                            <Download className="h-4 w-4" />
                            Exportar Excel
                        </Button>
                    </div>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div className="space-y-2">
                                <Label htmlFor="data_inicio">Data Início</Label>
                                <Input id="data_inicio" type="date" value={dataInicio} onChange={(e) => setDataInicio(e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="data_fim">Data Fim</Label>
                                <Input id="data_fim" type="date" value={dataFim} onChange={(e) => setDataFim(e.target.value)} />
                            </div>
                            <div className="flex items-end">
                                <Button onClick={handleFilter} disabled={isLoading} className="w-full gap-2">
                                    {isLoading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Filter className="h-4 w-4" />}
                                    Aplicar Filtros
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Métricas Principais */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Receita Total</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.receita_total)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">MRR</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.mrr)}</div>
                            <p className="text-xs text-muted-foreground">Monthly Recurring Revenue</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Ticket Médio</CardTitle>
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.ticket_medio)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">LTV</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.ltv)}</div>
                            <p className="text-xs text-muted-foreground">Lifetime Value</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Receita Recorrente</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.receita_recorrente)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pagamentos Pendentes</CardTitle>
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600">{formatCurrency(stats.pagamentos_pendentes)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pagamentos Vencidos</CardTitle>
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{formatCurrency(stats.pagamentos_vencidos)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Taxa de Churn</CardTitle>
                            {getTrendIcon(-stats.churn_rate)}
                        </CardHeader>
                        <CardContent>
                            <div className={`text-2xl font-bold ${getTrendColor(-stats.churn_rate)}`}>{stats.churn_rate}%</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Gráficos */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Evolução da Receita</CardTitle>
                            <CardDescription>Receita diária no período selecionado</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <LineChart data={evolucaoReceita}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="data" />
                                    <YAxis />
                                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Receita']} />
                                    <Line type="monotone" dataKey="receita" stroke="#22c55e" strokeWidth={2} />
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Top Planos por Receita</CardTitle>
                            <CardDescription>Planos que mais geraram receita</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <BarChart data={topPlanos}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="plano" />
                                    <YAxis />
                                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Receita']} />
                                    <Bar dataKey="receita" fill="#3b82f6" />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Análise de Inadimplência */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Análise de Inadimplência</CardTitle>
                            <CardDescription>Distribuição de pagamentos em atraso por idade</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-yellow-600">{formatNumber(inadimplencia.total_pendentes)}</div>
                                        <p className="text-sm text-muted-foreground">Total Pendentes</p>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-red-600">{inadimplencia.taxa_inadimplencia}%</div>
                                        <p className="text-sm text-muted-foreground">Taxa de Inadimplência</p>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    {inadimplencia.por_idade.map((item, index) => (
                                        <div key={index} className="flex items-center justify-between rounded bg-muted p-2">
                                            <span className="text-sm font-medium">{item.faixa}</span>
                                            <div className="text-right">
                                                <div className="text-sm font-bold">{formatNumber(item.quantidade)}</div>
                                                <div className="text-xs text-muted-foreground">{formatCurrency(item.valor_total)}</div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Distribuição de Receita por Plano</CardTitle>
                            <CardDescription>Participação de cada plano na receita total</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <PieChart>
                                    <Pie
                                        data={topPlanos}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ plano, percent }) => `${plano} ${((percent || 0) * 100).toFixed(0)}%`}
                                        outerRadius={80}
                                        fill="#8884d8"
                                        dataKey="receita"
                                    >
                                        {topPlanos.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                        ))}
                                    </Pie>
                                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Receita']} />
                                </PieChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
