<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('nome'); // Nome do template
            $table->string('assunto'); // Assunto do email
            $table->longText('conteudo_html'); // Conteúdo HTML do email
            $table->longText('conteudo_texto')->nullable(); // Conteúdo em texto puro
            $table->json('variaveis_disponiveis')->nullable(); // Variáveis que podem ser usadas no template
            $table->enum('tipo', [
                'afiliado_aprovacao',
                'afiliado_rejeicao',
                'pagamento_comissao_aprovado',
                'pagamento_comissao_rejeitado',
                'pagamento_comissao_pago',
                'nova_venda_afiliado',
                'lembrete_pagamento_comissao'
            ]); // Tipo do template
            $table->boolean('ativo')->default(true); // Se o template está ativo
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade'); // Admin que criou
            $table->timestamps();

            // Índices para performance
            $table->index(['tipo', 'ativo']);
            $table->index('ativo');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_templates');
    }
};
