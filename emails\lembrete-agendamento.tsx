import {
  Button,
  Heading,
  Hr,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';
import { EmailLayout } from './components/layout';

interface LembreteAgendamentoEmailProps {
  pacienteNome: string;
  fisioterapeutaNome: string;
  dataHora: string;
  endereco: string;
  horasAntecedencia: number;
  valor?: string;
  agendamentoUrl?: string;
  whatsappFisioterapeuta?: string;
}

export const LembreteAgendamentoEmail = ({
  pacienteNome = 'Paciente',
  fisioterapeutaNome = 'Fisioterapeuta',
  dataHora = '01/01/2025 às 10:00',
  endereco = 'Endereço não informado',
  horasAntecedencia = 24,
  valor,
  agendamentoUrl = 'https://f4fisio.com.br/agendamentos',
  whatsappFisioterapeuta,
}: LembreteAgendamentoEmailProps) => {
  const getTempoTexto = () => {
    if (horasAntecedencia === 24) {
      return 'amanhã';
    } else if (horasAntecedencia < 24) {
      return `em ${horasAntecedencia} horas`;
    } else {
      const dias = Math.floor(horasAntecedencia / 24);
      return `em ${dias} dia${dias > 1 ? 's' : ''}`;
    }
  };

  return (
    <EmailLayout preview={`Lembrete: Consulta ${getTempoTexto()} - ${dataHora}`}>
      <Section style={reminderBanner}>
        <Text style={reminderIcon}>⏰</Text>
        <Text style={reminderText}>
          LEMBRETE DE CONSULTA
        </Text>
      </Section>
      
      <Heading style={h1}>
        Sua consulta é {getTempoTexto()}!
      </Heading>
      
      <Text style={text}>
        Olá <strong>{pacienteNome}</strong>,
      </Text>
      
      <Text style={text}>
        Este é um lembrete amigável sobre sua consulta de fisioterapia agendada para {getTempoTexto()}.
      </Text>
      
      <Section style={detailsCard}>
        <Heading style={cardTitle}>Detalhes da Consulta</Heading>
        
        <div style={detailRow}>
          <Text style={detailLabel}>📅 Data e Horário:</Text>
          <Text style={detailValue}>{dataHora}</Text>
        </div>
        
        <div style={detailRow}>
          <Text style={detailLabel}>👨‍⚕️ Fisioterapeuta:</Text>
          <Text style={detailValue}>{fisioterapeutaNome}</Text>
        </div>
        
        <div style={detailRow}>
          <Text style={detailLabel}>📍 Local:</Text>
          <Text style={detailValue}>{endereco}</Text>
        </div>
        
        {valor && (
          <div style={detailRow}>
            <Text style={detailLabel}>💰 Valor:</Text>
            <Text style={detailValue}>{valor}</Text>
          </div>
        )}
      </Section>
      
      <Section style={buttonContainer}>
        <Button style={button} href={agendamentoUrl}>
          Ver Detalhes Completos
        </Button>
      </Section>
      
      {whatsappFisioterapeuta && (
        <Section style={whatsappSection}>
          <Text style={text}>
            <strong>Precisa falar com o fisioterapeuta?</strong>
          </Text>
          <Button style={whatsappButton} href={`https://wa.me/${whatsappFisioterapeuta}`}>
            💬 Conversar no WhatsApp
          </Button>
        </Section>
      )}
      
      <Hr style={hr} />
      
      <Section style={checklistSection}>
        <Heading style={h2}>Checklist para a Consulta</Heading>
        
        <div style={checklistItem}>
          <Text style={checkIcon}>✅</Text>
          <Text style={checkText}>Chegue com 10 minutos de antecedência</Text>
        </div>
        
        <div style={checklistItem}>
          <Text style={checkIcon}>✅</Text>
          <Text style={checkText}>Traga documento de identificação</Text>
        </div>
        
        <div style={checklistItem}>
          <Text style={checkIcon}>✅</Text>
          <Text style={checkText}>Use roupas confortáveis para exercícios</Text>
        </div>
        
        <div style={checklistItem}>
          <Text style={checkIcon}>✅</Text>
          <Text style={checkText}>Traga exames médicos (se houver)</Text>
        </div>
        
        <div style={checklistItem}>
          <Text style={checkIcon}>✅</Text>
          <Text style={checkText}>Confirme o endereço e meio de transporte</Text>
        </div>
      </Section>
      
      <Hr style={hr} />
      
      <Section style={cancelationSection}>
        <Text style={cancelationTitle}>
          <strong>Precisa reagendar ou cancelar?</strong>
        </Text>
        <Text style={cancelationText}>
          Entre em contato com antecedência mínima de 24 horas através do WhatsApp ou pela plataforma.
        </Text>
      </Section>
      
      <Text style={signature}>
        Nos vemos em breve!<br />
        Equipe F4 Fisio
      </Text>
    </EmailLayout>
  );
};

export default LembreteAgendamentoEmail;

const reminderBanner = {
  backgroundColor: '#f59e0b',
  padding: '16px',
  borderRadius: '8px',
  textAlign: 'center' as const,
  margin: '0 0 24px 0',
};

const reminderIcon = {
  fontSize: '24px',
  margin: '0 0 8px 0',
};

const reminderText = {
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0',
};

const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 0 20px 0',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px 0',
};

const detailsCard = {
  backgroundColor: '#f9fafb',
  border: '1px solid #e5e7eb',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
};

const cardTitle = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
};

const detailRow = {
  margin: '0 0 12px 0',
};

const detailLabel = {
  color: '#6b7280',
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0 0 4px 0',
};

const detailValue = {
  color: '#1f2937',
  fontSize: '16px',
  margin: '0',
};

const checklistSection = {
  backgroundColor: '#ecfdf5',
  border: '1px solid #10b981',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
};

const checklistItem = {
  display: 'flex',
  alignItems: 'center',
  margin: '0 0 12px 0',
};

const checkIcon = {
  fontSize: '16px',
  margin: '0 12px 0 0',
  minWidth: '20px',
};

const checkText = {
  color: '#374151',
  fontSize: '16px',
  margin: '0',
};

const cancelationSection = {
  backgroundColor: '#fef2f2',
  border: '1px solid #ef4444',
  borderRadius: '6px',
  padding: '16px',
  margin: '20px 0',
};

const cancelationTitle = {
  color: '#dc2626',
  fontSize: '16px',
  margin: '0 0 8px 0',
};

const cancelationText = {
  color: '#374151',
  fontSize: '14px',
  margin: '0',
};

const signature = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '24px 0 0 0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '24px 0',
};

const button = {
  backgroundColor: '#3b82f6',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const whatsappSection = {
  textAlign: 'center' as const,
  margin: '20px 0',
};

const whatsappButton = {
  backgroundColor: '#25d366',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  margin: '8px 0',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '24px 0',
};
