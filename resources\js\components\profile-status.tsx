import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Link } from '@inertiajs/react';
import { AlertCircle, CheckCircle, Info, User, XCircle } from 'lucide-react';

interface ProfileStatus {
    is_complete: boolean;
    percentage: number;
    missing_fields: string[];
    missing_count: number;
    status: 'complete' | 'good' | 'fair' | 'poor' | 'incomplete';
    next_steps: string[];
}

interface Props {
    status: ProfileStatus;
    completionUrl?: string;
    showCard?: boolean;
    showAlert?: boolean;
    className?: string;
}

export function ProfileStatus({ 
    status, 
    completionUrl, 
    showCard = false, 
    showAlert = true,
    className = '' 
}: Props) {
    const getStatusColor = (statusLevel: string) => {
        switch (statusLevel) {
            case 'complete':
                return 'text-green-600';
            case 'good':
                return 'text-blue-600';
            case 'fair':
                return 'text-yellow-600';
            case 'poor':
                return 'text-orange-600';
            case 'incomplete':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    const getStatusIcon = (statusLevel: string) => {
        switch (statusLevel) {
            case 'complete':
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case 'good':
            case 'fair':
                return <Info className="h-4 w-4 text-blue-600" />;
            case 'poor':
            case 'incomplete':
                return <AlertCircle className="h-4 w-4 text-red-600" />;
            default:
                return <XCircle className="h-4 w-4 text-gray-600" />;
        }
    };

    const getStatusText = (statusLevel: string) => {
        switch (statusLevel) {
            case 'complete':
                return 'Completo';
            case 'good':
                return 'Quase completo';
            case 'fair':
                return 'Parcialmente completo';
            case 'poor':
                return 'Incompleto';
            case 'incomplete':
                return 'Muito incompleto';
            default:
                return 'Desconhecido';
        }
    };

    const getAlertVariant = (statusLevel: string) => {
        switch (statusLevel) {
            case 'complete':
                return 'default';
            case 'good':
            case 'fair':
                return 'default';
            case 'poor':
            case 'incomplete':
                return 'destructive';
            default:
                return 'default';
        }
    };

    if (showCard) {
        return (
            <Card className={className}>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <User className="h-5 w-5" />
                        Status do Perfil
                    </CardTitle>
                    <CardDescription>
                        Mantenha seu perfil completo para melhor experiência
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Progress Bar */}
                    <div className="space-y-2">
                        <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">Completude</span>
                            <div className="flex items-center gap-2">
                                <Badge variant={status.is_complete ? "default" : "secondary"}>
                                    {status.percentage}%
                                </Badge>
                                {getStatusIcon(status.status)}
                            </div>
                        </div>
                        <Progress value={status.percentage} className="h-2" />
                        <p className={`text-sm ${getStatusColor(status.status)}`}>
                            {getStatusText(status.status)}
                        </p>
                    </div>

                    {/* Missing Fields */}
                    {status.missing_count > 0 && (
                        <div className="space-y-2">
                            <h4 className="text-sm font-medium">Próximos passos:</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                                {status.next_steps.slice(0, 3).map((step, index) => (
                                    <li key={index} className="flex items-start gap-2">
                                        <span className="text-muted-foreground">•</span>
                                        {step}
                                    </li>
                                ))}
                                {status.next_steps.length > 3 && (
                                    <li className="text-xs text-muted-foreground">
                                        +{status.next_steps.length - 3} mais...
                                    </li>
                                )}
                            </ul>
                        </div>
                    )}

                    {/* Action Button */}
                    {!status.is_complete && completionUrl && (
                        <Button asChild className="w-full">
                            <Link href={completionUrl}>
                                Completar Perfil
                            </Link>
                        </Button>
                    )}

                    {status.is_complete && (
                        <div className="flex items-center gap-2 text-sm text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            Perfil completo! Parabéns.
                        </div>
                    )}
                </CardContent>
            </Card>
        );
    }

    if (showAlert && !status.is_complete) {
        return (
            <Alert variant={getAlertVariant(status.status)} className={className}>
                {getStatusIcon(status.status)}
                <AlertDescription className="flex items-center justify-between">
                    <div>
                        <strong>Perfil {status.percentage}% completo.</strong>
                        {status.missing_count > 0 && (
                            <span className="ml-1">
                                Faltam {status.missing_count} informações.
                            </span>
                        )}
                    </div>
                    {completionUrl && (
                        <Button variant="outline" size="sm" asChild>
                            <Link href={completionUrl}>
                                Completar
                            </Link>
                        </Button>
                    )}
                </AlertDescription>
            </Alert>
        );
    }

    // Compact version (just progress bar)
    return (
        <div className={`space-y-2 ${className}`}>
            <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Perfil</span>
                <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">{status.percentage}%</span>
                    {getStatusIcon(status.status)}
                </div>
            </div>
            <Progress value={status.percentage} className="h-1" />
        </div>
    );
}
