import FormularioAfiliadoDashboard from '@/components/formulario-afiliado-dashboard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import { AlertCircle, CheckCircle, Clock, DollarSign, Handshake, TrendingUp, UserCheck, Users } from 'lucide-react';

const breadcrumbs = [
    { title: 'Início', href: '/paciente/dashboard' },
    { title: 'Programa de Afiliados', href: '/paciente/afiliados' },
];

export default function PacienteAfiliados() {
    const { auth } = usePage().props as any;
    const user = auth?.user;

    const getStatusInfo = () => {
        if (!user?.hasAfiliadoProfile) {
            return {
                status: 'not_registered',
                title: 'Torne-se um Afiliado F4 Fisio',
                description: 'Ganhe comissões recorrentes indicando nossos serviços',
                icon: Handshake,
                color: 'text-blue-600',
                bgColor: 'bg-blue-50',
                borderColor: 'border-blue-200',
            };
        }

        const afiliado = user.afiliado;
        switch (afiliado?.status) {
            case 'pendente':
                return {
                    status: 'pending',
                    title: 'Cadastro em Análise',
                    description: 'Seu cadastro está sendo analisado pela nossa equipe',
                    icon: Clock,
                    color: 'text-yellow-600',
                    bgColor: 'bg-yellow-50',
                    borderColor: 'border-yellow-200',
                };
            case 'aprovado':
                return {
                    status: 'approved',
                    title: 'Parabéns! Você é um Afiliado F4 Fisio',
                    description: 'Seu cadastro foi aprovado. Acesse o modo afiliado para começar',
                    icon: CheckCircle,
                    color: 'text-green-600',
                    bgColor: 'bg-green-50',
                    borderColor: 'border-green-200',
                };
            case 'rejeitado':
                return {
                    status: 'rejected',
                    title: 'Cadastro Não Aprovado',
                    description: 'Infelizmente seu cadastro não foi aprovado desta vez',
                    icon: AlertCircle,
                    color: 'text-red-600',
                    bgColor: 'bg-red-50',
                    borderColor: 'border-red-200',
                };
            default:
                return {
                    status: 'unknown',
                    title: 'Status Desconhecido',
                    description: 'Entre em contato com o suporte',
                    icon: AlertCircle,
                    color: 'text-gray-600',
                    bgColor: 'bg-gray-50',
                    borderColor: 'border-gray-200',
                };
        }
    };

    const statusInfo = getStatusInfo();

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Programa de Afiliados" />
            <div className="flex h-full flex-1 flex-col gap-6 overflow-x-auto p-4 md:p-6">
                {/* Header */}
                <div className="space-y-2">
                    <h1 className="text-2xl font-bold tracking-tight text-foreground md:text-3xl">Programa de Afiliados</h1>
                    <p className="text-base text-muted-foreground md:text-lg">
                        Ganhe comissões recorrentes indicando nossos serviços de fisioterapia
                    </p>
                </div>

                {/* Status Card */}
                <Card className={`${statusInfo.borderColor} ${statusInfo.bgColor}`}>
                    <CardHeader>
                        <CardTitle className={`flex items-center gap-2 ${statusInfo.color}`}>
                            <statusInfo.icon className="h-5 w-5" />
                            {statusInfo.title}
                        </CardTitle>
                        <CardDescription className={statusInfo.color}>{statusInfo.description}</CardDescription>
                    </CardHeader>
                    {user?.hasAfiliadoProfile && user.afiliado && (
                        <CardContent>
                            <div className="flex items-center gap-4">
                                <div>
                                    <p className="text-sm font-medium">Código do Afiliado</p>
                                    <p className="text-lg font-bold">{user.afiliado.codigo_afiliado}</p>
                                </div>
                                <Separator orientation="vertical" className="h-12" />
                                <div>
                                    <p className="text-sm font-medium">Status</p>
                                    <Badge variant={user.afiliado.status === 'aprovado' ? 'default' : 'secondary'}>
                                        {user.afiliado.status === 'aprovado'
                                            ? 'Aprovado'
                                            : user.afiliado.status === 'pendente'
                                              ? 'Pendente'
                                              : 'Rejeitado'}
                                    </Badge>
                                </div>
                                {user.afiliado.status === 'aprovado' && (
                                    <>
                                        <Separator orientation="vertical" className="h-12" />
                                        <div>
                                            <p className="text-sm font-medium">Comissões Este Mês</p>
                                            <p className="text-lg font-bold text-green-600">
                                                R$ {(Number(user.afiliado.comissoes_mes_atual) || 0).toFixed(2).replace('.', ',')}
                                            </p>
                                        </div>
                                    </>
                                )}
                            </div>
                        </CardContent>
                    )}
                </Card>

                {/* Action Buttons */}
                {user?.hasAfiliadoProfile && user.afiliado?.status === 'aprovado' && (
                    <div className="flex gap-4">
                        <Button asChild>
                            <a href="/afiliado/dashboard">
                                <UserCheck className="mr-2 h-4 w-4" />
                                Acessar Dashboard Afiliado
                            </a>
                        </Button>
                        <Button variant="outline" asChild>
                            <a href="/afiliado/materiais">
                                <TrendingUp className="mr-2 h-4 w-4" />
                                Materiais de Divulgação
                            </a>
                        </Button>
                    </div>
                )}

                {/* Benefits Section */}
                <div className="grid gap-6 md:grid-cols-3">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5 text-green-600" />
                                Comissões Recorrentes
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground">
                                Ganhe comissões mensais recorrentes por cada cliente que assinar nossos planos através do seu link.
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="h-5 w-5 text-blue-600" />
                                Crescimento Escalável
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground">
                                Quanto mais você indica, mais você ganha. Sem limite de comissões ou indicações.
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5 text-purple-600" />
                                Suporte Completo
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground">
                                Materiais de divulgação, treinamentos e suporte dedicado para maximizar suas vendas.
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Registration Form */}
                {!user?.hasAfiliadoProfile && (
                    <div className="space-y-6">
                        <Separator />
                        <div>
                            <h2 className="mb-4 text-xl font-semibold">Cadastre-se como Afiliado</h2>
                            <FormularioAfiliadoDashboard />
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
