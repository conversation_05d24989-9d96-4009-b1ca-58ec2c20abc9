<?php

namespace Database\Seeders;

use App\Models\Cupom;
use App\Models\Afiliado;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CupomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buscar um admin para criar os cupons
        $admin = User::where('role', 'admin')->first();
        if (!$admin) {
            $admin = User::first(); // Fallback para qualquer usuário
        }

        // Buscar um afiliado para cupons exclusivos
        $afiliado = Afiliado::where('status', 'aprovado')->first();

        // Cupons públicos
        Cupom::create([
            'codigo' => 'DESCONTO10',
            'tipo' => 'publico',
            'nome' => 'Desconto de 10%',
            'descricao' => 'Desconto de 10% em todos os planos',
            'tipo_desconto' => 'percentual',
            'valor_desconto' => 10,
            'data_inicio' => now()->subDays(7),
            'data_fim' => now()->addDays(30),
            'limite_uso' => 100,
            'ativo' => true,
            'created_by' => $admin->id,
        ]);

        Cupom::create([
            'codigo' => 'BEMVINDO',
            'tipo' => 'publico',
            'nome' => 'Bem-vindo',
            'descricao' => 'R$ 50 de desconto para novos clientes',
            'tipo_desconto' => 'valor_fixo',
            'valor_desconto' => 50,
            'valor_minimo_pedido' => 200,
            'data_inicio' => now()->subDays(30),
            'data_fim' => now()->addDays(60),
            'ativo' => true,
            'created_by' => $admin->id,
        ]);

        // Cupons exclusivos do afiliado (se existir)
        if ($afiliado) {
            Cupom::create([
                'codigo' => 'AFILIADO' . $afiliado->id,
                'tipo' => 'afiliado_exclusivo',
                'afiliado_id' => $afiliado->id,
                'nome' => 'Cupom Exclusivo do Afiliado',
                'descricao' => 'Desconto especial de 15% para clientes do afiliado',
                'tipo_desconto' => 'percentual',
                'valor_desconto' => 15,
                'data_inicio' => now()->subDays(7),
                'data_fim' => now()->addDays(90),
                'limite_uso' => 50,
                'ativo' => true,
                'created_by' => $admin->id,
            ]);

            Cupom::create([
                'codigo' => 'ESPECIAL' . $afiliado->id,
                'tipo' => 'afiliado_exclusivo',
                'afiliado_id' => $afiliado->id,
                'nome' => 'Oferta Especial',
                'descricao' => 'R$ 100 de desconto em planos anuais',
                'tipo_desconto' => 'valor_fixo',
                'valor_desconto' => 100,
                'valor_minimo_pedido' => 500,
                'data_inicio' => now(),
                'data_fim' => now()->addDays(45),
                'ativo' => true,
                'created_by' => $admin->id,
            ]);
        }
    }
}
