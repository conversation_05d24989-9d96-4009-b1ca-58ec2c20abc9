<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CustomRateLimiter
{
    private const DEFAULT_MAX_ATTEMPTS = 60;
    private const DEFAULT_DECAY_MINUTES = 1;

    public function handle(Request $request, Closure $next, ...$parameters): Response
    {
        $maxAttempts = (int) ($parameters[0] ?? self::DEFAULT_MAX_ATTEMPTS);
        $decayMinutes = (int) ($parameters[1] ?? self::DEFAULT_DECAY_MINUTES);
        $keyPrefix = $parameters[2] ?? 'rate_limit';

        $key = $this->resolveRequestSignature($request, $keyPrefix);
        
        if ($this->tooManyAttempts($key, $maxAttempts)) {
            Log::warning('Rate limit exceeded', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'key' => $key
            ]);

            return response()->json([
                'message' => 'Muitas tentativas. Tente novamente em alguns minutos.',
                'retry_after' => $this->getRetryAfter($key, $decayMinutes)
            ], 429);
        }

        $this->incrementAttempts($key, $decayMinutes);

        $response = $next($request);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts)
        );
    }

    protected function resolveRequestSignature(Request $request, string $prefix): string
    {
        $user = $request->user();
        
        if ($user) {
            return $prefix . ':user:' . $user->id;
        }

        return $prefix . ':ip:' . $request->ip();
    }

    protected function tooManyAttempts(string $key, int $maxAttempts): bool
    {
        return Cache::get($key, 0) >= $maxAttempts;
    }

    protected function incrementAttempts(string $key, int $decayMinutes): int
    {
        $attempts = Cache::get($key, 0) + 1;
        
        Cache::put($key, $attempts, now()->addMinutes($decayMinutes));
        
        return $attempts;
    }

    protected function calculateRemainingAttempts(string $key, int $maxAttempts): int
    {
        return max(0, $maxAttempts - Cache::get($key, 0));
    }

    protected function getRetryAfter(string $key, int $decayMinutes): int
    {
        $expiresAt = Cache::get($key . ':expires_at');
        
        if (!$expiresAt) {
            Cache::put($key . ':expires_at', now()->addMinutes($decayMinutes)->timestamp, now()->addMinutes($decayMinutes));
            return $decayMinutes * 60;
        }

        return max(0, $expiresAt - now()->timestamp);
    }

    protected function addHeaders(Response $response, int $maxAttempts, int $remainingAttempts): Response
    {
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
        ]);

        return $response;
    }
}
