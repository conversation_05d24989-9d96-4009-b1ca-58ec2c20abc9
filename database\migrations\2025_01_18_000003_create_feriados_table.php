<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('feriados', function (Blueprint $table) {
            $table->id();
            $table->string('nome');
            $table->date('data');
            $table->enum('tipo', ['nacional', 'estadual', 'municipal']);
            $table->string('estado', 2)->nullable(); // Para feriados estaduais
            $table->string('cidade')->nullable(); // Para feriados municipais
            $table->boolean('recorrente')->default(false); // Se repete anualmente
            $table->text('descricao')->nullable();
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Índices para performance
            $table->index(['data', 'tipo', 'ativo']);
            $table->index(['estado', 'cidade', 'ativo']);
            $table->index(['recorrente', 'ativo']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feriados');
    }
};
