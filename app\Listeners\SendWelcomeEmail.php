<?php

namespace App\Listeners;

use App\Services\NotificacaoService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendWelcomeEmail implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private NotificacaoService $notificacaoService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        try {
            // Aguardar um pouco para garantir que o usuário foi completamente criado
            sleep(2);
            
            // Enviar email de boas-vindas
            $this->notificacaoService->enviarEmailBoasVindas($event->user);
            
            Log::info('Listener SendWelcomeEmail executado com sucesso', [
                'user_id' => $event->user->id,
                'email' => $event->user->email
            ]);
            
        } catch (\Exception $e) {
            Log::error('Erro no listener SendWelcomeEmail', [
                'user_id' => $event->user->id ?? null,
                'email' => $event->user->email ?? null,
                'error' => $e->getMessage()
            ]);
            
            // Re-throw para que o job seja reprocessado
            throw $e;
        }
    }
}
