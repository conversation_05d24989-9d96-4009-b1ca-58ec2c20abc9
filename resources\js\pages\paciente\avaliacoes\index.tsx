import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useInitials } from '@/hooks/use-initials';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Calendar, MessageCircle, Star, ThumbsUp } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/paciente/dashboard',
    },
    {
        title: 'Minhas Avaliações',
        href: '/paciente/avaliacoes',
    },
];

interface Avaliacao {
    id: number;
    nota_geral: number;
    nota_pontualidade?: number;
    nota_profissionalismo?: number;
    nota_eficacia?: number;
    comentario?: string;
    recomendaria: boolean;
    aprovada: boolean;
    created_at: string;
    fisioterapeuta: {
        id: number;
        name: string;
        avatar?: string;
    };
    agendamento: {
        id: number;
        scheduled_at: string;
    };
}

interface Props {
    avaliacoes: {
        data: Avaliacao[];
        links: any[];
        meta: any;
    };
}

export default function PacienteAvaliacoes({ avaliacoes }: Props) {
    const getInitials = useInitials();

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, index) => (
            <Star
                key={index}
                className={`h-4 w-4 ${
                    index < rating
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                }`}
            />
        ));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
        });
    };

    const getStatusBadge = (aprovada: boolean) => {
        if (aprovada) {
            return <Badge variant="default" className="bg-green-100 text-green-800">Aprovada</Badge>;
        }
        return <Badge variant="secondary">Pendente</Badge>;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Minhas Avaliações" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                            <MessageCircle className="h-5 w-5" />
                        </div>
                        <div>
                            <h1 className="text-3xl font-bold">Minhas Avaliações</h1>
                            <p className="text-muted-foreground">
                                Veja todas as avaliações que você fez para fisioterapeutas
                            </p>
                        </div>
                    </div>
                </div>

                {/* Lista de Avaliações */}
                {avaliacoes.data.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <MessageCircle className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">Nenhuma avaliação encontrada</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                Você ainda não fez nenhuma avaliação. Após suas consultas, você poderá avaliar os fisioterapeutas.
                            </p>
                            <Button asChild>
                                <Link href={route('paciente.agendamentos.index')}>
                                    Ver Agendamentos
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="space-y-4">
                        {avaliacoes.data.map((avaliacao) => (
                            <Card key={avaliacao.id}>
                                <CardHeader>
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                            <Avatar className="h-12 w-12">
                                                <AvatarImage src={avaliacao.fisioterapeuta.avatar} />
                                                <AvatarFallback>
                                                    {getInitials(avaliacao.fisioterapeuta.name)}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <CardTitle className="text-lg">
                                                    {avaliacao.fisioterapeuta.name}
                                                </CardTitle>
                                                <CardDescription className="flex items-center gap-2">
                                                    <Calendar className="h-4 w-4" />
                                                    Consulta em {formatDate(avaliacao.agendamento.scheduled_at)}
                                                </CardDescription>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {getStatusBadge(avaliacao.aprovada)}
                                            <div className="text-sm text-muted-foreground">
                                                {formatDate(avaliacao.created_at)}
                                            </div>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Nota Geral */}
                                    <div className="flex items-center gap-2">
                                        <span className="font-medium">Nota Geral:</span>
                                        <div className="flex items-center gap-1">
                                            {renderStars(avaliacao.nota_geral)}
                                            <span className="ml-2 text-sm text-muted-foreground">
                                                ({avaliacao.nota_geral}/5)
                                            </span>
                                        </div>
                                    </div>

                                    {/* Notas Específicas */}
                                    {(avaliacao.nota_pontualidade || avaliacao.nota_profissionalismo || avaliacao.nota_eficacia) && (
                                        <div className="grid gap-2 sm:grid-cols-3">
                                            {avaliacao.nota_pontualidade && (
                                                <div className="text-sm">
                                                    <span className="font-medium">Pontualidade:</span>
                                                    <div className="flex items-center gap-1 mt-1">
                                                        {renderStars(avaliacao.nota_pontualidade)}
                                                    </div>
                                                </div>
                                            )}
                                            {avaliacao.nota_profissionalismo && (
                                                <div className="text-sm">
                                                    <span className="font-medium">Profissionalismo:</span>
                                                    <div className="flex items-center gap-1 mt-1">
                                                        {renderStars(avaliacao.nota_profissionalismo)}
                                                    </div>
                                                </div>
                                            )}
                                            {avaliacao.nota_eficacia && (
                                                <div className="text-sm">
                                                    <span className="font-medium">Eficácia:</span>
                                                    <div className="flex items-center gap-1 mt-1">
                                                        {renderStars(avaliacao.nota_eficacia)}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {/* Comentário */}
                                    {avaliacao.comentario && (
                                        <div className="bg-muted/50 rounded-lg p-3">
                                            <p className="text-sm">{avaliacao.comentario}</p>
                                        </div>
                                    )}

                                    {/* Recomendação */}
                                    <div className="flex items-center gap-2">
                                        <ThumbsUp className={`h-4 w-4 ${avaliacao.recomendaria ? 'text-green-600' : 'text-gray-400'}`} />
                                        <span className="text-sm">
                                            {avaliacao.recomendaria ? 'Recomendaria este fisioterapeuta' : 'Não recomendaria este fisioterapeuta'}
                                        </span>
                                    </div>

                                    {/* Status */}
                                    {!avaliacao.aprovada && (
                                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                                            <p className="text-sm text-yellow-800">
                                                <strong>Avaliação pendente:</strong> Sua avaliação está sendo analisada e será publicada em breve.
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        ))}

                        {/* Paginação */}
                        {avaliacoes.meta.last_page > 1 && (
                            <div className="flex justify-center gap-2">
                                {avaliacoes.links.map((link, index) => (
                                    <Button
                                        key={index}
                                        variant={link.active ? "default" : "outline"}
                                        size="sm"
                                        asChild={!!link.url}
                                        disabled={!link.url}
                                    >
                                        {link.url ? (
                                            <Link href={link.url} dangerouslySetInnerHTML={{ __html: link.label }} />
                                        ) : (
                                            <span dangerouslySetInnerHTML={{ __html: link.label }} />
                                        )}
                                    </Button>
                                ))}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
