const Ziggy = {"url":"http:\/\/localhost:8000","port":8000,"defaults":{},"routes":{"home":{"uri":"\/","methods":["GET","HEAD"]},"sobre":{"uri":"sobre","methods":["GET","HEAD"]},"servicos":{"uri":"servicos","methods":["GET","HEAD"]},"planos":{"uri":"planos","methods":["GET","HEAD"]},"contato":{"uri":"contato","methods":["GET","HEAD"]},"buscar":{"uri":"buscar","methods":["GET","HEAD"]},"estabelecimentos.buscar":{"uri":"api\/estabelecimentos\/buscar","methods":["POST"]},"estabelecimentos.categorias":{"uri":"api\/estabelecimentos\/categorias","methods":["GET","HEAD"]},"empresa.cadastro":{"uri":"empresa\/cadastro","methods":["GET","HEAD"]},"empresa.store":{"uri":"empresa\/cadastro","methods":["POST"]},"empresa.ativar-plano":{"uri":"empresa\/{estabelecimento}\/ativar-plano","methods":["POST"],"parameters":["estabelecimento"],"bindings":{"estabelecimento":"id"}},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"notificacoes.index":{"uri":"notificacoes","methods":["GET","HEAD"]},"notificacoes.nao-lidas":{"uri":"notificacoes\/nao-lidas","methods":["GET","HEAD"]},"notificacoes.marcar-lida":{"uri":"notificacoes\/{notificacao}\/marcar-lida","methods":["POST"],"parameters":["notificacao"],"bindings":{"notificacao":"id"}},"notificacoes.marcar-todas-lidas":{"uri":"notificacoes\/marcar-todas-lidas","methods":["POST"]},"notificacoes.destroy":{"uri":"notificacoes\/{notificacao}","methods":["DELETE"],"parameters":["notificacao"],"bindings":{"notificacao":"id"}},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.usuarios.index":{"uri":"admin\/usuarios","methods":["GET","HEAD"]},"admin.usuarios.create":{"uri":"admin\/usuarios\/create","methods":["GET","HEAD"]},"admin.usuarios.store":{"uri":"admin\/usuarios","methods":["POST"]},"admin.usuarios.show":{"uri":"admin\/usuarios\/{usuario}","methods":["GET","HEAD"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"admin.usuarios.edit":{"uri":"admin\/usuarios\/{usuario}\/edit","methods":["GET","HEAD"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"admin.usuarios.update":{"uri":"admin\/usuarios\/{usuario}","methods":["PUT","PATCH"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"admin.usuarios.destroy":{"uri":"admin\/usuarios\/{usuario}","methods":["DELETE"],"parameters":["usuario"],"bindings":{"usuario":"id"}},"admin.fisioterapeutas.index":{"uri":"admin\/fisioterapeutas","methods":["GET","HEAD"]},"admin.fisioterapeutas.create":{"uri":"admin\/fisioterapeutas\/create","methods":["GET","HEAD"]},"admin.fisioterapeutas.store":{"uri":"admin\/fisioterapeutas","methods":["POST"]},"admin.fisioterapeutas.show":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}","methods":["GET","HEAD"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.fisioterapeutas.edit":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}\/edit","methods":["GET","HEAD"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.fisioterapeutas.update":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}","methods":["PUT","PATCH"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.fisioterapeutas.destroy":{"uri":"admin\/fisioterapeutas\/{fisioterapeuta}","methods":["DELETE"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"admin.planos.index":{"uri":"admin\/planos","methods":["GET","HEAD"]},"admin.planos.create":{"uri":"admin\/planos\/create","methods":["GET","HEAD"]},"admin.planos.store":{"uri":"admin\/planos","methods":["POST"]},"admin.planos.show":{"uri":"admin\/planos\/{plano}","methods":["GET","HEAD"],"parameters":["plano"],"bindings":{"plano":"id"}},"admin.planos.edit":{"uri":"admin\/planos\/{plano}\/edit","methods":["GET","HEAD"],"parameters":["plano"],"bindings":{"plano":"id"}},"admin.planos.update":{"uri":"admin\/planos\/{plano}","methods":["PUT","PATCH"],"parameters":["plano"],"bindings":{"plano":"id"}},"admin.planos.destroy":{"uri":"admin\/planos\/{plano}","methods":["DELETE"],"parameters":["plano"],"bindings":{"plano":"id"}},"admin.pagamentos.index":{"uri":"admin\/pagamentos","methods":["GET","HEAD"]},"admin.pagamentos.show":{"uri":"admin\/pagamentos\/{pagamento}","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.mark-as-paid":{"uri":"admin\/pagamentos\/{pagamento}\/mark-as-paid","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.mark-as-failed":{"uri":"admin\/pagamentos\/{pagamento}\/mark-as-failed","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.cancel":{"uri":"admin\/pagamentos\/{pagamento}\/cancel","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.update":{"uri":"admin\/pagamentos\/{pagamento}","methods":["PUT"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"admin.pagamentos.generate":{"uri":"admin\/assinaturas\/{assinatura}\/generate-payment","methods":["POST"],"parameters":["assinatura"],"bindings":{"assinatura":"id"}},"admin.pagamentos.export":{"uri":"admin\/pagamentos\/export","methods":["GET","HEAD"]},"admin.relatorios.index":{"uri":"admin\/relatorios","methods":["GET","HEAD"]},"admin.relatorios.financeiro":{"uri":"admin\/relatorios\/financeiro","methods":["GET","HEAD"]},"admin.relatorios.operacional":{"uri":"admin\/relatorios\/operacional","methods":["GET","HEAD"]},"admin.relatorios.pacientes":{"uri":"admin\/relatorios\/pacientes","methods":["GET","HEAD"]},"admin.relatorios.fisioterapeutas":{"uri":"admin\/relatorios\/fisioterapeutas","methods":["GET","HEAD"]},"admin.relatorios.export":{"uri":"admin\/relatorios\/export","methods":["GET","HEAD"]},"fisioterapeuta.dashboard":{"uri":"fisioterapeuta\/dashboard","methods":["GET","HEAD"]},"fisioterapeuta.agenda.index":{"uri":"fisioterapeuta\/agenda","methods":["GET","HEAD"]},"fisioterapeuta.agenda.show":{"uri":"fisioterapeuta\/agenda\/{agendamento}","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.confirmar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/confirmar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.iniciar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/iniciar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.finalizar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/finalizar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.agenda.cancelar":{"uri":"fisioterapeuta\/agenda\/{agendamento}\/cancelar","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.pacientes.index":{"uri":"fisioterapeuta\/pacientes","methods":["GET","HEAD"]},"fisioterapeuta.pacientes.show":{"uri":"fisioterapeuta\/pacientes\/{paciente}","methods":["GET","HEAD"],"parameters":["paciente"],"bindings":{"paciente":"id"}},"fisioterapeuta.pacientes.notas":{"uri":"fisioterapeuta\/pacientes\/{paciente}\/notas","methods":["POST"],"parameters":["paciente"],"bindings":{"paciente":"id"}},"fisioterapeuta.disponibilidade.index":{"uri":"fisioterapeuta\/disponibilidade","methods":["GET","HEAD"]},"fisioterapeuta.disponibilidade.create":{"uri":"fisioterapeuta\/disponibilidade\/create","methods":["GET","HEAD"]},"fisioterapeuta.disponibilidade.store":{"uri":"fisioterapeuta\/disponibilidade","methods":["POST"]},"fisioterapeuta.disponibilidade.show":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}","methods":["GET","HEAD"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.disponibilidade.edit":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}\/edit","methods":["GET","HEAD"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.disponibilidade.update":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}","methods":["PUT","PATCH"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.disponibilidade.destroy":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}","methods":["DELETE"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.disponibilidade.toggle":{"uri":"fisioterapeuta\/disponibilidade\/{disponibilidade}\/toggle","methods":["POST"],"parameters":["disponibilidade"],"bindings":{"disponibilidade":"id"}},"fisioterapeuta.relatorios.index":{"uri":"fisioterapeuta\/relatorios","methods":["GET","HEAD"]},"fisioterapeuta.relatorios.pendentes":{"uri":"fisioterapeuta\/relatorios\/pendentes","methods":["GET","HEAD"]},"fisioterapeuta.relatorios.create":{"uri":"fisioterapeuta\/relatorios\/create\/{agendamento}","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.relatorios.store":{"uri":"fisioterapeuta\/relatorios\/{agendamento}","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"fisioterapeuta.relatorios.show":{"uri":"fisioterapeuta\/relatorios\/{relatorio}","methods":["GET","HEAD"],"parameters":["relatorio"],"bindings":{"relatorio":"id"}},"fisioterapeuta.relatorios.edit":{"uri":"fisioterapeuta\/relatorios\/{relatorio}\/edit","methods":["GET","HEAD"],"parameters":["relatorio"],"bindings":{"relatorio":"id"}},"fisioterapeuta.relatorios.update":{"uri":"fisioterapeuta\/relatorios\/{relatorio}","methods":["PUT"],"parameters":["relatorio"],"bindings":{"relatorio":"id"}},"paciente.dashboard":{"uri":"paciente\/dashboard","methods":["GET","HEAD"]},"paciente.fisioterapeutas.index":{"uri":"paciente\/fisioterapeutas","methods":["GET","HEAD"]},"paciente.fisioterapeutas.show":{"uri":"paciente\/fisioterapeutas\/{fisioterapeuta}","methods":["GET","HEAD"],"parameters":["fisioterapeuta"],"bindings":{"fisioterapeuta":"id"}},"paciente.agendamentos.index":{"uri":"paciente\/agendamentos","methods":["GET","HEAD"]},"paciente.agendamentos.create":{"uri":"paciente\/agendamentos\/create","methods":["GET","HEAD"]},"paciente.agendamentos.store":{"uri":"paciente\/agendamentos","methods":["POST"]},"paciente.agendamentos.show":{"uri":"paciente\/agendamentos\/{agendamento}","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.destroy":{"uri":"paciente\/agendamentos\/{agendamento}","methods":["DELETE"],"parameters":["agendamento"]},"paciente.agendamentos.cancel":{"uri":"paciente\/agendamentos\/{agendamento}\/cancel","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.reschedule":{"uri":"paciente\/agendamentos\/{agendamento}\/reschedule","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.update-reschedule":{"uri":"paciente\/agendamentos\/{agendamento}\/reschedule","methods":["PUT"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.report":{"uri":"paciente\/agendamentos\/{agendamento}\/report","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.evaluate":{"uri":"paciente\/agendamentos\/{agendamento}\/evaluate","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.store-evaluation":{"uri":"paciente\/agendamentos\/{agendamento}\/evaluate","methods":["POST"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.agendamentos.horarios-disponiveis":{"uri":"paciente\/agendamentos\/horarios-disponiveis","methods":["POST"]},"paciente.agendamentos.fisioterapeutas-disponiveis":{"uri":"paciente\/agendamentos\/fisioterapeutas-disponiveis","methods":["POST"]},"paciente.agendamentos.verificar-disponibilidade":{"uri":"paciente\/agendamentos\/verificar-disponibilidade","methods":["POST"]},"paciente.historico.index":{"uri":"paciente\/historico","methods":["GET","HEAD"]},"paciente.historico.show":{"uri":"paciente\/historico\/{agendamento}","methods":["GET","HEAD"],"parameters":["agendamento"],"bindings":{"agendamento":"id"}},"paciente.historico.export":{"uri":"paciente\/historico\/export","methods":["GET","HEAD"]},"paciente.pagamentos.index":{"uri":"paciente\/pagamentos","methods":["GET","HEAD"]},"paciente.pagamentos.show":{"uri":"paciente\/pagamentos\/{pagamento}","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.process":{"uri":"paciente\/pagamentos\/{pagamento}\/process","methods":["POST"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.boleto":{"uri":"paciente\/pagamentos\/{pagamento}\/boleto","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.pix":{"uri":"paciente\/pagamentos\/{pagamento}\/pix","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.pagamentos.comprovante":{"uri":"paciente\/pagamentos\/{pagamento}\/comprovante","methods":["GET","HEAD"],"parameters":["pagamento"],"bindings":{"pagamento":"id"}},"paciente.plano.index":{"uri":"paciente\/plano","methods":["GET","HEAD"]},"paciente.plano.subscribe":{"uri":"paciente\/plano\/subscribe","methods":["POST"]},"paciente.plano.cancel":{"uri":"paciente\/plano\/cancel","methods":["POST"]},"paciente.plano.change":{"uri":"paciente\/plano\/change","methods":["POST"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
