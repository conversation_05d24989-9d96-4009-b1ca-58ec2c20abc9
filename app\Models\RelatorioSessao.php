<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RelatorioSessao extends Model
{
    use HasFactory;

    protected $table = 'relatorios_sessao';

    protected $fillable = [
        'agendamento_id',
        'fisioterapeuta_id',
        'paciente_id',
        'objective',
        'activities_performed',
        'patient_response',
        'observations',
        'next_session_plan',
        'vital_signs',
        'pain_scale',
        'exercises_prescribed',
        'recommendations',
        'exercises',
        'progress_notes',
        'next_steps',
        'pain_level_before',
        'pain_level_after',
        'mobility_assessment',
        'patient_satisfaction',
    ];

    protected $casts = [
        'vital_signs' => 'array',
        'pain_level_before' => 'integer',
        'pain_level_after' => 'integer',
        'patient_satisfaction' => 'integer',
    ];

    // Relacionamentos
    public function agendamento()
    {
        return $this->belongsTo(Agendamento::class);
    }

    public function fisioterapeuta()
    {
        return $this->belongsTo(User::class, 'fisioterapeuta_id');
    }

    public function paciente()
    {
        return $this->belongsTo(User::class, 'paciente_id');
    }

    public function anexos()
    {
        return $this->hasMany(RelatorioAnexo::class);
    }

    // Métodos auxiliares
    public function getMobilidadeFormatadaAttribute()
    {
        $opcoes = [
            'limitada' => 'Limitada',
            'parcial' => 'Parcial',
            'normal' => 'Normal',
            'melhorada' => 'Melhorada',
        ];

        return $opcoes[$this->mobility_assessment] ?? $this->mobility_assessment;
    }

    public function getProgressoDorAttribute()
    {
        if ($this->pain_level_before === null || $this->pain_level_after === null) {
            return null;
        }

        $diferenca = $this->pain_level_before - $this->pain_level_after;

        if ($diferenca > 0) {
            return "Melhora de {$diferenca} pontos";
        } elseif ($diferenca < 0) {
            return "Piora de " . abs($diferenca) . " pontos";
        } else {
            return "Sem alteração";
        }
    }
}
