<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mensagens', function (Blueprint $table) {
            $table->id();
            $table->foreignId('remetente_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('destinatario_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('agendamento_id')->nullable()->constrained('agendamentos')->onDelete('set null');
            $table->text('conteudo');
            $table->enum('tipo', ['texto', 'imagem', 'arquivo', 'sistema'])->default('texto');
            $table->boolean('lida')->default(false);
            $table->timestamp('lida_em')->nullable();
            $table->json('anexos')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Índices para performance
            $table->index(['remetente_id', 'destinatario_id']);
            $table->index(['destinatario_id', 'lida']);
            $table->index('agendamento_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mensagens');
    }
};
