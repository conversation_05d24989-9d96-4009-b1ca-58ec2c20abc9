<?php

namespace App\Notifications;

use App\Models\Agendamento;
use App\Mail\AgendamentoConfirmado;
use App\Mail\AgendamentoCancelado;
use App\Mail\LembreteAgendamento;
use App\Mail\NovoAgendamento;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AgendamentoNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Agendamento $agendamento,
        public string $tipo,
        public string $motivo = ''
    ) {}

    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        return match($this->tipo) {
            'novo' => new NovoAgendamento($this->agendamento),
            'confirmado' => new AgendamentoConfirmado($this->agendamento),
            'cancelado' => new AgendamentoCancelado($this->agendamento, $this->motivo),
            'lembrete' => new LembreteAgendamento($this->agendamento),
            default => (new MailMessage)
                ->subject('Notificação de Agendamento - F4 Fisio')
                ->line('Você tem uma nova notificação sobre seu agendamento.')
                ->action('Ver Agendamento', url('/dashboard/agendamentos/' . $this->agendamento->id))
                ->line('Obrigado por usar nossa plataforma!')
        };
    }

    public function toDatabase(object $notifiable): array
    {
        $titulos = [
            'novo' => 'Novo Agendamento Recebido',
            'confirmado' => 'Agendamento Confirmado',
            'cancelado' => 'Agendamento Cancelado',
            'lembrete' => 'Lembrete de Consulta',
        ];

        $mensagens = [
            'novo' => "Você recebeu um novo agendamento de {$this->agendamento->paciente->name}",
            'confirmado' => "Seu agendamento foi confirmado para " . $this->agendamento->scheduled_at->format('d/m/Y H:i'),
            'cancelado' => "Seu agendamento foi cancelado. Motivo: {$this->motivo}",
            'lembrete' => "Lembrete: você tem consulta hoje às " . $this->agendamento->scheduled_at->format('H:i'),
        ];

        return [
            'agendamento_id' => $this->agendamento->id,
            'tipo' => $this->getTipoNotificacao(),
            'titulo' => $titulos[$this->tipo] ?? 'Notificação de Agendamento',
            'mensagem' => $mensagens[$this->tipo] ?? 'Você tem uma nova notificação.',
            'data_envio' => now(),
        ];
    }

    private function getTipoNotificacao(): string
    {
        return match($this->tipo) {
            'novo' => 'novo_agendamento',
            'confirmado' => 'agendamento_confirmado',
            'cancelado' => 'agendamento_cancelado',
            'lembrete' => 'lembrete_sessao',
            default => 'novo_agendamento'
        };
    }

    public function toArray(object $notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
