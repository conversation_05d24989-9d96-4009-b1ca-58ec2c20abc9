<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Agendamento;
use App\Models\Assinatura;
use App\Models\Avaliacao;
use App\Models\Pagamento;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the patient dashboard.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Estatísticas do paciente
        $stats = [
            'proximoAgendamento' => $this->getProximoAgendamento($user->id),
            'sessoesMes' => $this->getSessoesMes($user->id),
            'sessoesRestantes' => $this->getSessoesRestantes($user->id),
            'ultimaAvaliacao' => $this->getUltimaAvaliacao($user->id),
        ];

        // Agendamentos próximos (próximos 7 dias)
        $proximosAgendamentos = Agendamento::where('paciente_id', $user->id)
            ->where('data_hora', '>=', Carbon::now())
            ->where('data_hora', '<=', Carbon::now()->addDays(7))
            ->where('status', '!=', 'cancelado')
            ->with(['fisioterapeuta.user'])
            ->orderBy('data_hora', 'asc')
            ->limit(5)
            ->get();

        // Histórico recente (últimas 5 sessões)
        $historicoRecente = Agendamento::where('paciente_id', $user->id)
            ->where('status', 'concluido')
            ->with(['fisioterapeuta.user'])
            ->orderBy('data_hora', 'desc')
            ->limit(5)
            ->get();

        // Informações do plano atual
        $planoAtual = null;
        if ($user->has_subscription) {
            // Para plano avulso, não há assinatura na tabela assinaturas
            // Vamos assumir que é plano avulso se has_subscription = true mas não há assinatura
            $assinatura = Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->with('plano')
                ->first();

            if ($assinatura) {
                $planoAtual = $assinatura;
            } else {
                // Criar um objeto simulado para sessão avulsa
                $planoAtual = (object) [
                    'plano' => (object) [
                        'name' => 'Sessão Avulsa',
                        'price' => 0,
                        'sessions_per_month' => 999, // Ilimitado
                    ],
                    'status' => 'ativo',
                    'type' => 'avulsa'
                ];
            }
        }

        // Pagamentos pendentes
        $pagamentosPendentes = Pagamento::where('user_id', $user->id)
            ->where('status', 'pendente')
            ->orderBy('due_date', 'asc')
            ->limit(3)
            ->get();

        return Inertia::render('paciente/dashboard', [
            'stats' => $stats,
            'proximosAgendamentos' => $proximosAgendamentos,
            'historicoRecente' => $historicoRecente,
            'planoAtual' => $planoAtual,
            'pagamentosPendentes' => $pagamentosPendentes,
        ]);
    }

    /**
     * Get next appointment for the patient
     */
    private function getProximoAgendamento($pacienteId)
    {
        return Agendamento::where('paciente_id', $pacienteId)
            ->where('data_hora', '>', Carbon::now())
            ->where('status', '!=', 'cancelado')
            ->with(['fisioterapeuta.user'])
            ->orderBy('data_hora', 'asc')
            ->first();
    }

    /**
     * Get sessions count for current month
     */
    private function getSessoesMes($pacienteId)
    {
        return Agendamento::where('paciente_id', $pacienteId)
            ->where('status', 'concluido')
            ->whereMonth('data_hora', Carbon::now()->month)
            ->whereYear('data_hora', Carbon::now()->year)
            ->count();
    }

    /**
     * Get remaining sessions based on current plan
     */
    private function getSessoesRestantes($pacienteId)
    {
        $user = User::find($pacienteId);

        if (!$user || !$user->has_subscription) {
            return 0;
        }

        $assinatura = Assinatura::where('user_id', $pacienteId)
            ->where('status', 'ativa')
            ->with('plano')
            ->first();

        if ($assinatura) {
            // Plano mensal com limite de sessões
            $sessoesUsadas = $this->getSessoesMes($pacienteId);
            $sessoesPlano = $assinatura->plano->sessions_per_month;
            return max(0, $sessoesPlano - $sessoesUsadas);
        } else {
            // Sessão avulsa - ilimitado
            return 999;
        }
    }

    /**
     * Get last evaluation
     */
    private function getUltimaAvaliacao($pacienteId)
    {
        return Avaliacao::where('paciente_id', $pacienteId)
            ->with(['fisioterapeuta.user'])
            ->orderBy('created_at', 'desc')
            ->first();
    }
}
