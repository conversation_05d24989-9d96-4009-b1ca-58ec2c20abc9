<?php

namespace App\Http\Controllers\Empresa;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $estabelecimento = $user->estabelecimento;

        if (!$estabelecimento) {
            return redirect()->route('empresa.setup')
                ->with('warning', 'Você precisa configurar seu estabelecimento primeiro.');
        }

        // Estatísticas completas incluindo métricas
        $stats = [
            'plano_ativo' => $estabelecimento->plano_ativo,
            'plano_vencimento' => $estabelecimento->plano_vencimento?->format('d/m/Y'),
            'dias_restantes' => $estabelecimento->plano_vencimento ?
                max(0, $estabelecimento->plano_vencimento->diffInDays(now(), false)) : null,
            'avaliacao_media' => $estabelecimento->avaliacao_media ?? 0,
            'total_avaliacoes' => $estabelecimento->total_avaliacoes ?? 0,
            'categoria' => $estabelecimento->categoria,
            'ativo' => $estabelecimento->ativo,
            // Métricas de visualização
            'total_views' => $estabelecimento->total_views ?? 0,
            'views_mes_atual' => $estabelecimento->views_mes_atual ?? 0,
            'views_semana_atual' => $estabelecimento->views_semana_atual ?? 0,
            // Métricas de contato
            'total_contatos' => $estabelecimento->total_contatos ?? 0,
            'contatos_mes_atual' => $estabelecimento->contatos_mes_atual ?? 0,
            'contatos_whatsapp' => $estabelecimento->contatos_whatsapp ?? 0,
            'contatos_telefone' => $estabelecimento->contatos_telefone ?? 0,
            // Informações de pagamento
            'valor_plano' => $estabelecimento->valor_plano ?? 14.90,
            'ultimo_pagamento' => $estabelecimento->ultimo_pagamento?->format('d/m/Y'),
            'dias_inadimplencia' => $estabelecimento->dias_inadimplencia ?? 0,
        ];

        return Inertia::render('empresa/dashboard', [
            'estabelecimento' => $estabelecimento,
            'stats' => $stats,
        ]);
    }
}
