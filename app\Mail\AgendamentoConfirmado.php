<?php

namespace App\Mail;

use App\Models\Agendamento;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AgendamentoConfirmado extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(
        public Agendamento $agendamento
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Agendamento Confirmado - F4 Fisio',
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.agendamento-confirmado',
            with: [
                'agendamento' => $this->agendamento,
                'paciente' => $this->agendamento->paciente,
                'fisioterapeuta' => $this->agendamento->fisioterapeuta,
            ],
        );
    }

    public function attachments(): array
    {
        return [];
    }
}
