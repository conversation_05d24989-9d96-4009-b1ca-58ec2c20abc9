<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('estabelecimento_imagens', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estabelecimento_id')->constrained('estabelecimentos')->onDelete('cascade');
            $table->string('nome_arquivo');
            $table->string('nome_original');
            $table->string('tipo_mime');
            $table->integer('tamanho');
            $table->string('descricao')->nullable();
            $table->boolean('principal')->default(false);
            $table->integer('ordem')->default(0);
            $table->timestamps();

            // Índices
            $table->index(['estabelecimento_id', 'principal']);
            $table->index(['estabelecimento_id', 'ordem']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('estabelecimento_imagens');
    }
};
