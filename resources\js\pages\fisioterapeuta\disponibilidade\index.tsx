import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Ban, Calendar, CalendarDays, Clock, Edit, Eye, Filter, Plus, Search, ToggleLeft, ToggleRight, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Disponibilidade {
    id: number;
    tipo: 'disponivel' | 'indisponivel' | 'bloqueio';
    data_inicio: string;
    data_fim?: string;
    hora_inicio: string;
    hora_fim: string;
    dias_semana?: number[];
    recorrente: boolean;
    motivo?: string;
    ativo: boolean;
    formatted_horario: string;
    formatted_periodo: string;
    dias_semana_text: string;
    created_at: string;
}

interface Stats {
    total: number;
    ativas: number;
    bloqueios: number;
    recorrentes: number;
}

interface Props {
    disponibilidades: {
        data: Disponibilidade[];
        links: any[];
        meta: any;
    };
    filters: {
        tipo?: string;
        ativo?: boolean;
        periodo?: string;
    };
    stats: Stats;
}

export default function Index({ disponibilidades, filters, stats }: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedTipo, setSelectedTipo] = useState(filters.tipo || 'all');
    const [selectedAtivo, setSelectedAtivo] = useState(filters.ativo?.toString() || 'all');
    const [selectedPeriodo, setSelectedPeriodo] = useState(filters.periodo || 'all');

    const handleFilter = () => {
        router.get(
            route('fisioterapeuta.disponibilidade.index'),
            {
                tipo: selectedTipo === 'all' ? undefined : selectedTipo,
                ativo: selectedAtivo === 'all' ? undefined : selectedAtivo,
                periodo: selectedPeriodo === 'all' ? undefined : selectedPeriodo,
            },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    const handleToggle = (disponibilidade: Disponibilidade) => {
        router.post(
            route('fisioterapeuta.disponibilidade.toggle', disponibilidade.id),
            {},
            {
                preserveScroll: true,
            },
        );
    };

    const handleDelete = (disponibilidade: Disponibilidade) => {
        if (confirm('Tem certeza que deseja excluir esta disponibilidade?')) {
            router.delete(route('fisioterapeuta.disponibilidade.destroy', disponibilidade.id), {
                preserveScroll: true,
            });
        }
    };

    const getTipoColor = (tipo: string) => {
        switch (tipo) {
            case 'disponivel':
                return 'bg-green-100 text-green-800';
            case 'indisponivel':
                return 'bg-yellow-100 text-yellow-800';
            case 'bloqueio':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getTipoLabel = (tipo: string) => {
        switch (tipo) {
            case 'disponivel':
                return 'Disponível';
            case 'indisponivel':
                return 'Indisponível';
            case 'bloqueio':
                return 'Bloqueio';
            default:
                return tipo;
        }
    };

    return (
        <AppLayout>
            <Head title="Gerenciar Disponibilidade" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div>
                                <h2 className="text-3xl font-bold tracking-tight">Gerenciar Disponibilidade</h2>
                                <p className="text-muted-foreground">Configure seus horários de trabalho e bloqueios</p>
                            </div>
                            <Link href={route('fisioterapeuta.disponibilidade.create')}>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Nova Disponibilidade
                                </Button>
                            </Link>
                        </div>
                    </div>

                    {/* Stats */}
                    <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total</CardTitle>
                                <CalendarDays className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Ativas</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-600">{stats.ativas}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Bloqueios</CardTitle>
                                <Ban className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-red-600">{stats.bloqueios}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Recorrentes</CardTitle>
                                <Clock className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-blue-600">{stats.recorrentes}</div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filters */}
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Filter className="mr-2 h-4 w-4" />
                                Filtros
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                                <div>
                                    <label className="mb-2 block text-sm font-medium">Tipo</label>
                                    <Select value={selectedTipo} onValueChange={setSelectedTipo}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Todos os tipos" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">Todos os tipos</SelectItem>
                                            <SelectItem value="disponivel">Disponível</SelectItem>
                                            <SelectItem value="indisponivel">Indisponível</SelectItem>
                                            <SelectItem value="bloqueio">Bloqueio</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="mb-2 block text-sm font-medium">Status</label>
                                    <Select value={selectedAtivo} onValueChange={setSelectedAtivo}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Todos os status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">Todos os status</SelectItem>
                                            <SelectItem value="true">Ativo</SelectItem>
                                            <SelectItem value="false">Inativo</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <label className="mb-2 block text-sm font-medium">Período</label>
                                    <Select value={selectedPeriodo} onValueChange={setSelectedPeriodo}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Todos os períodos" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">Todos os períodos</SelectItem>
                                            <SelectItem value="ativas">Ativas</SelectItem>
                                            <SelectItem value="futuras">Futuras</SelectItem>
                                            <SelectItem value="passadas">Passadas</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="flex items-end">
                                    <Button onClick={handleFilter} className="w-full">
                                        <Search className="mr-2 h-4 w-4" />
                                        Filtrar
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Disponibilidades List */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Disponibilidades</CardTitle>
                            <CardDescription>{disponibilidades.meta.total} disponibilidade(s) encontrada(s)</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {disponibilidades.data.length === 0 ? (
                                <div className="py-8 text-center">
                                    <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma disponibilidade</h3>
                                    <p className="mt-1 text-sm text-gray-500">Comece criando uma nova disponibilidade.</p>
                                    <div className="mt-6">
                                        <Link href={route('fisioterapeuta.disponibilidade.create')}>
                                            <Button>
                                                <Plus className="mr-2 h-4 w-4" />
                                                Nova Disponibilidade
                                            </Button>
                                        </Link>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {disponibilidades.data.map((disponibilidade) => (
                                        <div key={disponibilidade.id} className="rounded-lg border p-4 transition-colors hover:bg-gray-50">
                                            <div className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="mb-2 flex items-center space-x-3">
                                                        <Badge className={getTipoColor(disponibilidade.tipo)}>
                                                            {getTipoLabel(disponibilidade.tipo)}
                                                        </Badge>
                                                        {disponibilidade.recorrente && (
                                                            <Badge variant="outline">
                                                                <Clock className="mr-1 h-3 w-3" />
                                                                Recorrente
                                                            </Badge>
                                                        )}
                                                        <Badge variant={disponibilidade.ativo ? 'default' : 'secondary'}>
                                                            {disponibilidade.ativo ? 'Ativo' : 'Inativo'}
                                                        </Badge>
                                                    </div>

                                                    <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
                                                        <div>
                                                            <span className="font-medium">Período:</span>
                                                            <p className="text-gray-600">{disponibilidade.formatted_periodo}</p>
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">Horário:</span>
                                                            <p className="text-gray-600">{disponibilidade.formatted_horario}</p>
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">Dias:</span>
                                                            <p className="text-gray-600">{disponibilidade.dias_semana_text}</p>
                                                        </div>
                                                    </div>

                                                    {disponibilidade.motivo && (
                                                        <div className="mt-2">
                                                            <span className="text-sm font-medium">Motivo:</span>
                                                            <p className="text-sm text-gray-600">{disponibilidade.motivo}</p>
                                                        </div>
                                                    )}
                                                </div>

                                                <div className="flex items-center space-x-2">
                                                    <Button variant="ghost" size="sm" onClick={() => handleToggle(disponibilidade)}>
                                                        {disponibilidade.ativo ? (
                                                            <ToggleRight className="h-4 w-4 text-green-600" />
                                                        ) : (
                                                            <ToggleLeft className="h-4 w-4 text-gray-400" />
                                                        )}
                                                    </Button>

                                                    <Link href={route('fisioterapeuta.disponibilidade.show', disponibilidade.id)}>
                                                        <Button variant="ghost" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>

                                                    <Link href={route('fisioterapeuta.disponibilidade.edit', disponibilidade.id)}>
                                                        <Button variant="ghost" size="sm">
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </Link>

                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleDelete(disponibilidade)}
                                                        className="text-red-600 hover:text-red-700"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
