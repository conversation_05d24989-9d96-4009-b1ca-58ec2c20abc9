import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle, HelpCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';

export interface ValidatedTextareaProps extends Omit<React.ComponentProps<typeof Textarea>, 'onChange' | 'onBlur'> {
    label?: string;
    error?: string;
    success?: boolean;
    tooltip?: string;
    required?: boolean;
    maxLength?: number;
    showCharCount?: boolean;
    onChange?: (value: string) => void;
    onBlur?: (value: string) => void;
    showValidationIcon?: boolean;
}

export function ValidatedTextarea({
    label,
    error,
    success,
    tooltip,
    required,
    maxLength,
    showCharCount = false,
    onChange,
    onBlur,
    showValidationIcon = true,
    className,
    ...props
}: ValidatedTextareaProps) {
    const [internalValue, setInternalValue] = useState(props.value || '');
    const [isFocused, setIsFocused] = useState(false);

    useEffect(() => {
        if (props.value !== undefined) {
            setInternalValue(String(props.value));
        }
    }, [props.value]);

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        setInternalValue(value);
        onChange?.(value);
    };

    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
        setIsFocused(false);
        onBlur?.(e.target.value);
    };

    const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
        setIsFocused(true);
        props.onFocus?.(e);
    };

    const hasError = Boolean(error);
    const hasSuccess = success && !hasError && !isFocused && internalValue;
    const charCount = String(internalValue).length;
    const isOverLimit = maxLength ? charCount > maxLength : false;

    return (
        <div className="space-y-2">
            {label && (
                <div className="flex items-center gap-2">
                    <Label htmlFor={props.id} className={cn(required && "after:ml-0.5 after:text-red-500 after:content-['*']")}>
                        {label}
                    </Label>
                    {tooltip && (
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 cursor-help text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p className="max-w-xs">{tooltip}</p>
                            </TooltipContent>
                        </Tooltip>
                    )}
                </div>
            )}

            <div className="relative">
                <Textarea
                    {...props}
                    value={internalValue}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    onFocus={handleFocus}
                    maxLength={maxLength}
                    className={cn(
                        hasError && 'border-red-500 focus-visible:ring-red-500/20',
                        hasSuccess && 'border-green-500 focus-visible:ring-green-500/20',
                        isOverLimit && 'border-red-500',
                        className,
                    )}
                    aria-invalid={hasError}
                    aria-describedby={error ? `${props.id}-error` : undefined}
                />

                {showValidationIcon && (hasError || hasSuccess) && (
                    <div className="absolute top-3 right-3">
                        {hasError && <AlertCircle className="h-4 w-4 text-red-500" />}
                        {hasSuccess && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                )}
            </div>

            <div className="flex items-center justify-between">
                <div>
                    {error && (
                        <p id={`${props.id}-error`} className="flex items-center gap-1 text-sm text-red-600">
                            <AlertCircle className="h-3 w-3" />
                            {error}
                        </p>
                    )}
                </div>

                {(showCharCount || maxLength) && (
                    <p className={cn('text-xs text-muted-foreground', isOverLimit && 'text-red-500')}>
                        {charCount}
                        {maxLength && `/${maxLength}`}
                    </p>
                )}
            </div>
        </div>
    );
}
