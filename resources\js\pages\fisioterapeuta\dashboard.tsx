import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { AlertTriangle, Calendar, Clock, DollarSign, Info, MapPin, Phone, Star, Users } from 'lucide-react';

interface Stats {
    agendamentosHoje: number;
    sessoesMes: number;
    pacientesAtivos: number;
    avaliacaoMedia: number;
    receitaMes: number;
}

interface Agendamento {
    id: number;
    data_hora: string;
    status: string;
    tipo: string;
    duracao: number;
    observacoes?: string;
    paciente: {
        id: number;
        name: string;
        phone?: string;
    };
    endereco?: string;
}

interface PacienteRecente {
    id: number;
    name: string;
    email: string;
    phone?: string;
    ultima_sessao: string;
    total_sessoes: number;
}

interface Avaliacao {
    id: number;
    rating: number;
    comentario?: string;
    recomenda: boolean;
    data: string;
    paciente: {
        name: string;
    };
}

interface EstatisticaSemana {
    data: string;
    dia: string;
    sessoes: number;
}

interface Alerta {
    tipo: 'warning' | 'info' | 'error';
    titulo: string;
    mensagem: string;
    acao?: string;
}

interface Props {
    stats: Stats;
    proximosAgendamentos: Agendamento[];
    agendamentosHoje: Agendamento[];
    pacientesRecentes: PacienteRecente[];
    avaliacoesRecentes: Avaliacao[];
    estatisticasSemana: EstatisticaSemana[];
    alertas: Alerta[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Painel',
        href: '/fisioterapeuta/dashboard',
    },
];

export default function FisioterapeutaDashboard() {
    const pageProps = usePage().props as any;
    const { stats, proximosAgendamentos, agendamentosHoje, pacientesRecentes, avaliacoesRecentes, estatisticasSemana, alertas } = pageProps;

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const formatDateTime = (dateString: string) => {
        return new Date(dateString).toLocaleString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatTime = (dateString: string) => {
        return new Date(dateString).toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            agendado: 'secondary',
            confirmado: 'default',
            em_andamento: 'default',
            concluido: 'default',
            cancelado: 'destructive',
        } as const;

        const labels = {
            agendado: 'Agendado',
            confirmado: 'Confirmado',
            em_andamento: 'Em Andamento',
            concluido: 'Concluído',
            cancelado: 'Cancelado',
        };

        return <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>{labels[status as keyof typeof labels] || status}</Badge>;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard - Fisioterapeuta" />
            <div className="flex h-full flex-1 flex-col gap-6 overflow-x-auto rounded-xl p-6">
                <div className="mb-6">
                    <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
                    <p className="text-gray-600">Visão geral da sua prática profissional</p>
                </div>

                {/* Alertas */}
                {alertas && alertas.length > 0 && (
                    <div className="space-y-4">
                        {alertas.map((alerta: Alerta, index: number) => (
                            <Alert
                                key={index}
                                className={alerta.tipo === 'warning' ? 'border-yellow-200 bg-yellow-50' : 'border-blue-200 bg-blue-50'}
                            >
                                {alerta.tipo === 'warning' ? <AlertTriangle className="h-4 w-4" /> : <Info className="h-4 w-4" />}
                                <AlertDescription>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <strong>{alerta.titulo}</strong>
                                            <p className="mt-1 text-sm">{alerta.mensagem}</p>
                                        </div>
                                        {alerta.acao && (
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href={alerta.acao}>Ver</Link>
                                            </Button>
                                        )}
                                    </div>
                                </AlertDescription>
                            </Alert>
                        ))}
                    </div>
                )}

                {/* Cards de Estatísticas */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Hoje</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.agendamentosHoje || 0}</div>
                            <p className="text-xs text-muted-foreground">agendamentos</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Este Mês</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.sessoesMes || 0}</div>
                            <p className="text-xs text-muted-foreground">sessões realizadas</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pacientes Ativos</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.pacientesAtivos || 0}</div>
                            <p className="text-xs text-muted-foreground">últimos 30 dias</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Avaliação</CardTitle>
                            <Star className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.avaliacaoMedia?.toFixed(1) || '0.0'}</div>
                            <p className="text-xs text-muted-foreground">média geral</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Receita</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats?.receitaMes || 0)}</div>
                            <p className="text-xs text-muted-foreground">este mês</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Seção Principal */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Agendamentos de Hoje */}
                    <Card className="lg:col-span-2">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Agenda de Hoje
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {agendamentosHoje && agendamentosHoje.length > 0 ? (
                                <div className="space-y-4">
                                    {agendamentosHoje.map((agendamento: Agendamento) => (
                                        <div key={agendamento.id} className="flex items-center justify-between rounded-lg border p-4">
                                            <div className="flex-1">
                                                <div className="mb-1 flex items-center gap-2">
                                                    <span className="font-medium">{formatTime(agendamento.data_hora)}</span>
                                                    {getStatusBadge(agendamento.status)}
                                                </div>
                                                <p className="text-sm text-gray-600">{agendamento.paciente.name}</p>
                                                <p className="text-xs text-gray-500">{agendamento.tipo}</p>
                                                {agendamento.endereco && (
                                                    <div className="mt-1 flex items-center gap-1">
                                                        <MapPin className="h-3 w-3 text-gray-400" />
                                                        <span className="text-xs text-gray-500">{agendamento.endereco}</span>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="flex items-center gap-2">
                                                {agendamento.paciente.phone && (
                                                    <Button variant="outline" size="sm">
                                                        <Phone className="h-4 w-4" />
                                                    </Button>
                                                )}
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/fisioterapeuta/agenda/${agendamento.id}`}>Ver</Link>
                                                </Button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="py-8 text-center text-gray-500">Nenhum agendamento para hoje</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Pacientes Recentes */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5" />
                                Pacientes Recentes
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {pacientesRecentes && pacientesRecentes.length > 0 ? (
                                <div className="space-y-4">
                                    {pacientesRecentes.map((paciente: PacienteRecente) => (
                                        <div key={paciente.id} className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <p className="text-sm font-medium">{paciente.name}</p>
                                                <p className="text-xs text-gray-500">{paciente.total_sessoes} sessões</p>
                                            </div>
                                            <Button variant="ghost" size="sm" asChild>
                                                <Link href={`/fisioterapeuta/pacientes/${paciente.id}`}>Ver</Link>
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="py-8 text-center text-gray-500">Nenhum paciente encontrado</p>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
