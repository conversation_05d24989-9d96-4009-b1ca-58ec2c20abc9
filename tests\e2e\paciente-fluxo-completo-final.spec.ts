import { test, expect, type Page } from '@playwright/test';

// Configuração de teste
const BASE_URL = 'http://localhost:8000';

// Paciente com assinatura ativa (criada manualmente)
const PACIENTE_COM_ASSINATURA = {
    email: '<EMAIL>',
    password: 'paciente123',
    name: '<PERSON>'
};

test.describe('Fluxo Completo do Paciente - COM Assinatura Ativa', () => {
    test.beforeEach(async ({ page }) => {
        test.setTimeout(120000);
        page.setDefaultTimeout(30000);
        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');
    });

    test('1. Login e Acesso ao Dashboard', async ({ page }) => {
        console.log('🔐 Testando login com paciente com assinatura ativa...');

        // Fazer login
        await page.goto('/login');
        await page.waitForLoadState('networkidle');

        await page.fill('input[type="email"], input[name="email"]', PACIENTE_COM_ASSINATURA.email);
        await page.fill('input[type="password"], input[name="password"]', PACIENTE_COM_ASSINATURA.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Verificar redirecionamento para dashboard
        const currentUrl = page.url();
        console.log('URL após login:', currentUrl);

        if (currentUrl.includes('/paciente/dashboard')) {
            console.log('✅ Redirecionado para dashboard - Assinatura ativa confirmada!');
        } else if (currentUrl.includes('/paciente/planos')) {
            console.log('❌ Ainda redirecionado para planos - Assinatura não ativa');
            throw new Error('Assinatura não está ativa');
        } else {
            console.log('⚠️ Redirecionamento inesperado:', currentUrl);
        }

        // Verificar elementos do dashboard
        await expect(page.locator('h1, h2, .title')).toBeVisible();
        console.log('✅ Dashboard carregado com sucesso');
    });

    test('2. Teste Completo do Formulário de Perfil', async ({ page }) => {
        console.log('👤 Testando formulário de perfil com assinatura ativa...');

        // Fazer login
        await fazerLogin(page);

        // Ir para perfil
        await page.goto('/paciente/perfil');
        await page.waitForLoadState('networkidle');

        // Verificar se chegou na página de perfil
        const currentUrl = page.url();
        expect(currentUrl).toContain('/paciente/perfil');
        console.log('✅ Página de perfil acessível');

        // Verificar se o formulário está presente
        const inputName = page.locator('input[name="name"]');
        const inputPhone = page.locator('input[name="phone"]');
        const inputEmail = page.locator('input[name="email"]');

        if (await inputName.isVisible()) {
            console.log('✅ Campo nome encontrado');
            
            // Testar edição do nome
            const nomeOriginal = await inputName.inputValue();
            console.log('Nome original:', nomeOriginal);
            
            await inputName.fill('José da Silva - Editado');
            console.log('✅ Nome editado com sucesso');
        } else {
            console.log('❌ Campo nome não encontrado');
        }

        if (await inputPhone.isVisible()) {
            console.log('✅ Campo telefone encontrado');
            await inputPhone.fill('(11) 99999-9999');
            console.log('✅ Telefone editado com sucesso');
        } else {
            console.log('❌ Campo telefone não encontrado');
        }

        // Procurar botão de salvar
        const botaoSalvar = page.locator('button[type="submit"], button:has-text("Salvar"), button:has-text("Atualizar")').first();
        if (await botaoSalvar.isVisible()) {
            await botaoSalvar.click();
            await page.waitForLoadState('networkidle');
            console.log('✅ Formulário enviado com sucesso');
        } else {
            console.log('⚠️ Botão de salvar não encontrado');
        }
    });

    test('3. Navegação Completa da Sidebar', async ({ page }) => {
        console.log('🧭 Testando navegação completa da sidebar...');

        await fazerLogin(page);

        const paginasParaTestar = [
            { nome: 'Dashboard', href: '/paciente/dashboard' },
            { nome: 'Perfil', href: '/paciente/perfil' },
            { nome: 'Agendamentos', href: '/paciente/agendamentos' },
            { nome: 'Histórico', href: '/paciente/historico' },
            { nome: 'Pagamentos', href: '/paciente/pagamentos' },
            { nome: 'Planos', href: '/paciente/planos' },
            { nome: 'Avaliações', href: '/paciente/avaliacoes' },
            { nome: 'Afiliados', href: '/paciente/afiliados' }
        ];

        for (const pagina of paginasParaTestar) {
            console.log(`📄 Testando página: ${pagina.nome}`);

            await page.goto(pagina.href);
            await page.waitForLoadState('networkidle');

            const currentUrl = page.url();
            if (currentUrl.includes(pagina.href)) {
                console.log(`✅ ${pagina.nome} acessível`);
                
                // Verificar se a página carregou conteúdo
                const temConteudo = await page.locator('h1, h2, .title, .content, main').count() > 0;
                if (temConteudo) {
                    console.log(`✅ ${pagina.nome} com conteúdo carregado`);
                } else {
                    console.log(`⚠️ ${pagina.nome} sem conteúdo visível`);
                }
            } else {
                console.log(`❌ ${pagina.nome} redirecionada para: ${currentUrl}`);
            }

            await page.waitForTimeout(1000); // Pausa entre navegações
        }
    });

    test('4. Teste de Agendamento (Se Disponível)', async ({ page }) => {
        console.log('📅 Testando funcionalidade de agendamento...');

        await fazerLogin(page);

        // Ir para agendamentos
        await page.goto('/paciente/agendamentos');
        await page.waitForLoadState('networkidle');

        if (page.url().includes('/paciente/agendamentos')) {
            console.log('✅ Página de agendamentos acessível');

            // Procurar botão de novo agendamento
            const botaoNovo = page.locator('button:has-text("Novo"), button:has-text("Agendar"), a:has-text("Novo"), a:has-text("Agendar")').first();
            
            if (await botaoNovo.isVisible()) {
                console.log('✅ Botão de novo agendamento encontrado');
                await botaoNovo.click();
                await page.waitForLoadState('networkidle');

                const currentUrl = page.url();
                if (currentUrl.includes('create') || currentUrl.includes('novo')) {
                    console.log('✅ Página de criação de agendamento acessível');

                    // Verificar elementos do formulário
                    const temFormulario = await page.locator('form, select, input[type="date"], input[type="time"]').count() > 0;
                    if (temFormulario) {
                        console.log('✅ Formulário de agendamento presente');
                    } else {
                        console.log('⚠️ Formulário de agendamento não encontrado');
                    }
                } else {
                    console.log('⚠️ Não redirecionou para página de criação');
                }
            } else {
                console.log('⚠️ Botão de novo agendamento não encontrado');
            }
        } else {
            console.log('❌ Página de agendamentos não acessível');
        }
    });

    test('5. Teste de Responsividade Mobile', async ({ page }) => {
        console.log('📱 Testando responsividade mobile...');

        // Configurar viewport mobile
        await page.setViewportSize({ width: 375, height: 667 });

        await fazerLogin(page);

        // Verificar se o menu mobile funciona
        const menuMobile = page.locator('button[data-sidebar="trigger"], [data-sidebar="trigger"]').first();
        
        if (await menuMobile.isVisible()) {
            console.log('✅ Menu mobile encontrado');
            await menuMobile.click();
            await page.waitForTimeout(500);
            console.log('✅ Menu mobile funciona');

            // Testar navegação mobile
            const linkPerfil = page.locator('a[href="/paciente/perfil"]').first();
            if (await linkPerfil.isVisible()) {
                await linkPerfil.click();
                await page.waitForLoadState('networkidle');
                
                if (page.url().includes('/paciente/perfil')) {
                    console.log('✅ Navegação mobile para perfil funciona');
                }
            }
        } else {
            console.log('⚠️ Menu mobile não encontrado');
        }

        // Verificar se não há overflow horizontal
        const hasHorizontalScroll = await page.evaluate(() => {
            return document.documentElement.scrollWidth > document.documentElement.clientWidth;
        });
        
        if (!hasHorizontalScroll) {
            console.log('✅ Sem overflow horizontal em mobile');
        } else {
            console.log('⚠️ Overflow horizontal detectado em mobile');
        }
    });

    test('6. Teste de Logout', async ({ page }) => {
        console.log('🔒 Testando logout...');

        await fazerLogin(page);

        // Procurar menu do usuário
        const menuUsuario = page.locator('button:has-text("José"), button:has-text("J")').first();
        
        if (await menuUsuario.isVisible()) {
            await menuUsuario.click();
            await page.waitForTimeout(500);

            const botaoSair = page.locator('text=Sair, text=Logout').first();
            if (await botaoSair.isVisible()) {
                await botaoSair.click();
                await page.waitForLoadState('networkidle');

                const currentUrl = page.url();
                if (currentUrl.includes('/login') || currentUrl === BASE_URL + '/') {
                    console.log('✅ Logout realizado com sucesso');

                    // Tentar acessar página protegida
                    await page.goto('/paciente/dashboard');
                    await page.waitForLoadState('networkidle');

                    if (page.url().includes('/login')) {
                        console.log('✅ Redirecionamento de segurança funcionando');
                    } else {
                        console.log('⚠️ Página protegida acessível após logout');
                    }
                } else {
                    console.log('⚠️ Logout não funcionou corretamente');
                }
            } else {
                console.log('⚠️ Botão de logout não encontrado');
            }
        } else {
            console.log('⚠️ Menu do usuário não encontrado');
        }
    });

    // Função auxiliar para fazer login
    async function fazerLogin(page: Page) {
        await page.goto('/login');
        await page.waitForLoadState('networkidle');

        await page.fill('input[type="email"], input[name="email"]', PACIENTE_COM_ASSINATURA.email);
        await page.fill('input[type="password"], input[name="password"]', PACIENTE_COM_ASSINATURA.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Verificar se o login foi bem-sucedido
        const currentUrl = page.url();
        if (!currentUrl.includes('/paciente/dashboard')) {
            throw new Error('Login falhou - não redirecionou para dashboard');
        }
    }
});
