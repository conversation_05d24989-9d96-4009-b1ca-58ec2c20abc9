import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Eye, Download } from 'lucide-react';
import { toast } from 'sonner';

interface PreviewDia {
    data: string;
    data_formatada: string;
    dia_semana: string;
    horarios: string[];
    total_horarios: number;
}

export function PreviewCalendario() {
    const [dataInicio, setDataInicio] = useState('');
    const [dataFim, setDataFim] = useState('');
    const [preview, setPreview] = useState<PreviewDia[]>([]);
    const [loading, setLoading] = useState(false);

    const gerarPreview = async () => {
        if (!dataInicio || !dataFim) {
            toast.error('Selecione as datas de início e fim');
            return;
        }

        setLoading(true);
        try {
            const response = await fetch(route('fisioterapeuta.horarios.preview'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    data_inicio: dataInicio,
                    data_fim: dataFim,
                }),
            });

            const result = await response.json();
            
            if (result.success) {
                setPreview(result.preview);
                toast.success('Preview gerado com sucesso!');
            } else {
                toast.error('Erro ao gerar preview');
            }
        } catch (error) {
            console.error('Erro ao gerar preview:', error);
            toast.error('Erro ao gerar preview');
        } finally {
            setLoading(false);
        }
    };

    const getStatusColor = (totalHorarios: number) => {
        if (totalHorarios === 0) {
            return 'bg-red-100 text-red-800';
        } else if (totalHorarios <= 4) {
            return 'bg-yellow-100 text-yellow-800';
        } else {
            return 'bg-green-100 text-green-800';
        }
    };

    const getStatusText = (totalHorarios: number) => {
        if (totalHorarios === 0) {
            return 'Indisponível';
        } else if (totalHorarios <= 4) {
            return 'Disponibilidade Limitada';
        } else {
            return 'Disponível';
        }
    };

    const calcularEstatisticas = () => {
        if (preview.length === 0) return null;

        const diasDisponiveis = preview.filter(dia => dia.total_horarios > 0).length;
        const diasIndisponiveis = preview.filter(dia => dia.total_horarios === 0).length;
        const totalHorarios = preview.reduce((total, dia) => total + dia.total_horarios, 0);
        const mediaHorariosPorDia = totalHorarios / preview.length;

        return {
            diasDisponiveis,
            diasIndisponiveis,
            totalHorarios,
            mediaHorariosPorDia: Math.round(mediaHorariosPorDia * 10) / 10,
        };
    };

    const estatisticas = calcularEstatisticas();

    // Definir datas padrão (próximos 14 dias)
    const hoje = new Date();
    const proximosDias = new Date(hoje);
    proximosDias.setDate(hoje.getDate() + 14);

    const dataInicioDefault = dataInicio || hoje.toISOString().split('T')[0];
    const dataFimDefault = dataFim || proximosDias.toISOString().split('T')[0];

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-2xl font-bold">Preview de Disponibilidade</h2>
                <p className="text-muted-foreground">
                    Visualize como suas configurações afetam sua disponibilidade
                </p>
            </div>

            {/* Controles de Preview */}
            <Card>
                <CardHeader>
                    <CardTitle>Gerar Preview</CardTitle>
                    <CardDescription>
                        Selecione um período para visualizar sua disponibilidade
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div className="grid gap-2">
                            <Label htmlFor="data_inicio">Data Início</Label>
                            <Input
                                id="data_inicio"
                                type="date"
                                value={dataInicio || dataInicioDefault}
                                onChange={(e) => setDataInicio(e.target.value)}
                            />
                        </div>
                        <div className="grid gap-2">
                            <Label htmlFor="data_fim">Data Fim</Label>
                            <Input
                                id="data_fim"
                                type="date"
                                value={dataFim || dataFimDefault}
                                onChange={(e) => setDataFim(e.target.value)}
                            />
                        </div>
                    </div>
                    <Button onClick={gerarPreview} disabled={loading} className="gap-2">
                        <Eye className="h-4 w-4" />
                        {loading ? 'Gerando...' : 'Gerar Preview'}
                    </Button>
                </CardContent>
            </Card>

            {/* Estatísticas */}
            {estatisticas && (
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Dias Disponíveis</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{estatisticas.diasDisponiveis}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Dias Indisponíveis</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{estatisticas.diasIndisponiveis}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Horários</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{estatisticas.totalHorarios}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Média por Dia</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{estatisticas.mediaHorariosPorDia}</div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Preview dos Dias */}
            {preview.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle>Disponibilidade por Dia</CardTitle>
                        <CardDescription>
                            Visualização detalhada da sua disponibilidade no período selecionado
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {preview.map((dia) => (
                                <div
                                    key={dia.data}
                                    className="flex items-center justify-between p-4 border rounded-lg"
                                >
                                    <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-2">
                                            <h4 className="font-medium">
                                                {dia.data_formatada} - {dia.dia_semana}
                                            </h4>
                                            <Badge className={getStatusColor(dia.total_horarios)}>
                                                {getStatusText(dia.total_horarios)}
                                            </Badge>
                                            <span className="text-sm text-muted-foreground">
                                                {dia.total_horarios} horário(s)
                                            </span>
                                        </div>
                                        {dia.horarios.length > 0 && (
                                            <div className="flex flex-wrap gap-1">
                                                {dia.horarios.slice(0, 8).map((horario) => (
                                                    <Badge key={horario} variant="outline" className="text-xs">
                                                        {horario}
                                                    </Badge>
                                                ))}
                                                {dia.horarios.length > 8 && (
                                                    <Badge variant="outline" className="text-xs">
                                                        +{dia.horarios.length - 8} mais
                                                    </Badge>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {preview.length === 0 && !loading && (
                <Card>
                    <CardContent className="text-center py-12">
                        <Calendar className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                        <h3 className="text-lg font-medium mb-2">Nenhum preview gerado</h3>
                        <p className="text-muted-foreground mb-4">
                            Selecione um período e clique em "Gerar Preview" para visualizar sua disponibilidade
                        </p>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
