<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class PerfilController extends Controller
{
    /**
     * Display the paciente profile.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Estados brasileiros
        $estados = [
            'AC' => 'Acre',
            'AL' => 'Alagoas',
            'AP' => 'Amapá',
            'AM' => 'Amazonas',
            'BA' => 'Bahia',
            'CE' => 'Ceará',
            'DF' => 'Distrito Federal',
            'ES' => 'Espírito Santo',
            'GO' => 'Goiás',
            'MA' => 'Maranhão',
            'MT' => 'Mato Grosso',
            'MS' => 'Mato Grosso do Sul',
            'MG' => 'Minas Gerais',
            'PA' => 'Pará',
            'PB' => 'Paraíba',
            'PR' => 'Paraná',
            'PE' => 'Pernambuco',
            'PI' => 'Piauí',
            'RJ' => 'Rio de Janeiro',
            'RN' => 'Rio Grande do Norte',
            'RS' => 'Rio Grande do Sul',
            'RO' => 'Rondônia',
            'RR' => 'Roraima',
            'SC' => 'Santa Catarina',
            'SP' => 'São Paulo',
            'SE' => 'Sergipe',
            'TO' => 'Tocantins',
        ];
        
        return Inertia::render('paciente/perfil', [
            'user' => $user,
            'estados' => $estados,
        ]);
    }
    
    /**
     * Update the paciente profile.
     */
    public function update(Request $request)
    {
        $user = auth()->user();
        
        $validated = $request->validate([
            // Dados pessoais
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'birth_date' => 'nullable|date|before:today',
            'gender' => 'nullable|in:masculino,feminino,outro',
            
            // Endereço
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|size:2',
            'zip_code' => 'nullable|string|max:9',
            
            // Informações médicas
            'medical_history' => 'nullable|string|max:2000',
            'emergency_contact' => 'nullable|string|max:500',

            // Configurações de privacidade
            'privacy_profile_visible' => 'boolean',
            'privacy_contact_visible' => 'boolean',
            'privacy_medical_visible' => 'boolean',
            'privacy_allow_marketing' => 'boolean',

            // Avatar
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);
        
        // Upload de avatar se fornecido
        if ($request->hasFile('avatar')) {
            // Deletar avatar anterior se existir
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = $avatarPath;
        }
        
        // Atualizar dados do usuário
        $user->update($validated);
        
        return redirect()->route('paciente.perfil')
            ->with('success', 'Perfil atualizado com sucesso!');
    }
    
    /**
     * Upload avatar
     */
    public function uploadAvatar(Request $request)
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);
        
        $user = auth()->user();
        
        // Deletar avatar anterior se existir
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
        }
        
        $avatarPath = $request->file('avatar')->store('avatars', 'public');
        $user->update(['avatar' => $avatarPath]);
        
        return response()->json([
            'success' => true,
            'avatar_url' => Storage::url($avatarPath),
            'message' => 'Avatar atualizado com sucesso!'
        ]);
    }
    
    /**
     * Remove avatar
     */
    public function removeAvatar()
    {
        $user = auth()->user();
        
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
            $user->update(['avatar' => null]);
        }
        
        return response()->json([
            'success' => true,
            'message' => 'Avatar removido com sucesso!'
        ]);
    }
}
