import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import { safeRoute } from '@/utils/route-helper';

interface User {
    id: number;
    name: string;
    email: string;
}

interface Props {
    usuariosDisponiveis: User[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Fisioterapeutas',
        href: '/admin/fisioterapeutas',
    },
    {
        title: 'Novo Fisioterapeuta',
        href: '#',
    },
];

const specializations = [
    'Ortopedia',
    'Neurologia',
    'Cardiorrespiratória',
    'Pediatria',
    'Geriatria',
    'Esportiva',
    'Dermatofuncional',
    'Uroginecologia',
    'Traumatologia',
    'Reumatologia',
];

export default function FisioterapeutaCreate({ usuariosDisponiveis }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        user_id: '',
        crefito: '',
        specializations: [] as string[],
        bio: '',
        experience_years: '',
        hourly_rate: '',
        active: true,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const routeUrl = safeRoute('admin.fisioterapeutas.store');
        if (routeUrl !== '#') {
            post(routeUrl);
        }
    };

    const handleSpecializationChange = (specialization: string, checked: boolean) => {
        if (checked) {
            setData('specializations', [...data.specializations, specialization]);
        } else {
            setData('specializations', data.specializations.filter(s => s !== specialization));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Novo Fisioterapeuta" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Novo Fisioterapeuta</h1>
                        <p className="text-muted-foreground">
                            Cadastre um novo fisioterapeuta no sistema
                        </p>
                    </div>
                    <Link href={safeRoute('admin.fisioterapeutas.index')} preserveState>
                        <Button variant="ghost">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar
                        </Button>
                    </Link>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Informações do Fisioterapeuta</CardTitle>
                        <CardDescription>
                            Preencha os dados do fisioterapeuta
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="user_id">Usuário</Label>
                                    <Select value={data.user_id} onValueChange={(value) => setData('user_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione um usuário" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {usuariosDisponiveis.map((user) => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {user.name} ({user.email})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.user_id && <p className="text-sm text-red-600">{errors.user_id}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="crefito">CREFITO</Label>
                                    <Input
                                        id="crefito"
                                        value={data.crefito}
                                        onChange={(e) => setData('crefito', e.target.value)}
                                        placeholder="Ex: 123456-F"
                                    />
                                    {errors.crefito && <p className="text-sm text-red-600">{errors.crefito}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="experience_years">Anos de Experiência</Label>
                                    <Input
                                        id="experience_years"
                                        type="number"
                                        value={data.experience_years}
                                        onChange={(e) => setData('experience_years', e.target.value)}
                                        placeholder="Ex: 5"
                                    />
                                    {errors.experience_years && <p className="text-sm text-red-600">{errors.experience_years}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="hourly_rate">Valor por Hora (R$)</Label>
                                    <Input
                                        id="hourly_rate"
                                        type="number"
                                        step="0.01"
                                        value={data.hourly_rate}
                                        onChange={(e) => setData('hourly_rate', e.target.value)}
                                        placeholder="Ex: 80.00"
                                    />
                                    {errors.hourly_rate && <p className="text-sm text-red-600">{errors.hourly_rate}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label>Especializações</Label>
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                                    {specializations.map((specialization) => (
                                        <div key={specialization} className="flex items-center space-x-2">
                                            <Checkbox
                                                id={specialization}
                                                checked={data.specializations.includes(specialization)}
                                                onCheckedChange={(checked) => 
                                                    handleSpecializationChange(specialization, checked as boolean)
                                                }
                                            />
                                            <Label htmlFor={specialization} className="text-sm">
                                                {specialization}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                                {errors.specializations && <p className="text-sm text-red-600">{errors.specializations}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="bio">Biografia</Label>
                                <Textarea
                                    id="bio"
                                    value={data.bio}
                                    onChange={(e) => setData('bio', e.target.value)}
                                    placeholder="Descreva a experiência e especialidades do fisioterapeuta..."
                                    rows={4}
                                />
                                {errors.bio && <p className="text-sm text-red-600">{errors.bio}</p>}
                            </div>

                            <div className="flex items-center justify-end space-x-4">
                                <Link href={safeRoute('admin.fisioterapeutas.index')} preserveState>
                                    <Button type="button" variant="outline">
                                        Cancelar
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Salvando...' : 'Salvar Fisioterapeuta'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
