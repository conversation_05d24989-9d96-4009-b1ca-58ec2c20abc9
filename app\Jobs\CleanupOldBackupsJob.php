<?php

namespace App\Jobs;

use App\Models\BackupLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class CleanupOldBackupsJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    protected $daysToKeep;

    /**
     * Create a new job instance.
     */
    public function __construct(int $daysToKeep = 30)
    {
        $this->daysToKeep = $daysToKeep;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Iniciando limpeza de backups antigos', [
                'days_to_keep' => $this->daysToKeep
            ]);

            $cutoffDate = Carbon::now()->subDays($this->daysToKeep);

            $oldBackups = BackupLog::where('created_at', '<', $cutoffDate)
                ->whereIn('type', [BackupLog::TYPE_BACKUP, BackupLog::TYPE_EXPORT])
                ->get();

            $deletedCount = 0;
            $totalSize = 0;

            foreach ($oldBackups as $backup) {
                try {
                    // Deletar arquivo físico se existir
                    if ($backup->file_path) {
                        $disk = $backup->type === BackupLog::TYPE_BACKUP ? 'local' : 'local';

                        if (Storage::disk($disk)->exists($backup->file_path)) {
                            $totalSize += $backup->file_size ?? 0;
                            Storage::disk($disk)->delete($backup->file_path);
                        }
                    }

                    // Deletar registro do banco
                    $backup->delete();
                    $deletedCount++;

                } catch (\Exception $e) {
                    Log::warning('Falha ao deletar backup antigo', [
                        'backup_id' => $backup->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('Limpeza de backups concluída', [
                'deleted_count' => $deletedCount,
                'freed_space' => $this->formatBytes($totalSize),
                'cutoff_date' => $cutoffDate->toDateString()
            ]);

        } catch (\Exception $e) {
            Log::error('Falha na limpeza de backups', [
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Formatar bytes em formato legível
     */
    protected function formatBytes(int $bytes): string
    {
        if ($bytes === 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Job de limpeza de backups falhou', [
            'days_to_keep' => $this->daysToKeep,
            'error' => $exception->getMessage()
        ]);
    }
}
