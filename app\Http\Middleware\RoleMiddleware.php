<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $currentMode = session('user_mode', 'normal');

        if (!$user->active) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Sua conta está inativa. Entre em contato com o suporte.');
        }

        // Se estiver em modo afiliado e tiver permissão para isso
        if ($currentMode === 'afiliado' && $user->canSwitchToAfiliadoMode()) {
            // Se a rota atual requer role de afiliado, permitir acesso
            if (in_array('afiliado', $roles)) {
                return $next($request);
            }

            // Se não, verificar se o role normal do usuário tem acesso
            if (!in_array($user->role, $roles)) {
                abort(403, 'Acesso negado. Você não tem permissão para acessar esta página.');
            }
        } else {
            // Verificação normal de role
            if (!in_array($user->role, $roles)) {
                abort(403, 'Acesso negado. Você não tem permissão para acessar esta página.');
            }
        }

        return $next($request);
    }
}
