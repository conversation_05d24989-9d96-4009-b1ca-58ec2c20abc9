<?php

require_once 'vendor/autoload.php';

use App\Models\Afiliado;
use App\Models\User;
use App\Models\Plano;
use App\Models\Assinatura;
use App\Models\VendaAfiliado;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 Testando Fluxo Completo de Afiliado/Comissão\n";
echo "================================================\n\n";

try {
    // 1. Buscar o afiliado
    echo "1. Buscando afiliado...\n";
    $afiliado = Afiliado::where('codigo_afiliado', 'AF77DQFLZX')->first();
    if (!$afiliado) {
        echo "❌ Afiliado não encontrado!\n";
        exit(1);
    }
    echo "✅ Afiliado encontrado: {$afiliado->nome}\n";
    echo "   - Código: {$afiliado->codigo_afiliado}\n";
    echo "   - Status: {$afiliado->status}\n";
    echo "   - Ativo: " . ($afiliado->ativo ? 'Sim' : 'Não') . "\n";
    echo "   - Total comissões atual: R$ {$afiliado->total_comissoes}\n\n";

    // 2. Buscar o usuário cliente
    echo "2. Buscando cliente...\n";
    $cliente = User::where('email', '<EMAIL>')->first();
    if (!$cliente) {
        echo "❌ Cliente não encontrado!\n";
        exit(1);
    }
    echo "✅ Cliente encontrado: {$cliente->name}\n";
    echo "   - Email: {$cliente->email}\n";
    echo "   - Role: {$cliente->role}\n\n";

    // 3. Buscar plano
    echo "3. Buscando plano...\n";
    $plano = Plano::where('name', 'LIKE', '%Pessoal%')->first();
    if (!$plano) {
        $plano = Plano::first();
    }
    if (!$plano) {
        echo "❌ Nenhum plano encontrado!\n";
        exit(1);
    }
    echo "✅ Plano encontrado: {$plano->name}\n";
    echo "   - Preço: R$ {$plano->price}\n\n";

    // 4. Criar assinatura
    echo "4. Criando assinatura...\n";
    $assinatura = Assinatura::create([
        'user_id' => $cliente->id,
        'plano_id' => $plano->id,
        'status' => 'ativa',
        'start_date' => now(),
        'end_date' => now()->addMonth(),
        'current_period_start' => now(),
        'current_period_end' => now()->addMonth(),
        'monthly_price' => $plano->price,
    ]);
    echo "✅ Assinatura criada: ID {$assinatura->id}\n";
    echo "   - Status: {$assinatura->status}\n";
    echo "   - Valor mensal: R$ {$assinatura->monthly_price}\n\n";

    // 5. Criar venda de afiliado
    echo "5. Criando venda de afiliado...\n";
    $vendaAfiliado = VendaAfiliado::criarVenda(
        $afiliado->id,
        $cliente->id,
        'pessoal',
        (float) $plano->price,
        $assinatura->id
    );
    echo "✅ Venda de afiliado criada: ID {$vendaAfiliado->id}\n";
    echo "   - Valor da venda: R$ {$vendaAfiliado->valor_venda}\n";
    echo "   - Comissão: R$ {$vendaAfiliado->comissao}\n";
    echo "   - Percentual: {$vendaAfiliado->percentual_comissao}%\n";
    echo "   - Status: {$vendaAfiliado->status}\n\n";

    // 6. Confirmar a venda (simular webhook do Mercado Pago)
    echo "6. Confirmando venda (simulando webhook)...\n";
    $vendaAfiliado->update([
        'status' => 'confirmada',
        'data_confirmacao' => now()
    ]);
    echo "✅ Venda confirmada!\n";
    echo "   - Status: {$vendaAfiliado->fresh()->status}\n";
    echo "   - Data confirmação: {$vendaAfiliado->fresh()->data_confirmacao}\n\n";

    // 7. Atualizar estatísticas do afiliado
    echo "7. Atualizando estatísticas do afiliado...\n";
    $afiliado->atualizarEstatisticas();
    $afiliadoAtualizado = $afiliado->fresh();
    echo "✅ Estatísticas atualizadas!\n";
    echo "   - Total vendas: R$ {$afiliadoAtualizado->total_vendas}\n";
    echo "   - Total comissões: R$ {$afiliadoAtualizado->total_comissoes}\n";
    echo "   - Vendas mês atual: {$afiliadoAtualizado->vendas_mes_atual}\n";
    echo "   - Comissões mês atual: R$ {$afiliadoAtualizado->comissoes_mes_atual}\n\n";

    // 8. Verificar se o cliente tem o plano associado
    echo "8. Verificando associação do plano ao cliente...\n";
    $clienteAtualizado = $cliente->fresh();
    $assinaturaAtiva = $clienteAtualizado->assinaturas()->where('status', 'ativa')->first();
    if ($assinaturaAtiva) {
        echo "✅ Cliente tem plano ativo!\n";
        echo "   - Plano: {$assinaturaAtiva->plano->name}\n";
        echo "   - Status: {$assinaturaAtiva->status}\n";
        echo "   - Início: {$assinaturaAtiva->start_date}\n";
        echo "   - Fim: {$assinaturaAtiva->end_date}\n";
    } else {
        echo "❌ Cliente não tem plano ativo!\n";
    }

    echo "\n🎉 TESTE CONCLUÍDO COM SUCESSO!\n";
    echo "================================\n";
    echo "✅ Cookie de afiliado foi rastreado\n";
    echo "✅ Venda foi associada ao afiliado\n";
    echo "✅ Comissão foi calculada e registrada\n";
    echo "✅ Plano foi associado ao cliente\n";
    echo "✅ Estatísticas do afiliado foram atualizadas\n";

} catch (Exception $e) {
    echo "❌ Erro durante o teste: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}
