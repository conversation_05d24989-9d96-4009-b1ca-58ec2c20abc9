import React from 'react';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { AlertTriangle, CheckCircle, Info, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export type ConfirmationType = 'danger' | 'warning' | 'info' | 'success';

export interface ConfirmationDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    title: string;
    description: string;
    confirmText?: string;
    cancelText?: string;
    type?: ConfirmationType;
    onConfirm: () => void;
    onCancel?: () => void;
    loading?: boolean;
}

const typeConfig = {
    danger: {
        icon: XCircle,
        iconClassName: 'text-red-500',
        confirmClassName: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
    },
    warning: {
        icon: Alert<PERSON>riangle,
        iconClassName: 'text-yellow-500',
        confirmClassName: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
    },
    info: {
        icon: Info,
        iconClassName: 'text-blue-500',
        confirmClassName: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
    },
    success: {
        icon: CheckCircle,
        iconClassName: 'text-green-500',
        confirmClassName: 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
    }
};

export function ConfirmationDialog({
    open,
    onOpenChange,
    title,
    description,
    confirmText = 'Confirmar',
    cancelText = 'Cancelar',
    type = 'info',
    onConfirm,
    onCancel,
    loading = false
}: ConfirmationDialogProps) {
    const config = typeConfig[type];
    const Icon = config.icon;

    const handleConfirm = () => {
        onConfirm();
    };

    const handleCancel = () => {
        onCancel?.();
        onOpenChange(false);
    };

    return (
        <AlertDialog open={open} onOpenChange={onOpenChange}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <div className="flex items-center gap-3">
                        <Icon className={cn('h-6 w-6', config.iconClassName)} />
                        <AlertDialogTitle>{title}</AlertDialogTitle>
                    </div>
                    <AlertDialogDescription className="text-left">
                        {description}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel onClick={handleCancel} disabled={loading}>
                        {cancelText}
                    </AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleConfirm}
                        disabled={loading}
                        className={cn(config.confirmClassName)}
                    >
                        {loading ? (
                            <>
                                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                                Processando...
                            </>
                        ) : (
                            confirmText
                        )}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}

// Hook para usar o dialog de confirmação
export function useConfirmationDialog() {
    const [dialog, setDialog] = React.useState<{
        open: boolean;
        title: string;
        description: string;
        confirmText?: string;
        cancelText?: string;
        type?: ConfirmationType;
        onConfirm: () => void;
        onCancel?: () => void;
        loading?: boolean;
    }>({
        open: false,
        title: '',
        description: '',
        onConfirm: () => {}
    });

    const showConfirmation = React.useCallback((options: Omit<typeof dialog, 'open'>) => {
        setDialog({
            ...options,
            open: true
        });
    }, []);

    const hideConfirmation = React.useCallback(() => {
        setDialog(prev => ({ ...prev, open: false }));
    }, []);

    const setLoading = React.useCallback((loading: boolean) => {
        setDialog(prev => ({ ...prev, loading }));
    }, []);

    return {
        dialog,
        showConfirmation,
        hideConfirmation,
        setLoading,
        ConfirmationDialog: (props: Partial<ConfirmationDialogProps>) => (
            <ConfirmationDialog
                {...dialog}
                {...props}
                onOpenChange={hideConfirmation}
            />
        )
    };
}
