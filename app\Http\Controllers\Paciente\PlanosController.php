<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Plano;
use App\Models\Assinatura;
use App\Services\AffiliateTrackingService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class PlanosController extends Controller
{
    /**
     * Exibe a página de seleção de planos
     */
    public function index()
    {
        $planos = [
            [
                'id' => 'sessao-avulsa',
                'name' => 'Sessão Avulsa',
                'price' => 180.00,
                'description' => 'Por sessão',
                'features' => [
                    'Sessão de 60 minutos',
                    'Avaliação básica incluída',
                    'Equipamentos profissionais',
                    'Orientações domiciliares',
                    'Pagamento no ato',
                ],
                'type' => 'avulsa'
            ],
            [
                'id' => 'plano-mensal',
                'name' => 'Plano Mensal',
                'price' => 640.00,
                'original_price' => 720.00,
                'description' => 'Por mês',
                'savings' => 80.00,
                'features' => [
                    '4 sessões de 60min por mês',
                    'Avaliação inicial gratuita',
                    'Plano terapêutico personalizado',
                    'Relatórios quinzenais',
                    'Suporte via WhatsApp',
                    'Horários flexíveis',
                    'Sem taxa de adesão',
                    'Cancelamento flexível',
                ],
                'type' => 'mensal',
                'popular' => true
            ],
            [
                'id' => 'plano-empresarial',
                'name' => 'Plano Empresarial',
                'price' => null,
                'description' => 'Para grupos e empresas',
                'features' => [
                    'Atendimento para grupos',
                    'Planos corporativos',
                    'Desconto por volume',
                    'Gestão centralizada',
                    'Relatórios gerenciais',
                    'Suporte dedicado',
                    'Flexibilidade de horários',
                    'Contrato personalizado',
                ],
                'type' => 'empresarial'
            ]
        ];

        $user = auth()->user();

        // Verificar se o usuário já tem um plano selecionado
        $planoAtual = null;
        if ($user->has_subscription) {
            // Para plano avulso, não há assinatura na tabela assinaturas
            // Vamos assumir que é plano avulso se has_subscription = true mas não há assinatura
            $assinatura = \App\Models\Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->first();

            if ($assinatura) {
                $planoAtual = [
                    'type' => 'mensal',
                    'name' => 'Plano Mensal',
                    'status' => $assinatura->status
                ];
            } else {
                $planoAtual = [
                    'type' => 'avulsa',
                    'name' => 'Sessão Avulsa',
                    'status' => 'ativo'
                ];
            }
        }

        return Inertia::render('paciente/planos', [
            'planos' => $planos,
            'planoAtual' => $planoAtual,
            'hasSubscription' => $user->has_subscription
        ]);
    }

    /**
     * Processa a seleção do plano
     */
    public function store(Request $request)
    {
        \Log::info('PlanosController@store called', [
            'user' => auth()->user(),
            'request_data' => $request->all()
        ]);

        $request->validate([
            'plano_type' => 'required|string|in:avulsa,mensal,empresarial'
        ]);

        $user = auth()->user();
        $planoType = $request->plano_type;

        \Log::info('Processing plan selection', [
            'user_id' => $user->id,
            'plano_type' => $planoType,
            'current_has_subscription' => $user->has_subscription
        ]);

        if ($planoType === 'empresarial') {
            // Para plano empresarial, redireciona para WhatsApp
            return redirect()->away(
                'https://wa.me/5511978196207?text=' . urlencode(
                    'Olá! Gostaria de saber mais sobre o plano empresarial de fisioterapia domiciliar para grupos/empresas. Meu nome é ' . $user->name . ' e meu email é ' . $user->email . '.'
                )
            );
        }

        if ($planoType === 'avulsa') {
            // Se já tem assinatura mensal, cancelar primeiro
            if ($user->has_subscription) {
                $assinaturaAtiva = \App\Models\Assinatura::where('user_id', $user->id)
                    ->where('status', 'ativa')
                    ->first();

                if ($assinaturaAtiva) {
                    $assinaturaAtiva->update(['status' => 'cancelada']);
                    \Log::info('Cancelled previous subscription for plan change', [
                        'user_id' => $user->id,
                        'old_subscription_id' => $assinaturaAtiva->id
                    ]);
                }
            }

            // Para plano avulso, apenas marca que tem assinatura e redireciona para dashboard
            $updateResult = $user->update(['has_subscription' => true]);

            \Log::info('Updated user subscription status to avulsa', [
                'user_id' => $user->id,
                'update_result' => $updateResult,
                'new_has_subscription' => $user->fresh()->has_subscription
            ]);

            $message = $user->has_subscription ?
                'Plano alterado para Sessão Avulsa com sucesso! Você pode agendar suas consultas e pagar no momento do agendamento.' :
                'Plano Sessão Avulsa confirmado! Você pode agendar suas consultas e pagar no momento do agendamento.';

            return redirect()->route('paciente.planos')
                ->with('success', $message);
        }

        // Para plano mensal, cancelar assinatura anterior se existir
        if ($user->has_subscription) {
            $assinaturaAtiva = \App\Models\Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->first();

            if ($assinaturaAtiva) {
                $assinaturaAtiva->update(['status' => 'cancelada']);
                \Log::info('Cancelled previous subscription for plan change', [
                    'user_id' => $user->id,
                    'old_subscription_id' => $assinaturaAtiva->id
                ]);
            }
        }

        // Para plano mensal, criar assinatura e redirecionar para pagamento
        $user->update(['has_subscription' => true]);

        $assinatura = Assinatura::create([
            'user_id' => $user->id,
            'plano_id' => null, // Será definido após confirmação
            'status' => 'ativa', // Ativo para teste, em produção seria 'pendente'
            'start_date' => Carbon::now(),
            'end_date' => Carbon::now()->addMonth(),
            'sessions_used' => 0,
            'current_period_start' => Carbon::now(),
            'current_period_end' => Carbon::now()->addMonth(),
            'monthly_price' => 640.00,
        ]);

        \Log::info('Created new monthly subscription', [
            'user_id' => $user->id,
            'subscription_id' => $assinatura->id
        ]);

        // Processar venda de afiliado se houver referência
        $affiliateService = new AffiliateTrackingService();
        $affiliateService->createAffiliateSale(
            $user,
            $assinatura->id,
            'mensal',
            640.00,
            $request
        );

        // Redirecionar para pagamento via Mercado Pago
        // Por enquanto, vamos apenas confirmar a seleção
        return redirect()->route('paciente.planos')
            ->with('success', 'Plano Mensal selecionado! Em breve você será redirecionado para o pagamento via Mercado Pago.');
    }

    /**
     * Retorna dados do plano baseado no tipo
     */
    private function getPlanoData($type)
    {
        $planos = [
            'avulsa' => ['price' => 180.00, 'sessions' => 1],
            'mensal' => ['price' => 640.00, 'sessions' => 4],
        ];

        return $planos[$type] ?? ['price' => 0, 'sessions' => 0];
    }
}
