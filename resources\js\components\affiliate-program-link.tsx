import { Badge } from '@/components/ui/badge';
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type User } from '@/types';
import { Link, router } from '@inertiajs/react';
import { AlertCir<PERSON>, Clock, Handshake, UserCheck } from 'lucide-react';

interface AffiliateProgramLinkProps {
    user: User;
    currentUserMode?: string;
}

export function AffiliateProgramLink({ user, currentUserMode }: AffiliateProgramLinkProps) {
    const userMode = currentUserMode || 'normal';

    // Determinar o status do usuário em relação ao programa de afiliados
    const getAffiliateStatus = () => {
        if (!user.hasAfiliadoProfile) {
            // Determinar a URL baseada no role do usuário
            const dashboardUrl = user.role === 'fisioterapeuta' ? '/fisioterapeuta/afiliados' : '/paciente/afiliados';

            return {
                status: 'not_registered',
                label: 'Programa de Afiliados',
                description: 'Torne-se um parceiro',
                icon: Handshake,
                href: dashboardUrl,
                variant: 'default' as const,
                badge: null,
            };
        }

        const afiliado = user.afiliado as any;

        switch (afiliado?.status) {
            case 'pendente':
                // Determinar a URL baseada no role do usuário
                const dashboardUrl = user.role === 'fisioterapeuta' ? '/fisioterapeuta/afiliados' : '/paciente/afiliados';

                return {
                    status: 'pending',
                    label: 'Programa de Afiliados',
                    description: 'Aguardando aprovação',
                    icon: Clock,
                    href: dashboardUrl,
                    variant: 'default' as const,
                    badge: { text: 'Pendente', variant: 'secondary' as const },
                };
            case 'aprovado':
                if (userMode === 'afiliado') {
                    return {
                        status: 'active_affiliate',
                        label: 'Dashboard Afiliado',
                        description: 'Modo ativo',
                        icon: UserCheck,
                        href: '/afiliado/dashboard',
                        variant: 'default' as const,
                        badge: { text: 'Ativo', variant: 'default' as const },
                    };
                } else {
                    return {
                        status: 'approved',
                        label: 'Modo Afiliado',
                        description: 'Alternar para afiliado',
                        icon: UserCheck,
                        href: '#',
                        variant: 'default' as const,
                        badge: { text: 'Aprovado', variant: 'default' as const },
                        onClick: () => {
                            router.post(
                                '/switch-mode',
                                { mode: 'afiliado' },
                                {
                                    preserveState: true,
                                    preserveScroll: true,
                                },
                            );
                        },
                    };
                }
            case 'rejeitado':
                // Determinar a URL baseada no role do usuário
                const rejectedDashboardUrl = user.role === 'fisioterapeuta' ? '/fisioterapeuta/afiliados' : '/paciente/afiliados';

                return {
                    status: 'rejected',
                    label: 'Programa de Afiliados',
                    description: 'Solicitar nova análise',
                    icon: AlertCircle,
                    href: rejectedDashboardUrl,
                    variant: 'default' as const,
                    badge: { text: 'Rejeitado', variant: 'destructive' as const },
                };
            default:
                // Determinar a URL baseada no role do usuário
                const defaultDashboardUrl = user.role === 'fisioterapeuta' ? '/fisioterapeuta/afiliados' : '/paciente/afiliados';

                return {
                    status: 'not_registered',
                    label: 'Programa de Afiliados',
                    description: 'Torne-se um parceiro',
                    icon: Handshake,
                    href: defaultDashboardUrl,
                    variant: 'default' as const,
                    badge: null,
                };
        }
    };

    const affiliateInfo = getAffiliateStatus();

    return (
        <SidebarGroup className="px-2 py-0">
            <SidebarGroupLabel>Programa de Afiliados</SidebarGroupLabel>
            <SidebarMenu>
                <SidebarMenuItem>
                    {affiliateInfo.onClick ? (
                        <SidebarMenuButton
                            onClick={affiliateInfo.onClick}
                            tooltip={{ children: affiliateInfo.description }}
                            className="cursor-pointer"
                        >
                            <affiliateInfo.icon className="h-4 w-4" />
                            <div className="flex flex-1 items-center justify-between">
                                <span>{affiliateInfo.label}</span>
                                {affiliateInfo.badge && (
                                    <Badge variant={affiliateInfo.badge.variant} className="ml-2 text-xs">
                                        {affiliateInfo.badge.text}
                                    </Badge>
                                )}
                            </div>
                        </SidebarMenuButton>
                    ) : (
                        <SidebarMenuButton asChild tooltip={{ children: affiliateInfo.description }}>
                            <Link href={affiliateInfo.href} prefetch>
                                <affiliateInfo.icon className="h-4 w-4" />
                                <div className="flex flex-1 items-center justify-between">
                                    <span>{affiliateInfo.label}</span>
                                    {affiliateInfo.badge && (
                                        <Badge variant={affiliateInfo.badge.variant} className="ml-2 text-xs">
                                            {affiliateInfo.badge.text}
                                        </Badge>
                                    )}
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    )}
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarGroup>
    );
}
