<?php

test('registration screen can be rendered', function () {
    $response = $this->get('/register');

    $response->assertStatus(200);
});

test('new users can register', function () {
    // Usar um email que não requer validação DNS real
    $email = 'test' . time() . '@gmail.com';

    $response = $this->post('/register', [
        'name' => 'Test User',
        'email' => $email,
        'password' => 'Password123', // Senha que atende aos critérios
        'password_confirmation' => 'Password123',
        'role' => 'paciente',
        'phone' => '(11) 99999-9999',
        'birth_date' => '1990-01-01',
        'gender' => 'masculino',
        'address' => 'Rua Teste, 123',
        'city' => 'São Paulo',
        'state' => 'SP',
        'zip_code' => '01234-567',
    ]);

    $this->assertAuthenticated();
    $response->assertRedirect(route('dashboard'));
});