import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { safeRoute } from '@/utils/route-helper';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Edit, Eye, Plus, Search, Trash2 } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Fisioterapeutas',
        href: '/admin/fisioterapeutas',
    },
];

interface Fisioterapeuta {
    id: number;
    crefito: string;
    specializations: string[];
    bio?: string;
    hourly_rate: number;
    available_areas: string[];
    working_hours: any;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
        active: boolean;
        phone?: string;
    };
}

interface Props {
    fisioterapeutas: {
        data: Fisioterapeuta[];
        links: any[];
        meta: any;
    };
    filters: {
        active?: boolean;
        search?: string;
    };
}

export default function AdminFisioterapeutas({ fisioterapeutas, filters }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const { data, setData, get } = useForm({
        active: filters.active?.toString() || undefined,
        search: filters.search || '',
    });

    const handleFilter = () => {
        try {
            const routeUrl = safeRoute('admin.fisioterapeutas.index');
            if (routeUrl !== '#') {
                get(routeUrl, {
                    preserveState: true,
                    replace: true,
                });
            }
        } catch (error) {
            console.error('Erro ao filtrar fisioterapeutas:', error);
        }
    };

    const handleDelete = (id: number) => {
        if (confirm('Tem certeza que deseja remover este fisioterapeuta?')) {
            try {
                const routeUrl = safeRoute('admin.fisioterapeutas.destroy', id);
                if (routeUrl !== '#') {
                    router.delete(routeUrl);
                }
            } catch (error) {
                console.error('Erro ao deletar fisioterapeuta:', error);
            }
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Fisioterapeutas" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Fisioterapeutas</h1>
                        <p className="text-gray-600">Gerencie os fisioterapeutas cadastrados</p>
                    </div>
                    <Link href={safeRoute('admin.fisioterapeutas.create')} preserveState>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Novo Fisioterapeuta
                        </Button>
                    </Link>
                </div>

                {/* Filtros */}
                <div className="flex items-end gap-4">
                    <div className="flex-1">
                        <Input
                            placeholder="Buscar por nome ou CREFITO..."
                            value={search}
                            onChange={(e) => {
                                setSearch(e.target.value);
                                setData('search', e.target.value);
                            }}
                            className="max-w-sm"
                        />
                    </div>
                    <Select value={data.active || undefined} onValueChange={(value) => setData('active', value || undefined)}>
                        <SelectTrigger className="w-48">
                            <SelectValue placeholder="Filtrar por status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos os status</SelectItem>
                            <SelectItem value="true">Ativo</SelectItem>
                            <SelectItem value="false">Inativo</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button onClick={handleFilter}>
                        <Search className="mr-2 h-4 w-4" />
                        Filtrar
                    </Button>
                </div>

                {/* Cards dos Fisioterapeutas */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {fisioterapeutas.data.map((fisioterapeuta) => (
                        <div key={fisioterapeuta.id} className="rounded-lg border bg-card p-6">
                            <div className="mb-4 flex items-start justify-between">
                                <div className="flex-1">
                                    <h3 className="text-lg font-semibold">{fisioterapeuta.user.name}</h3>
                                    <p className="text-sm text-muted-foreground">CREFITO: {fisioterapeuta.crefito}</p>
                                    <p className="text-sm text-muted-foreground">{fisioterapeuta.user.email}</p>
                                    {fisioterapeuta.user.phone && <p className="text-sm text-muted-foreground">{fisioterapeuta.user.phone}</p>}
                                </div>
                                <span
                                    className={`rounded-full px-2 py-1 text-xs ${
                                        fisioterapeuta.user.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                    }`}
                                >
                                    {fisioterapeuta.user.active ? 'Ativo' : 'Inativo'}
                                </span>
                            </div>

                            <div className="space-y-3">
                                <div>
                                    <p className="text-sm font-medium text-gray-700">Especializações:</p>
                                    <div className="mt-1 flex flex-wrap gap-1">
                                        {fisioterapeuta.specializations.map((spec, index) => (
                                            <span key={index} className="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800">
                                                {spec}
                                            </span>
                                        ))}
                                    </div>
                                </div>

                                <div>
                                    <p className="text-sm font-medium text-gray-700">Valor por hora:</p>
                                    <p className="text-lg font-bold text-green-600">
                                        {new Intl.NumberFormat('pt-BR', {
                                            style: 'currency',
                                            currency: 'BRL',
                                        }).format(fisioterapeuta.hourly_rate)}
                                    </p>
                                </div>

                                <div>
                                    <p className="text-sm font-medium text-gray-700">Áreas de atendimento:</p>
                                    <p className="text-sm text-muted-foreground">{fisioterapeuta.available_areas.join(', ')}</p>
                                </div>

                                {fisioterapeuta.bio && (
                                    <div>
                                        <p className="text-sm font-medium text-gray-700">Bio:</p>
                                        <p className="line-clamp-2 text-sm text-muted-foreground">{fisioterapeuta.bio}</p>
                                    </div>
                                )}
                            </div>

                            <div className="mt-6 flex items-center justify-between border-t pt-4">
                                <p className="text-xs text-muted-foreground">
                                    Cadastrado em {new Date(fisioterapeuta.created_at).toLocaleDateString('pt-BR')}
                                </p>
                                <div className="flex items-center gap-2">
                                    <Link href={safeRoute('admin.fisioterapeutas.show', fisioterapeuta.id)} preserveState>
                                        <Button variant="ghost" size="sm">
                                            <Eye className="h-4 w-4" />
                                        </Button>
                                    </Link>
                                    <Link href={safeRoute('admin.fisioterapeutas.edit', fisioterapeuta.id)} preserveState>
                                        <Button variant="ghost" size="sm">
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                    </Link>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleDelete(fisioterapeuta.id)}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {fisioterapeutas.data.length === 0 && (
                    <div className="py-12 text-center">
                        <p className="text-muted-foreground">Nenhum fisioterapeuta encontrado</p>
                        <Link href={route('admin.fisioterapeutas.create')} className="mt-4 inline-block">
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Cadastrar Primeiro Fisioterapeuta
                            </Button>
                        </Link>
                    </div>
                )}

                {/* Paginação */}
                {fisioterapeutas.links && fisioterapeutas.data.length > 0 && (
                    <div className="flex items-center justify-between">
                        <p className="text-sm text-muted-foreground">
                            Mostrando {fisioterapeutas.meta?.from || 1} a {fisioterapeutas.meta?.to || fisioterapeutas.data.length} de{' '}
                            {fisioterapeutas.meta?.total || fisioterapeutas.data.length} resultados
                        </p>
                        <div className="flex gap-2">
                            {fisioterapeutas.links.map((link: any, index: number) => (
                                <Button
                                    key={index}
                                    variant={link.active ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => link.url && router.get(link.url)}
                                    disabled={!link.url}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
