#!/usr/bin/env node

const { render } = require('@react-email/render');
const fs = require('fs');
const path = require('path');
const React = require('react');

// Função para renderizar um template de email
async function renderEmailTemplate(templateName, props = {}) {
  try {
    // Por enquanto, vamos usar templates simples em JavaScript
    // até configurarmos um bundler adequado
    const template = getTemplateByName(templateName, props);

    if (!template) {
      throw new Error(`Template ${templateName} não encontrado`);
    }

    // Renderizar o template
    const html = await render(template);

    return html;
  } catch (error) {
    console.error(`Erro ao renderizar template ${templateName}:`, error);
    throw error;
  }
}

// Templates simples em JavaScript para teste
function getTemplateByName(templateName, props) {
  const templates = {
    'boas-vindas': createBoasVindasTemplate(props),
    'agendamento-confirmado': createAgendamentoConfirmadoTemplate(props),
    'lembrete-agendamento': createLembreteAgendamentoTemplate(props),
    'agendamento-cancelado': createAgendamentoCanceladoTemplate(props),
  };

  return templates[templateName];
}

// Função principal para ser chamada via linha de comando
async function main() {
  const args = process.argv.slice(2);

  if (args.length < 1) {
    console.error('Uso: node render-email.js <template-name> [props-json]');
    console.error('Exemplo: node render-email.js boas-vindas \'{"nome":"João","email":"<EMAIL>","tipoUsuario":"paciente"}\'');
    process.exit(1);
  }

  const templateName = args[0];
  const propsJson = args[1] || '{}';

  try {
    const props = JSON.parse(propsJson);
    const html = await renderEmailTemplate(templateName, props);

    // Escrever o HTML para stdout
    console.log(html);
  } catch (error) {
    console.error('Erro:', error.message);
    process.exit(1);
  }
}

// Templates simples usando React Email components
function createBoasVindasTemplate(props) {
  const { Html, Head, Preview, Body, Container, Section, Heading, Text, Button, Hr } = require('@react-email/components');

  const {
    nome = 'Usuário',
    email = '<EMAIL>',
    tipoUsuario = 'paciente',
    loginUrl = 'https://f4fisio.com.br/login'
  } = props;

  return React.createElement(Html, {},
    React.createElement(Head),
    React.createElement(Preview, {}, `Bem-vindo(a) à F4 Fisio, ${nome}!`),
    React.createElement(Body, { style: { backgroundColor: '#f6f9fc', fontFamily: 'Arial, sans-serif' } },
      React.createElement(Container, { style: { backgroundColor: '#ffffff', margin: '0 auto', padding: '20px', maxWidth: '600px' } },
        React.createElement(Section, { style: { textAlign: 'center', marginBottom: '30px' } },
          React.createElement(Heading, { style: { color: '#1f2937', fontSize: '24px' } }, 'Bem-vindo(a) à F4 Fisio! 🎉')
        ),
        React.createElement(Text, { style: { color: '#374151', fontSize: '16px', lineHeight: '24px' } },
          `Olá ${nome},`
        ),
        React.createElement(Text, { style: { color: '#374151', fontSize: '16px', lineHeight: '24px' } },
          `É com grande alegria que damos as boas-vindas a você como novo ${tipoUsuario === 'fisioterapeuta' ? 'fisioterapeuta' : tipoUsuario === 'empresa' ? 'estabelecimento' : 'paciente'} da F4 Fisio!`
        ),
        React.createElement(Section, { style: { textAlign: 'center', margin: '30px 0' } },
          React.createElement(Button, {
            href: loginUrl,
            style: { backgroundColor: '#3b82f6', color: '#ffffff', padding: '12px 24px', textDecoration: 'none', borderRadius: '6px' }
          }, 'Acessar Minha Conta')
        ),
        React.createElement(Hr),
        React.createElement(Text, { style: { color: '#374151', fontSize: '16px' } },
          'Atenciosamente,\nEquipe F4 Fisio'
        )
      )
    )
  );
}

function createAgendamentoConfirmadoTemplate(props) {
  const { Html, Head, Preview, Body, Container, Section, Heading, Text, Button } = require('@react-email/components');

  const {
    pacienteNome = 'Paciente',
    fisioterapeutaNome = 'Fisioterapeuta',
    dataHora = '01/01/2025 às 10:00',
    endereco = 'Endereço não informado'
  } = props;

  return React.createElement(Html, {},
    React.createElement(Head),
    React.createElement(Preview, {}, `Agendamento confirmado para ${dataHora}`),
    React.createElement(Body, { style: { backgroundColor: '#f6f9fc', fontFamily: 'Arial, sans-serif' } },
      React.createElement(Container, { style: { backgroundColor: '#ffffff', margin: '0 auto', padding: '20px', maxWidth: '600px' } },
        React.createElement(Section, { style: { backgroundColor: '#10b981', padding: '12px', borderRadius: '6px', textAlign: 'center', marginBottom: '24px' } },
          React.createElement(Text, { style: { color: '#ffffff', fontSize: '14px', fontWeight: 'bold', margin: '0' } },
            '✅ AGENDAMENTO CONFIRMADO'
          )
        ),
        React.createElement(Heading, { style: { color: '#1f2937', fontSize: '24px', textAlign: 'center' } },
          'Seu agendamento foi confirmado!'
        ),
        React.createElement(Text, { style: { color: '#374151', fontSize: '16px' } },
          `Olá ${pacienteNome},`
        ),
        React.createElement(Text, { style: { color: '#374151', fontSize: '16px' } },
          'Temos o prazer de confirmar seu agendamento de fisioterapia.'
        ),
        React.createElement(Section, { style: { backgroundColor: '#f9fafb', padding: '20px', borderRadius: '8px', margin: '20px 0' } },
          React.createElement(Text, { style: { fontWeight: 'bold', marginBottom: '16px' } }, 'Detalhes do Agendamento'),
          React.createElement(Text, {}, `📅 Data e Horário: ${dataHora}`),
          React.createElement(Text, {}, `👨‍⚕️ Fisioterapeuta: ${fisioterapeutaNome}`),
          React.createElement(Text, {}, `📍 Local: ${endereco}`)
        )
      )
    )
  );
}

function createLembreteAgendamentoTemplate(props) {
  const { Html, Head, Preview, Body, Container, Section, Heading, Text } = require('@react-email/components');

  const {
    pacienteNome = 'Paciente',
    dataHora = '01/01/2025 às 10:00'
  } = props;

  return React.createElement(Html, {},
    React.createElement(Head),
    React.createElement(Preview, {}, `Lembrete: Consulta ${dataHora}`),
    React.createElement(Body, { style: { backgroundColor: '#f6f9fc', fontFamily: 'Arial, sans-serif' } },
      React.createElement(Container, { style: { backgroundColor: '#ffffff', margin: '0 auto', padding: '20px', maxWidth: '600px' } },
        React.createElement(Heading, { style: { color: '#1f2937', fontSize: '24px', textAlign: 'center' } },
          '⏰ Lembrete de Consulta'
        ),
        React.createElement(Text, { style: { color: '#374151', fontSize: '16px' } },
          `Olá ${pacienteNome}, sua consulta é ${dataHora}!`
        )
      )
    )
  );
}

function createAgendamentoCanceladoTemplate(props) {
  const { Html, Head, Preview, Body, Container, Section, Heading, Text } = require('@react-email/components');

  const {
    pacienteNome = 'Paciente',
    dataHora = '01/01/2025 às 10:00'
  } = props;

  return React.createElement(Html, {},
    React.createElement(Head),
    React.createElement(Preview, {}, `Agendamento cancelado - ${dataHora}`),
    React.createElement(Body, { style: { backgroundColor: '#f6f9fc', fontFamily: 'Arial, sans-serif' } },
      React.createElement(Container, { style: { backgroundColor: '#ffffff', margin: '0 auto', padding: '20px', maxWidth: '600px' } },
        React.createElement(Heading, { style: { color: '#1f2937', fontSize: '24px', textAlign: 'center' } },
          '❌ Agendamento Cancelado'
        ),
        React.createElement(Text, { style: { color: '#374151', fontSize: '16px' } },
          `Olá ${pacienteNome}, seu agendamento para ${dataHora} foi cancelado.`
        )
      )
    )
  );
}

// Exportar funções para uso em outros módulos
module.exports = {
  renderEmailTemplate,
};

// Executar se chamado diretamente
if (require.main === module) {
  main();
}
