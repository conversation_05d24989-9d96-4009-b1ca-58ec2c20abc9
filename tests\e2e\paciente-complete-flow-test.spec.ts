import { test, expect, type Page } from '@playwright/test';

// Configuração de teste
const BASE_URL = 'http://localhost:8000';

// Credenciais dos pacientes de teste (do seeder)
const PACIENTES_TESTE = [
    { email: '<EMAIL>', password: 'paciente123', name: '<PERSON>' },
    { email: '<EMAIL>', password: 'paciente123', name: '<PERSON>' },
    { email: '<EMAIL>', password: 'paciente123', name: '<PERSON>' },
    { email: '<EMAIL>', password: 'paciente123', name: '<PERSON>' }
];

test.describe('Fluxo Completo do Paciente - Teste Abrangente', () => {
    test.beforeEach(async ({ page }) => {
        // Configurar timeouts mais longos
        test.setTimeout(120000);
        page.setDefaultTimeout(30000);
        
        // Navegar para a página inicial
        await page.goto(BASE_URL);
        await page.waitForLoadState('networkidle');
    });

    test('1. Navegação Pública - Páginas Acessíveis Sem Login', async ({ page }) => {
        console.log('🔍 Testando navegação pública...');

        // Página inicial
        await page.goto('/');
        await expect(page).toHaveTitle(/F4 Fisio/);
        console.log('✅ Página inicial carregada');

        // Página sobre
        await page.goto('/sobre');
        await page.waitForLoadState('networkidle');
        await expect(page.locator('h1, h2, .title')).toBeVisible();
        console.log('✅ Página sobre acessível');

        // Página de busca
        await page.goto('/buscar');
        await page.waitForLoadState('networkidle');
        await expect(page.locator('input[type="search"], input[placeholder*="buscar"], input[placeholder*="Buscar"]')).toBeVisible();
        console.log('✅ Página de busca acessível');

        // Página de login
        await page.goto('/login');
        await page.waitForLoadState('networkidle');
        await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
        await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
        console.log('✅ Página de login acessível');

        // Página de registro
        await page.goto('/register');
        await page.waitForLoadState('networkidle');
        await expect(page.locator('input[name="name"]')).toBeVisible();
        await expect(page.locator('input[name="email"]')).toBeVisible();
        console.log('✅ Página de registro acessível');
    });

    test('2. Fluxo de Login e Autenticação', async ({ page }) => {
        console.log('🔐 Testando fluxo de login...');

        const paciente = PACIENTES_TESTE[0];

        // Ir para login
        await page.goto('/login');
        await page.waitForLoadState('networkidle');

        // Preencher credenciais
        await page.fill('input[type="email"], input[name="email"]', paciente.email);
        await page.fill('input[type="password"], input[name="password"]', paciente.password);

        // Fazer login
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Verificar redirecionamento
        const currentUrl = page.url();
        console.log('URL após login:', currentUrl);

        // Deve estar em uma página do paciente
        expect(currentUrl).toMatch(/\/paciente\//);
        console.log('✅ Login realizado com sucesso');

        // Verificar se a sidebar do paciente está presente
        await expect(page.locator('[data-sidebar="sidebar"]')).toBeVisible();
        console.log('✅ Sidebar do paciente carregada');
    });

    test('3. Navegação da Sidebar - Todas as Páginas do Paciente', async ({ page }) => {
        console.log('🧭 Testando navegação da sidebar...');

        const paciente = PACIENTES_TESTE[1];

        // Fazer login
        await page.goto('/login');
        await page.fill('input[type="email"], input[name="email"]', paciente.email);
        await page.fill('input[type="password"], input[name="password"]', paciente.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Lista de páginas para testar
        const paginasPaciente = [
            { nome: 'Dashboard', href: '/paciente/dashboard', texto: 'Início' },
            { nome: 'Perfil', href: '/paciente/perfil', texto: 'Perfil' },
            { nome: 'Agendamentos', href: '/paciente/agendamentos', texto: 'Agendamentos' },
            { nome: 'Histórico', href: '/paciente/historico', texto: 'Histórico' },
            { nome: 'Pagamentos', href: '/paciente/pagamentos', texto: 'Pagamentos' },
            { nome: 'Planos', href: '/paciente/planos', texto: 'Planos' },
            { nome: 'Avaliações', href: '/paciente/avaliacoes', texto: 'Avaliações' }
        ];

        for (const pagina of paginasPaciente) {
            console.log(`📄 Testando página: ${pagina.nome}`);

            // Navegar pela sidebar
            const linkSidebar = page.locator(`a[href="${pagina.href}"]`).first();
            if (await linkSidebar.isVisible()) {
                await linkSidebar.click();
                await page.waitForLoadState('networkidle');

                // Verificar se chegou na página correta
                expect(page.url()).toContain(pagina.href);
                console.log(`✅ ${pagina.nome} acessível via sidebar`);

                // Verificar se não há erros JavaScript
                const errors = await page.evaluate(() => {
                    return window.console.error || [];
                });
                
                // Aguardar um pouco para a página carregar completamente
                await page.waitForTimeout(1000);
            } else {
                console.log(`⚠️ Link da sidebar para ${pagina.nome} não encontrado`);
            }
        }
    });

    test('4. Teste do Formulário de Perfil', async ({ page }) => {
        console.log('👤 Testando formulário de perfil...');

        const paciente = PACIENTES_TESTE[2];

        // Fazer login
        await page.goto('/login');
        await page.fill('input[type="email"], input[name="email"]', paciente.email);
        await page.fill('input[type="password"], input[name="password"]', paciente.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Ir para perfil
        await page.goto('/paciente/perfil');
        await page.waitForLoadState('networkidle');

        // Verificar se o formulário está presente
        await expect(page.locator('input[name="name"]')).toBeVisible();
        await expect(page.locator('input[name="phone"]')).toBeVisible();
        console.log('✅ Formulário de perfil carregado');

        // Testar edição de dados
        const novoTelefone = '(11) 99999-9999';
        await page.fill('input[name="phone"]', novoTelefone);

        // Procurar botão de salvar
        const botaoSalvar = page.locator('button[type="submit"], button:has-text("Salvar"), button:has-text("Atualizar")').first();
        if (await botaoSalvar.isVisible()) {
            await botaoSalvar.click();
            await page.waitForLoadState('networkidle');
            console.log('✅ Dados do perfil atualizados');
        } else {
            console.log('⚠️ Botão de salvar não encontrado');
        }
    });

    test('5. Teste de Busca de Serviços', async ({ page }) => {
        console.log('🔍 Testando funcionalidade de busca...');

        // Ir para página de busca
        await page.goto('/buscar');
        await page.waitForLoadState('networkidle');

        // Procurar campo de busca
        const campoBusca = page.locator('input[type="search"], input[placeholder*="buscar"], input[placeholder*="Buscar"]').first();
        
        if (await campoBusca.isVisible()) {
            // Testar busca
            await campoBusca.fill('fisioterapia');
            await page.keyboard.press('Enter');
            await page.waitForLoadState('networkidle');
            console.log('✅ Busca realizada');

            // Verificar se há resultados ou mensagem
            const temResultados = await page.locator('.resultado, .card, .item').count() > 0;
            const temMensagem = await page.locator('text=/nenhum resultado/i, text=/não encontrado/i').isVisible();
            
            if (temResultados || temMensagem) {
                console.log('✅ Página de resultados funcionando');
            } else {
                console.log('⚠️ Resultados de busca não claros');
            }
        } else {
            console.log('⚠️ Campo de busca não encontrado');
        }
    });

    test('6. Teste de Agendamento (Fluxo Básico)', async ({ page }) => {
        console.log('📅 Testando fluxo de agendamento...');

        const paciente = PACIENTES_TESTE[3];

        // Fazer login
        await page.goto('/login');
        await page.fill('input[type="email"], input[name="email"]', paciente.email);
        await page.fill('input[type="password"], input[name="password"]', paciente.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Ir para agendamentos
        await page.goto('/paciente/agendamentos');
        await page.waitForLoadState('networkidle');

        // Procurar botão de novo agendamento
        const botaoNovo = page.locator('button:has-text("Novo"), button:has-text("Agendar"), a:has-text("Novo"), a:has-text("Agendar")').first();
        
        if (await botaoNovo.isVisible()) {
            await botaoNovo.click();
            await page.waitForLoadState('networkidle');

            // Verificar se chegou na página de criação
            const currentUrl = page.url();
            if (currentUrl.includes('create') || currentUrl.includes('novo')) {
                console.log('✅ Página de novo agendamento acessível');

                // Verificar elementos do formulário de agendamento
                const temFormulario = await page.locator('form, select, input[type="date"]').count() > 0;
                if (temFormulario) {
                    console.log('✅ Formulário de agendamento presente');
                } else {
                    console.log('⚠️ Formulário de agendamento não encontrado');
                }
            } else {
                console.log('⚠️ Não redirecionou para página de criação');
            }
        } else {
            console.log('⚠️ Botão de novo agendamento não encontrado');
        }
    });

    test('7. Teste de Responsividade Mobile', async ({ page }) => {
        console.log('📱 Testando responsividade mobile...');

        // Configurar viewport mobile
        await page.setViewportSize({ width: 375, height: 667 });

        const paciente = PACIENTES_TESTE[0];

        // Fazer login
        await page.goto('/login');
        await page.fill('input[type="email"], input[name="email"]', paciente.email);
        await page.fill('input[type="password"], input[name="password"]', paciente.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Verificar se a sidebar mobile funciona
        const menuMobile = page.locator('button[data-sidebar="trigger"], button:has([data-sidebar="trigger"]), [data-sidebar="trigger"]').first();
        
        if (await menuMobile.isVisible()) {
            await menuMobile.click();
            await page.waitForTimeout(500);
            console.log('✅ Menu mobile funciona');
        } else {
            console.log('⚠️ Menu mobile não encontrado');
        }

        // Testar algumas páginas em mobile
        const paginasMobile = ['/paciente/dashboard', '/paciente/perfil', '/paciente/agendamentos'];
        
        for (const pagina of paginasMobile) {
            await page.goto(pagina);
            await page.waitForLoadState('networkidle');
            
            // Verificar se não há overflow horizontal
            const hasHorizontalScroll = await page.evaluate(() => {
                return document.documentElement.scrollWidth > document.documentElement.clientWidth;
            });
            
            if (!hasHorizontalScroll) {
                console.log(`✅ ${pagina} responsiva`);
            } else {
                console.log(`⚠️ ${pagina} tem overflow horizontal`);
            }
        }
    });

    test('8. Teste de Programa de Afiliados', async ({ page }) => {
        console.log('🤝 Testando programa de afiliados...');

        const paciente = PACIENTES_TESTE[1];

        // Fazer login
        await page.goto('/login');
        await page.fill('input[type="email"], input[name="email"]', paciente.email);
        await page.fill('input[type="password"], input[name="password"]', paciente.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Ir para página de afiliados
        await page.goto('/paciente/afiliados');
        await page.waitForLoadState('networkidle');

        // Verificar se a página carrega
        const currentUrl = page.url();
        expect(currentUrl).toContain('/paciente/afiliados');
        console.log('✅ Página de afiliados acessível');

        // Verificar se há conteúdo relacionado a afiliados
        const temConteudoAfiliado = await page.locator('text=/afiliado/i, text=/indicação/i, text=/comissão/i').count() > 0;
        if (temConteudoAfiliado) {
            console.log('✅ Conteúdo de afiliados presente');
        } else {
            console.log('⚠️ Conteúdo de afiliados não encontrado');
        }
    });

    test('9. Teste de Logout e Segurança', async ({ page }) => {
        console.log('🔒 Testando logout e segurança...');

        const paciente = PACIENTES_TESTE[2];

        // Fazer login
        await page.goto('/login');
        await page.fill('input[type="email"], input[name="email"]', paciente.email);
        await page.fill('input[type="password"], input[name="password"]', paciente.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Procurar botão de logout
        const botaoLogout = page.locator('button:has-text("Sair"), button:has-text("Logout"), a:has-text("Sair"), a:has-text("Logout")').first();
        
        if (await botaoLogout.isVisible()) {
            await botaoLogout.click();
            await page.waitForLoadState('networkidle');

            // Verificar se foi redirecionado para página pública
            const currentUrl = page.url();
            const isPublicPage = currentUrl.includes('/login') || currentUrl === BASE_URL + '/' || !currentUrl.includes('/paciente/');
            
            if (isPublicPage) {
                console.log('✅ Logout realizado com sucesso');

                // Tentar acessar página protegida após logout
                await page.goto('/paciente/dashboard');
                await page.waitForLoadState('networkidle');

                const urlAposLogout = page.url();
                if (urlAposLogout.includes('/login')) {
                    console.log('✅ Redirecionamento de segurança funcionando');
                } else {
                    console.log('⚠️ Página protegida acessível após logout');
                }
            } else {
                console.log('⚠️ Logout não funcionou corretamente');
            }
        } else {
            console.log('⚠️ Botão de logout não encontrado');
        }
    });

    test('10. Relatório Final de Acessibilidade', async ({ page }) => {
        console.log('📊 Gerando relatório final...');

        const paciente = PACIENTES_TESTE[0];

        // Fazer login
        await page.goto('/login');
        await page.fill('input[type="email"], input[name="email"]', paciente.email);
        await page.fill('input[type="password"], input[name="password"]', paciente.password);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Verificar erros JavaScript em todas as páginas
        const paginasParaVerificar = [
            '/paciente/dashboard',
            '/paciente/perfil',
            '/paciente/agendamentos',
            '/paciente/historico',
            '/paciente/pagamentos',
            '/paciente/planos',
            '/paciente/avaliacoes'
        ];

        let paginasComErro = [];
        let paginasSemErro = [];

        for (const pagina of paginasParaVerificar) {
            try {
                await page.goto(pagina);
                await page.waitForLoadState('networkidle');
                await page.waitForTimeout(2000);

                // Verificar erros JavaScript
                const errors = await page.evaluate(() => {
                    // @ts-ignore
                    return window.jsErrors || [];
                });

                if (errors.length === 0) {
                    paginasSemErro.push(pagina);
                } else {
                    paginasComErro.push({ pagina, errors });
                }
            } catch (error) {
                paginasComErro.push({ pagina, errors: [error.message] });
            }
        }

        console.log('\n📊 RELATÓRIO FINAL:');
        console.log(`✅ Páginas sem erros: ${paginasSemErro.length}`);
        console.log(`❌ Páginas com erros: ${paginasComErro.length}`);
        
        if (paginasSemErro.length > 0) {
            console.log('\n✅ Páginas funcionando corretamente:');
            paginasSemErro.forEach(pagina => console.log(`   - ${pagina}`));
        }

        if (paginasComErro.length > 0) {
            console.log('\n❌ Páginas com problemas:');
            paginasComErro.forEach(({ pagina, errors }) => {
                console.log(`   - ${pagina}: ${errors.join(', ')}`);
            });
        }
    });
});
