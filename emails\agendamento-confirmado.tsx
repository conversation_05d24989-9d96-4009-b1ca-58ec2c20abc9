import {
  Button,
  Heading,
  Hr,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';
import { EmailLayout } from './components/layout';

interface AgendamentoConfirmadoEmailProps {
  pacienteNome: string;
  fisioterapeutaNome: string;
  dataHora: string;
  endereco: string;
  valor?: string;
  observacoes?: string;
  agendamentoUrl?: string;
  whatsappFisioterapeuta?: string;
}

export const AgendamentoConfirmadoEmail = ({
  pacienteNome = 'Paciente',
  fisioterapeutaNome = 'Fisioterapeuta',
  dataHora = '01/01/2025 às 10:00',
  endereco = 'Endereço não informado',
  valor,
  observacoes,
  agendamentoUrl = 'https://f4fisio.com.br/agendamentos',
  whatsappFisioterapeuta,
}: AgendamentoConfirmadoEmailProps) => {
  return (
    <EmailLayout preview={`Agendamento confirmado para ${dataHora}`}>
      <Section style={statusBanner}>
        <Text style={statusText}>
          ✅ AGENDAMENTO CONFIRMADO
        </Text>
      </Section>
      
      <Heading style={h1}>
        Seu agendamento foi confirmado!
      </Heading>
      
      <Text style={text}>
        Olá <strong>{pacienteNome}</strong>,
      </Text>
      
      <Text style={text}>
        Temos o prazer de confirmar seu agendamento de fisioterapia. Todos os detalhes estão listados abaixo:
      </Text>
      
      <Section style={detailsCard}>
        <Heading style={cardTitle}>Detalhes do Agendamento</Heading>
        
        <div style={detailRow}>
          <Text style={detailLabel}>📅 Data e Horário:</Text>
          <Text style={detailValue}>{dataHora}</Text>
        </div>
        
        <div style={detailRow}>
          <Text style={detailLabel}>👨‍⚕️ Fisioterapeuta:</Text>
          <Text style={detailValue}>{fisioterapeutaNome}</Text>
        </div>
        
        <div style={detailRow}>
          <Text style={detailLabel}>📍 Local:</Text>
          <Text style={detailValue}>{endereco}</Text>
        </div>
        
        {valor && (
          <div style={detailRow}>
            <Text style={detailLabel}>💰 Valor:</Text>
            <Text style={detailValue}>{valor}</Text>
          </div>
        )}
      </Section>
      
      {observacoes && (
        <>
          <Heading style={h2}>Observações</Heading>
          <Section style={observacoesCard}>
            <Text style={text}>{observacoes}</Text>
          </Section>
        </>
      )}
      
      <Section style={buttonContainer}>
        <Button style={button} href={agendamentoUrl}>
          Ver Detalhes do Agendamento
        </Button>
      </Section>
      
      {whatsappFisioterapeuta && (
        <Section style={whatsappSection}>
          <Text style={text}>
            <strong>Contato direto com o fisioterapeuta:</strong>
          </Text>
          <Button style={whatsappButton} href={`https://wa.me/${whatsappFisioterapeuta}`}>
            💬 Conversar no WhatsApp
          </Button>
        </Section>
      )}
      
      <Hr style={hr} />
      
      <Heading style={h2}>Preparação para a Consulta</Heading>
      
      <Text style={listItem}>
        • Chegue com 10 minutos de antecedência
      </Text>
      <Text style={listItem}>
        • Traga documentos de identificação
      </Text>
      <Text style={listItem}>
        • Use roupas confortáveis para os exercícios
      </Text>
      <Text style={listItem}>
        • Traga exames médicos relevantes (se houver)
      </Text>
      
      <Hr style={hr} />
      
      <Text style={text}>
        Em caso de necessidade de reagendamento ou cancelamento, entre em contato com antecedência mínima de 24 horas.
      </Text>
      
      <Text style={signature}>
        Atenciosamente,<br />
        Equipe F4 Fisio
      </Text>
    </EmailLayout>
  );
};

export default AgendamentoConfirmadoEmail;

const statusBanner = {
  backgroundColor: '#10b981',
  padding: '12px',
  borderRadius: '6px',
  textAlign: 'center' as const,
  margin: '0 0 24px 0',
};

const statusText = {
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0',
};

const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 0 20px 0',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '24px 0 16px 0',
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px 0',
};

const listItem = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 8px 0',
};

const detailsCard = {
  backgroundColor: '#f9fafb',
  border: '1px solid #e5e7eb',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
};

const cardTitle = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
};

const detailRow = {
  margin: '0 0 12px 0',
};

const detailLabel = {
  color: '#6b7280',
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0 0 4px 0',
};

const detailValue = {
  color: '#1f2937',
  fontSize: '16px',
  margin: '0',
};

const observacoesCard = {
  backgroundColor: '#fef3c7',
  border: '1px solid #f59e0b',
  borderRadius: '6px',
  padding: '16px',
  margin: '16px 0',
};

const signature = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '24px 0 0 0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '24px 0',
};

const button = {
  backgroundColor: '#3b82f6',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const whatsappSection = {
  textAlign: 'center' as const,
  margin: '20px 0',
};

const whatsappButton = {
  backgroundColor: '#25d366',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  margin: '8px 0',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '24px 0',
};
