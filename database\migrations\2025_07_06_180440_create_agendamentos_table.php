<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agendamentos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('paciente_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('fisioterapeuta_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('assinatura_id')->constrained()->onDelete('cascade');
            $table->datetime('scheduled_at'); // Data e hora agendada
            $table->integer('duration')->default(60); // Duração em minutos
            $table->enum('status', ['agendado', 'confirmado', 'em_andamento', 'concluido', 'cancelado'])->default('agendado');
            $table->text('service_type'); // Tipo de serviço
            $table->text('notes')->nullable(); // Observações
            $table->text('address'); // Endereço do atendimento
            $table->decimal('price', 8, 2)->nullable(); // Preço da sessão
            $table->datetime('started_at')->nullable(); // Início real da sessão
            $table->datetime('finished_at')->nullable(); // Fim real da sessão
            $table->text('cancellation_reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agendamentos');
    }
};
