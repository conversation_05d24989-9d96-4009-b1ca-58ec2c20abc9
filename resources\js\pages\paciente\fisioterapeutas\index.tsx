import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { Calendar, Clock, Filter, MapPin, Search, Star } from 'lucide-react';

interface Fisioterapeuta {
    id: number;
    user: {
        name: string;
        email: string;
        avatar?: string;
    };
    crefito: string;
    specializations: string[];
    bio: string;
    hourly_rate: number;
    available_areas: string[];
    rating: number;
    total_reviews: number;
    available: boolean;
    next_available_slot?: string;
}

interface Props {
    fisioterapeutas: {
        data: Fisioterapeuta[];
        links: any[];
        meta: any;
    };
    filters: {
        search?: string;
        specialization?: string;
        area?: string;
        rating?: string;
        available_only?: boolean;
    };
    specializations: string[];
    areas: string[];
}

export default function PacienteFisioterapeutas({ fisioterapeutas, filters, specializations, areas }: Props) {
    const { data, setData, get } = useForm({
        search: filters.search || '',
        specialization: filters.specialization || undefined,
        area: filters.area || undefined,
        rating: filters.rating || undefined,
        available_only: filters.available_only || false,
    });

    const handleFilter = () => {
        get(route('paciente.fisioterapeutas.index'), {
            preserveState: true,
            replace: true,
        });
    };

    const clearFilters = () => {
        setData({
            search: '',
            specialization: undefined,
            area: undefined,
            rating: undefined,
            available_only: false,
        });
        get(route('paciente.fisioterapeutas.index'));
    };

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, i) => (
            <Star key={i} className={`h-4 w-4 ${i < Math.floor(rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} />
        ));
    };

    return (
        <AppLayout>
            <Head title="Buscar Fisioterapeutas" />

            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">Buscar Fisioterapeutas</h1>
                        <p className="text-muted-foreground">Encontre o profissional ideal para seu tratamento</p>
                    </div>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros de Busca
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Buscar por nome</label>
                                <Input
                                    placeholder="Nome do fisioterapeuta..."
                                    value={data.search}
                                    onChange={(e) => setData('search', e.target.value)}
                                />
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">Especialização</label>
                                <Select
                                    value={data.specialization || 'all'}
                                    onValueChange={(value) => setData('specialization', value === 'all' ? undefined : value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Todas as especializações" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Todas as especializações</SelectItem>
                                        {specializations.map((spec) => (
                                            <SelectItem key={spec} value={spec}>
                                                {spec}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">Área de atendimento</label>
                                <Select value={data.area || 'all'} onValueChange={(value) => setData('area', value === 'all' ? undefined : value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Todas as áreas" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Todas as áreas</SelectItem>
                                        {areas.map((area) => (
                                            <SelectItem key={area} value={area}>
                                                {area}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">Avaliação mínima</label>
                                <Select
                                    value={data.rating || 'all'}
                                    onValueChange={(value) => setData('rating', value === 'all' ? undefined : value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Qualquer avaliação" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Qualquer avaliação</SelectItem>
                                        <SelectItem value="4">4+ estrelas</SelectItem>
                                        <SelectItem value="3">3+ estrelas</SelectItem>
                                        <SelectItem value="2">2+ estrelas</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <div className="mt-4 flex items-center gap-4">
                            <label className="flex items-center gap-2">
                                <input
                                    type="checkbox"
                                    checked={data.available_only}
                                    onChange={(e) => setData('available_only', e.target.checked)}
                                    className="rounded border-gray-300"
                                />
                                <span className="text-sm">Apenas disponíveis agora</span>
                            </label>
                        </div>

                        <div className="mt-4 flex gap-2">
                            <Button onClick={handleFilter}>
                                <Search className="mr-2 h-4 w-4" />
                                Buscar
                            </Button>
                            <Button variant="outline" onClick={clearFilters}>
                                Limpar Filtros
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Lista de Fisioterapeutas */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {fisioterapeutas.data.map((fisio) => (
                        <Card key={fisio.id} className="transition-shadow hover:shadow-lg">
                            <CardContent className="p-6">
                                <div className="flex items-start gap-4">
                                    <Avatar className="h-16 w-16">
                                        <AvatarImage src={fisio.user.avatar} />
                                        <AvatarFallback>
                                            {fisio.user.name
                                                .split(' ')
                                                .map((n) => n[0])
                                                .join('')
                                                .toUpperCase()}
                                        </AvatarFallback>
                                    </Avatar>

                                    <div className="min-w-0 flex-1">
                                        <h3 className="truncate text-lg font-semibold">{fisio.user.name}</h3>
                                        <p className="text-sm text-muted-foreground">CREFITO: {fisio.crefito}</p>

                                        <div className="mt-1 flex items-center gap-1">
                                            {renderStars(fisio.rating)}
                                            <span className="ml-1 text-sm text-muted-foreground">({fisio.total_reviews} avaliações)</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-4 space-y-3">
                                    {/* Especializações */}
                                    <div>
                                        <p className="mb-2 text-sm font-medium">Especializações:</p>
                                        <div className="flex flex-wrap gap-1">
                                            {fisio.specializations.slice(0, 3).map((spec) => (
                                                <Badge key={spec} variant="secondary" className="text-xs">
                                                    {spec}
                                                </Badge>
                                            ))}
                                            {fisio.specializations.length > 3 && (
                                                <Badge variant="outline" className="text-xs">
                                                    +{fisio.specializations.length - 3}
                                                </Badge>
                                            )}
                                        </div>
                                    </div>

                                    {/* Áreas de atendimento */}
                                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                        <MapPin className="h-4 w-4" />
                                        <span>{fisio.available_areas.slice(0, 2).join(', ')}</span>
                                        {fisio.available_areas.length > 2 && <span>+{fisio.available_areas.length - 2}</span>}
                                    </div>

                                    {/* Valor e disponibilidade */}
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2 text-sm">
                                            <Clock className="h-4 w-4 text-muted-foreground" />
                                            <span className="font-medium">R$ {fisio.hourly_rate ? fisio.hourly_rate.toFixed(2) : '0,00'}/hora</span>
                                        </div>

                                        {fisio.available && (
                                            <Badge variant="default" className="bg-green-100 text-green-800">
                                                Disponível
                                            </Badge>
                                        )}
                                    </div>

                                    {fisio.next_available_slot && (
                                        <div className="text-xs text-muted-foreground">Próximo horário: {fisio.next_available_slot}</div>
                                    )}
                                </div>

                                <div className="mt-4 flex gap-2">
                                    <Link href={route('paciente.fisioterapeutas.show', fisio.id)} className="flex-1">
                                        <Button variant="outline" className="w-full">
                                            Ver Perfil
                                        </Button>
                                    </Link>
                                    <Link href={route('paciente.agendamentos.create', { fisioterapeuta: fisio.id })} className="flex-1">
                                        <Button className="w-full">
                                            <Calendar className="mr-2 h-4 w-4" />
                                            Agendar
                                        </Button>
                                    </Link>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {fisioterapeutas.data.length === 0 && (
                    <div className="py-12 text-center">
                        <Search className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="mt-2 text-muted-foreground">Nenhum fisioterapeuta encontrado</p>
                        <p className="text-sm text-muted-foreground">Tente ajustar os filtros de busca</p>
                    </div>
                )}

                {/* Paginação */}
                {fisioterapeutas.links && fisioterapeutas.data.length > 0 && (
                    <div className="flex items-center justify-center gap-2">
                        {fisioterapeutas.links.map((link: any, index: number) => (
                            <Button
                                key={index}
                                variant={link.active ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => link.url && get(link.url)}
                                disabled={!link.url}
                                dangerouslySetInnerHTML={{ __html: link.label }}
                            />
                        ))}
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
