<?php

namespace App\Http\Controllers\Empresa;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;

class PerfilController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $estabelecimento = $user->estabelecimento;

        if (!$estabelecimento) {
            return redirect()->route('empresa.setup');
        }

        return Inertia::render('empresa/perfil', [
            'estabelecimento' => $estabelecimento,
        ]);
    }

    public function update(Request $request)
    {
        $user = auth()->user();
        $estabelecimento = $user->estabelecimento;

        if (!$estabelecimento) {
            return redirect()->route('empresa.setup');
        }

        $validated = $request->validate([
            'nome' => 'required|string|max:255',
            'categoria' => 'required|in:dentista,farmacia,fisioterapia,outros',
            'descricao' => 'nullable|string|max:1000',
            'telefone' => 'nullable|string|max:20',
            'whatsapp' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'endereco' => 'required|string|max:255',
            'cidade' => 'required|string|max:100',
            'estado' => 'required|string|size:2',
            'cep' => 'required|string|size:9',
            'horario_funcionamento' => 'nullable|array',
            'servicos_oferecidos' => 'nullable|string|max:1000',
            'site' => 'nullable|url|max:255',
            'instagram' => 'nullable|string|max:255',
            'facebook' => 'nullable|string|max:255',
        ]);

        // Obter coordenadas se endereço mudou
        $enderecoMudou = $estabelecimento->endereco !== $validated['endereco'] || 
                        $estabelecimento->cidade !== $validated['cidade'] ||
                        $estabelecimento->cep !== $validated['cep'];

        if ($enderecoMudou) {
            $coordenadas = $this->obterCoordenadas($validated['endereco'], $validated['cidade'], $validated['cep']);
            if ($coordenadas) {
                $validated['latitude'] = $coordenadas['lat'];
                $validated['longitude'] = $coordenadas['lng'];
            }
        }

        // Gerar novo slug se nome mudou
        if ($estabelecimento->nome !== $validated['nome']) {
            $validated['slug'] = $estabelecimento->generateSlug();
        }

        $estabelecimento->update($validated);

        return back()->with('success', 'Perfil atualizado com sucesso!');
    }

    private function obterCoordenadas($endereco, $cidade, $cep)
    {
        try {
            $enderecoCompleto = "{$endereco}, {$cidade}, {$cep}";
            $response = Http::get('https://maps.googleapis.com/maps/api/geocode/json', [
                'address' => $enderecoCompleto,
                'key' => config('services.google.maps_api_key')
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (!empty($data['results'])) {
                    $location = $data['results'][0]['geometry']['location'];
                    return [
                        'lat' => $location['lat'],
                        'lng' => $location['lng']
                    ];
                }
            }
        } catch (\Exception $e) {
            \Log::error('Erro ao obter coordenadas: ' . $e->getMessage());
        }

        return null;
    }
}
