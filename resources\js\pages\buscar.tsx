import BuscaEstabelecimentos from '@/components/busca-estabelecimentos';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import PublicLayout from '@/layouts/public-layout';
import { MapPin, MessageCircle, MessageSquare, Search } from 'lucide-react';

export default function Buscar() {
    return (
        <PublicLayout
            title="Buscar Serviços de Saúde - F4 Fisio"
            description="Encontre dentistas, farmácias e clínicas de fisioterapia próximas a você. Busca por localização com contato direto via WhatsApp."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-36">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-5xl font-medium text-balance md:text-6xl">
                                Encontre Serviços de
                                <span className="block text-primary">Saúde Próximos</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Localize dentistas, farmácias e clínicas de fisioterapia na sua região. Contato direto via WhatsApp e informações
                                completas dos estabelecimentos.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Busca Section */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <BuscaEstabelecimentos />
                </div>
            </section>

            {/* Como Funciona Section */}
            <section className="bg-muted/30 py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Como Funciona a Busca</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Processo simples e rápido para encontrar os melhores serviços de saúde
                        </p>
                    </div>

                    <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="mb-4 h-12 w-12">
                                <MapPin className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Informe sua Localização</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Digite seu CEP, cidade ou use sua localização atual para encontrar estabelecimentos próximos.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="mb-4 h-12 w-12">
                                <Search className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Escolha a Categoria</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Selecione o tipo de serviço: dentistas, farmácias, fisioterapia ou todos os serviços.
                            </p>
                        </div>

                        <div className="flex flex-col items-center text-center">
                            <Badge variant="gradient" size="icon-lg" className="mb-4 h-12 w-12">
                                <MessageSquare className="h-6 w-6" />
                            </Badge>
                            <h3 className="mt-4 text-base font-semibold text-foreground">Entre em Contato</h3>
                            <p className="mt-2 text-sm leading-relaxed text-muted-foreground">
                                Clique no botão WhatsApp para conversar diretamente com o estabelecimento e agendar.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Para Empresas Section */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mx-auto max-w-4xl rounded-2xl border bg-card p-6 shadow-sm sm:p-8">
                        <div className="text-center">
                            <h3 className="text-2xl font-medium text-card-foreground">Para empresas da área da saúde</h3>
                            <p className="mt-4 text-xl text-balance text-muted-foreground">
                                Quer aumentar o número de clientes da sua empresa?
                                <br />
                                Assine nosso plano mensal de <span className="font-semibold text-primary">R$ 14,90/mês</span> e coloque sua empresa no
                                mapa da saúde.
                            </p>

                            <div className="mt-8 grid gap-4 text-left sm:grid-cols-2">
                                <div className="flex items-center gap-3">
                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                                        <svg className="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <span className="text-sm">Apareça nos resultados de busca</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                                        <svg className="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <span className="text-sm">Contato direto via WhatsApp</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                                        <svg className="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <span className="text-sm">Perfil completo com avaliações</span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                                        <svg className="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <span className="text-sm">Sem taxa de setup</span>
                                </div>
                            </div>

                            <div className="mt-8 flex flex-col gap-3 sm:flex-row sm:justify-center">
                                <Button asChild>
                                    <a
                                        href="https://wa.me/5511978196207?text=Olá! Gostaria de cadastrar minha empresa no F4 Fisio"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                    >
                                        <MessageCircle className="h-4 w-4" />
                                        Falar no WhatsApp
                                    </a>
                                </Button>
                                <Button variant="outline" asChild>
                                    <a href="/empresa/cadastro">
                                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                            />
                                        </svg>
                                        Cadastrar Empresa
                                    </a>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
