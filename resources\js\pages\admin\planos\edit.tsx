import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Plus, X } from 'lucide-react';
import { useState } from 'react';

interface Plano {
    id: number;
    name: string;
    description: string;
    price: number;
    sessions_per_month: number;
    session_duration: number;
    included_services: string[];
    benefits: string[];
    active: boolean;
    created_at: string;
    assinaturas_count: number;
}

interface Props {
    plano: Plano;
}

export default function EditPlano({ plano }: Props) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/admin/dashboard',
        },
        {
            title: 'Planos',
            href: '/admin/planos',
        },
        {
            title: plano.name,
            href: `/admin/planos/${plano.id}/edit`,
        },
    ];

    const { data, setData, put, processing, errors } = useForm({
        name: plano.name,
        description: plano.description,
        price: plano.price.toString(),
        sessions_per_month: plano.sessions_per_month.toString(),
        session_duration: plano.session_duration.toString(),
        included_services: plano.included_services || [],
        benefits: plano.benefits || [],
        active: plano.active,
    });

    const [newService, setNewService] = useState('');
    const [newBenefit, setNewBenefit] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.planos.update', plano.id));
    };

    const addService = () => {
        if (newService.trim() && !data.included_services.includes(newService.trim())) {
            setData('included_services', [...data.included_services, newService.trim()]);
            setNewService('');
        }
    };

    const removeService = (index: number) => {
        setData('included_services', data.included_services.filter((_, i) => i !== index));
    };

    const addBenefit = () => {
        if (newBenefit.trim() && !data.benefits.includes(newBenefit.trim())) {
            setData('benefits', [...data.benefits, newBenefit.trim()]);
            setNewBenefit('');
        }
    };

    const removeBenefit = (index: number) => {
        setData('benefits', data.benefits.filter((_, i) => i !== index));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Editar ${plano.name}`} />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                <div className="flex items-center gap-4">
                    <Link href={route('admin.planos.index')}>
                        <Button variant="ghost" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Editar {plano.name}</h1>
                        <p className="text-gray-600">Atualize as informações do plano</p>
                    </div>
                </div>

                {plano.assinaturas_count > 0 && (
                    <div className="rounded-md border border-yellow-200 bg-yellow-50 p-4">
                        <p className="text-sm text-yellow-800">
                            <strong>Atenção:</strong> Este plano possui {plano.assinaturas_count} assinatura(s) ativa(s). 
                            Alterações podem afetar os usuários existentes.
                        </p>
                    </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Informações Básicas</CardTitle>
                            <CardDescription>
                                Atualize as informações principais do plano
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Nome do Plano</Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Ex: Fisio Essencial"
                                        required
                                    />
                                    {errors.name && (
                                        <p className="text-sm text-red-600">{errors.name}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="price">Preço Mensal (R$)</Label>
                                    <Input
                                        id="price"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        value={data.price}
                                        onChange={(e) => setData('price', e.target.value)}
                                        placeholder="129.90"
                                        required
                                    />
                                    {errors.price && (
                                        <p className="text-sm text-red-600">{errors.price}</p>
                                    )}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Descrição</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Descreva os detalhes do plano..."
                                    rows={3}
                                    required
                                />
                                {errors.description && (
                                    <p className="text-sm text-red-600">{errors.description}</p>
                                )}
                            </div>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="sessions_per_month">Sessões por Mês</Label>
                                    <Input
                                        id="sessions_per_month"
                                        type="number"
                                        min="1"
                                        value={data.sessions_per_month}
                                        onChange={(e) => setData('sessions_per_month', e.target.value)}
                                        placeholder="4"
                                        required
                                    />
                                    {errors.sessions_per_month && (
                                        <p className="text-sm text-red-600">{errors.sessions_per_month}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="session_duration">Duração da Sessão (minutos)</Label>
                                    <Input
                                        id="session_duration"
                                        type="number"
                                        min="30"
                                        step="15"
                                        value={data.session_duration}
                                        onChange={(e) => setData('session_duration', e.target.value)}
                                        placeholder="60"
                                        required
                                    />
                                    {errors.session_duration && (
                                        <p className="text-sm text-red-600">{errors.session_duration}</p>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Serviços Inclusos</CardTitle>
                            <CardDescription>
                                Gerencie os serviços que estão inclusos neste plano
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex gap-2">
                                <Input
                                    value={newService}
                                    onChange={(e) => setNewService(e.target.value)}
                                    placeholder="Ex: Fisioterapia domiciliar"
                                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addService())}
                                />
                                <Button type="button" onClick={addService} size="sm">
                                    <Plus className="h-4 w-4" />
                                </Button>
                            </div>

                            <div className="space-y-2">
                                {data.included_services.map((service, index) => (
                                    <div key={index} className="flex items-center justify-between rounded-md border p-2">
                                        <span className="text-sm">{service}</span>
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => removeService(index)}
                                        >
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </div>
                                ))}
                            </div>

                            {errors.included_services && (
                                <p className="text-sm text-red-600">{errors.included_services}</p>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Benefícios</CardTitle>
                            <CardDescription>
                                Gerencie os benefícios exclusivos deste plano
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex gap-2">
                                <Input
                                    value={newBenefit}
                                    onChange={(e) => setNewBenefit(e.target.value)}
                                    placeholder="Ex: Atendimento prioritário"
                                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addBenefit())}
                                />
                                <Button type="button" onClick={addBenefit} size="sm">
                                    <Plus className="h-4 w-4" />
                                </Button>
                            </div>

                            <div className="space-y-2">
                                {data.benefits.map((benefit, index) => (
                                    <div key={index} className="flex items-center justify-between rounded-md border p-2">
                                        <span className="text-sm">{benefit}</span>
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => removeBenefit(index)}
                                        >
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </div>
                                ))}
                            </div>

                            {errors.benefits && (
                                <p className="text-sm text-red-600">{errors.benefits}</p>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Configurações</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="active"
                                    checked={data.active}
                                    onCheckedChange={(checked) => setData('active', !!checked)}
                                />
                                <Label htmlFor="active">Plano ativo</Label>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="flex justify-end gap-4">
                        <Link href={route('admin.planos.index')}>
                            <Button variant="outline" type="button">
                                Cancelar
                            </Button>
                        </Link>
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Salvando...' : 'Atualizar Plano'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
