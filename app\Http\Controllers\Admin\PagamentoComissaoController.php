<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PagamentoComissao;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PagamentoComissaoController extends Controller
{
    /**
     * Display a listing of payment requests
     */
    public function index(Request $request)
    {
        $query = PagamentoComissao::with(['afiliado', 'aprovadoPor']);

        // Filtros
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $query->whereHas('afiliado', function ($q) use ($request) {
                $q->where('nome', 'like', '%' . $request->search . '%')
                  ->orWhere('codigo_afiliado', 'like', '%' . $request->search . '%');
            });
        }

        $pagamentos = $query->orderBy('created_at', 'desc')
                           ->paginate(15)
                           ->withQueryString();

        // Estatísticas
        $stats = [
            'total_pendentes' => PagamentoComissao::pendentes()->count(),
            'total_aprovados' => PagamentoComissao::aprovados()->count(),
            'total_pagos' => PagamentoComissao::pagos()->count(),
            'valor_total_pendente' => PagamentoComissao::pendentes()->sum('valor_solicitado'),
            'valor_total_pago' => PagamentoComissao::pagos()->sum('valor_solicitado'),
        ];

        return Inertia::render('admin/pagamentos-comissao/index', [
            'pagamentos' => $pagamentos,
            'stats' => $stats,
            'filters' => $request->only(['status', 'search']),
        ]);
    }

    /**
     * Display the specified payment request
     */
    public function show(PagamentoComissao $pagamento)
    {
        $pagamento->load(['afiliado', 'aprovadoPor']);

        return Inertia::render('admin/pagamentos-comissao/show', [
            'pagamento' => $pagamento,
        ]);
    }

    /**
     * Approve payment request
     */
    public function aprovar(Request $request, PagamentoComissao $pagamento)
    {
        if ($pagamento->status !== 'pendente') {
            return back()->with('error', 'Esta solicitação não pode ser aprovada.');
        }

        $request->validate([
            'observacoes_admin' => 'nullable|string|max:1000',
        ]);

        $pagamento->aprovar(auth()->id(), $request->observacoes_admin);

        return redirect()->route('admin.pagamentos-comissao.index')
                        ->with('success', 'Solicitação de pagamento aprovada com sucesso!');
    }

    /**
     * Reject payment request
     */
    public function rejeitar(Request $request, PagamentoComissao $pagamento)
    {
        if ($pagamento->status !== 'pendente') {
            return back()->with('error', 'Esta solicitação não pode ser rejeitada.');
        }

        $request->validate([
            'observacoes_admin' => 'required|string|max:1000',
        ]);

        $pagamento->rejeitar(auth()->id(), $request->observacoes_admin);

        return redirect()->route('admin.pagamentos-comissao.index')
                        ->with('success', 'Solicitação de pagamento rejeitada.');
    }

    /**
     * Mark payment as paid
     */
    public function marcarComoPago(Request $request, PagamentoComissao $pagamento)
    {
        if ($pagamento->status !== 'aprovado') {
            return back()->with('error', 'Esta solicitação não pode ser marcada como paga.');
        }

        $request->validate([
            'comprovante' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
        ]);

        $comprovantePath = null;
        if ($request->hasFile('comprovante')) {
            $comprovantePath = $request->file('comprovante')->store('comprovantes-pagamento', 'public');
        }

        $pagamento->marcarComoPago($comprovantePath);

        return redirect()->route('admin.pagamentos-comissao.index')
                        ->with('success', 'Pagamento marcado como realizado com sucesso!');
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:aprovar,rejeitar',
            'pagamentos' => 'required|array|min:1',
            'pagamentos.*' => 'exists:pagamentos_comissao,id',
            'observacoes_admin' => 'nullable|string|max:1000',
        ]);

        $pagamentos = PagamentoComissao::whereIn('id', $request->pagamentos)
                                      ->where('status', 'pendente')
                                      ->get();

        if ($pagamentos->isEmpty()) {
            return back()->with('error', 'Nenhuma solicitação válida encontrada.');
        }

        $count = 0;
        foreach ($pagamentos as $pagamento) {
            if ($request->action === 'aprovar') {
                $pagamento->aprovar(auth()->id(), $request->observacoes_admin);
            } else {
                $pagamento->rejeitar(auth()->id(), $request->observacoes_admin ?: 'Rejeitado em lote');
            }
            $count++;
        }

        $action = $request->action === 'aprovar' ? 'aprovadas' : 'rejeitadas';

        return redirect()->route('admin.pagamentos-comissao.index')
                        ->with('success', "{$count} solicitações {$action} com sucesso!");
    }
}
