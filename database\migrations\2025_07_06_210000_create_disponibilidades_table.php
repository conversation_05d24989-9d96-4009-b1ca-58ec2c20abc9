<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('disponibilidades', function (Blueprint $table) {
            $table->id();
            $table->foreignId('fisioterapeuta_id')->constrained('users')->onDelete('cascade');
            $table->enum('tipo', ['disponivel', 'indisponivel', 'bloqueio'])->default('disponivel');
            $table->date('data_inicio');
            $table->date('data_fim')->nullable(); // Para bloqueios recorrentes
            $table->time('hora_inicio');
            $table->time('hora_fim');
            $table->json('dias_semana')->nullable(); // [1,2,3,4,5] para seg-sex
            $table->boolean('recorrente')->default(false);
            $table->text('motivo')->nullable(); // Motivo do bloqueio
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Índices para performance
            $table->index(['fisioterapeuta_id', 'data_inicio', 'data_fim']);
            $table->index(['fisioterapeuta_id', 'tipo', 'ativo']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('disponibilidades');
    }
};
