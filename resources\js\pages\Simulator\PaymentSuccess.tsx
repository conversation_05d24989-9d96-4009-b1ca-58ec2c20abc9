import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Home, Receipt } from 'lucide-react';

interface Pagamento {
    id: number;
    transaction_id: string;
    amount: number;
    formatted_amount: string;
    status: string;
    paid_at?: string;
}

interface Props {
    pagamento: Pagamento;
}

export default function PaymentSuccess({ pagamento }: Props) {
    return (
        <>
            <Head title="Pagamento Aprovado - Simulador" />
            
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
                <div className="w-full max-w-md">
                    <div className="text-center mb-6">
                        <div className="mx-auto w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mb-4">
                            <CheckCircle className="w-10 h-10 text-white" />
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Pagamento Aprovado!
                        </h1>
                        <p className="text-gray-600">
                            Sua transação foi processada com sucesso
                        </p>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-center text-green-600">
                                Transação Concluída
                            </CardTitle>
                            <CardDescription className="text-center">
                                ID: {pagamento.transaction_id}
                            </CardDescription>
                        </CardHeader>
                        
                        <CardContent className="space-y-4">
                            <div className="bg-green-50 p-4 rounded-lg text-center">
                                <p className="text-sm text-gray-600 mb-1">Valor Pago</p>
                                <p className="text-3xl font-bold text-green-600">
                                    {pagamento.formatted_amount}
                                </p>
                            </div>

                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Status:</span>
                                    <span className="font-medium text-green-600">
                                        {pagamento.status === 'pago' ? 'Aprovado' : pagamento.status}
                                    </span>
                                </div>
                                {pagamento.paid_at && (
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Data/Hora:</span>
                                        <span className="font-medium">{pagamento.paid_at}</span>
                                    </div>
                                )}
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Método:</span>
                                    <span className="font-medium">PIX (Simulado)</span>
                                </div>
                            </div>

                            <div className="pt-4 border-t space-y-3">
                                <h4 className="font-semibold text-gray-900">
                                    Próximos Passos:
                                </h4>
                                <ul className="text-sm text-gray-600 space-y-1">
                                    <li>• Sua assinatura foi ativada automaticamente</li>
                                    <li>• Você receberá um email de confirmação</li>
                                    <li>• O acesso aos recursos está liberado</li>
                                </ul>
                            </div>

                            <div className="grid grid-cols-2 gap-3 pt-4">
                                <Button asChild variant="outline">
                                    <Link href={route('paciente.pagamentos.show', pagamento.id)}>
                                        <Receipt className="w-4 h-4 mr-2" />
                                        Ver Comprovante
                                    </Link>
                                </Button>
                                
                                <Button asChild>
                                    <Link href={route('dashboard')}>
                                        <Home className="w-4 h-4 mr-2" />
                                        Ir ao Dashboard
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="mt-6 text-center">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <p className="text-sm text-blue-800 font-medium mb-1">
                                🎉 Simulação Concluída
                            </p>
                            <p className="text-xs text-blue-600">
                                Este pagamento foi processado em ambiente de desenvolvimento.
                                Em produção, seria processado pelo Mercado Pago real.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
