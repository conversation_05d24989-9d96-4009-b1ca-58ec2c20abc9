<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('relatorios_sessao', function (Blueprint $table) {
            $table->foreignId('fisioterapeuta_id')->nullable()->after('agendamento_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('paciente_id')->nullable()->after('fisioterapeuta_id')->constrained('users')->onDelete('cascade');
            $table->text('exercises')->nullable()->after('observations');
            $table->text('progress_notes')->nullable()->after('exercises');
            $table->text('next_steps')->nullable()->after('progress_notes');
            $table->integer('pain_level_before')->nullable()->after('next_steps');
            $table->integer('pain_level_after')->nullable()->after('pain_level_before');
            $table->enum('mobility_assessment', ['limitada', 'parcial', 'normal', 'melhorada'])->nullable()->after('pain_level_after');
            $table->integer('patient_satisfaction')->nullable()->after('mobility_assessment');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('relatorios_sessao', function (Blueprint $table) {
            $table->dropForeign(['fisioterapeuta_id']);
            $table->dropForeign(['paciente_id']);
            $table->dropColumn([
                'fisioterapeuta_id',
                'paciente_id',
                'exercises',
                'progress_notes',
                'next_steps',
                'pain_level_before',
                'pain_level_after',
                'mobility_assessment',
                'patient_satisfaction'
            ]);
        });
    }
};
