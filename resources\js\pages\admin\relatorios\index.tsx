import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { BarChart3, Calendar, DollarSign, Download, FileText, Filter, RefreshCw, TrendingDown, TrendingUp, Users } from 'lucide-react';
import { useState } from 'react';
import { Bar, BarChart, CartesianGrid, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface Stats {
    total_pacientes: number;
    total_fisioterapeutas: number;
    sessoes_periodo: number;
    receita_periodo: number;
    assinaturas_ativas: number;
    taxa_crescimento: number;
}

interface ChartDataItem {
    date: string;
    sessoes: number;
    receita: number;
}

interface Props {
    stats: Stats;
    chartData: ChartDataItem[];
    filtros: {
        data_inicio?: string;
        data_fim?: string;
        tipo?: string;
    };
    tiposRelatorio: Record<string, string>;
}

export default function RelatoriosIndex({ stats, chartData, filtros, tiposRelatorio }: Props) {
    const [dataInicio, setDataInicio] = useState(filtros.data_inicio || '');
    const [dataFim, setDataFim] = useState(filtros.data_fim || '');
    const [tipoRelatorio, setTipoRelatorio] = useState(filtros.tipo || 'geral');
    const [isLoading, setIsLoading] = useState(false);

    const handleFilter = () => {
        setIsLoading(true);
        router.get(
            route('admin.relatorios.index'),
            {
                data_inicio: dataInicio,
                data_fim: dataFim,
                tipo: tipoRelatorio,
            },
            {
                preserveState: true,
                onFinish: () => setIsLoading(false),
            },
        );
    };

    const handleExport = (formato: string) => {
        const params = new URLSearchParams({
            tipo: tipoRelatorio,
            formato,
            data_inicio: dataInicio,
            data_fim: dataFim,
        });

        window.open(`${route('admin.relatorios.export')}?${params.toString()}`, '_blank');
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const formatNumber = (value: number) => {
        return new Intl.NumberFormat('pt-BR').format(value);
    };

    const getTrendIcon = (value: number) => {
        if (value > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
        if (value < 0) return <TrendingDown className="h-4 w-4 text-red-600" />;
        return null;
    };

    const getTrendColor = (value: number) => {
        if (value > 0) return 'text-green-600';
        if (value < 0) return 'text-red-600';
        return 'text-gray-600';
    };

    return (
        <AppLayout>
            <Head title="Relatórios" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Relatórios</h1>
                        <p className="text-muted-foreground">Análise completa do desempenho da plataforma</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={() => handleExport('csv')} className="gap-2">
                            <Download className="h-4 w-4" />
                            Exportar CSV
                        </Button>
                        <Button variant="outline" onClick={() => handleExport('excel')} className="gap-2">
                            <Download className="h-4 w-4" />
                            Exportar Excel
                        </Button>
                    </div>
                </div>

                {/* Filtros */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="space-y-2">
                                <Label htmlFor="data_inicio">Data Início</Label>
                                <Input id="data_inicio" type="date" value={dataInicio} onChange={(e) => setDataInicio(e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="data_fim">Data Fim</Label>
                                <Input id="data_fim" type="date" value={dataFim} onChange={(e) => setDataFim(e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="tipo">Tipo de Relatório</Label>
                                <Select value={tipoRelatorio} onValueChange={setTipoRelatorio}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(tiposRelatorio).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>
                                                {label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-end">
                                <Button onClick={handleFilter} disabled={isLoading} className="w-full gap-2">
                                    {isLoading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <BarChart3 className="h-4 w-4" />}
                                    Aplicar Filtros
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Cards de Estatísticas */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Pacientes</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(stats.total_pacientes)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total de Fisioterapeutas</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(stats.total_fisioterapeutas)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Sessões no Período</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(stats.sessoes_periodo)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Receita no Período</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.receita_periodo)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Assinaturas Ativas</CardTitle>
                            <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatNumber(stats.assinaturas_ativas)}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Taxa de Crescimento</CardTitle>
                            {getTrendIcon(stats.taxa_crescimento)}
                        </CardHeader>
                        <CardContent>
                            <div className={`text-2xl font-bold ${getTrendColor(stats.taxa_crescimento)}`}>
                                {stats.taxa_crescimento > 0 ? '+' : ''}
                                {stats.taxa_crescimento}%
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Gráficos */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Evolução de Sessões</CardTitle>
                            <CardDescription>Número de sessões por dia no período</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <LineChart data={chartData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="date" />
                                    <YAxis />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="sessoes" stroke="#8884d8" strokeWidth={2} />
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Evolução da Receita</CardTitle>
                            <CardDescription>Receita por dia no período</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <BarChart data={chartData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="date" />
                                    <YAxis />
                                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Receita']} />
                                    <Bar dataKey="receita" fill="#82ca9d" />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Links para Relatórios Específicos */}
                <Card>
                    <CardHeader>
                        <CardTitle>Relatórios Específicos</CardTitle>
                        <CardDescription>Acesse relatórios detalhados por categoria</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <Link href={route('admin.relatorios.financeiro')}>
                                <Card className="cursor-pointer transition-shadow hover:shadow-md">
                                    <CardContent className="p-6 text-center">
                                        <DollarSign className="mx-auto mb-2 h-8 w-8 text-green-600" />
                                        <h3 className="font-semibold">Financeiro</h3>
                                        <p className="text-sm text-muted-foreground">Receitas, pagamentos e análises financeiras</p>
                                    </CardContent>
                                </Card>
                            </Link>

                            <Link href={route('admin.relatorios.operacional')}>
                                <Card className="cursor-pointer transition-shadow hover:shadow-md">
                                    <CardContent className="p-6 text-center">
                                        <BarChart3 className="mx-auto mb-2 h-8 w-8 text-blue-600" />
                                        <h3 className="font-semibold">Operacional</h3>
                                        <p className="text-sm text-muted-foreground">Performance e métricas operacionais</p>
                                    </CardContent>
                                </Card>
                            </Link>

                            <Link href={route('admin.relatorios.pacientes')}>
                                <Card className="cursor-pointer transition-shadow hover:shadow-md">
                                    <CardContent className="p-6 text-center">
                                        <Users className="mx-auto mb-2 h-8 w-8 text-purple-600" />
                                        <h3 className="font-semibold">Pacientes</h3>
                                        <p className="text-sm text-muted-foreground">Análise de pacientes e retenção</p>
                                    </CardContent>
                                </Card>
                            </Link>

                            <Link href={route('admin.relatorios.fisioterapeutas')}>
                                <Card className="cursor-pointer transition-shadow hover:shadow-md">
                                    <CardContent className="p-6 text-center">
                                        <Users className="mx-auto mb-2 h-8 w-8 text-orange-600" />
                                        <h3 className="font-semibold">Fisioterapeutas</h3>
                                        <p className="text-sm text-muted-foreground">Performance e ranking de fisioterapeutas</p>
                                    </CardContent>
                                </Card>
                            </Link>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
