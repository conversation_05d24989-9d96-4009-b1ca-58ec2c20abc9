import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import PublicLayout from '@/layouts/public-layout';
import { AlertCircle, Building2, Check, MapPin, Phone } from 'lucide-react';
import { useState } from 'react';

interface FormData {
    nome: string;
    categoria: string;
    descricao: string;
    telefone: string;
    whatsapp: string;
    email: string;
    endereco: string;
    cidade: string;
    estado: string;
    cep: string;
    servicos_oferecidos: string;
    site: string;
    instagram: string;
    facebook: string;
}

export default function CadastroEmpresa() {
    const [formData, setFormData] = useState<FormData>({
        nome: '',
        categoria: '',
        descricao: '',
        telefone: '',
        whatsapp: '',
        email: '',
        endereco: '',
        cidade: '',
        estado: '',
        cep: '',
        servicos_oferecidos: '',
        site: '',
        instagram: '',
        facebook: '',
    });

    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState('');
    const [estabelecimentoId, setEstabelecimentoId] = useState<number | null>(null);

    const handleInputChange = (field: keyof FormData, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
    };

    const buscarCep = async (cep: string) => {
        const cepLimpo = cep.replace(/\D/g, '');
        if (cepLimpo.length === 8) {
            try {
                const response = await fetch(`https://viacep.com.br/ws/${cepLimpo}/json/`);
                const data = await response.json();

                if (!data.erro) {
                    setFormData((prev) => ({
                        ...prev,
                        endereco: data.logradouro || prev.endereco,
                        cidade: data.localidade || prev.cidade,
                        estado: data.uf || prev.estado,
                    }));
                }
            } catch (err) {
                // Falha silenciosa
            }
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
            const response = await fetch('/empresa/cadastro', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    Accept: 'application/json',
                },
                body: JSON.stringify(formData),
            });

            const data = await response.json();

            if (data.success) {
                setSuccess(true);
                setEstabelecimentoId(data.estabelecimento_id);
            } else {
                setError(data.message || 'Erro ao cadastrar empresa');
            }
        } catch (err) {
            setError('Erro ao conectar com o servidor');
        } finally {
            setLoading(false);
        }
    };

    const ativarPlano = async () => {
        if (!estabelecimentoId) return;

        setLoading(true);
        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
            const response = await fetch(`/empresa/${estabelecimentoId}/ativar-plano`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    Accept: 'application/json',
                },
                body: JSON.stringify({ metodo_pagamento: 'pix' }),
            });

            const data = await response.json();

            if (data.success) {
                alert(`Plano ativado! Vencimento: ${data.vencimento}`);
                window.location.href = '/';
            } else {
                setError(data.message || 'Erro ao ativar plano');
            }
        } catch (err) {
            setError('Erro ao ativar plano');
        } finally {
            setLoading(false);
        }
    };

    if (success) {
        return (
            <PublicLayout
                title="Cadastro Realizado - F4 Fisio"
                description="Sua empresa foi cadastrada com sucesso! Ative seu plano para aparecer nas buscas por localização."
            >
                {/* Hero Section */}
                <section className="bg-gradient-to-b from-background to-muted/30">
                    <div className="relative py-20 md:py-36">
                        <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="text-center">
                                <h1 className="mx-auto max-w-4xl text-5xl font-medium text-balance md:text-6xl">
                                    Cadastro
                                    <span className="block text-primary">Realizado!</span>
                                </h1>
                                <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                    Sua empresa foi cadastrada com sucesso em nosso sistema. Agora você pode ativar seu plano para aparecer nas
                                    buscas.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Conteúdo Section */}
                <section className="bg-background py-12">
                    <div className="mx-auto max-w-2xl px-4 sm:px-6 lg:px-8">
                        <Card>
                            <CardHeader className="text-center">
                                <Badge variant="gradient" size="icon-lg" className="mx-auto mb-4 h-16 w-16">
                                    <Check className="h-8 w-8" />
                                </Badge>
                                <CardTitle className="text-2xl text-primary">Cadastro Realizado com Sucesso!</CardTitle>
                                <CardDescription>
                                    Sua empresa foi cadastrada em nosso sistema. Agora você pode ativar seu plano para aparecer nas buscas.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="rounded-lg bg-primary/5 p-6">
                                    <div className="mb-4 flex items-center gap-3">
                                        <Badge variant="gradient" size="icon-lg" className="h-8 w-8">
                                            <Building2 className="h-4 w-4" />
                                        </Badge>
                                        <h3 className="text-lg font-semibold">Plano Empresarial</h3>
                                    </div>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex items-center gap-2">
                                            <svg className="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                            </svg>
                                            <span>Apareça nas buscas por localização</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <svg className="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                            </svg>
                                            <span>Link direto para WhatsApp</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <svg className="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                            </svg>
                                            <span>Mais visibilidade para seu negócio</span>
                                        </div>
                                    </div>
                                    <div className="mt-4 text-center">
                                        <div className="text-2xl font-bold text-primary">R$ 14,90/mês</div>
                                        <div className="text-sm text-muted-foreground">Primeiro mês grátis!</div>
                                    </div>
                                </div>

                                <div className="flex gap-4">
                                    <Button onClick={ativarPlano} disabled={loading} className="flex-1">
                                        {loading ? 'Ativando...' : 'Ativar Plano Agora'}
                                    </Button>
                                    <Button variant="outline" onClick={() => (window.location.href = '/')}>
                                        Ativar Depois
                                    </Button>
                                </div>

                                {error && (
                                    <Alert variant="destructive">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription>{error}</AlertDescription>
                                    </Alert>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </section>
            </PublicLayout>
        );
    }

    return (
        <PublicLayout
            title="Cadastre sua Empresa - F4 Fisio"
            description="Cadastre sua empresa de saúde na F4 Fisio e apareça nas buscas por localização. Aumente sua visibilidade e conquiste mais clientes."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-36">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-5xl font-medium text-balance md:text-6xl">
                                Cadastre sua
                                <span className="block text-primary">Empresa</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Coloque sua empresa no mapa da saúde e aumente seu número de clientes. Apareça nas buscas por localização e conecte-se
                                diretamente com seus pacientes.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Formulário Section */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Building2 className="h-5 w-5" />
                                Informações da Empresa
                            </CardTitle>
                            <CardDescription>Preencha os dados da sua empresa para aparecer nas buscas por localização</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Informações Básicas */}
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold">Informações Básicas</h3>

                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="nome">Nome da Empresa *</Label>
                                            <Input
                                                id="nome"
                                                value={formData.nome}
                                                onChange={(e) => handleInputChange('nome', e.target.value)}
                                                placeholder="Ex: Clínica Odontológica Sorriso"
                                                required
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="categoria">Categoria *</Label>
                                            <select
                                                id="categoria"
                                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                                                value={formData.categoria}
                                                onChange={(e) => handleInputChange('categoria', e.target.value)}
                                                required
                                            >
                                                <option value="">Selecione uma categoria</option>
                                                <option value="dentista">🦷 Dentista</option>
                                                <option value="farmacia">💊 Farmácia</option>
                                                <option value="fisioterapia">💪 Fisioterapia</option>
                                                <option value="outros">🏥 Outros</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="descricao">Descrição</Label>
                                        <Textarea
                                            id="descricao"
                                            value={formData.descricao}
                                            onChange={(e) => handleInputChange('descricao', e.target.value)}
                                            placeholder="Descreva os serviços oferecidos pela sua empresa..."
                                            rows={3}
                                        />
                                    </div>
                                </div>

                                <Separator />

                                {/* Contato */}
                                <div className="space-y-4">
                                    <h3 className="flex items-center gap-2 text-lg font-semibold">
                                        <Phone className="h-5 w-5" />
                                        Contato
                                    </h3>

                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="whatsapp">WhatsApp * (com DDD)</Label>
                                            <Input
                                                id="whatsapp"
                                                value={formData.whatsapp}
                                                onChange={(e) => handleInputChange('whatsapp', e.target.value)}
                                                placeholder="Ex: 11987654321"
                                                required
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="telefone">Telefone (com DDD)</Label>
                                            <Input
                                                id="telefone"
                                                value={formData.telefone}
                                                onChange={(e) => handleInputChange('telefone', e.target.value)}
                                                placeholder="Ex: 1134567890"
                                            />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="email">E-mail</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={formData.email}
                                            onChange={(e) => handleInputChange('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                </div>

                                <Separator />

                                {/* Endereço */}
                                <div className="space-y-4">
                                    <h3 className="flex items-center gap-2 text-lg font-semibold">
                                        <MapPin className="h-5 w-5" />
                                        Localização
                                    </h3>

                                    <div className="grid gap-4 md:grid-cols-3">
                                        <div className="space-y-2">
                                            <Label htmlFor="cep">CEP *</Label>
                                            <Input
                                                id="cep"
                                                value={formData.cep}
                                                onChange={(e) => {
                                                    const cep = e.target.value;
                                                    handleInputChange('cep', cep);
                                                    if (cep.length === 9) {
                                                        buscarCep(cep);
                                                    }
                                                }}
                                                placeholder="00000-000"
                                                maxLength={9}
                                                required
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="cidade">Cidade *</Label>
                                            <Input
                                                id="cidade"
                                                value={formData.cidade}
                                                onChange={(e) => handleInputChange('cidade', e.target.value)}
                                                placeholder="São Paulo"
                                                required
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="estado">Estado *</Label>
                                            <Input
                                                id="estado"
                                                value={formData.estado}
                                                onChange={(e) => handleInputChange('estado', e.target.value)}
                                                placeholder="SP"
                                                maxLength={2}
                                                required
                                            />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="endereco">Endereço Completo *</Label>
                                        <Input
                                            id="endereco"
                                            value={formData.endereco}
                                            onChange={(e) => handleInputChange('endereco', e.target.value)}
                                            placeholder="Rua, número, bairro"
                                            required
                                        />
                                    </div>
                                </div>

                                {error && (
                                    <Alert variant="destructive">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription>{error}</AlertDescription>
                                    </Alert>
                                )}

                                <Button type="submit" disabled={loading} className="w-full">
                                    {loading ? 'Cadastrando...' : 'Cadastrar Empresa'}
                                </Button>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </section>
        </PublicLayout>
    );
}
