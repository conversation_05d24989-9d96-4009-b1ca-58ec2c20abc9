import { Button } from '@/components/ui/button';
import PublicLayout from '@/layouts/public-layout';
import { Link } from '@inertiajs/react';
import { CheckCircle, Home, MessageCircle, Phone } from 'lucide-react';

export default function ContatoSucesso() {
    return (
        <PublicLayout
            title="Mensagem Enviada - F4 Fisio"
            description="Sua mensagem foi enviada com sucesso. Nossa equipe entrará em contato em breve."
        >
            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30">
                <div className="relative py-20 md:py-32">
                    <div className="relative z-10 mx-auto w-full max-w-4xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="mx-auto mb-8 flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
                                <CheckCircle className="h-10 w-10 text-green-600" />
                            </div>
                            <h1 className="mx-auto max-w-4xl text-4xl font-medium text-balance md:text-5xl">
                                Mensagem Enviada com
                                <span className="block text-green-600">Sucesso!</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Recebemos sua mensagem e nossa equipe entrará em contato em até 24 horas. Obrigado por escolher a F4 Fisio!
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Próximos Passos */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">O que acontece agora?</h2>
                        <p className="mx-auto mt-4 max-w-2xl text-xl text-balance text-muted-foreground">
                            Veja os próximos passos do nosso processo de atendimento
                        </p>
                    </div>

                    <div className="mt-12 grid gap-8 md:grid-cols-3">
                        <div className="text-center">
                            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                                <span className="text-lg font-bold text-blue-600">1</span>
                            </div>
                            <h3 className="text-lg font-semibold">Análise da Mensagem</h3>
                            <p className="mt-2 text-muted-foreground">
                                Nossa equipe analisará sua solicitação e preparará uma resposta personalizada.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                                <span className="text-lg font-bold text-green-600">2</span>
                            </div>
                            <h3 className="text-lg font-semibold">Contato Direto</h3>
                            <p className="mt-2 text-muted-foreground">
                                Entraremos em contato por email ou telefone em até 24 horas úteis.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                                <span className="text-lg font-bold text-purple-600">3</span>
                            </div>
                            <h3 className="text-lg font-semibold">Atendimento</h3>
                            <p className="mt-2 text-muted-foreground">
                                Agendar sua consulta ou esclarecer todas as suas dúvidas.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Contato Urgente */}
            <section className="bg-muted/30 py-12">
                <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                    <div className="rounded-2xl border bg-card p-6 text-center shadow-sm sm:p-8">
                        <h2 className="text-2xl font-medium text-card-foreground">Precisa de Atendimento Urgente?</h2>
                        <p className="mt-4 text-xl text-balance text-muted-foreground">
                            Para casos urgentes, entre em contato diretamente conosco
                        </p>
                        <div className="mt-8 flex flex-col gap-3 sm:flex-row sm:justify-center">
                            <Button asChild>
                                <a href="tel:+5511978196207">
                                    <Phone className="h-4 w-4" />
                                    Ligar Agora
                                </a>
                            </Button>
                            <Button variant="outline" asChild>
                                <a
                                    href="https://wa.me/5511978196207?text=Olá! Enviei uma mensagem pelo site e preciso de atendimento urgente."
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <MessageCircle className="h-4 w-4" />
                                    WhatsApp
                                </a>
                            </Button>
                        </div>
                    </div>
                </div>
            </section>

            {/* Navegação */}
            <section className="bg-background py-12">
                <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h2 className="text-2xl font-medium text-balance">Enquanto aguarda nossa resposta</h2>
                        <p className="mt-4 text-muted-foreground">
                            Conheça mais sobre nossos serviços e como podemos ajudar você
                        </p>
                        <div className="mt-8 flex flex-col gap-3 sm:flex-row sm:justify-center">
                            <Button variant="outline" asChild>
                                <Link href="/">
                                    <Home className="h-4 w-4" />
                                    Voltar ao Início
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/servicos">
                                    Nossos Serviços
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/faq">
                                    Perguntas Frequentes
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
