import {
  Button,
  Heading,
  Hr,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';
import { EmailLayout } from './components/layout';

interface BoasVindasEmailProps {
  nome: string;
  email: string;
  tipoUsuario: 'paciente' | 'fisioterapeuta' | 'empresa';
  loginUrl?: string;
}

export const BoasVindasEmail = ({
  nome = 'Usuário',
  email = '<EMAIL>',
  tipoUsuario = 'paciente',
  loginUrl = 'https://f4fisio.com.br/login',
}: BoasVindasEmailProps) => {
  const getTipoTexto = () => {
    switch (tipoUsuario) {
      case 'fisioterapeuta':
        return 'fisioterapeuta';
      case 'empresa':
        return 'estabelecimento';
      default:
        return 'paciente';
    }
  };

  const getDescricaoServicos = () => {
    switch (tipoUsuario) {
      case 'fisioterapeuta':
        return 'Agora você pode gerenciar seus agendamentos, atender pacientes e expandir sua prática profissional através da nossa plataforma.';
      case 'empresa':
        return 'Agora você pode gerenciar seu estabelecimento, receber agendamentos e aumentar sua visibilidade na plataforma.';
      default:
        return 'Agora você pode agendar consultas, encontrar profissionais qualificados e cuidar da sua saúde de forma prática e segura.';
    }
  };

  const getProximosPassos = () => {
    switch (tipoUsuario) {
      case 'fisioterapeuta':
        return [
          'Complete seu perfil profissional',
          'Configure sua disponibilidade',
          'Defina seus preços e serviços',
          'Comece a receber agendamentos',
        ];
      case 'empresa':
        return [
          'Complete as informações do seu estabelecimento',
          'Adicione fotos e descrição dos serviços',
          'Configure horários de funcionamento',
          'Ative sua visibilidade nas buscas',
        ];
      default:
        return [
          'Complete seu perfil',
          'Explore profissionais na sua região',
          'Agende sua primeira consulta',
          'Avalie os serviços recebidos',
        ];
    }
  };

  return (
    <EmailLayout preview={`Bem-vindo(a) à F4 Fisio, ${nome}!`}>
      <Heading style={h1}>
        Bem-vindo(a) à F4 Fisio! 🎉
      </Heading>
      
      <Text style={text}>
        Olá <strong>{nome}</strong>,
      </Text>
      
      <Text style={text}>
        É com grande alegria que damos as boas-vindas a você como novo {getTipoTexto()} da F4 Fisio!
      </Text>
      
      <Text style={text}>
        {getDescricaoServicos()}
      </Text>
      
      <Section style={buttonContainer}>
        <Button style={button} href={loginUrl}>
          Acessar Minha Conta
        </Button>
      </Section>
      
      <Hr style={hr} />
      
      <Heading style={h2}>
        Próximos Passos
      </Heading>
      
      {getProximosPassos().map((passo, index) => (
        <Text key={index} style={listItem}>
          {index + 1}. {passo}
        </Text>
      ))}
      
      <Hr style={hr} />
      
      <Text style={text}>
        <strong>Dados da sua conta:</strong>
      </Text>
      <Text style={accountInfo}>
        Email: {email}
      </Text>
      
      <Text style={text}>
        Se você tiver alguma dúvida ou precisar de ajuda, nossa equipe de suporte está sempre disponível para ajudá-lo.
      </Text>
      
      <Text style={text}>
        Mais uma vez, seja muito bem-vindo(a)!
      </Text>
      
      <Text style={signature}>
        Atenciosamente,<br />
        Equipe F4 Fisio
      </Text>
    </EmailLayout>
  );
};

export default BoasVindasEmail;

const h1 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 0 20px 0',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#1f2937',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '20px 0 16px 0',
};

const text = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px 0',
};

const listItem = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 8px 0',
};

const accountInfo = {
  color: '#6b7280',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0 0 16px 0',
  padding: '12px',
  backgroundColor: '#f9fafb',
  borderRadius: '6px',
};

const signature = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '24px 0 0 0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '24px 0',
};

const button = {
  backgroundColor: '#3b82f6',
  borderRadius: '6px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '24px 0',
};
