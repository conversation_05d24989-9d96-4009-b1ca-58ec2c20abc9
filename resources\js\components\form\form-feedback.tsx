import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

export type FeedbackType = 'success' | 'error' | 'warning' | 'info';

export interface FormFeedbackProps {
    type: FeedbackType;
    message: string;
    title?: string;
    className?: string;
    onClose?: () => void;
}

const feedbackConfig = {
    success: {
        icon: CheckCircle,
        className: 'border-green-200 bg-green-50 text-green-800',
        iconClassName: 'text-green-600'
    },
    error: {
        icon: XCircle,
        className: 'border-red-200 bg-red-50 text-red-800',
        iconClassName: 'text-red-600'
    },
    warning: {
        icon: AlertTriangle,
        className: 'border-yellow-200 bg-yellow-50 text-yellow-800',
        iconClassName: 'text-yellow-600'
    },
    info: {
        icon: Info,
        className: 'border-blue-200 bg-blue-50 text-blue-800',
        iconClassName: 'text-blue-600'
    }
};

export function FormFeedback({ type, message, title, className, onClose }: FormFeedbackProps) {
    const config = feedbackConfig[type];
    const Icon = config.icon;

    return (
        <Alert className={cn(config.className, className)}>
            <Icon className={cn("h-4 w-4", config.iconClassName)} />
            <div className="flex-1">
                {title && <div className="font-medium mb-1">{title}</div>}
                <AlertDescription>{message}</AlertDescription>
            </div>
            {onClose && (
                <button
                    onClick={onClose}
                    className="ml-auto opacity-70 hover:opacity-100 transition-opacity"
                    aria-label="Fechar"
                >
                    <XCircle className="h-4 w-4" />
                </button>
            )}
        </Alert>
    );
}

// Hook para gerenciar feedback de formulário
export function useFormFeedback() {
    const [feedback, setFeedback] = React.useState<{
        type: FeedbackType;
        message: string;
        title?: string;
    } | null>(null);

    const showFeedback = React.useCallback((type: FeedbackType, message: string, title?: string) => {
        setFeedback({ type, message, title });
    }, []);

    const clearFeedback = React.useCallback(() => {
        setFeedback(null);
    }, []);

    const showSuccess = React.useCallback((message: string, title?: string) => {
        showFeedback('success', message, title);
    }, [showFeedback]);

    const showError = React.useCallback((message: string, title?: string) => {
        showFeedback('error', message, title);
    }, [showFeedback]);

    const showWarning = React.useCallback((message: string, title?: string) => {
        showFeedback('warning', message, title);
    }, [showFeedback]);

    const showInfo = React.useCallback((message: string, title?: string) => {
        showFeedback('info', message, title);
    }, [showFeedback]);

    return {
        feedback,
        showFeedback,
        clearFeedback,
        showSuccess,
        showError,
        showWarning,
        showInfo
    };
}
